# Quantamental Stock Trading Strategy

**Source:** https://github.com/JonathanJing/Stock-Trading-Strategy


## Thesis

The project aims to develop a stock trading strategy that leverages quantamental analysis, combining quantitative and fundamental approaches. By using machine learning algorithms on financial indicators from Quandl and Yahoo Finance, the team predicts future stock returns and constructs a portfolio based on these predictions. The strategy selects stocks with fundamentals outperforming S&P 500 by 10%, optimizes weights for maximum Sharpe ratio using Monte Carlo simulation and Nelder-Mead Simplex algorithm, and backtests performance against benchmarks like SPY ETF.

## Universe & Rebalancing

**Universe:** The stock universe consists of companies listed on major US exchanges with market capitalization over $10 million. The team uses fundamental data from Quandl for the period between March 31, 2010, and November 11, 2020, and stock price data from Yahoo Finance.
**Rebalancing:** weekly


## Signals

- **Fundamental Outperformance Signal:** Stocks are selected if their fundamentals predict a mean quarterly return exceeding S&P 500 by at least 10%.
- **Sharpe Ratio Optimization Signal:** Portfolio weights are optimized weekly using Monte Carlo simulation and Nelder-Mead Simplex algorithm to maximize Sharpe ratio, assuming normal distribution of returns.
