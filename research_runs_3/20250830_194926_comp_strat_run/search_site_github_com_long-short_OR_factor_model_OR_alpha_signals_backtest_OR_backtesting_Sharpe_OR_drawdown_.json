[{"title": "A general backtest system for alpha trading strategy", "url": "https://github.com/yuba316/Alpha_Strategy_BackTest_System", "engines": ["google"]}, {"title": "Backtested trading strategies using ML for signals", "url": "https://github.com/AndreasTheodoulou/ML_Trading_Strategies", "engines": ["google"]}, {"title": "Backtesting Algo Strategies", "url": "https://github.com/PhilipWaddilove/Backtesting-Algos", "engines": ["google"]}, {"title": "replacementAI/A-Backtest-A-Day", "url": "https://github.com/replacementAI/A-Backtest-A-Day", "engines": ["google"]}, {"title": "Reya-Labs/v1-sbf", "url": "https://github.com/Reya-Labs/v1-sbf", "engines": ["google"]}, {"title": "3. Backtesting using Zipline.ipynb", "url": "https://github.com/twiecki/financial-analysis-python-tutorial/blob/master/3.%20Backtesting%20using%20Zipline.ipynb", "engines": ["google"]}, {"title": "smullins998/Tr4der: Backtesting Library for Strategy Ideation", "url": "https://github.com/smullins998/Tr4der", "engines": ["google"]}, {"title": "A long-short equity quantitative trading strategy (sentiment- ...", "url": "https://github.com/jsisaacs/QuantStrategies", "engines": ["google"]}, {"title": "JonathanJing/Stock-Trading-Strategy", "url": "https://github.com/JonathanJing/Stock-Trading-Strategy", "engines": ["google"]}, {"title": "A Python project that simulates a portfolio backtest using ...", "url": "https://github.com/sapk806/cross_sectional_factor_backtest_project", "engines": ["google"]}]