["clean_stock_price\n.\nto_excel\n(\n\"Cleaned SPX Price.xlsx\"\n)\n\nclean_stock_price\n.\nindex\n \n=\n \npd\n.\nto_datetime\n(\nclean_stock_price\n.\nindex\n)\n\nclean_stock_semiannually_return\n \n=\n \nclean_stock_price\n.\nresample\n(\n\"M\"\n).\nlast\n().\nrolling\n(\nwindow\n=\n6\n).\napply\n(\n    \nlambda\n \nx\n: (\nx\n[\n-\n1\n] \n-\n \nx\n[\n0\n]) \n/\n \nx\n[\n0\n]).\ndropna\n()\n\nclean_stock_semiannually_return\n.\nindex\n \n=\n [\nstr\n(\ni\n \n+\n \ntimedelta\n(\ndays\n=\n15\n)).\nrsplit\n(\n\"-\"\n, \n1\n)[\n0\n] \n+\n \n\"-01\"\n \nfor\n \ni\n \nin\n \nclean_stock_semiannually_return\n.\nindex\n]", "long_list_output\n \n=\n \npd\n.\nDataFrame\n(\ncolumns\n=\nrange\n(\n1\n, \n51\n))\n\nshort_list_output\n \n=\n \npd\n.\nDataFrame\n(\ncolumns\n=\nrange\n(\n1\n, \n51\n))\n\nfor\n \ni\n \nin\n \nrange\n(\nclean_stock_semiannually_return\n.\nshape\n[\n0\n]):\n    \nsemiannual_return\n \n=\n \nclean_stock_semiannually_return\n.\niloc\n[\ni\n].\nvalues\n\n    \nsorted_indices\n \n=\n \nnp\n.\nargsort\n(\n-\nsemiannual_return\n)\n    \nsorted_semiannual_return\n \n=\n \nsemiannual_return\n[\nsorted_indices\n]\n    \nsorted_company_names\n \n=\n \ncompany_names\n[\nsorted_indices\n]\n    \npositive_count\n \n=\n \nnp\n.\nsum\n(\nsorted_semiannual_return\n \n>\n \n0\n)\n    \nnegative_count\n \n=\n \nnp\n.\nsum\n(\nsorted_semiannual_return\n \n<\n \n0\n)\n    \npositive_number\n \n=\n \nmin\n(\n50\n, \npositive_count\n)\n    \nnegative_number\n \n=\n \nmin\n(\n50\n, \nnegative_count\n)\n    \narray_shape\n \n=\n (\n50\n,)\n    \npositive_insert_array\n \n=\n \nnp\n.\nresize\n(\nsorted_company_names\n[:\npositive_number\n], \narray_shape\n)\n    \nnegative_insert_array\n \n=\n \nnp\n.\nresize\n(\nsorted_company_names\n[\n-\nnegative_number\n:], \narray_shape\n)\n    \nremaining_positive_columns\n \n=\n \n50\n \n-\n \npositive_number\n\n    \nremaining_negative_columns\n \n=\n \n50\n \n-\n \nnegative_number\n\n    \npositive_insert_array\n[\n-\nremaining_positive_columns\n:] \n=\n \n''\n\n    \nnegative_insert_array\n[\n-\nremaining_negative_columns\n:] \n=\n \n''\n\n    \nif\n \nremaining_positive_columns\n \n==\n \n0\n:\n        \nlong_list_output\n.\nloc\n[\ni\n] \n=\n \nsorted_company_names\n[:\npositive_number\n]\n    \nelse\n:\n        \nlong_list_output\n.\nloc\n[\ni\n] \n=\n \npositive_insert_array\n\n    \nif\n \nremaining_negative_columns\n \n==\n \n0\n:\n        \nshort_list_output\n.\nloc\n[\ni\n] \n=\n \nsorted_company_names\n[\n-\nnegative_number\n:]\n    \nelse\n:\n        \nshort_list_output\n.\nloc\n[\ni\n] \n=\n \nnegative_insert_array", "# List of Mean and Var of beta_m\n\n\ndef\n \npost_beta\n(\nself\n, \nbeta_0\n=\nNone\n, \ng\n=\nNone\n) \n->\n \ntuple\n[\nlist\n[\nnp\n.\nndar<PERSON>\n], \nlist\n[\nnp\n.\nndarray\n]]:\n    \nif\n \nnot\n \nbeta_0\n:\n        \nbeta_0\n \n=\n \nnp\n.\nzeros\n(\nself\n.\nK\n)\n    \nif\n \nnot\n \ng\n:\n        \ng\n \n=\n \nself\n.\ng_star\n\n    \nbeta_mean_list\n \n=\n []\n    \nbeta_var_list\n \n=\n []\n    \nfor\n \nm\n \nin\n \nrange\n(\nself\n.\nM\n):\n        \nr_m\n \n=\n \nself\n.\nstock_data\n[:, \nm\n]\n        \nbeta_hat_m\n \n=\n \nnp\n.\nlinalg\n.\ninv\n(\nself\n.\nF\n.\nT\n @ \nself\n.\nF\n) @ \nself\n.\nF\n.\nT\n @ \nr_m\n\n        \nbeta_m_bar\n \n=\n (\nbeta_0\n \n+\n \ng\n \n*\n \nbeta_hat_m\n) \n/\n (\n1\n \n+\n \ng\n)\n        \nbeta_mean_list\n.\nappend\n(\nnp\n.\narray\n(\nbeta_m_bar\n))\n\n        \nSSR\n \n=\n (\nr_m\n \n-\n \nself\n.\nF\n @ \nbeta_hat_m\n).\nT\n @ (\nr_m\n \n-\n \nself\n.\nF\n @ \nbeta_hat_m\n) \n+\n \n1\n \n/\n (\ng\n \n+\n \n1\n) \n*\n (\nbeta_hat_m\n \n-\n \nbeta_0\n).\nT\n @ \nself\n.\nF\n.\nT\n @ \nself\n.\nF\n @ (\n            \nbeta_hat_m\n \n-\n \nbeta_0\n\n        )\n        \nsig_m\n \n=\n \ng\n \n/\n (\ng\n \n+\n \n1\n) \n*\n \nnp\n.\nlinalg\n.\ninv\n(\nself\n.\nF\n.\nT\n @ \nself\n.\nF\n) \n*\n \nSSR\n \n/\n \nself\n.\nT\n\n        \nbeta_var_list\n.\nappend\n(\nself\n.\nT\n \n/\n (\nself\n.\nT\n \n-\n \n2\n) \n*\n \nsig_m\n)\n    \nreturn\n \nbeta_mean_list\n, \nbeta_var_list", "# List of Mean of sigma^2_m (length: m, m is number of stocks)\n\n\ndef\n \npost_sig2_mean\n(\nself\n, \nbeta_0\n=\nNone\n, \ng\n=\nNone\n) \n->\n \nlist\n[\nfloat\n]:\n    \nif\n \nnot\n \nbeta_0\n:\n        \nbeta_0\n \n=\n \nnp\n.\nzeros\n(\nself\n.\nK\n)\n    \nif\n \nnot\n \ng\n:\n        \ng\n \n=\n \nself\n.\ng_star\n\n    \nsig2_list\n \n=\n []\n    \nfor\n \nm\n \nin\n \nrange\n(\nself\n.\nM\n):\n        \nr_m\n \n=\n \nself\n.\nstock_data\n[:, \nm\n]\n        \nbeta_hat_m\n \n=\n \nnp\n.\nlinalg\n.\ninv\n(\nself\n.\nF\n.\nT\n @ \nself\n.\nF\n) @ \nself\n.\nF\n.\nT\n @ \nr_m\n\n        \nSSR\n \n=\n (\nr_m\n \n-\n \nself\n.\nF\n @ \nbeta_hat_m\n).\nT\n @ (\nr_m\n \n-\n \nself\n.\nF\n @ \nbeta_hat_m\n) \n+\n \n1\n \n/\n (\ng\n \n+\n \n1\n) \n*\n (\nbeta_hat_m\n \n-\n \nbeta_0\n).\nT\n @ \nself\n.\nF\n.\nT\n @ \nself\n.\nF\n @ (\n            \nbeta_hat_m\n \n-\n \nbeta_0\n\n        )\n        \nsig2_list\n.\nappend\n(\nSSR\n \n/\n \n2\n \n/\n (\nself\n.\nT\n \n/\n \n2\n \n-\n \n1\n))\n    \nreturn\n \nsig2_list", "# Mean and Var of miu_f\n\n\ndef\n \npost_miu_f\n(\nself\n) \n->\n \ntuple\n[\nnp\n.\nndarray\n, \nnp\n.\nndarray\n]:\n    \nf_bar\n \n=\n \nnp\n.\narray\n(\nself\n.\nF\n.\nmean\n(\naxis\n=\n0\n)).\nT\n\n    \nLambda_n\n \n=\n \nnp\n.\nzeros\n((\nself\n.\nK\n, \nself\n.\nK\n))\n    \nfor\n \nt\n \nin\n \nrange\n(\nself\n.\nT\n):\n        \nf_t\n \n=\n \nself\n.\nF\n[\nt\n, :]\n        \nLambda_n\n \n+=\n \nnp\n.\nouter\n(\nf_t\n \n-\n \nf_bar\n, \nf_t\n \n-\n \nf_bar\n)\n    \n# Without views about future factor returns\n\n    \nif\n \nself\n.\nP\n \nis\n \nNone\n \nor\n \nself\n.\nQ\n \nis\n \nNone\n:\n        \nmiu_f_mean\n \n=\n \nf_bar\n\n        \nmiu_f_var\n \n=\n \n1\n \n/\n (\nself\n.\nT\n \n-\n \nself\n.\nK\n \n-\n \n2\n) \n*\n \nLambda_n\n \n/\n \nself\n.\nT\n\n    \nreturn\n \nmiu_f_mean\n, \nmiu_f_var", "# Mean of Lambda_n\n\n\ndef\n \npost_Lambda_n\n(\nself\n) \n->\n \nnp\n.\nndarray\n:\n    \nf_bar\n \n=\n \nself\n.\nF\n.\nmean\n(\naxis\n=\n0\n)\n    \nLambda_n\n \n=\n \nnp\n.\nzeros\n((\nself\n.\nK\n, \nself\n.\nK\n))\n    \nfor\n \nt\n \nin\n \nrange\n(\nself\n.\nT\n):\n        \nf_t\n \n=\n \nself\n.\nF\n[\nt\n, :]\n        \nLambda_n\n \n+=\n \nnp\n.\nouter\n(\nf_t\n \n-\n \nf_bar\n, \nf_t\n \n-\n \nf_bar\n)\n    \nreturn\n \nLambda_n\n \n/\n (\nself\n.\nT\n \n-\n \nself\n.\nK\n \n-\n \n2\n)", "# Objective function for finding g*\n\n\ndef\n \ng_likelihood\n(\nself\n, \ng\n) \n->\n \nfloat\n:\n    \nR_squared_list\n \n=\n []\n    \nfor\n \nm\n \nin\n \nrange\n(\nself\n.\nM\n):\n        \nr_m\n \n=\n \nself\n.\nstock_data\n[:, \nm\n]\n        \nr_m_bar\n \n=\n \nr_m\n.\nmean\n(\naxis\n=\n0\n)\n        \nbeta_hat_m\n \n=\n \nnp\n.\nlinalg\n.\ninv\n(\nself\n.\nF\n.\nT\n @ \nself\n.\nF\n) @ \nself\n.\nF\n.\nT\n @ \nr_m\n\n        \nR_squared_m\n \n=\n \n1\n \n-\n ((\nr_m\n \n-\n \nself\n.\nF\n @ \nbeta_hat_m\n).\nT\n @ (\nr_m\n \n-\n \nself\n.\nF\n @ \nbeta_hat_m\n)) \n/\n ((\nr_m\n \n-\n \nr_m_bar\n).\nT\n @ (\nr_m\n \n-\n \nr_m_bar\n))\n        \nR_squared_list\n.\nappend\n(\nR_squared_m\n)\n    \nR_squared_list\n \n=\n \nnp\n.\narray\n(\nR_squared_list\n)\n    \nreturn\n \nsum\n(\n-\n(\nself\n.\nT\n \n-\n \nself\n.\nK\n \n-\n \n1\n) \n/\n \n2\n \n*\n \nnp\n.\nlog\n(\n1\n \n+\n \ng\n) \n+\n (\nself\n.\nT\n \n-\n \n1\n) \n/\n \n2\n \n*\n \nnp\n.\nlog\n(\n1\n \n+\n \ng\n \n*\n (\n1\n \n-\n \nR_squared_list\n)))", "# Posterior predictive return distribution (mean vector and covariance matrix) and shrinkage parameter g*\n\n\ndef\n \nposterior_predictive\n(\nself\n) \n->\n \ntuple\n[\nnp\n.\nndarray\n, \nnp\n.\nndarray\n, \nfloat\n]:\n    \nsig2_mean\n \n=\n \nself\n.\npost_sig2_mean\n()\n    \nmiu_f_mean\n, \nmiu_f_var\n \n=\n \nself\n.\npost_miu_f\n()\n    \nLambda_n_mean\n \n=\n \nself\n.\npost_Lambda_n\n()\n    \nbeta_mean_list\n, \nbeta_var_list\n \n=\n \nself\n.\npost_beta\n()\n\n    \nf_ft_mean\n \n=\n \nLambda_n_mean\n \n+\n \nmiu_f_var\n \n+\n \nnp\n.\nouter\n(\nmiu_f_mean\n, \nmiu_f_mean\n)\n    \nf_var\n \n=\n \nLambda_n_mean\n \n+\n \nmiu_f_var\n\n\n    \nr_mean_list\n \n=\n []\n    \nr_cov_mat\n \n=\n \nnp\n.\nzeros\n((\nself\n.\nM\n, \nself\n.\nM\n))\n    \nfor\n \nm\n \nin\n \nrange\n(\nself\n.\nM\n):\n        \nr_mean\n \n=\n \nbeta_mean_list\n[\nm\n] @ \nmiu_f_mean\n\n        \nr_mean_list\n.\nappend\n(\nr_mean\n)\n        \nfor\n \nj\n \nin\n \nrange\n(\nm\n, \nself\n.\nM\n):\n            \nif\n \nm\n \n==\n \nj\n:\n                \nr_cov_mat\n[\nm\n, \nm\n] \n=\n \nsig2_mean\n[\nm\n] \n+\n \nnp\n.\ntrace\n(\nf_ft_mean\n @ \nbeta_var_list\n[\nm\n]) \n+\n \nbeta_mean_list\n[\nm\n].\nT\n @ \nf_var\n @ \nbeta_mean_list\n[\nm\n]\n            \nelse\n:\n                \nr_cov_mat\n[\nm\n, \nj\n] \n=\n \nbeta_mean_list\n[\nm\n].\nT\n @ \nf_var\n @ \nbeta_mean_list\n[\nj\n]\n                \nr_cov_mat\n[\nj\n, \nm\n] \n=\n \nr_cov_mat\n[\nm\n, \nj\n]\n    \nreturn\n \nnp\n.\narray\n(\nr_mean_list\n), \nnp\n.\narray\n(\nr_cov_mat\n), \nself\n.\ng_star", "def\n \npost_miu_f\n(\nself\n) \n->\n \ntuple\n[\nnp\n.\nndar<PERSON>\n, \nnp\n.\nndarray\n]:\n        \nf_bar\n \n=\n \nnp\n.\narray\n(\nself\n.\nF\n.\nmean\n(\naxis\n=\n0\n)).\nT\n\n        \nLambda_n\n \n=\n \nnp\n.\nzeros\n((\nself\n.\nK\n, \nself\n.\nK\n))\n        \nfor\n \nt\n \nin\n \nrange\n(\nself\n.\nT\n):\n            \nf_t\n \n=\n \nself\n.\nF\n[\nt\n, :]\n            \nLambda_n\n \n+=\n \nnp\n.\nouter\n(\nf_t\n \n-\n \nf_bar\n, \nf_t\n \n-\n \nf_bar\n)\n        \n# With views about future factor returns\n\n        \nif\n \nself\n.\nP\n \n==\n \n\"absolute\"\n:\n            \nself\n.\nP\n \n=\n \nnp\n.\neye\n(\nself\n.\nK\n)\n        \nelif\n \nself\n.\nP\n \n==\n \n\"relative\"\n:\n            \nself\n.\nP\n \n=\n \nnp\n.\neye\n(\nself\n.\nK\n)\n            \nfor\n \ni\n \nin\n \nrange\n(\nself\n.\nK\n \n-\n \n1\n):\n                \nself\n.\nP\n[\ni\n, \ni\n \n+\n \n1\n] \n=\n \n-\n1\n\n        \nif\n \nnot\n \nself\n.\nc\n:\n            \nself\n.\nc\n \n=\n \nnp\n.\nsqrt\n(\nself\n.\nT\n)\n        \nSigma_n\n \n=\n \nLambda_n\n \n/\n \nself\n.\nT\n \n/\n (\nself\n.\nT\n \n-\n \nself\n.\nK\n)\n        \nmiu_f_mean\n \n=\n \nf_bar\n \n+\n \n1\n \n/\n (\nself\n.\nc\n \n+\n \n1\n) \n*\n \nSigma_n\n @ \nself\n.\nP\n.\nT\n @ \nnp\n.\nlinalg\n.\ninv\n(\nself\n.\nP\n @ \nSigma_n\n @ \nself\n.\nP\n.\nT\n) @ (\nself\n.\nQ\n \n-\n \nself\n.\nP\n @ \nf_bar\n)\n        \nmiu_f_var\n \n=\n (\n            (\nself\n.\nT\n \n-\n \nself\n.\nK\n)\n            \n/\n (\nself\n.\nT\n \n-\n \nself\n.\nK\n \n-\n \n2\n)\n            \n*\n (\nSigma_n\n \n-\n \n1\n \n/\n (\nself\n.\nc\n \n+\n \n1\n) \n*\n \nSigma_n\n @ \nself\n.\nP\n.\nT\n @ \nnp\n.\nlinalg\n.\ninv\n(\nself\n.\nP\n @ \nSigma_n\n @ \nself\n.\nP\n.\nT\n) @ \nself\n.\nP\n @ \nSigma_n\n)\n        )\n        \nreturn\n \nmiu_f_mean\n, \nmiu_f_var", "# define portfolio data feeds\n\n\nclass\n \nPandasData\n(\nbt\n.\nfeeds\n.\nPandasData\n):\n    \nlines\n \n=\n (\n\"open\"\n, \n\"close\"\n)\n    \nparams\n \n=\n (\n        (\n\"datetime\"\n, \nNone\n),  \n# use index as datetime\n\n        (\n\"open\"\n, \n0\n),  \n# the [0] column is open price\n\n        (\n\"close\"\n, \n1\n),  \n# the [1] column is close price\n\n        (\n\"high\"\n, \n0\n),\n        (\n\"low\"\n, \n0\n),\n        (\n\"volume\"\n, \n0\n),\n        (\n\"openinterest\"\n, \n0\n),\n    )\n\n\n# new observer for portfolio\n\n\nclass\n \nPortfolioValueObserver\n(\nbt\n.\nObserver\n):\n    \nlines\n \n=\n (\n\"value\"\n,)\n    \nplotinfo\n \n=\n \ndict\n(\nplot\n=\nTrue\n, \nsubplot\n=\nTrue\n)\n\n    \ndef\n \nnext\n(\nself\n):\n        \nself\n.\nlines\n.\nvalue\n[\n0\n] \n=\n \nself\n.\n_owner\n.\nbroker\n.\ngetvalue\n()\n\n\n# backtest given prices, weights, initial cash, commission fee\n\n\ndef\n \nRunBacktest\n(\nstock_list\n, \ncombined_df\n, \nweights_df\n, \nini_cash\n, \ncomm_fee\n, \nnotify\n, \nlog\n):\n    \ncerebro\n \n=\n \nbt\n.\nCerebro\n()  \n# initiate cerebro engine\n\n\n    \n# load data feeds\n\n    \nfor\n \ncol\n \nin\n \nstock_list\n:\n        \ndata\n \n=\n \nPandasData\n(\ndataname\n=\ncombined_df\n[[\ncol\n \n+\n \n\"_open\"\n, \ncol\n \n+\n \n\"_close\"\n]])\n        \ncerebro\n.\nadddata\n(\ndata\n, \nname\n=\ncol\n)\n\n    \n# strategy setting\n\n    \nweights_df\n \n=\n \nweights_df\n \n/\n \nweights_df\n.\nsum\n(\naxis\n=\n1\n).\nvalues\n.\nreshape\n(\n-\n1\n, \n1\n) \n*\n \n0.9\n  \n# margin\n\n    \n# set initial cash\n\n    \ncerebro\n.\nbroker\n.\nsetcash\n(\n100000000\n)\n    \ncerebro\n.\nbroker\n.\nsetcommission\n(\ncommission\n=\ncomm_fee\n)  \n# set commission\n\n    \ncerebro\n.\naddstrategy\n(\nBLStrategy\n, \nweights\n=\nweights_df\n, \nstocks\n=\nstock_list\n, \nprintnotify\n=\nFalse\n, \nprintlog\n=\nFalse\n)  \n# set strategy\n\n    \ncerebro\n.\naddobserver\n(\nPortfolioValueObserver\n)  \n# add observer\n\n    \ncerebro\n.\naddanalyzer\n(\nbt\n.\nanalyzers\n.\nPyFolio\n, \n_name\n=\n\"pyfolio\"\n)  \n# add analyzer\n\n\n    \n# run the strategy\n\n    \nresults\n \n=\n \ncerebro\n.\nrun\n()\n    \nreturn\n \nresults"]