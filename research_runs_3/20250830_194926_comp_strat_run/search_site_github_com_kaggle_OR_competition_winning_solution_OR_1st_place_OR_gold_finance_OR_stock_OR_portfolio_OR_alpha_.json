[{"title": "hansen7/Kaggle_Competition: Summary of the Kaggle ...", "url": "https://github.com/hansen7/Kaggle_Competition", "engines": ["google"]}, {"title": "guitarmind/kaggle_moa_winner_hungry_for_gold - GitHub", "url": "https://github.com/guitarmind/kaggle_moa_winner_hungry_for_gold", "engines": ["duckduck<PERSON>"]}, {"title": "goto_conversion - Powered over 10 Gold Medals and 100 ...", "url": "https://github.com/gotoConversion/goto_conversion", "engines": ["google"]}, {"title": "the-black-knight-01/Data-Science-Competitions", "url": "https://github.com/the-black-knight-01/Data-Science-Competitions", "engines": ["google"]}, {"title": "dimsariyanto/Gold-Price-Prediction", "url": "https://github.com/dimsariyanto/Gold-Price-Prediction", "engines": ["google"]}, {"title": "<PERSON><PERSON>", "url": "https://github.com/erlemar", "engines": ["google"]}, {"title": "<PERSON><PERSON><PERSON>-<PERSON>/My-kaggle-notebooks", "url": "https://github.com/<PERSON>-<PERSON>-<PERSON>/My-kaggle-notebooks", "engines": ["google"]}, {"title": "Prize-winning solution to the M6 Forecasting Competition", "url": "https://github.com/<PERSON>/wound-ignite", "engines": ["google"]}, {"title": "scaomath/kaggle-jane-street: Machine learning models to ...", "url": "https://github.com/scaomath/kaggle-jane-street", "engines": ["google"]}, {"title": "Gold-Loan-Prediction-by-machine-learning-Random-forest", "url": "https://github.com/BittuPanchal/Gold-Loan-Prediction-by-machine-learning-Random-forest", "engines": ["google"]}, {"title": "kaggle-elo-recommendation/lit_review/README.md at ...", "url": "https://github.com/freestander/kaggle-elo-recommendation/blob/master/lit_review/README.md", "engines": ["google"]}]