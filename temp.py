import os
from pyexpat import features
import sys
import json
import math
import warnings
from dataclasses import dataclass, asdict
from typing import List, Optional, Dict, Tuple

from sklearn.impute import SimpleImputer
import numpy as np
import pandas as pd

from sklearn.linear_model import Ridge
from sklearn.ensemble import HistGradientBoostingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_squared_error
from sklearn.isotonic import IsotonicRegression
from sklearn.decomposition import PCA
from scipy.stats import spearmanr
from sklearn.impute import SimpleImputer
from sklearn.linear_model import RidgeCV, HuberRegressor
from scipy.stats import spearmanr

from any_algo_runs.run_20250824_204721.algos.FallbackRidge_ep1 import train

# Optional LightGBM
try:
    from lightgbm import LGBMRegressor
    LGBM_AVAILABLE = True
except Exception:
    LGBM_AVAILABLE = False

try:
    from lightgbm import LGBMRanker
    LGBM_RANKER_AVAILABLE = True
except Exception:
    LGBM_RANKER_AVAILABLE = False

try:
    from xgboost import XGBRanker
    XGBR_AVAILABLE = True
except Exception:
    XGBR_AVAILABLE = False

try:
    import pywt  # wavelets
    PYWT_AVAILABLE = True
except Exception:
    PYWT_AVAILABLE = False

try:
    from PyEMD import EMD  # empirical mode decomposition
    PYEMD_AVAILABLE = True
except Exception:
    PYEMD_AVAILABLE = False

warnings.filterwarnings("ignore", category=UserWarning)
pd.set_option("mode.chained_assignment", None)

# ------------------------------
# Config
# ------------------------------
QUICK_TEST = False      # Set True to sanity-check (few OOS years)
USE_RANKERS = False
TOP_N = 60              # must be between 50 and 100 per competition guidelines
N_SHORT = 80            # same count for the short leg (only used in long-short portfolio)
SEED = 42
BETA_ROLLING_WIN = 36   # months
BETA_MIN_PERIODS = 12
VOL_WIN = 6            # months for IVP weights
BUFFER_N = 30           # turnover smoothing buffer (keep prev names while rank <= TOP_N + BUFFER_N)

# ------------------------------
# Utils
# ------------------------------
def find_file(candidates: List[str]) -> Optional[str]:
    for p in candidates:
        if p and os.path.exists(p):
            return p
    return None

def annualize_return(monthly_ret: float) -> float:
    return (1 + monthly_ret) ** 12 - 1

def annualize_std(monthly_std: float) -> float:
    return monthly_std * np.sqrt(12.0)

def max_drawdown_from_returns(series: pd.Series) -> float:
    cum = (1 + series).cumprod()
    peaks = cum.cummax()
    dd = (cum / peaks) - 1.0
    return float(dd.min())

def robust_rank_to_unit(x: pd.Series) -> pd.Series:
    med = x.median(skipna=True)
    x2 = x.fillna(med).rank(method="dense")
    maxv = x2.max()
    if pd.isna(maxv) or maxv <= 1:
        return pd.Series(np.zeros(len(x2)), index=x.index)
    x_scaled = ((x2 - 1) / (maxv - 1)) * 2 - 1
    return x_scaled

def detect_feature_cols(df: pd.DataFrame, target_col="stock_exret") -> List[str]:
    blacklist = set([
        target_col, "ret_eom","year","month","permno","date","rf",
        "stock_ticker","cusip","comp_name","shrcd","exchcd",
    ])
    numeric_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]
    feats = [c for c in numeric_cols if c not in blacklist]
    return feats

def month_groups(df: pd.DataFrame) -> np.ndarray:
    # Number of stocks per month (LightGBM Ranker "group" array)
    return df.groupby('date').size().values

def pca_augment(X_tr, X_va, X_te, n_max=15, var_thresh=0.60):
    X_tr = np.nan_to_num(X_tr, nan=0.0, posinf=0.0, neginf=0.0)
    X_va = np.nan_to_num(X_va, nan=0.0, posinf=0.0, neginf=0.0)
    X_te = np.nan_to_num(X_te, nan=0.0, posinf=0.0, neginf=0.0)
    X_trva = np.vstack([X_tr, X_va])
    n_comp = min(n_max, X_tr.shape[1])
    pca = PCA(n_components=n_comp, random_state=42)
    pca.fit(X_trva)
    cum = np.cumsum(pca.explained_variance_ratio_)
    k = int(np.searchsorted(cum, var_thresh) + 1)
    k = max(3, min(n_max, k))
    pca_k = PCA(n_components=k, random_state=42)
    pca_k.fit(X_trva)
    return (np.hstack([X_tr, pca_k.transform(X_tr)]),
            np.hstack([X_va, pca_k.transform(X_va)]),
            np.hstack([X_te, pca_k.transform(X_te)]))

def interaction_augment(train_df, val_df, test_df, feat_names, topk=8):
    """Pick topk features by Spearman rank-IC on TRAIN only; add pairwise products."""
    y = train_df['stock_exret'].values
    ic = []
    for f in feat_names:
        if f not in train_df.columns: continue
        icv = spearmanr(train_df[f].values, y).correlation
        ic.append((abs(0.0 if np.isnan(icv) else icv), f))
    ic.sort(reverse=True)
    keep = [f for _,f in ic[:topk]] if ic else []
    pairs = [(keep[i], keep[j]) for i in range(len(keep)) for j in range(i+1, len(keep))]
    def _mkX(df):
        base = df[feat_names].values
        if not pairs: return base
        inter = [ (df[a].values * df[b].values).reshape(-1,1) for (a,b) in pairs if a in df.columns and b in df.columns ]
        if inter:
            return np.hstack([base] + inter)
        return base
    return _mkX(train_df), _mkX(val_df), _mkX(test_df)

def drop_sparse_columns(X_tr, X_va, X_te, max_nan_frac=0.8):
    """
    Drop feature columns whose NaN fraction in TRAIN+VAL is >= max_nan_frac.
    """
    trva = np.vstack([X_tr, X_va])
    nan_frac = np.mean(np.isnan(trva), axis=0)
    keep = nan_frac < max_nan_frac
    return X_tr[:, keep], X_va[:, keep], X_te[:, keep]

def impute_median_trainval(X_tr, X_va, X_te):
    """
    Fit a median imputer on TRAIN+VAL only, transform all splits.
    Also zero-out any infs that might sneak in.
    """
    imp = SimpleImputer(strategy='median')
    imp.fit(np.vstack([X_tr, X_va]))
    Xt = imp.transform(X_tr)
    Xv = imp.transform(X_va)
    Xe = imp.transform(X_te)
    Xt = np.nan_to_num(Xt, nan=0.0, posinf=0.0, neginf=0.0)
    Xv = np.nan_to_num(Xv, nan=0.0, posinf=0.0, neginf=0.0)
    Xe = np.nan_to_num(Xe, nan=0.0, posinf=0.0, neginf=0.0)
    return Xt, Xv, Xe

def select_stable_features(df_tr: pd.DataFrame, feat_list, k=60) -> list:
    # per-month Spearman IC for each feature, then median |IC|
    ics = []
    for f in feat_list:
        if f not in df_tr.columns: 
            continue
        vals = []
        for _, g in df_tr.groupby('date'):
            if g[f].notna().sum() > 20:
                ic = spearmanr(g[f].values, g['stock_exret'].values, nan_policy='omit').correlation
                if np.isfinite(ic):
                    vals.append(ic)
        if vals:
            ics.append((np.median(np.abs(vals)), f))
    ics.sort(reverse=True)
    keep = [f for _, f in ics[:k]] if ics else feat_list[:k]
    return keep

def feature_neutralize_month(g: pd.DataFrame,
                             score_col: str,
                             feat_cols: List[str],
                             proportion: float = 1.0,
                             add_suffix: str = '_fn') -> pd.DataFrame:
    """
    Regress score on selected features (cross-section of a month) and subtract the projection.
    This is the 'feature neutralization' used by top Numerai models.
    """
    use = [c for c in feat_cols if c in g.columns]
    if not use:
        g[score_col + add_suffix] = g[score_col]
        return g
    X = g[use].replace([np.inf, -np.inf], np.nan).fillna(0.0).values
    y = g[score_col].values.astype(float)
    try:
        coef, *_ = np.linalg.lstsq(X, y, rcond=None)
        proj = X @ coef
        g[score_col + add_suffix] = y - proportion * proj
    except Exception:
        g[score_col + add_suffix] = g[score_col]
    return g

def softmax_weights_from_score(s: pd.Series, temp: float = 0.20) -> pd.Series:
    """
    Convert a score vector into convex weights via softmax on a standardized score.
    `temp` controls concentration (lower = more concentrated).
    """
    z = (s - s.mean()) / (s.std(ddof=0) + 1e-12)
    e = np.exp(z / max(1e-6, temp))
    w = e / (e.sum() + 1e-12)
    return pd.Series(w, index=s.index)

def drop_sparse_columns(X_tr, X_va, X_te, max_nan_frac=0.80):
    trva = np.vstack([X_tr, X_va])
    keep = (np.isnan(trva).mean(axis=0) < max_nan_frac)
    return X_tr[:, keep], X_va[:, keep], X_te[:, keep]

def impute_median_trainval(X_tr, X_va, X_te):
    imp = SimpleImputer(strategy='median')
    imp.fit(np.vstack([X_tr, X_va]))
    Xt = np.nan_to_num(imp.transform(X_tr), nan=0.0, posinf=0.0, neginf=0.0)
    Xv = np.nan_to_num(imp.transform(X_va), nan=0.0, posinf=0.0, neginf=0.0)
    Xe = np.nan_to_num(imp.transform(X_te), nan=0.0, posinf=0.0, neginf=0.0)
    return Xt, Xv, Xe

def pick_topN_sector_quota(g: pd.DataFrame, rank_col: str = 'rank_final',
                           top_n: int = 60, sector_col: str = 'gsector',
                           max_sector_frac: float = 0.25, min_per_sector: int = 2) -> List[int]:
    """
    Select up to `top_n` permnos with per-sector caps (<= max_sector_frac * top_n).
    Fills the remainder by best ranks across all sectors.
    """
    if sector_col not in g.columns:
        # fallback: no sector info
        return g.sort_values(rank_col).head(top_n)['permno'].tolist()

    g = g.sort_values(rank_col)
    cap = max(1, int(np.ceil(max_sector_frac * top_n)))

    sel = []
    # initial pass: allocate proportional to sector presence, bounded by cap
    sector_counts = g[sector_col].value_counts(normalize=True)
    quotas = (sector_counts * top_n).round().astype(int).clip(lower=min_per_sector, upper=cap)

    for sec, q in quotas.items():
        cand = g[g[sector_col] == sec].head(q)['permno'].tolist()
        sel.extend(cand)

    # fill leftover slots, if any
    if len(sel) < top_n:
        need = top_n - len(sel)
        extra = g[~g['permno'].isin(sel)].head(need)['permno'].tolist()
        sel.extend(extra)

    return sel[:top_n]

@dataclass
class Metrics:
    sharpe_ann: float
    max_1m_loss: float
    max_drawdown: float
    alpha_monthly: Optional[float]
    alpha_ann: Optional[float]
    alpha_tstat: Optional[float]
    info_ratio_ann: Optional[float]
    avg_ret_ann: float
    vol_ann: float
    turnover: float
    beta: Optional[float]

def compute_alpha_stats(port_df: pd.DataFrame) -> Tuple[Optional[float], Optional[float], Optional[float], Optional[float], Optional[float]]:
    if 'mkt_rf' not in port_df.columns:
        return None, None, None, None, None
    try:
        import statsmodels.api as sm
        X = sm.add_constant(port_df['mkt_rf'].values, has_constant='add')
        y = port_df['port_ret'].values
        model = sm.OLS(y, X).fit(cov_type='HAC', cov_kwds={'maxlags':3})
        alpha_monthly = float(model.params[0])
        beta = float(model.params[1])
        t_alpha = float(model.tvalues[0])
        mse = float(model.mse_resid)
        info_ratio_ann = (alpha_monthly / np.sqrt(mse)) * np.sqrt(12.0)
        return alpha_monthly, alpha_monthly*12.0, t_alpha, info_ratio_ann, beta
    except Exception:
        X = np.c_[np.ones(len(port_df)), port_df['mkt_rf'].values]
        y = port_df['port_ret'].values
        beta_hat, *_ = np.linalg.lstsq(X, y, rcond=None)
        resid = y - X @ beta_hat
        s2 = (resid**2).sum() / (len(y) - X.shape[1])
        cov_beta = s2 * np.linalg.inv(X.T @ X)
        se_alpha = np.sqrt(cov_beta[0,0])
        alpha_monthly = float(beta_hat[0])
        beta = float(beta_hat[1])
        t_alpha = float(alpha_monthly / (se_alpha + 1e-12))
        return alpha_monthly, alpha_monthly*12.0, t_alpha, None, beta
    
def build_macro_features(mkt: Optional[pd.DataFrame]) -> Optional[pd.DataFrame]:
    if mkt is None or mkt.empty:
        return None
    df = mkt.copy().sort_values(['year','month']).reset_index(drop=True)
    # build a monthly date key
    df['date'] = pd.to_datetime(df['year'].astype(str) + '-' + df['month'].astype(str) + '-01')

    # basic regime features on market excess returns
    df['macro_mom_3']  = df['mkt_rf'].rolling(3,  min_periods=2).sum()
    df['macro_mom_6']  = df['mkt_rf'].rolling(6,  min_periods=3).sum()
    df['macro_mom_12'] = df['mkt_rf'].rolling(12, min_periods=6).sum()
    df['macro_vol_6']  = df['mkt_rf'].rolling(6,  min_periods=3).std()
    df['macro_vol_12'] = df['mkt_rf'].rolling(12, min_periods=6).std()

    # drawdown features
    cum = (1.0 + df['mkt_rf'].fillna(0.0)).cumprod()
    peak = cum.cummax()
    dd = (cum/peak) - 1.0
    df['macro_dd'] = dd
    # speed of drawdown / recovery
    df['macro_dd_chg_3'] = df['macro_dd'].diff().rolling(3, min_periods=2).mean()

    # optional wavelet energies (12m rolling)
    if PYWT_AVAILABLE and len(df) > 20:
        # discrete wavelet (db2) – level 3
        coeffs = pywt.wavedec(df['mkt_rf'].fillna(0.0).values, 'db2', level=3, mode='periodization')
        # detail coefficients D1, D2, D3
        for i, arr in enumerate(coeffs[1:], start=1):
            e = pd.Series(arr**2).rolling(12, min_periods=6).mean()
            # align by truncating/expanding to length
            e_full = pd.Series(np.interp(np.linspace(0, len(e)-1, num=len(df)), np.arange(len(e)), e.fillna(method='bfill').fillna(0.0)))
            df[f'macro_wav_e{i}'] = e_full.values

    # optional EMD energy of first IMF (12m)
    if PYEMD_AVAILABLE and len(df) > 30:
        try:
            imfs = EMD().emd(df['mkt_rf'].fillna(0.0).values)
            if imfs.shape[0] >= 1:
                imf1 = pd.Series(imfs[0])
                df['macro_emd_e1'] = (imf1**2).rolling(12, min_periods=6).mean()
        except Exception:
            pass

    cols = [c for c in df.columns if c.startswith('macro_')]
    return df[['year','month'] + cols]

# ------------------------------
# Loading
# ------------------------------
def load_core_data() -> Tuple[pd.DataFrame, List[str]]:
    data_path = find_file([
        "./asset/hackathon_sample_v2.csv",
        os.getenv("HACKATHON_DATA_PATH"),
        "./hackathon_sample_v2.csv",
        "./data/hackathon_sample_v2.csv",
        "/mnt/data/hackathon_sample_v2.csv",
    ])
    if data_path is None:
        raise FileNotFoundError("hackathon_sample_v2.csv not found.")

    df = pd.read_csv(data_path, low_memory=False, parse_dates=['date'])
    if 'year' not in df.columns or 'month' not in df.columns:
        df['year'] = df['date'].dt.year
        df['month'] = df['date'].dt.month

    factor_path = find_file([
        "./asset/factor_char_list.csv",
        os.getenv("FACTOR_LIST_PATH"),
        "./factor_char_list.csv",
        "./data/factor_char_list.csv",
        "/mnt/data/factor_char_list.csv",
    ])
    if factor_path is not None:
        fac = pd.read_csv(factor_path)
        cand_cols = [c for c in fac.columns if c.lower() == "variable"]
        if cand_cols:
            varcol = cand_cols[0]
            features = [str(v) for v in fac[varcol].dropna().unique().tolist() if v in df.columns]
        else:
            features = detect_feature_cols(df)
    else:
        features = detect_feature_cols(df)

    required = ['date','year','month','permno','stock_exret']
    for r in required:
        if r not in df.columns:
            raise ValueError(f"Required column missing: {r}")

    df = df[df['stock_exret'].notna()].copy()
    # Cross-sectional rank-scale features monthly
    out = []
    for dt, g in df.groupby('date', sort=True):
        gg = g.copy()
        for f in features:
            if f not in gg.columns: continue
            gg[f] = robust_rank_to_unit(gg[f])
        out.append(gg)
    data = pd.concat(out, ignore_index=True).sort_values(['date','permno']).reset_index(drop=True)
    return data, features

def load_market_factors() -> Optional[pd.DataFrame]:
    mkt_path = find_file([
        "./asset/mkt_ind.csv",
        os.getenv("MKT_FILE_PATH"),
        "./mkt_ind.csv",
        "./data/mkt_ind.csv",
        "/mnt/data/mkt_ind.csv",
    ])
    if mkt_path is None:
        return None
    mkt = pd.read_csv(mkt_path)
    if not {'year','month','mkt_rf'}.issubset(set(mkt.columns)):
        return None
    return mkt[['year','month','mkt_rf']].copy()

# ------------------------------
# Trailing betas & volatilities
# ------------------------------
def add_trailing_beta(data: pd.DataFrame, mkt: Optional[pd.DataFrame]) -> pd.DataFrame:
    if mkt is None:
        data['beta_mkt'] = np.nan
        return data
    tmp = data[['permno','date','year','month','stock_exret']].merge(mkt, on=['year','month'], how='left')
    tmp = tmp.sort_values(['permno','date'])
    def _beta_group(g):
        r = g['stock_exret']
        m = g['mkt_rf'].fillna(0.0)
        cov = r.rolling(BETA_ROLLING_WIN, min_periods=BETA_MIN_PERIODS).cov(m)
        var = m.rolling(BETA_ROLLING_WIN, min_periods=BETA_MIN_PERIODS).var()
        beta = cov / (var.replace(0, np.nan))
        return beta
    beta_series = tmp.groupby('permno', group_keys=False).apply(_beta_group)
    tmp['beta_mkt'] = beta_series.values
    data = data.merge(tmp[['permno','date','beta_mkt']], on=['permno','date'], how='left')
    return data

def add_trailing_vol(data: pd.DataFrame) -> pd.DataFrame:
    # trailing 12m volatility of stock_exret
    data = data.sort_values(['permno','date'])
    def _vol_group(g):
        return g['stock_exret'].rolling(VOL_WIN, min_periods=6).std()
    vol = data.groupby('permno', group_keys=False).apply(_vol_group)
    data['vol_12m'] = vol.values
    return data

# ------------------------------
# Modeling
# ------------------------------
def train_predict_monthly(data: pd.DataFrame, features: List[str]) -> Tuple[pd.DataFrame, Dict, Dict]:
    """
    Classification-first monthly pipeline:
      - Cross-sectional winsorization of returns
      - Per-month labels: Top decile (1) vs not (0), Bottom decile (1) vs not (0)
      - Time-decayed sample weights (recent months emphasized)
      - Models: Logistic (saga), HistGBClassifier, MLPClassifier
      - Score = P(top) - P(bottom), with simple decile calibration on VAL
      - Stable feature selection, NaN-safe (drop-sparse + median-impute)
      - Returns OOS predictions + R^2 and rank-IC
    """
    from sklearn.linear_model import LogisticRegression
    from sklearn.neural_network import MLPClassifier
    from sklearn.ensemble import HistGradientBoostingClassifier
    from sklearn.isotonic import IsotonicRegression
    from scipy.stats import spearmanr

    start = pd.Timestamp("2000-01-01")
    end_oos_end = pd.Timestamp("2024-01-01")

    out_rows = []
    r2_by_model = {m: [] for m in ['cls_logit','cls_hgb','cls_mlp','cls_meta']}
    ic_by_model = {m: [] for m in ['cls_logit','cls_hgb','cls_mlp','cls_meta']}

    # ----- Robustify target (winsorize cross-sectionally each month) -----
    data = data.copy()
    def _winsor_cs(s, lo=0.02, hi=0.98):
        ql, qh = s.quantile(lo), s.quantile(hi)
        return s.clip(ql, qh)
    data['stock_exret'] = data.groupby('date')['stock_exret'].transform(_winsor_cs)

    # helper: monthly percentile labels
    def make_top_bot_labels(df, top_q=0.9, bot_q=0.1):
        df = df.copy()
        # per-month thresholds
        q_top = df.groupby('date')['stock_exret'].transform(lambda s: s.quantile(top_q))
        q_bot = df.groupby('date')['stock_exret'].transform(lambda s: s.quantile(bot_q))
        df['y_top'] = (df['stock_exret'] >= q_top).astype(int)
        df['y_bot'] = (df['stock_exret'] <= q_bot).astype(int)
        return df

    # exponential time-decay weights (per-observation), fit using train months only
    def add_time_weights(df, span_months=36):
        df = df.copy()
        # map each month to an integer index
        months = sorted(df['date'].dt.to_period('M').unique())
        idx_map = {m: i for i, m in enumerate(months)}
        df['_midx'] = df['date'].dt.to_period('M').map(idx_map).astype(int)
        # decay so that recent months get higher weight
        # weight = exp((midx - max_midx)/tau)
        tau = max(6, span_months / 2.0)
        max_midx = df['_midx'].max()
        w = np.exp((df['_midx'] - max_midx) / tau)
        # normalize per-month so cross-sections don't skew by count
        w = w / df.groupby('date')[['_midx']].transform('count')['_midx']
        df['w'] = w.astype(float)
        return df.drop(columns=['_midx'])

    counter = 0
    oos_years_done = 0

    while (start + pd.DateOffset(years=11 + counter)) <= end_oos_end:
        cutoff_train_end = start + pd.DateOffset(years=8 + counter)
        cutoff_val_end   = start + pd.DateOffset(years=10 + counter)
        cutoff_test_end  = start + pd.DateOffset(years=11 + counter)

        train = data[(data['date'] >= start) & (data['date'] < cutoff_train_end)]
        val   = data[(data['date'] >= cutoff_train_end) & (data['date'] < cutoff_val_end)]
        test  = data[(data['date'] >= cutoff_val_end) & (data['date'] < cutoff_test_end)]

        if len(test) == 0 or len(train) == 0 or len(val) == 0:
            counter += 1
            continue

        # labels + time weights
        train = make_top_bot_labels(train)
        val   = make_top_bot_labels(val)
        test  = test.copy()  # labels not needed, keep returns for metrics

        train = add_time_weights(train, span_months=36)
        val   = add_time_weights(val,   span_months=36)

        # choose stable features (TRAIN only)
        feat_win = select_stable_features(train, features, k=60)

        # matrices
        X_tr = train[feat_win].values
        X_va = val[feat_win].values
        X_te = test[feat_win].values

        # NaN-safe
        X_tr, X_va, X_te = drop_sparse_columns(X_tr, X_va, X_te, max_nan_frac=0.80)
        X_tr, X_va, X_te = impute_median_trainval(X_tr, X_va, X_te)

        # top and bottom labels
        y_tr_top = train['y_top'].values
        y_va_top = val['y_top'].values

        y_tr_bot = train['y_bot'].values
        y_va_bot = val['y_bot'].values

        w_tr = train['w'].values
        w_va = val['w'].values

        # ------------------------
        # Base classifiers (Top)
        # ------------------------
        logit_top = LogisticRegression(
            penalty='l2', solver='saga', max_iter=2000, C=1.0, n_jobs=None, random_state=SEED
        )
        logit_top.fit(X_tr, y_tr_top, sample_weight=w_tr)
        p_top_logit_va = logit_top.predict_proba(X_va)[:,1]
        p_top_logit_te = logit_top.predict_proba(X_te)[:,1]

        hgb_top = HistGradientBoostingClassifier(
            max_depth=4, learning_rate=0.08, max_iter=600,
            min_samples_leaf=30, random_state=SEED
        )
        hgb_top.fit(X_tr, y_tr_top, sample_weight=w_tr)
        p_top_hgb_va = hgb_top.predict_proba(X_va)[:,1]
        p_top_hgb_te = hgb_top.predict_proba(X_te)[:,1]

        mlp_top = MLPClassifier(
            hidden_layer_sizes=(48,24), activation='relu', solver='adam',
            alpha=1e-3, batch_size=512, learning_rate_init=1e-3,
            max_iter=200, early_stopping=True, n_iter_no_change=10,
            validation_fraction=0.1, random_state=SEED
        )
        mlp_top.fit(np.vstack([X_tr, X_va]), np.concatenate([y_tr_top, y_va_top]))
        p_top_mlp_te = mlp_top.predict_proba(X_te)[:,1]
        p_top_mlp_va = mlp_top.predict_proba(X_va)[:,1]

        # ------------------------
        # Base classifiers (Bottom)
        # ------------------------
        logit_bot = LogisticRegression(
            penalty='l2', solver='saga', max_iter=2000, C=1.0, n_jobs=None, random_state=SEED
        )
        logit_bot.fit(X_tr, y_tr_bot, sample_weight=w_tr)
        p_bot_logit_va = logit_bot.predict_proba(X_va)[:,1]
        p_bot_logit_te = logit_bot.predict_proba(X_te)[:,1]

        hgb_bot = HistGradientBoostingClassifier(
            max_depth=4, learning_rate=0.08, max_iter=600,
            min_samples_leaf=30, random_state=SEED
        )
        hgb_bot.fit(X_tr, y_tr_bot, sample_weight=w_tr)
        p_bot_hgb_va = hgb_bot.predict_proba(X_va)[:,1]
        p_bot_hgb_te = hgb_bot.predict_proba(X_te)[:,1]

        mlp_bot = MLPClassifier(
            hidden_layer_sizes=(48,24), activation='relu', solver='adam',
            alpha=1e-3, batch_size=512, learning_rate_init=1e-3,
            max_iter=200, early_stopping=True, n_iter_no_change=10,
            validation_fraction=0.1, random_state=SEED
        )
        mlp_bot.fit(np.vstack([X_tr, X_va]), np.concatenate([y_tr_bot, y_va_bot]))
        p_bot_mlp_te = mlp_bot.predict_proba(X_te)[:,1]
        p_bot_mlp_va = mlp_bot.predict_proba(X_va)[:,1]

        # ------------------------
        # Meta on validation: learn best linear blend of probs
        # ------------------------
        # Top meta
        meta_top_X_va = np.column_stack([p_top_logit_va, p_top_hgb_va, p_top_mlp_va])
        meta_top_X_te = np.column_stack([p_top_logit_te, p_top_hgb_te, p_top_mlp_te])

        if len(np.unique(y_va_top)) >= 2:
            meta_top = LogisticRegression(
                penalty=None, solver='lbfgs', max_iter=2000, random_state=SEED
            )
            meta_top.fit(meta_top_X_va, y_va_top, sample_weight=w_va)
            p_top_meta_te = meta_top.predict_proba(meta_top_X_te)[:, 1]
        else:
            # fallback: equal weights if VAL has a single class
            w_eq = np.array([1/3, 1/3, 1/3])
            p_top_meta_te = meta_top_X_te @ w_eq
            p_top_meta_te = np.clip(p_top_meta_te, 0.0, 1.0)

        # Bottom meta
        meta_bot_X_va = np.column_stack([p_bot_logit_va, p_bot_hgb_va, p_bot_mlp_va])
        meta_bot_X_te = np.column_stack([p_bot_logit_te, p_bot_hgb_te, p_bot_mlp_te])

        if len(np.unique(y_va_bot)) >= 2:
            meta_bot = LogisticRegression(
                penalty=None, solver='lbfgs', max_iter=2000, random_state=SEED
            )
            meta_bot.fit(meta_bot_X_va, y_va_bot, sample_weight=w_va)
            p_bot_meta_te = meta_bot.predict_proba(meta_bot_X_te)[:, 1]
        else:
            w_eq = np.array([1/3, 1/3, 1/3])
            p_bot_meta_te = meta_bot_X_te @ w_eq
            p_bot_meta_te = np.clip(p_bot_meta_te, 0.0, 1.0)

        # ------------------------
        # Simple VAL-based calibration → expected-return proxy
        # ------------------------
        # --- Robust VAL-based calibration ---
        val_df = val[['date','stock_exret']].copy()
        p_top_meta_va = (meta_top.predict_proba(meta_top_X_va)[:,1]
                        if 'meta_top' in locals() and len(np.unique(y_va_top))>=2
                        else np.clip(meta_top_X_va @ np.array([1/3,1/3,1/3]), 0.0, 1.0))
        p_bot_meta_va = (meta_bot.predict_proba(meta_bot_X_va)[:,1]
                        if 'meta_bot' in locals() and len(np.unique(y_va_bot))>=2
                        else np.clip(meta_bot_X_va @ np.array([1/3,1/3,1/3]), 0.0, 1.0))

        val_df['score_raw'] = p_top_meta_va - p_bot_meta_va

        # handle constant score case
        if np.allclose(val_df['score_raw'].max(), val_df['score_raw'].min()):
            # no spread → use global mean return as calibration
            mean_ret = float(val_df['stock_exret'].mean())
            score_te_cal = np.full_like(p_top_meta_te, mean_ret, dtype=float)
        else:
            # deciles on VAL
            try:
                val_df['dec'] = val_df.groupby('date')['score_raw'].transform(
                    lambda x: pd.qcut(x.rank(method='first'), 10, labels=False, duplicates='drop')
                )
            except Exception:
                val_df['dec'] = 4  # middle bucket if qcut fails

            dec_tbl = val_df.dropna(subset=['dec']).groupby('dec')['stock_exret'].mean()
            dec_tbl = dec_tbl.reindex(range(10)).fillna(dec_tbl.mean())

            def calibrate(scores):
                # global edges across VAL; fallback to linear edges if quantiles fail
                try:
                    edges = np.quantile(val_df['score_raw'].values, np.linspace(0, 1, 11))
                except Exception:
                    smin, smax = float(val_df['score_raw'].min()), float(val_df['score_raw'].max())
                    edges = np.linspace(smin, smax, 11)
                dec_idx = np.clip(np.searchsorted(edges, scores, side='right') - 1, 0, 9)
                return dec_tbl.values[dec_idx]

            score_te_raw = p_top_meta_te - p_bot_meta_te
            score_te_cal = calibrate(score_te_raw)

        # ------------------------
        # Collect outputs for TEST window
        # ------------------------
        reg_pred = test[['date','year','month','permno','stock_exret','beta_mkt','vol_12m']].copy()
        # keep model-specific “scores” as probabilities for diagnostics
        reg_pred['pred_cls_logit'] = p_top_logit_te - p_bot_logit_te
        reg_pred['pred_cls_hgb']   = p_top_hgb_te   - p_bot_hgb_te
        reg_pred['pred_cls_mlp']   = p_top_mlp_te   - p_bot_mlp_te
        reg_pred['pred_cls_meta']  = score_te_cal  # calibrated score = expected-return proxy
        out_rows.append(reg_pred)

        # ------------------------
        # R^2 and IC on TEST for diagnostics
        # ------------------------
        y_true = test['stock_exret'].values

        def _r2(y, yhat):
            denom = np.sum(y ** 2)
            return 1.0 - np.sum((y - yhat) ** 2) / (denom if denom > 0 else 1e-12)
        def _ic(y, yhat):
            return float(spearmanr(y, yhat).correlation)

        preds_map = {
            'cls_logit': reg_pred['pred_cls_logit'].values,
            'cls_hgb'  : reg_pred['pred_cls_hgb'].values,
            'cls_mlp'  : reg_pred['pred_cls_mlp'].values,
            'cls_meta' : reg_pred['pred_cls_meta'].values,
        }
        for k, yhat in preds_map.items():
            r2_by_model[k].append(_r2(y_true, yhat))
            ic_by_model[k].append(_ic(y_true, yhat))

        counter += 1
        oos_years_done += 1
        if QUICK_TEST and oos_years_done >= 2:
            break

    if not out_rows:
        raise RuntimeError("No OOS predictions produced.")

    out_df = pd.concat(out_rows, ignore_index=True)

    r2_avg = {k: float(np.nanmean(v)) for k, v in r2_by_model.items() if len(v)}
    ic_avg = {k: float(np.nanmean(v)) for k, v in ic_by_model.items() if len(v)}
    return out_df, {"oos_years": oos_years_done}, {"oos_r2": r2_avg, "oos_ic": ic_avg}

# ------------------------------
# Scoring: ensemble + beta-neutralization
# ------------------------------
def beta_neutralize_month(g: pd.DataFrame, score_col: str, beta_col: str='beta_mkt') -> pd.Series:
    if beta_col not in g.columns: return g[score_col]
    b = g[beta_col]
    s = g[score_col]
    if b.isna().all() or s.isna().all():
        return s
    var = np.nanvar(b.values)
    if var <= 1e-12:
        return s
    cov = np.nanmean((b - np.nanmean(b)) * (s - np.nanmean(s)))
    beta_hat = cov / var
    return s - beta_hat * b

def make_scores(df: pd.DataFrame) -> pd.DataFrame:
    import numpy as np
    from scipy.stats import spearmanr

    df = df.copy()
    model_cols = [c for c in ['pred_cls_logit','pred_cls_hgb','pred_cls_mlp','pred_cls_meta'] if c in df.columns]

    # 1) Monthly rank-IC for each model
    ic_rows = []
    for (y,m), g in df.groupby(['year','month']):
        y_true = g['stock_exret'].values
        for mcol in model_cols:
            y_hat = g[mcol].values
            ic = spearmanr(y_true, y_hat).correlation
            ic_rows.append({'year': y, 'month': m, 'model': mcol, 'ic': np.nan_to_num(ic)})
    ic = pd.DataFrame(ic_rows).sort_values(['model','year','month'])

    # 2) 12m EMA of IC, lagged by 1 month (only past data)
    ic['date_key'] = pd.to_datetime(ic['year'].astype(str) + '-' + ic['month'].astype(str) + '-01')
    w_rows = []
    for mcol, g in ic.groupby('model'):
        g = g.sort_values('date_key')
        # exponential weights: more recent months matter more
        ema = g['ic'].ewm(span=12, min_periods=3, adjust=False).mean().shift(1)
        w_rows.append(pd.DataFrame({'model': mcol, 'date_key': g['date_key'], 'w': ema}))
    w = pd.concat(w_rows, ignore_index=True)
    w['w'] = w['w'].clip(lower=0.0)  # no negative weights
    if w['w'].sum() == 0:
        # fallback if early months: equal weights
        w['w'] = 1.0

    # 3) Merge weights back and compute weighted rank-ensemble
    df['date_key'] = pd.to_datetime(df['year'].astype(str) + '-' + df['month'].astype(str) + '-01')

    # precompute per-month ranks for each model
    for mcol in model_cols:
        df[f'rank_{mcol}'] = df.groupby(['year','month'])[mcol].rank(method='average')

    # for each month, build a dict of weights
    w_map = { }
    for (dk), gg in w.groupby('date_key'):
        ww = gg.set_index('model')['w'].reindex(model_cols).fillna(0.0)
        if ww.sum() == 0: ww[:] = 1.0
        w_map[dk] = ww / ww.sum()

    # weighted sum of ranks
    def _score_month(g):
        ww = w_map.get(g['date_key'].iloc[0], pd.Series(1.0, index=model_cols))
        score = 0.0
        for mcol in model_cols:
            score += ww.get(mcol, 0.0) * g[f'rank_{mcol}']
        g['score_raw'] = score / (ww.sum() if ww.sum() > 0 else 1.0)
        return g

    df = df.groupby(['year','month'], group_keys=False).apply(_score_month)

    # 4) Neutralize score against beta + (optional) size/value/momentum if present (Numerai-style) :contentReference[oaicite:1]{index=1}
    def _neutralize(g):
        X = [np.ones(len(g))]
        if 'beta_mkt' in g.columns:
            X.append(g['beta_mkt'].fillna(0.0).values)
        for cand in ['mktcap','ME','lme','size','bm','book_to_market','mom12m','mom_12_1']:
            if cand in g.columns:
                col = g[cand]
                col = np.log(col.clip(lower=1)) if ('cap' in cand or cand in ['ME','lme','size']) else col
                X.append(col.fillna(0.0).values)
        X = np.vstack(X).T
        y = g['score_raw'].values
        try:
            coef, *_ = np.linalg.lstsq(X, y, rcond=None)
            resid = y - X @ coef
            g['score_neut'] = resid
        except Exception:
            g['score_neut'] = g['score_raw']
        return g

    df = df.groupby(['year','month'], group_keys=False).apply(_neutralize)
    def _calibrate_month(g, hist):
        if len(hist) < 5000:
            g['score_cal'] = g['score_neut']
            return g
        ir = IsotonicRegression(out_of_bounds='clip')
        ir.fit(hist['score_neut'].values, hist['stock_exret'].values)
        g['score_cal'] = ir.transform(g['score_neut'].values)
        return g

    df = df.sort_values(['date','permno'])
    df['score_cal'] = df['score_neut']
    # rolling 24m calibration window
    months = sorted(df[['year','month']].drop_duplicates().itertuples(index=False, name=None))
    for i,(y,m) in enumerate(months):
        cur_dt = pd.Timestamp(year=y, month=m, day=1)
        hist_start = cur_dt - pd.DateOffset(months=24)
        hist = df[(df['date']>=hist_start) & (df['date']<cur_dt)][['score_neut','stock_exret']].dropna()
        mask = (df['year']==y) & (df['month']==m)
        df.loc[mask, :] = _calibrate_month(df.loc[mask, :], hist)
    df['rank_final'] = df.groupby(['year','month'])['score_cal'].rank(ascending=False, method='first')
    return df, model_cols

# ------------------------------
# Portfolio construction
# ------------------------------
def inverse_variance_weights(returns_df: pd.DataFrame) -> pd.Series:
    # returns_df: columns = permno, one column 'stock_exret' per row for historical window
    # We'll compute per permno vol and set w ~ 1/vol; normalize to sum to 1
    vols = returns_df.groupby('permno')['stock_exret'].std()
    vols = vols.replace(0, np.nan)
    iv = 1.0 / vols
    iv = iv.replace([np.inf, -np.inf], np.nan).fillna(0.0)
    if iv.sum() <= 0:
        # fallback to equal weights
        iv[:] = 1.0
    w = iv / iv.sum()
    return w

def beta_balance_long_short(wL: pd.Series, wS: pd.Series,
                            betaL: pd.Series, betaS: pd.Series) -> Tuple[pd.Series, pd.Series]:
    """
    Rescale long and short legs so that beta_L * a  ≈  beta_S * b  (net beta ~ 0),
    while keeping sums close to 1 on each side.
    """
    bL = float((betaL.reindex(wL.index).fillna(0.0) * wL).sum())
    bS = float((betaS.reindex(wS.index).fillna(0.0) * wS).sum())
    if bL <= 1e-12 or bS <= 1e-12:
        return wL, wS
    # scale factors a, b to match betas
    a = bS / (bL + 1e-12)
    b = bL / (bS + 1e-12)
    # cap to avoid extreme scaling
    a = float(np.clip(a, 0.5, 2.0))
    b = float(np.clip(b, 0.5, 2.0))
    wL2 = wL * a
    wS2 = wS * b
    # re-normalize each leg to sum to 1
    wL2 = wL2 / (wL2.sum() + 1e-12)
    wS2 = wS2 / (wS2.sum() + 1e-12)
    return wL2, wS2

def beta_neutral_weights(w0: pd.Series, beta_vec: pd.Series, beta_target: float = 0.0) -> pd.Series:
    """
    Project base weights w0 onto constraints:
      sum(w) = 1,  beta_vec·w = beta_target
    Minimizes ||w - w0||^2, then clips negatives and renormalizes.
    """
    w0 = w0.copy().astype(float)
    b = beta_vec.reindex(w0.index).fillna(0.0).values
    A = np.vstack([np.ones_like(b), b])               # 2 x n
    rhs = np.array([1.0, beta_target])               # desired sums
    # L2 projection (solve for lambdas)
    # Solve (A A^T) λ = rhs - A w0
    M = A @ A.T                                       # 2 x 2
    lam = np.linalg.solve(M + 1e-10*np.eye(2), rhs - A @ w0.values)
    w = w0.values + A.T @ lam
    w = np.clip(w, 0.0, None)
    s = w.sum()
    if s > 0: w = w / s
    return pd.Series(w, index=w0.index)

def _compute_factor_mom(scored_df):
    # simple long-short EW factor from score_neut for momentum estimation
    fac = []
    for (y,m), g in scored_df.groupby(['year','month']):
        g = g.sort_values('rank_final')
        long_ids = g['permno'].head(TOP_N).tolist()
        short_ids = g['permno'].tail(TOP_N).tolist() if len(g) >= 2*TOP_N else []
        r = g.set_index('permno')['stock_exret']
        if long_ids and short_ids:
            fac_ret = float(r.reindex(long_ids).mean() - r.reindex(short_ids).mean())
        elif long_ids:
            fac_ret = float(r.reindex(long_ids).mean())
        else:
            fac_ret = 0.0
        fac.append({'year': y, 'month': m, 'ret': fac_ret})
    fac = pd.DataFrame(fac).sort_values(['year','month']).reset_index(drop=True)
    fac['mom6'] = fac['ret'].rolling(6, min_periods=3).sum()
    # bounded scaling ~[0.6, 1.4]
    fac['scale'] = 1.0 + 0.4 * np.tanh(4.0 * fac['mom6'].fillna(0.0))
    return fac[['year','month','scale']]

def build_portfolios(data_all: pd.DataFrame, top_n:int=TOP_N, n_short:int=N_SHORT, buffer_n:int=BUFFER_N,
                     weighting:str='EW', hist_window:int=VOL_WIN, mkt: Optional[pd.DataFrame]=None) -> Tuple[Dict[str, pd.DataFrame], Dict]:
    """
    Returns dict of portfolios:
      - 'long_only_EW', 'long_only_IVP', 'long_short_EW', 'long_short_IVP'
    Each dataframe has columns year, month, port_ret
    """
    # Prepare history for IVP weights
    data_all = data_all.sort_values(['date','permno']).copy()

    scale_df = _compute_factor_mom(data_all)

    portfolios = {k: [] for k in ['long_only_EW','long_only_IVP','long_short_EW','long_short_IVP']}
    prev_long = set()
    prev_short = set()

    months = sorted(data_all[['year','month']].drop_duplicates().itertuples(index=False, name=None))

    for (y,m) in months:
        g = data_all[(data_all['year']==y) & (data_all['month']==m)].copy()
        if g.empty: continue

        # base ranks already computed in make_scores()
        g = g.sort_values('rank_final')

        sector_col = None
        for cand in ['gsector','ggroup','gind','gsubind','sector','industry','sic2','naics2','exchcd']:
            if cand in g.columns:
                sector_col = cand; break

        long_names = pick_topN_sector_quota(
            g, rank_col='rank_final', top_n=TOP_N, sector_col=(sector_col or 'gsector'),
            max_sector_frac=0.25, min_per_sector=2
        )

        # For long-short, construct shorts from the bottom
        g_bot = g.sort_values('rank_final', ascending=False)  # worst at top
        keep_short = g_bot[g_bot['permno'].isin(prev_short) & (g_bot['rank_final'] >= (len(g_bot) - (n_short + buffer_n)))]['permno'].tolist()
        best_short = g_bot[~g_bot['permno'].isin(keep_short)]['permno'].head(max(0, n_short - len(keep_short))).tolist()
        short_names = keep_short + best_short

        # Compute returns for this month
        month_ret = g.set_index('permno')['stock_exret']

        # Weighting
        # Historical window for volatility/IVP weights
        dt = pd.Timestamp(year=y, month=m, day=1)
        hist_end = dt - pd.DateOffset(months=1)
        hist_start = hist_end - pd.DateOffset(months=hist_window-1)
        hist = data_all[(data_all['date']>=hist_start) & (data_all['date']<=hist_end)][['permno','date','stock_exret']].copy()

        # Long-only with confidence-based weighting
        if long_names:
            # build score series aligned to permno for the month
            sc_name = 'score_neut_fn' if 'score_neut_fn' in g.columns else ('score_neut' if 'score_neut' in g.columns else 'score_raw')
            score_series = g.set_index('permno')[sc_name].reindex(long_names)

            # confidence weights (use softmax of clean score)
            w_conf = softmax_weights_from_score(score_series, temp=0.18)

            # combine with IVP (if available) or use confidence alone for EW variant
            if 'vol_12m' in g.columns:
                iv = g.set_index('permno')['vol_12m'].reindex(long_names).replace(0, np.nan)
                w_ivp = (1.0 / iv).fillna((1.0 / iv.replace(0,np.nan)).median())
                w_ivp = w_ivp / (w_ivp.sum() + 1e-12)
                w_base_EW  = w_conf.copy()                               # confidence-only version (for EW)
                w_base_IVP = (0.5 * w_conf + 0.5 * w_ivp)                # blend confidence & IVP
            else:
                w_base_EW  = w_conf.copy()
                w_base_IVP = w_conf.copy()

            # β-neutral projection (keeps sum(w)=1, beta≈0)
            beta_long = g.set_index('permno')['beta_mkt'].reindex(long_names)
            w_EW_long  = beta_neutral_weights(w_base_EW,  beta_long, beta_target=0.0)
            w_IVP_long = beta_neutral_weights(w_base_IVP, beta_long, beta_target=0.0)

            # Calculate returns
            ret_LO_EW = float(month_ret.reindex(long_names).fillna(0.0).multiply(w_EW_long, fill_value=0).sum())
            ret_LO_IVP = float(month_ret.reindex(long_names).fillna(0.0).multiply(w_IVP_long, fill_value=0).sum())
        else:
            ret_LO_EW = 0.0
            ret_LO_IVP = 0.0

        # Long-Short EW
        if long_names and short_names:
            wL = pd.Series(1.0/len(long_names), index=long_names)
            wS = pd.Series(1.0/len(short_names), index=short_names)
            beta_series = g.set_index('permno')['beta_mkt']
            wL, wS = beta_balance_long_short(wL, wS, beta_series, beta_series)
            ret_LS_EW = float(month_ret.reindex(long_names).fillna(0.0).multiply(wL, fill_value=0).sum()
                            - month_ret.reindex(short_names).fillna(0.0).multiply(wS, fill_value=0).sum())
        else:
            ret_LS_EW = 0.0

        # Long-Short IVP
        if long_names and short_names and not hist.empty:
            hist_long = hist[hist['permno'].isin(long_names)]
            hist_short = hist[hist['permno'].isin(short_names)]
            if len(hist_long['permno'].unique()) >= 2 and len(hist_short['permno'].unique()) >= 2:
                wL = inverse_variance_weights(hist_long).reindex(long_names).fillna(0.0)
                wS = inverse_variance_weights(hist_short).reindex(short_names).fillna(0.0)
                if wL.sum() > 0: wL = wL / wL.sum()
                if wS.sum() > 0: wS = wS / wS.sum()
                beta_series = g.set_index('permno')['beta_mkt']
                wL, wS = beta_balance_long_short(wL, wS, beta_series, beta_series)
                ret_LS_IVP = float(month_ret.reindex(long_names).fillna(0.0).multiply(wL, fill_value=0).sum()
                                - month_ret.reindex(short_names).fillna(0.0).multiply(wS, fill_value=0).sum())
            else:
                ret_LS_IVP = ret_LS_EW
        else:
            ret_LS_IVP = ret_LS_EW

        sc = float(scale_df.loc[(scale_df['year']==y) & (scale_df['month']==m), 'scale'].fillna(1.0).values[:1] or [1.0])

        ret_LO_EW  *= sc
        ret_LO_IVP *= sc
        ret_LS_EW  *= sc
        ret_LS_IVP *= sc

        portfolios['long_only_EW'].append({'year':y,'month':m,'port_ret':ret_LO_EW})
        portfolios['long_only_IVP'].append({'year':y,'month':m,'port_ret':ret_LO_IVP})
        portfolios['long_short_EW'].append({'year':y,'month':m,'port_ret':ret_LS_EW})
        portfolios['long_short_IVP'].append({'year':y,'month':m,'port_ret':ret_LS_IVP})

        prev_long = set(long_names)
        prev_short = set(short_names)

    # Convert to DataFrames
    portfolios = {k: pd.DataFrame(v).sort_values(['year','month']).reset_index(drop=True) for k,v in portfolios.items()}
    # Turnover (long leg only)
    def _turnover(series_of_sets: List[set]) -> float:
        if len(series_of_sets) < 2: return 0.0
        changes = []
        for i in range(1, len(series_of_sets)):
            prev = series_of_sets[i-1]; cur = series_of_sets[i]
            if len(prev)==0: continue
            changes.append(1.0 - len(prev & cur)/float(len(prev)))
        return float(np.mean(changes)) if changes else 0.0

    # For reporting turnover, reconstruct long holdings per month again
    longs_seq = []
    prev_long = set()
    for (y,m) in months:
        g = data_all[(data_all['year']==y) & (data_all['month']==m)].copy().sort_values('rank_final')
        keep_long = g[g['permno'].isin(prev_long) & (g['rank_final'] <= (top_n + buffer_n))]['permno'].tolist()
        best_long = g[~g['permno'].isin(keep_long)]['permno'].head(max(0, top_n - len(keep_long))).tolist()
        long_names = set(keep_long + best_long)
        longs_seq.append(long_names)
        prev_long = long_names

    turnover = _turnover(longs_seq)
    return portfolios, {'turnover': turnover}

# ------------------------------
# Merge market for alpha
# ------------------------------
def merge_market_data(port: pd.DataFrame, mkt: Optional[pd.DataFrame]) -> pd.DataFrame:
    if mkt is None:
        return port
    return pd.merge(port, mkt, on=['year','month'], how='left')

# ------------------------------
# Metrics
# ------------------------------
def compute_metrics(port: pd.DataFrame) -> Metrics:
    if len(port) == 0:
        return Metrics(0,0,0,None,None,None,None,0,0,0,None)
    sharpe_ann = (port['port_ret'].mean() / (port['port_ret'].std() + 1e-12)) * np.sqrt(12.0)
    max_1m_loss = float(port['port_ret'].min())
    max_dd = max_drawdown_from_returns(port['port_ret'])
    avg_ret_ann = annualize_return(port['port_ret'].mean())
    vol_ann = annualize_std(port['port_ret'].std())
    a_m, a_a, t_a, ir_a, beta = compute_alpha_stats(port)
    return Metrics(
        sharpe_ann=float(sharpe_ann),
        max_1m_loss=float(max_1m_loss),
        max_drawdown=float(max_dd),
        alpha_monthly=None if a_m is None else float(a_m),
        alpha_ann=None if a_a is None else float(a_a),
        alpha_tstat=None if t_a is None else float(t_a),
        info_ratio_ann=None if ir_a is None else float(ir_a),
        avg_ret_ann=float(avg_ret_ann),
        vol_ann=float(vol_ann),
        turnover=0.0,  # set later
        beta=None if beta is None else float(beta),
    )

# ------------------------------
# Main
# ------------------------------
def main(out_dir="/mnt/data", use_existing_predictions=False):
    os.makedirs(out_dir, exist_ok=True)

    # Always load market factors (needed for portfolio construction)
    mkt = load_market_factors()

    # Check if we should use existing predictions
    preds_file = os.path.join(out_dir, "predictions_plus.csv")
    if use_existing_predictions and os.path.exists(preds_file):
        print(f"Loading existing predictions from {preds_file}")
        preds = pd.read_csv(preds_file, parse_dates=['date'])
        train_info = {"note": "Using existing predictions"}
        extra_info = {"note": "Using existing predictions"}
        # Still need to initialize macro_cols for consistency
        macro_cols = []
    else:
        print("Running full training pipeline...")
        data, features = load_core_data()
        data = add_trailing_beta(data, mkt)
        data = add_trailing_vol(data)

        macro_df = build_macro_features(mkt)
        macro_cols = []
        if macro_df is not None:
            data = data.merge(macro_df, on=['year','month'], how='left')
            macro_cols = [c for c in data.columns if c.startswith('macro_')]
        features = features + macro_cols

        preds, train_info, extra_info = train_predict_monthly(data, features)
        preds_out = os.path.join(out_dir, "predictions_plus.csv")
        preds.to_csv(preds_out, index=False)

    scored, used_models = make_scores(preds)

    # Build portfolios
    ports, port_info = build_portfolios(scored, mkt=mkt)
    # Merge market for alpha calc and compute metrics per variant
    results = {}
    for name, port in ports.items():
        ports[name] = merge_market_data(port, mkt)
        results[name] = compute_metrics(ports[name])
        results[name].turnover = float(port_info.get('turnover', 0.0))

    # Save portfolio returns
    for name, port in ports.items():
        port.to_csv(os.path.join(out_dir, f"portfolio_{name}.csv"), index=False)

    # Compose JSON
    out_json = {
        "info": {
            "train": train_info,
            "models_used": used_models,
            "oos_r2": extra_info.get("oos_r2", {}),
            "turnover_long_leg": port_info.get('turnover', 0.0),
            "lightgbm_available": LGBM_AVAILABLE,
        },
        "metrics": {k: asdict(v) for k,v in results.items()}
    }
    with open(os.path.join(out_dir, "metrics_plus.json"), "w") as f:
        json.dump(out_json, f, indent=2)

    print(json.dumps(out_json, indent=2))

if __name__ == "__main__":
    out_dir = sys.argv[1] if len(sys.argv) > 1 else "./pred_8"
    use_existing = "--use-existing" in sys.argv or "-e" in sys.argv
    main(out_dir, use_existing_predictions=use_existing)