# Algorithmic Trading Strategies

**Source:** https://en.wikipedia.org/wiki/Algorithmic_trading


## Thesis

The trading strategy leverages algorithmic methods to exploit market inefficiencies, particularly through directional change detection and statistical arbitrage. By analyzing historical price patterns and market dynamics, the system aims to identify profitable trades in volatile or trending markets while minimizing exposure to random noise. The use of deep reinforcement learning (DRL) enables adaptive policies that balance risk and reward, providing a significant edge over static systems in dynamic market conditions. The strategy leverages short-term market inefficiencies and mean reversion in asset prices to generate alpha. By identifying pairs of highly correlated assets, the algorithm exploits pricing discrepancies between them, expecting the spread to revert to its historical average. This approach is particularly effective during periods of market stress or reduced liquidity when temporary mispricings are more likely.

## Universe & Rebalancing

**Universe:** Pairs of highly correlated equities (e.g., sector peers) and ETFs vs their underlying indices (e.g., S&P 500 ETF vs S&P 500)
**Rebalancing:** Monthly


## Signals

- **Directional Change (DC) Algorithm:** Detects subtle trend transitions by identifying price movements beyond a threshold followed by a confirmation period. Triggers trades based on upward or downward trends.
- **Technical Indicators:** Uses indicators like relative strength index (RSI), moving averages, and other momentum-based metrics to automate buy/sell decisions.
- **Statistical Arbitrage:** Identifies deviations from statistically significant relationships between asset prices, exploiting temporary mispricings in the market.
- **Mean Reversion:** Seeks to capitalize on price reversion to historical averages by identifying overextended prices and expecting a return to mean.
- **Spread Relative to Historical Average:** The ratio of the current spread between paired assets to its historical average spread, used to identify deviations from normal pricing relationships.
- **Volatility:** Market volatility as measured by standard deviation of asset returns over a specified period, used to detect periods of heightened price movement indicative of potential mean reversion opportunities.
