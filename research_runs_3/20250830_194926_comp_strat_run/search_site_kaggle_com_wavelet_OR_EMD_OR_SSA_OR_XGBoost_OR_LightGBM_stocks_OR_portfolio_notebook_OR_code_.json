[{"title": "🦚 XGBoost & LightGBM Order Book Fusion (CPU)", "url": "https://www.kaggle.com/code/verracodeguacas/xgboost-lightgbm-order-book-fusion-cpu/input", "engines": ["google"]}, {"title": "XGBoost for stock trend & prices prediction", "url": "https://www.kaggle.com/code/mtszkw/xgboost-for-stock-trend-prices-prediction", "engines": ["google"]}, {"title": "wavelet stock market analyst", "url": "https://www.kaggle.com/code/edwardhuangtw/wavelet-stock-market-analyst", "engines": ["google"]}, {"title": "Predicting stock returns by XGBoost", "url": "https://www.kaggle.com/code/zonghao/predicting-stock-returns-by-xgboost", "engines": ["google"]}, {"title": "Canadian Banks Stock Market Analysis", "url": "https://www.kaggle.com/code/nghihuynh/canadian-banks-stock-market-analysis", "engines": ["google"]}, {"title": "Stock Prediction(next period) LightGBM", "url": "https://www.kaggle.com/code/kenneth30/stock-prediction-next-period-lightgbm", "engines": ["google"]}, {"title": "GLM, Neural Nets and XGBoost for Insurance Pricing", "url": "https://www.kaggle.com/code/floser/glm-neural-nets-and-xgboost-for-insurance-pricing/notebook", "engines": ["google"]}, {"title": "JPX Stock Market Analysis & Prediction with LGBM", "url": "https://www.kaggle.com/code/kellibelcher/jpx-stock-market-analysis-prediction-with-lgbm", "engines": ["google"]}, {"title": "Prediction of Stock index using XGBoost,RF and SVM", "url": "https://www.kaggle.com/code/sad<PERSON><PERSON><PERSON><PERSON><PERSON>/prediction-of-stock-index-using-xgboost-rf-and-svm", "engines": ["google"]}, {"title": "Beat the Stock market: the lazy strategy", "url": "https://www.kaggle.com/code/cnic92/beat-the-stock-market-the-lazy-strategy", "engines": ["google"]}]