[{"title": "<PERSON><PERSON><PERSON>ub <PERSON>", "url": "https://paperswithcode.com/search?q=author%3AJaydip+Sen&order_by=stars", "engines": ["google"]}, {"title": "Integrating Hidden Markov Models with Neural Networks", "url": "https://paperswithcode.com/paper/ai-powered-energy-algorithmic-trading", "engines": ["google"]}, {"title": "<PERSON><PERSON><PERSON> - Github Stars", "url": "https://paperswithcode.com/search?q=author%3AThierry+<PERSON><PERSON><PERSON>&order_by=stars", "engines": ["google"]}, {"title": "Astock: A New Dataset and Automated Stock Trading ...", "url": "https://paperswithcode.com/paper/astock-a-new-dataset-and-automated-stock/review/", "engines": ["google"]}, {"title": "<PERSON>", "url": "https://paperswithcode.com/author/markus-pelger", "engines": ["google"]}, {"title": "<PERSON>", "url": "https://paperswithcode.com/search?q=author%3ADamian+<PERSON><PERSON><PERSON>&order_by=stars", "engines": ["google"]}, {"title": "<PERSON><PERSON><PERSON>", "url": "https://paperswithcode.com/search?q=author%3ABastien+<PERSON><PERSON><PERSON>&order_by=stars", "engines": ["google"]}, {"title": "Regret-Optimized Portfolio Enhancement through Deep ...", "url": "https://paperswithcode.com/paper/regret-optimized-portfolio-enhancement", "engines": ["google"]}, {"title": "Stock Market Prediction", "url": "https://paperswithcode.com/task/stock-market-prediction/latest?page=2", "engines": ["google"]}, {"title": "<PERSON><PERSON><PERSON>", "url": "https://paperswithcode.com/author/arpit-awad", "engines": ["google"]}]