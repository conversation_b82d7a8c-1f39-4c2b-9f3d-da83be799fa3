[{"title": "A general backtest system for alpha trading strategy", "url": "https://github.com/yuba316/Alpha_Strategy_BackTest_System", "engines": ["google"]}, {"title": "Backtested trading strategies using ML for signals", "url": "https://github.com/AndreasTheodoulou/ML_Trading_Strategies", "engines": ["google"]}, {"title": "Backtesting Algo Strategies", "url": "https://github.com/PhilipWaddilove/Backtesting-Algos", "engines": ["google"]}, {"title": "replacementAI/A-Backtest-A-Day", "url": "https://github.com/replacementAI/A-Backtest-A-Day", "engines": ["google"]}, {"title": "A Python project that simulates a portfolio backtest using ...", "url": "https://github.com/sapk806/cross_sectional_factor_backtest_project", "engines": ["google"]}, {"title": "Reya-Labs/v1-sbf", "url": "https://github.com/Reya-Labs/v1-sbf", "engines": ["google"]}, {"title": "stefan-jansen/machine-learning-for-trading", "url": "https://github.com/stefan-jansen/machine-learning-for-trading", "engines": ["google"]}, {"title": "JonathanJing/Stock-Trading-Strategy", "url": "https://github.com/JonathanJing/Stock-Trading-Strategy", "engines": ["google"]}, {"title": "smullins998/Tr4der: Backtesting Library for Strategy Ideation", "url": "https://github.com/smullins998/Tr4der", "engines": ["google"]}, {"title": "Gavin-OP/fina4380-project", "url": "https://github.com/Gavin-OP/fina4380-project", "engines": ["google"]}]