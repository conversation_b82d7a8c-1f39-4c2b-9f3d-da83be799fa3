# Voltz LongRateStrategy

**Source:** https://github.com/Reya-Labs/v1-sbf


## Thesis

The Voltz SBF evaluates trading strategies on an event-by-event basis, focusing on the handling of market data and execution events. The LongRateStrategy is a simplistic strategy designed to demonstrate how strategies are implemented within this framework, inheriting from the Strategy abstract base class and utilizing MarketEvent for receiving new market data and SignalEvent for generating signals.

## Universe & Rebalancing

**Universe:** The universe of assets is not explicitly defined in the provided text but it can be inferred that it includes tokens such as 'Aave USDC' and IRS swaps based on the context of the events described (e.g., OrderEvent, FillEvent).
**Rebalancing:** Not specified; rebalancing cadence would need to be determined by the specific implementation of the strategy.


## Signals

- **SignalEvent:** Handles the event of sending a Signal from a Strategy object. This signal is received by a Portfolio object and acted upon, potentially leading to long/short/exit positions being assigned.
