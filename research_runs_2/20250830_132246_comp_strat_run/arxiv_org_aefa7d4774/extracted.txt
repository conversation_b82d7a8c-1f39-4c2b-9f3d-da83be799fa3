Quantitative Finance > Trading and Market Microstructure
Title:Optimal Trade Execution with Uncertain Volume Target
View PDFAbstract:In the seminal paper on optimal execution of portfolio transactions, <PERSON><PERSON><PERSON> and <PERSON> (2001) define the optimal trading strategy to liquidate a fixed volume of a single security under price uncertainty. Yet there exist situations, such as in the power market, in which the volume to be traded can only be estimated and becomes more accurate when approaching a specified delivery time. During the course of execution, a trader should then constantly adapt their trading strategy to meet their fluctuating volume target. In this paper, we develop a model that accounts for volume uncertainty and we show that a risk-averse trader has benefit in delaying their trades. More precisely, we argue that the optimal strategy is a trade-off between early and late trades in order to balance risk associated with both price and volume. By incorporating a risk term related to the volume to trade, the static optimal strategies suggested by our model avoid the explosion in the algorithmic complexity usually associated with dynamic programming solutions, all the while yielding competitive performance.
Submission history
From: <PERSON> [view email][v1] Sun, 28 Oct 2018 23:48:05 UTC (160 KB)
[v2] <PERSON><PERSON>, 8 Jan 2019 12:04:28 UTC (383 KB)
[v3] Wed, 2 Oct 2019 15:54:47 UTC (699 KB)
[v4] Mon, 18 May 2020 14:38:23 UTC (735 KB)
[v5] <PERSON><PERSON>, 14 Sep 2021 13:13:05 UTC (6,095 KB)
Bibliographic and Citation Tools
Code, Data and Media Associated with this Article
Demos
Recommenders and Search Tools
arXivLabs: experimental projects with community collaborators
arXivLabs is a framework that allows collaborators to develop and share new arXiv features directly on our website.
Both individuals and organizations that work with arXivLabs have embraced and accepted our values of openness, community, excellence, and user data privacy. arXiv is committed to these values and only works with partners that adhere to them.
Have an idea for a project that will add value for arXiv's community? Learn more about arXivLabs.