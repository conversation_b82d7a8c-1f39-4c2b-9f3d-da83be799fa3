Computer Science > Artificial Intelligence
Title:Neural Feature Learning From Relational Database
View PDFAbstract:Feature engineering is one of the most important but most tedious tasks in data science. This work studies automation of feature learning from relational database. We first prove theoretically that finding the optimal features from relational data for predictive tasks is NP-hard. We propose an efficient rule-based approach based on heuristics and a deep neural network to automatically learn appropriate features from relational data. We benchmark our approaches in ensembles in past Kaggle competitions. Our new approach wins late medals and beats the state-of-the-art solutions with significant margins. To the best of our knowledge, this is the first time an automated data science system could win medals in Kaggle competitions with complex relational database.
Submission history
From: <PERSON><PERSON> [view email][v1] Tue, 16 Jan 2018 17:18:29 UTC (784 KB)
[v2] Fri, 18 May 2018 14:41:52 UTC (293 KB)
[v3] Sun, 17 Jun 2018 07:28:31 UTC (297 KB)
[v4] Sat, 15 Jun 2019 05:25:25 UTC (464 KB)
Bibliographic and Citation Tools
Code, Data and Media Associated with this Article
Demos
Recommenders and Search Tools
arXivLabs: experimental projects with community collaborators
arXivLabs is a framework that allows collaborators to develop and share new arXiv features directly on our website.
Both individuals and organizations that work with arXivLabs have embraced and accepted our values of openness, community, excellence, and user data privacy. arXiv is committed to these values and only works with partners that adhere to them.
Have an idea for a project that will add value for arXiv's community? Learn more about arXivLabs.