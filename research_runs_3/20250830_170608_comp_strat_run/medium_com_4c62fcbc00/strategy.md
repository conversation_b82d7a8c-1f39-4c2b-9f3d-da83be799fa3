# Essential Tips for Machine Learning with Tabular Data

**Source:** https://medium.com/@vince-lam/decoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d


## Thesis

The blog post highlights the importance of feature engineering, robust cross-validation techniques, and the use of Gradient Boosted Decision Trees (GBDTs) over deep learning models for tabular data. It emphasizes that almost all competition-winning solutions involve a large ensemble of diverse models to improve prediction performance.

## Universe & Rebalancing

**Universe:** Not explicitly defined in the text; however, it can be inferred from context that this strategy applies to financial and healthcare datasets among others where tabular data is used.
**Rebalancing:** Monthly


## Signals

- **Feature Engineering:** The process of creating, selecting, and transforming variables to maximize their predictive power in models. This includes techniques such as aggregations, differences, ratios, lags, normalizations, removal of zero importance features, stepped hierarchical permutation importance, stepped permutation importance, forward feature selection, and time-series cross-validation.
- **Cross-Validation:** The process of evaluating a machine learning model by training on subsets of the data and testing performance on remaining data to understand how well the model generalizes. Common techniques include k-fold cross-validation and time-series cross-validation.
- **Gradient Boosted Decision Trees (GBDT):** A family of traditional machine learning models for tabular data that constructs a strong predictive model by combining weak learners, decision trees iteratively to correct errors made by the existing ensemble. The three main GBDT models are XGBoost, LightGBM, and CatBoost.
- **Ensembling:** The process of combining multiple different machine learning models to improve overall prediction performance. This can be done through techniques such as averaging (weighted average), voting, bagging, boosting, stacking.
