{"title": "Quantamental Stock Trading Strategy", "source_url": "https://github.com/JonathanJing/Stock-Trading-Strategy", "thesis": "The project aims to develop a stock trading strategy that leverages quantamental analysis, combining quantitative and fundamental approaches. By using machine learning algorithms on financial indicators from Quandl and Yahoo Finance, the team predicts future stock returns and constructs a portfolio based on these predictions. The strategy selects stocks with fundamentals outperforming S&P 500 by 10%, optimizes weights for maximum Sharpe ratio using Monte Carlo simulation and Nelder-Mead Simplex algorithm, and backtests performance against benchmarks like SPY ETF.", "universe": "The stock universe consists of companies listed on major US exchanges with market capitalization over $10 million. The team uses fundamental data from Quandl for the period between March 31, 2010, and November 11, 2020, and stock price data from Yahoo Finance.", "rebalancing": "weekly", "signals": [{"name": "Fundamental Outperformance Signal", "definition": "Stocks are selected if their fundamentals predict a mean quarterly return exceeding S&P 500 by at least 10%."}, {"name": "Sharpe Ratio Optimization Signal", "definition": "Portfolio weights are optimized weekly using <PERSON> Carlo simulation and Nelder-Mead Simplex algorithm to maximize Sharpe ratio, assuming normal distribution of returns."}]}