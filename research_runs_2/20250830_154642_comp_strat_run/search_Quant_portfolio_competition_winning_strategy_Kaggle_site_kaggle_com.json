[{"title": "Benchmarking M6 Competitors: An Analysis of Financial Metrics and Discussion of Incentives", "url": "http://arxiv.org/abs/2406.19105v2", "engines": ["arxiv"]}, {"title": "Kaggle LSHTC4 Winning Solution", "url": "http://arxiv.org/abs/1405.0546v2", "engines": ["arxiv"]}, {"title": "M6 Investment Challenge: The Role of Luck and Strategic Considerations", "url": "http://arxiv.org/abs/2412.04490v1", "engines": ["arxiv"]}, {"title": "IceCube -- Neutrinos in Deep Ice The Top 3 Solutions from the Public Kaggle Competition", "url": "http://arxiv.org/abs/2310.15674v1", "engines": ["arxiv"]}, {"title": "Mini-Giants: \"Small\" Language Models and Open Source Win-Win", "url": "http://arxiv.org/abs/2307.08189v2", "engines": ["arxiv"]}, {"title": "Neural Feature Learning From Relational Database", "url": "http://arxiv.org/abs/1801.05372v4", "engines": ["arxiv"]}, {"title": "Risk Analysis of Passive Portfolios", "url": "http://arxiv.org/abs/2407.08332v1", "engines": ["arxiv"]}, {"title": "2nd Place Solution to Google Landmark Retrieval 2021", "url": "http://arxiv.org/abs/2110.04294v1", "engines": ["arxiv"]}, {"title": "MLBench: How Good Are Machine Learning Clouds for Binary Classification Tasks on Structured Data?", "url": "http://arxiv.org/abs/1707.09562v3", "engines": ["arxiv"]}, {"title": "IIT Madras Millennium Quant Challenge", "url": "https://www.kaggle.com/competitions/iit-madras-millennium-quant-challenge", "engines": ["google"]}, {"title": "Copy of Millennium Hiring Test", "url": "https://kaggle.com/competitions/copy-of-millennium-hiring-test", "engines": ["google"]}, {"title": "Kaggle Competitions", "url": "https://www.kaggle.com/competitions", "engines": ["google"]}, {"title": "🤖 Trading with Machine Learning: Classification", "url": "https://www.kaggle.com/code/lusfernandotorres/trading-with-machine-learning-classification", "engines": ["google"]}, {"title": "Millennium Statistical Modeling & Quant Challenge", "url": "https://www.kaggle.com/competitions/millennium-statistical-modeling-quant-challenge", "engines": ["google"]}, {"title": "Kaggle Winning Solutions: AI Trends & Insights", "url": "https://www.kaggle.com/code/tahaalselwii/kaggle-winning-solutions-ai-trends-insights", "engines": ["google"]}, {"title": "JPX Stock Exchange", "url": "https://www.kaggle.com/code/javigallego/jpx-stock-exchange", "engines": ["google"]}, {"title": "Algorithmic trading with Keras (using LSTM)", "url": "https://www.kaggle.com/code/fedewole/algorithmic-trading-with-keras-using-lstm", "engines": ["google"]}, {"title": "ML Project - Stock Return Prediction & Analysis", "url": "https://www.kaggle.com/code/heiwanleung/ml-project-stock-return-prediction-analysis", "engines": ["google"]}, {"title": "A Multi-step Approach for Minimizing Risk in Decentralized Exchanges", "url": "http://arxiv.org/abs/2406.07200v2", "engines": ["arxiv"]}, {"title": "Introduction To Financial Mathematics", "url": "https://www.kaggle.com/code/carlolepelaars/introduction-to-financial-mathematics", "engines": ["google"]}]