<!doctype html><html lang="en"><head><title data-rh="true">Building your Long Term Portfolio using Unsupervised ML with Community Detection Analysis in Python &amp; R | by <PERSON><PERSON><PERSON> | Medium</title><meta data-rh="true" charset="utf-8"/><meta data-rh="true" name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1,maximum-scale=1"/><meta data-rh="true" name="theme-color" content="#000000"/><meta data-rh="true" name="twitter:app:name:iphone" content="Medium"/><meta data-rh="true" name="twitter:app:id:iphone" content="828256236"/><meta data-rh="true" property="al:ios:app_name" content="Medium"/><meta data-rh="true" property="al:ios:app_store_id" content="828256236"/><meta data-rh="true" property="al:android:package" content="com.medium.reader"/><meta data-rh="true" property="fb:app_id" content="542599432471018"/><meta data-rh="true" property="og:site_name" content="Medium"/><meta data-rh="true" name="apple-itunes-app" content="app-id=828256236, app-argument=/community-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9, affiliate-data=pt=698524&amp;ct=smart_app_banner&amp;mt=8"/><meta data-rh="true" property="og:type" content="article"/><meta data-rh="true" property="article:published_time" content="2020-10-13T23:43:06.722Z"/><meta data-rh="true" name="title" content="Building your Long Term Portfolio using Unsupervised ML with Community Detection Analysis in Python &amp; R | by Aakash Kedia | Medium"/><meta data-rh="true" property="og:title" content="Community Detection &amp; Network Analysis of the Stock Market in Python &amp; R — Part 3"/><meta data-rh="true" property="al:android:url" content="medium://p/ca4e0fbb8ca9"/><meta data-rh="true" property="al:ios:url" content="medium://p/ca4e0fbb8ca9"/><meta data-rh="true" property="al:android:app_name" content="Medium"/><meta data-rh="true" name="description" content="We finally get to the Portfolio Building where we can try to beat the SPY average returns Year over Yer (YoY). The whole code base can be found here: We have done everything to required from data…"/><meta data-rh="true" property="og:description" content="We finally get to the Portfolio Building where we can try to beat the SPY benchmark returns  Year over Yer (YoY). The whole code base can…"/><meta data-rh="true" property="og:url" content="https://akiboy96.medium.com/community-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9"/><meta data-rh="true" property="al:web:url" content="https://akiboy96.medium.com/community-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9"/><meta data-rh="true" property="og:image" content="https://miro.medium.com/v2/resize:fit:700/1*lhR-zvcYQhsOswSL8unAJw.png"/><meta data-rh="true" property="article:author" content="https://akiboy96.medium.com"/><meta data-rh="true" name="author" content="Aakash Kedia"/><meta data-rh="true" name="robots" content="index,noarchive,follow,max-image-preview:large"/><meta data-rh="true" name="referrer" content="unsafe-url"/><meta data-rh="true" property="twitter:title" content="Community Detection &amp; Network Analysis of the Stock Market in Python &amp; R — Part 3"/><meta data-rh="true" name="twitter:site" content="@Medium"/><meta data-rh="true" name="twitter:app:url:iphone" content="medium://p/ca4e0fbb8ca9"/><meta data-rh="true" property="twitter:description" content="We finally get to the Portfolio Building where we can try to beat the SPY benchmark returns  Year over Yer (YoY). The whole code base can…"/><meta data-rh="true" name="twitter:image:src" content="https://miro.medium.com/v2/resize:fit:700/1*lhR-zvcYQhsOswSL8unAJw.png"/><meta data-rh="true" name="twitter:card" content="summary_large_image"/><meta data-rh="true" name="twitter:label1" content="Reading time"/><meta data-rh="true" name="twitter:data1" content="8 min read"/><link data-rh="true" rel="icon" href="https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19"/><link data-rh="true" rel="search" type="application/opensearchdescription+xml" title="Medium" href="/osd.xml"/><link data-rh="true" rel="apple-touch-icon" sizes="152x152" href="https://miro.medium.com/v2/resize:fill:304:304/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="apple-touch-icon" sizes="120x120" href="https://miro.medium.com/v2/resize:fill:240:240/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="apple-touch-icon" sizes="76x76" href="https://miro.medium.com/v2/resize:fill:152:152/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="apple-touch-icon" sizes="60x60" href="https://miro.medium.com/v2/resize:fill:120:120/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="mask-icon" href="https://miro.medium.com/v2/resize:fill:1000:1000/7*GAOKVe--MXbEJmV9230oOQ.png" color="#171717"/><link data-rh="true" rel="preconnect" href="https://glyph.medium.com" crossOrigin=""/><link data-rh="true" rel="manifest" href="/manifest.json"/><link data-rh="true" rel="preconnect" href="https://www.google.com"/><link data-rh="true" rel="preconnect" href="https://www.gstatic.com" crossOrigin=""/><link data-rh="true" id="glyph_preload_link" rel="preload" as="style" type="text/css" href="https://glyph.medium.com/css/unbound.css"/><link data-rh="true" id="glyph_link" rel="stylesheet" type="text/css" href="https://glyph.medium.com/css/unbound.css"/><link data-rh="true" rel="author" href="https://akiboy96.medium.com"/><link data-rh="true" rel="canonical" href="https://akiboy96.medium.com/swlh/community-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9"/><link data-rh="true" rel="alternate" href="android-app://com.medium.reader/https/medium.com/p/ca4e0fbb8ca9"/><script data-rh="true" type="application/ld+json">{"@context":"https://schema.org","@id":"https://medium.com/swlh/community-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9","@type":"SocialMediaPosting","image":["https://miro.medium.com/1*lhR-zvcYQhsOswSL8unAJw.png"],"url":"https://medium.com/swlh/community-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9","dateCreated":"2020-10-01T22:39:02Z","datePublished":"2020-10-01T22:39:02Z","dateModified":"2020-10-13T23:43:06Z","headline":"Building your Long Term Portfolio using Unsupervised ML with Community Detection Analysis in Python…","name":"Building your Long Term Portfolio using Unsupervised ML with Community Detection Analysis in Python…","description":"Building your Long Term Portfolio using Unsupervised ML with Community Detection Analysis in Python \u0026 R\nWe finally get to the Portfolio Building where we can try to beat the SPY average returns Year …","identifier":"ca4e0fbb8ca9","author":{"@context":"https://schema.org","@id":"https://medium.com/@akiboy96","@type":"Person","identifier":"akiboy96","name":"Aakash Kedia","url":"https://medium.com/@akiboy96"},"creator":{"@context":"https://schema.org","@id":"https://medium.com/@akiboy96","@type":"Person","identifier":"akiboy96","name":"Aakash Kedia","url":"https://medium.com/@akiboy96"},"publisher":{"@context":"https://schema.org","@type":"Organization","@id":"https://medium.com/swlh","name":"The Startup","description":"Get smarter at building your thing. Follow to join The Startup’s +8 million monthly readers \u0026 +772K followers.","url":"https://medium.com/swlh","sameAs":["https://twitter.com/GrowthSupply"],"logo":{"@type":"ImageObject","width":1832,"height":1832,"url":"https://miro.medium.com/v2/resize:fit:1832/1%2ApKOfOAOvx-fWzfITATgGRg.jpeg"}},"mainEntityOfPage":"https://medium.com/swlh/community-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9","isAccessibleForFree":true}</script><script data-rh="true" src="https://www.google.com/recaptcha/enterprise.js?render=6Le-uGgpAAAAAPprRaokM8AKthQ9KNGdoxaGUvVp" async="true"></script><style type="text/css" data-fela-rehydration="544" data-fela-type="STATIC">html{box-sizing:border-box;-webkit-text-size-adjust:100%}*, *:before, *:after{box-sizing:inherit}body{margin:0;padding:0;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;color:rgba(0,0,0,0.8);position:relative;min-height:100vh}h1, h2, h3, h4, h5, h6, dl, dd, ol, ul, menu, figure, blockquote, p, pre, form{margin:0}menu, ol, ul{padding:0;list-style:none;list-style-image:none}main{display:block}a{color:inherit;text-decoration:none}a, button, input{-webkit-tap-highlight-color:transparent}img, svg{vertical-align:middle}button{background:transparent;overflow:visible}button, input, optgroup, select, textarea{margin:0}:root{--reach-tabs:1;--reach-menu-button:1}#speechify-root{font-family:Sohne, sans-serif}div[data-popper-reference-hidden="true"]{visibility:hidden;pointer-events:none}.grecaptcha-badge{visibility:hidden}
/*XCode style (c) Angel Garcia <<EMAIL>>*/.hljs {background: #fff;color: black;
}/* Gray DOCTYPE selectors like WebKit */
.xml .hljs-meta {color: #c0c0c0;
}.hljs-comment,
.hljs-quote {color: #007400;
}.hljs-tag,
.hljs-attribute,
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-name {color: #aa0d91;
}.hljs-variable,
.hljs-template-variable {color: #3F6E74;
}.hljs-code,
.hljs-string,
.hljs-meta .hljs-string {color: #c41a16;
}.hljs-regexp,
.hljs-link {color: #0E0EFF;
}.hljs-title,
.hljs-symbol,
.hljs-bullet,
.hljs-number {color: #1c00cf;
}.hljs-section,
.hljs-meta {color: #643820;
}.hljs-title.class_,
.hljs-class .hljs-title,
.hljs-type,
.hljs-built_in,
.hljs-params {color: #5c2699;
}.hljs-attr {color: #836C28;
}.hljs-subst {color: #000;
}.hljs-formula {background-color: #eee;font-style: italic;
}.hljs-addition {background-color: #baeeba;
}.hljs-deletion {background-color: #ffc8bd;
}.hljs-selector-id,
.hljs-selector-class {color: #9b703f;
}.hljs-doctag,
.hljs-strong {font-weight: bold;
}.hljs-emphasis {font-style: italic;
}
</style><style type="text/css" data-fela-rehydration="544" data-fela-type="KEYFRAME">@-webkit-keyframes k1{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}@-moz-keyframes k1{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}@keyframes k1{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}</style><style type="text/css" data-fela-rehydration="544" data-fela-type="RULE">.a{font-family:medium-content-sans-serif-font, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif}.b{font-weight:400}.c{background-color:rgba(255, 255, 255, 1)}.d{display:none}.m{display:block}.n{position:sticky}.o{top:0}.p{z-index:500}.q{padding:0 24px}.r{align-items:center}.s{border-bottom:solid 1px #F2F2F2}.z{height:41px}.ab{line-height:20px}.ac{display:flex}.ae{height:57px}.af{flex:1 0 auto}.ag{color:inherit}.ah{fill:inherit}.ai{font-size:inherit}.aj{border:none}.ak{font-family:inherit}.al{letter-spacing:inherit}.am{font-weight:inherit}.an{padding:0}.ao{margin:0}.ap{cursor:pointer}.aq:disabled{cursor:not-allowed}.ar:disabled{color:#6B6B6B}.as:disabled{fill:#6B6B6B}.av{width:auto}.aw path{fill:#242424}.ax{height:25px}.ay{margin-left:24px}.az{border-radius:20px}.ba{width:240px}.bb{background:#F9F9F9}.bc path{fill:#6B6B6B}.be{outline:none}.bf{font-family:sohne, "Helvetica Neue", Helvetica, Arial, sans-serif}.bg{font-size:14px}.bh{width:100%}.bi{padding:10px 20px 10px 0}.bj{background-color:transparent}.bk{color:#242424}.bl::placeholder{color:#6B6B6B}.bm{display:inline-block}.bn{margin-left:12px}.bo{margin-right:12px}.bp{border-radius:4px}.bq{height:24px}.bw{background-color:#F9F9F9}.bx{border-radius:50%}.by{height:32px}.bz{width:32px}.ca{flex:1 1 auto}.cb{justify-content:center}.ch{max-width:680px}.ci{min-width:0}.cj{animation:k1 1.2s ease-in-out infinite}.ck{height:100vh}.cl{margin-bottom:16px}.cm{margin-top:48px}.cn{align-items:flex-start}.co{flex-direction:column}.cp{justify-content:space-between}.cq{margin-bottom:24px}.cw{width:80%}.cx{background-color:#F2F2F2}.dd{height:44px}.de{width:44px}.df{margin:auto 0}.dg{margin-bottom:4px}.dh{height:16px}.di{width:120px}.dj{width:80px}.dp{margin-bottom:8px}.dq{width:96%}.dr{width:98%}.ds{width:81%}.dt{margin-left:8px}.du{color:#6B6B6B}.dv{font-size:13px}.dw{height:100%}.ep{color:#FFFFFF}.eq{fill:#FFFFFF}.er{background:#1A8917}.es{border-color:#1A8917}.ew:disabled{cursor:inherit !important}.ex:disabled{opacity:0.3}.ey:disabled:hover{background:#1A8917}.ez:disabled:hover{border-color:#1A8917}.fa{border-radius:99em}.fb{border-width:1px}.fc{border-style:solid}.fd{box-sizing:border-box}.fe{text-decoration:none}.ff{text-align:center}.fg{margin-left:16px}.fh{border:inherit}.fk{margin-right:32px}.fl{position:relative}.fm{fill:#6B6B6B}.fp{background:transparent}.fq svg{margin-left:4px}.fr svg{fill:#6B6B6B}.ft{box-shadow:inset 0 0 0 1px rgba(0, 0, 0, 0.05)}.fu{position:absolute}.gb{margin:0 24px}.gf{background:rgba(255, 255, 255, 1)}.gg{border:1px solid #F2F2F2}.gh{box-shadow:0 1px 4px #F2F2F2}.gi{max-height:100vh}.gj{overflow-y:auto}.gk{left:0}.gl{top:calc(100vh + 100px)}.gm{bottom:calc(100vh + 100px)}.gn{width:10px}.go{pointer-events:none}.gp{word-break:break-word}.gq{word-wrap:break-word}.gr:after{display:block}.gs:after{content:""}.gt:after{clear:both}.gu{line-height:1.23}.gv{letter-spacing:0}.gw{font-style:normal}.gx{font-weight:700}.ih{gap:12px}.ii{align-items:baseline}.ij{width:36px}.ik{height:36px}.il{border:2px solid rgba(255, 255, 255, 1)}.im{z-index:0}.in{box-shadow:none}.io{border:1px solid rgba(0, 0, 0, 0.05)}.ip{margin-bottom:2px}.iq{flex-wrap:nowrap}.is{width:12px}.it{flex-wrap:wrap}.iu{padding-left:8px}.iv{padding-right:8px}.jw> *{flex-shrink:0}.jx{overflow-x:scroll}.jy::-webkit-scrollbar{display:none}.jz{scrollbar-width:none}.ka{-ms-overflow-style:none}.kb{width:74px}.kc{flex-direction:row}.kd{z-index:2}.ke{margin-right:4px}.kh{-webkit-user-select:none}.ki{border:0}.kj{fill:rgba(117, 117, 117, 1)}.km{outline:0}.kn{user-select:none}.ko> svg{pointer-events:none}.kx{cursor:progress}.ky{opacity:1}.kz{padding:4px 0}.lc{margin-top:0px}.ld{width:16px}.lf{display:inline-flex}.ll{max-width:100%}.lm{padding:8px 2px}.ln svg{color:#6B6B6B}.me{line-height:1.58}.mf{letter-spacing:-0.004em}.mg{font-family:source-serif-pro, Georgia, Cambria, "Times New Roman", Times, serif}.nb{margin-bottom:-0.46em}.nc{text-decoration:underline}.nd{margin-left:auto}.ne{margin-right:auto}.nk{clear:both}.nl{height:auto}.nm{padding-left:30px}.nn{line-height:40px}.no{letter-spacing:-0.009em}.np{font-weight:300}.nq{font-size:28px}.ob{max-width:916px}.od{cursor:zoom-in}.oe{z-index:auto}.og{width:1px}.oh{height:1px}.oi{margin:-1px}.oj{overflow:hidden}.ok{clip:rect(0, 0, 0, 0)}.ol{white-space:nowrap}.om{border-width:0}.on{box-shadow:inset 3px 0 0 0 #242424}.oo{padding-left:23px}.op{margin-left:-20px}.oq{font-style:italic}.or{overflow-x:auto}.os{font-family:source-code-pro, Menlo, Monaco, "Courier New", Courier, monospace}.ot{padding:20px}.ou{border-radius:0}.ov{background:#F2F2F2}.ow{line-height:1.18}.ox{letter-spacing:-0.022em}.oy{font-size:16px}.oz{margin-top:-0.09em}.pa{margin-bottom:-0.09em}.pb{white-space:pre-wrap}.pc{min-width:fit-content}.pd{margin-top:0.91em}.pe{max-width:955px}.pf{font-weight:600}.pv{margin-bottom:-0.31em}.pw{max-width:1032px}.qc{max-width:700px}.qd{margin-top:10px}.qe{max-width:728px}.qm{margin-bottom:26px}.qn{margin-top:6px}.qo{margin-top:8px}.qp{margin-right:8px}.qq{padding:8px 16px}.qr{border-radius:100px}.qs{transition:background 300ms ease}.qu{border-top:none}.qv{margin-bottom:50px}.qw{height:52px}.qx{max-height:52px}.qy{box-sizing:content-box}.qz{position:static}.ra{z-index:1}.rc{max-width:155px}.ri{margin-right:20px}.rj{flex:0 0 auto}.rk{margin-bottom:64px}.rx{height:48px}.ry{width:48px}.sa{height:64px}.sb{width:64px}.sc{align-self:flex-end}.si{padding-right:4px}.sj{font-weight:500}.sq{margin:0 8px}.sr{margin-top:16px}.ss{margin-bottom:54px}.st{height:0px}.th{gap:18px}.ti{fill:rgba(61, 61, 61, 1)}.tu{border-bottom:solid 1px #E5E5E5}.tv{margin-top:72px}.tw{padding:24px 0}.tx{margin-bottom:0px}.ty{margin-right:16px}.at:hover:not(:disabled){color:rgba(25, 25, 25, 1)}.au:hover:not(:disabled){fill:rgba(25, 25, 25, 1)}.et:hover{background:#156D12}.eu:hover{border-color:#156D12}.ev:hover{cursor:pointer}.fn:hover{color:#242424}.fo:hover{fill:#242424}.fs:hover svg{fill:#242424}.fv:hover{background-color:rgba(0, 0, 0, 0.1)}.ir:hover{text-decoration:underline}.kl:hover{fill:rgba(8, 8, 8, 1)}.la:hover{fill:#000000}.lb:hover p{color:#000000}.le:hover{color:#000000}.lo:hover svg{color:#000000}.qt:hover{background-color:#F2F2F2}.rz:hover{background-color:none}.tj:hover{fill:rgba(25, 25, 25, 1)}.bd:focus-within path{fill:#242424}.kk:focus{fill:rgba(8, 8, 8, 1)}.lp:focus svg{color:#000000}.of:focus{transform:scale(1.01)}.kp:active{border-style:none}</style><style type="text/css" data-fela-rehydration="544" data-fela-type="RULE" media="all and (min-width: 1080px)">.e{display:none}.bv{width:64px}.cg{margin:0 64px}.cv{height:48px}.dc{margin-bottom:52px}.do{margin-bottom:48px}.ef{font-size:14px}.eg{line-height:20px}.em{font-size:13px}.eo{padding:5px 12px}.fj{display:flex}.ga{margin-bottom:50px}.ge{max-width:680px}.hs{font-size:42px}.ht{margin-top:1.19em}.hu{margin-bottom:32px}.hv{line-height:52px}.hw{letter-spacing:-0.011em}.if{align-items:center}.ig{flex-direction:row}.ji{border-top:solid 1px #F2F2F2}.jj{border-bottom:solid 1px #F2F2F2}.jk{margin:32px 0 0}.jl{padding:3px 8px}.ju> *{margin-right:24px}.jv> :last-child{margin-right:0}.kw{margin-top:0px}.lk{margin:0}.mx{font-size:20px}.my{margin-top:2.14em}.mz{line-height:32px}.na{letter-spacing:-0.003em}.nj{margin-top:56px}.nv{margin-top:2.75em}.oa{margin-top:2.64em}.ps{margin-top:1.72em}.pt{line-height:24px}.pu{letter-spacing:0}.qb{margin-top:66px}.ql{margin-top:1.75em}.rh{display:inline-block}.rn{margin-bottom:0}.ro{margin-right:20px}.sd{max-width:500px}.te{font-size:24px}.tf{line-height:30px}.tg{letter-spacing:-0.016em}.to{margin:40px 0 0}.tt{padding-top:72px}</style><style type="text/css" data-fela-rehydration="544" data-fela-type="RULE" media="all and (max-width: 1079.98px)">.f{display:none}.kv{margin-top:0px}.qf{margin-left:auto}.qg{text-align:center}.rg{display:inline-block}</style><style type="text/css" data-fela-rehydration="544" data-fela-type="RULE" media="all and (max-width: 903.98px)">.g{display:none}.ku{margin-top:0px}.rf{display:inline-block}</style><style type="text/css" data-fela-rehydration="544" data-fela-type="RULE" media="all and (max-width: 727.98px)">.h{display:none}.ks{margin-top:0px}.kt{margin-right:0px}.re{display:inline-block}</style><style type="text/css" data-fela-rehydration="544" data-fela-type="RULE" media="all and (max-width: 551.98px)">.i{display:none}.t{display:flex}.u{justify-content:space-between}.br{width:24px}.cc{margin:0 24px}.cr{height:40px}.cy{margin-bottom:44px}.dk{margin-bottom:32px}.dx{font-size:13px}.dy{line-height:20px}.eh{padding:0px 8px 1px}.fw{margin-bottom:2px}.gy{font-size:32px}.gz{margin-top:1.01em}.ha{margin-bottom:24px}.hb{line-height:38px}.hc{letter-spacing:-0.014em}.hx{align-items:flex-start}.hy{flex-direction:column-reverse}.iw{margin:24px -24px 0}.ix{padding:0}.jm> *{margin-right:8px}.jn> :last-child{margin-right:24px}.kf{margin-left:0px}.kq{margin-top:0px}.kr{margin-right:0px}.lg{margin:0}.lq{border:1px solid #F2F2F2}.lr{border-radius:99em}.ls{padding:0px 16px 0px 12px}.lt{height:38px}.lu{align-items:center}.lw svg{margin-right:8px}.mh{font-size:18px}.mi{margin-top:1.56em}.mj{line-height:28px}.mk{letter-spacing:-0.003em}.nf{margin-top:40px}.nr{margin-top:1.42em}.nw{margin-top:2em}.pg{font-size:16px}.ph{margin-top:1.23em}.pi{letter-spacing:0}.px{margin-top:48px}.qh{margin-top:1.08em}.rd{display:inline-block}.rm{flex-direction:column}.rv{margin-bottom:20px}.rw{margin-right:0}.sh{max-width:100%}.sk{font-size:24px}.sl{line-height:30px}.sm{letter-spacing:-0.016em}.su{font-size:20px}.sv{line-height:24px}.tk{margin:32px 0 0}.tp{padding-top:48px}.lv:hover{border-color:#E5E5E5}</style><style type="text/css" data-fela-rehydration="544" data-fela-type="RULE" media="all and (min-width: 904px) and (max-width: 1079.98px)">.j{display:none}.bu{width:64px}.cf{margin:0 64px}.cu{height:48px}.db{margin-bottom:52px}.dn{margin-bottom:48px}.ed{font-size:14px}.ee{line-height:20px}.ek{font-size:13px}.el{padding:5px 12px}.fi{display:flex}.fz{margin-bottom:50px}.gd{max-width:680px}.hn{font-size:42px}.ho{margin-top:1.19em}.hp{margin-bottom:32px}.hq{line-height:52px}.hr{letter-spacing:-0.011em}.id{align-items:center}.ie{flex-direction:row}.je{border-top:solid 1px #F2F2F2}.jf{border-bottom:solid 1px #F2F2F2}.jg{margin:32px 0 0}.jh{padding:3px 8px}.js> *{margin-right:24px}.jt> :last-child{margin-right:0}.lj{margin:0}.mt{font-size:20px}.mu{margin-top:2.14em}.mv{line-height:32px}.mw{letter-spacing:-0.003em}.ni{margin-top:56px}.nu{margin-top:2.75em}.nz{margin-top:2.64em}.pp{margin-top:1.72em}.pq{line-height:24px}.pr{letter-spacing:0}.qa{margin-top:66px}.qk{margin-top:1.75em}.rp{margin-bottom:0}.rq{margin-right:20px}.se{max-width:500px}.tb{font-size:24px}.tc{line-height:30px}.td{letter-spacing:-0.016em}.tn{margin:40px 0 0}.ts{padding-top:72px}</style><style type="text/css" data-fela-rehydration="544" data-fela-type="RULE" media="all and (min-width: 728px) and (max-width: 903.98px)">.k{display:none}.x{display:flex}.y{justify-content:space-between}.bt{width:64px}.ce{margin:0 48px}.ct{height:48px}.da{margin-bottom:52px}.dm{margin-bottom:48px}.eb{font-size:13px}.ec{line-height:20px}.ej{padding:0px 8px 1px}.fy{margin-bottom:50px}.gc{max-width:680px}.hi{font-size:42px}.hj{margin-top:1.19em}.hk{margin-bottom:32px}.hl{line-height:52px}.hm{letter-spacing:-0.011em}.ib{align-items:center}.ic{flex-direction:row}.ja{border-top:solid 1px #F2F2F2}.jb{border-bottom:solid 1px #F2F2F2}.jc{margin:32px 0 0}.jd{padding:3px 8px}.jq> *{margin-right:24px}.jr> :last-child{margin-right:0}.li{margin:0}.mp{font-size:20px}.mq{margin-top:2.14em}.mr{line-height:32px}.ms{letter-spacing:-0.003em}.nh{margin-top:56px}.nt{margin-top:2.75em}.ny{margin-top:2.64em}.pm{margin-top:1.72em}.pn{line-height:24px}.po{letter-spacing:0}.pz{margin-top:66px}.qj{margin-top:1.75em}.rr{margin-bottom:0}.rs{margin-right:20px}.sf{max-width:500px}.sy{font-size:24px}.sz{line-height:30px}.ta{letter-spacing:-0.016em}.tm{margin:40px 0 0}.tr{padding-top:72px}</style><style type="text/css" data-fela-rehydration="544" data-fela-type="RULE" media="all and (min-width: 552px) and (max-width: 727.98px)">.l{display:none}.v{display:flex}.w{justify-content:space-between}.bs{width:24px}.cd{margin:0 24px}.cs{height:40px}.cz{margin-bottom:44px}.dl{margin-bottom:32px}.dz{font-size:13px}.ea{line-height:20px}.ei{padding:0px 8px 1px}.fx{margin-bottom:2px}.hd{font-size:32px}.he{margin-top:1.01em}.hf{margin-bottom:24px}.hg{line-height:38px}.hh{letter-spacing:-0.014em}.hz{align-items:flex-start}.ia{flex-direction:column-reverse}.iy{margin:24px 0 0}.iz{padding:0}.jo> *{margin-right:8px}.jp> :last-child{margin-right:8px}.kg{margin-left:0px}.lh{margin:0}.lx{border:1px solid #F2F2F2}.ly{border-radius:99em}.lz{padding:0px 16px 0px 12px}.ma{height:38px}.mb{align-items:center}.md svg{margin-right:8px}.ml{font-size:18px}.mm{margin-top:1.56em}.mn{line-height:28px}.mo{letter-spacing:-0.003em}.ng{margin-top:40px}.ns{margin-top:1.42em}.nx{margin-top:2em}.pj{font-size:16px}.pk{margin-top:1.23em}.pl{letter-spacing:0}.py{margin-top:48px}.qi{margin-top:1.08em}.rl{flex-direction:column}.rt{margin-bottom:20px}.ru{margin-right:0}.sg{max-width:100%}.sn{font-size:24px}.so{line-height:30px}.sp{letter-spacing:-0.016em}.sw{font-size:20px}.sx{line-height:24px}.tl{margin:32px 0 0}.tq{padding-top:48px}.mc:hover{border-color:#E5E5E5}</style><style type="text/css" data-fela-rehydration="544" data-fela-type="RULE" media="print">.rb{display:none}</style><style type="text/css" data-fela-rehydration="544" data-fela-type="RULE" media="(prefers-reduced-motion: no-preference)">.oc{transition:transform 300ms cubic-bezier(0.2, 0, 0.2, 1)}</style></head><body><div id="root"><div class="a b c"><a href="/sitemap/sitemap.xml" class="d">Sitemap</a><div class="e f g h i j k l"></div><script>document.domain = document.domain;</script><div class="m c"><div class="m n o p c"><div class="q r s t u v w x y j e z ab"><a class="du ah dv bf al b an ao ap aq ar as at au t v x j e r dw ab" href="https://rsci.app.link/?%24canonical_url=https%3A%2F%2Fmedium.com%2Fp%2Fca4e0fbb8ca9&amp;%7Efeature=LoOpenInAppButton&amp;%7Echannel=ShowPostUnderUser&amp;%7Estage=mobileNavBar&amp;source=post_page---top_nav_layout_nav-----------------------------------------" rel="noopener follow">Open in app<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="none" viewBox="0 0 10 10" class="dt"><path fill="currentColor" d="M.985 8.485a.375.375 0 1 0 .53.53zM8.75 1.25h.375A.375.375 0 0 0 8.75.875zM8.375 6.5a.375.375 0 1 0 .75 0zM3.5.875a.375.375 0 1 0 0 .75zm-1.985 8.14 7.5-7.5-.53-.53-7.5 7.5zm6.86-7.765V6.5h.75V1.25zM3.5 1.625h5.25v-.75H3.5z"></path></svg></a><div class="ac r"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><button class="bf b dx dy eh dz ea ei eb ec ej ek ee el em eg eo ep eq er es et eu ev ew ex ey ez fa fb fc fd bm fe ff" data-testid="headerSignUpButton">Sign up</button></span></p><div class="fg m"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerSignInButton" href="https://medium.com/m/signin?operation=login&amp;redirect=https%3A%2F%2Fakiboy96.medium.com%2Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9&amp;source=post_page---top_nav_layout_nav-----------------------global_nav------------------" rel="noopener follow">Sign in</a></span></p></div></div></div><div class="q r s ac ae"><div class="ac r af"><a class="ag ah ai aj ak al am an ao ap aq ar as at au ac" aria-label="Homepage" data-testid="headerMediumLogo" href="https://medium.com/?source=post_page---top_nav_layout_nav-----------------------------------------" rel="noopener follow"><svg xmlns="http://www.w3.org/2000/svg" width="719" height="160" fill="none" aria-labelledby="wordmark-medium-desc" viewBox="0 0 719 160" class="av aw ax"><desc id="wordmark-medium-desc">Medium Logo</desc><path fill="#242424" d="m174.104 9.734.215-.047V8.02H130.39L89.6 103.89 48.81 8.021H1.472v1.666l.212.047c8.018 1.81 12.09 4.509 12.09 14.242V137.93c0 9.734-4.087 12.433-12.106 14.243l-.212.047v1.671h32.118v-1.665l-.213-.048c-8.018-1.809-12.089-4.509-12.089-14.242V30.586l52.399 123.305h2.972l53.925-126.743V140.75c-.687 7.688-4.721 10.062-11.982 11.701l-.215.05v1.652h55.948v-1.652l-.215-.05c-7.269-1.639-11.4-4.013-12.087-11.701l-.037-116.774h.037c0-9.733 4.071-12.432 12.087-14.242m25.555 75.488c.915-20.474 8.268-35.252 20.606-35.507 3.806.063 6.998 1.312 9.479 3.714 5.272 5.118 7.751 15.812 7.368 31.793zm-.553 5.77h65.573v-.275c-.186-15.656-4.721-27.834-13.466-36.196-7.559-7.227-18.751-11.203-30.507-11.203h-.263c-6.101 0-13.584 1.48-18.909 4.16-6.061 2.807-11.407 7.003-15.855 12.511-7.161 8.874-11.499 20.866-12.554 34.343q-.05.606-.092 1.212a50 50 0 0 0-.065 1.151 85.807 85.807 0 0 0-.094 5.689c.71 30.524 17.198 54.917 46.483 54.917 25.705 0 40.675-18.791 44.407-44.013l-1.886-.664c-6.557 13.556-18.334 21.771-31.738 20.769-18.297-1.369-32.314-19.922-31.042-42.395m139.722 41.359c-2.151 5.101-6.639 7.908-12.653 7.908s-11.513-4.129-15.418-11.63c-4.197-8.053-6.405-19.436-6.405-32.92 0-28.067 8.729-46.22 22.24-46.22 5.657 0 10.111 2.807 12.236 7.704zm43.499 20.008c-8.019-1.897-12.089-4.722-12.089-14.951V1.309l-48.716 14.353v1.757l.299-.024c6.72-.543 11.278.386 13.925 2.83 2.072 1.915 3.082 4.853 3.082 8.987v18.66c-4.803-3.067-10.516-4.56-17.448-4.56-14.059 0-26.909 5.92-36.176 16.672-9.66 11.205-14.767 26.518-14.767 44.278-.003 31.72 15.612 53.039 38.851 53.039 13.595 0 24.533-7.449 29.54-20.013v16.865h43.711v-1.746zM424.1 19.819c0-9.904-7.468-17.374-17.375-17.374-9.859 0-17.573 7.632-17.573 17.374s7.721 17.374 17.573 17.374c9.907 0 17.375-7.47 17.375-17.374m11.499 132.546c-8.019-1.897-12.089-4.722-12.089-14.951h-.035V43.635l-43.714 12.551v1.705l.263.024c9.458.842 12.047 4.1 12.047 15.152v81.086h43.751v-1.746zm112.013 0c-8.018-1.897-12.089-4.722-12.089-14.951V43.635l-41.621 12.137v1.71l.246.026c7.733.813 9.967 4.257 9.967 15.36v59.279c-2.578 5.102-7.415 8.131-13.274 8.336-9.503 0-14.736-6.419-14.736-18.073V43.638l-43.714 12.55v1.703l.262.024c9.459.84 12.05 4.097 12.05 15.152v50.17a56.3 56.3 0 0 0 .91 10.444l.787 3.423c3.701 13.262 13.398 20.197 28.59 20.197 12.868 0 24.147-7.966 29.115-20.43v17.311h43.714v-1.747zm169.818 1.788v-1.749l-.213-.05c-8.7-2.006-12.089-5.789-12.089-13.49v-63.79c0-19.89-11.171-31.761-29.883-31.761-13.64 0-25.141 7.882-29.569 20.16-3.517-13.01-13.639-20.16-28.606-20.16-13.146 0-23.449 6.938-27.869 18.657V43.643L545.487 55.68v1.715l.263.024c9.345.829 12.047 4.181 12.047 14.95v81.784h40.787v-1.746l-.215-.053c-6.941-1.631-9.181-4.606-9.181-12.239V66.998c1.836-4.289 5.537-9.37 12.853-9.37 9.086 0 13.692 6.296 13.692 18.697v77.828h40.797v-1.746l-.215-.053c-6.94-1.631-9.18-4.606-9.18-12.239V75.066a42 42 0 0 0-.578-7.26c1.947-4.661 5.86-10.177 13.475-10.177 9.214 0 13.691 6.114 13.691 18.696v77.828z"></path></svg></a><div class="ay i"><div class="ac aj az ba bb r bc bd"><div class="bm" aria-hidden="false" aria-describedby="searchResults" aria-labelledby="searchResults" aria-haspopup="listbox" role="listbox"></div><div class="bn bo ac"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M4.092 11.06a6.95 6.95 0 1 1 13.9 0 6.95 6.95 0 0 1-13.9 0m6.95-8.05a8.05 8.05 0 1 0 5.13 14.26l3.75 3.75a.56.56 0 1 0 .79-.79l-3.73-3.73A8.05 8.05 0 0 0 11.042 3z" clip-rule="evenodd"></path></svg></div><input role="combobox" aria-controls="searchResults" aria-expanded="false" aria-label="search" data-testid="headerSearchInput" tabindex="0" class="aj be bf bg ab bh bi bj bk bl" placeholder="Search" value=""/></div></div></div><div class="i l x fi fj"><div class="fk ac"><span data-dd-action-name="Susi presentation tracker new_post_topnav"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerWriteButton" href="https://medium.com/m/signin?operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fnew-story&amp;source=---top_nav_layout_nav-----------------------new_post_topnav------------------" rel="noopener follow"><div class="bf b bg ab du fl fm ac r fn fo"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" aria-label="Write"><path fill="currentColor" d="M14 4a.5.5 0 0 0 0-1zm7 6a.5.5 0 0 0-1 0zm-7-7H4v1h10zM3 4v16h1V4zm1 17h16v-1H4zm17-1V10h-1v10zm-1 1a1 1 0 0 0 1-1h-1zM3 20a1 1 0 0 0 1 1v-1zM4 3a1 1 0 0 0-1 1h1z"></path><path stroke="currentColor" d="m17.5 4.5-8.458 8.458a.25.25 0 0 0-.06.098l-.824 2.47a.25.25 0 0 0 .316.316l2.47-.823a.25.25 0 0 0 .098-.06L19.5 6.5m-2-2 2.323-2.323a.25.25 0 0 1 .354 0l1.646 1.646a.25.25 0 0 1 0 .354L19.5 6.5m-2-2 2 2"></path></svg><div class="dt m">Write</div></div></a></span></div></div><div class="l k j e"><div class="fk ac"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerSearchButton" href="https://medium.com/search?source=post_page---top_nav_layout_nav-----------------------------------------" rel="noopener follow"><div class="bf b bg ab du fl fm ac r fn fo"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" aria-label="Search"><path fill="currentColor" fill-rule="evenodd" d="M4.092 11.06a6.95 6.95 0 1 1 13.9 0 6.95 6.95 0 0 1-13.9 0m6.95-8.05a8.05 8.05 0 1 0 5.13 14.26l3.75 3.75a.56.56 0 1 0 .79-.79l-3.73-3.73A8.05 8.05 0 0 0 11.042 3z" clip-rule="evenodd"></path></svg></div></a></div></div><div class="fk i l k"><div class="ac r"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><button class="bf b dx dy eh dz ea ei eb ec ej ek ee el em eg eo ep eq er es et eu ev ew ex ey ez fa fb fc fd bm fe ff" data-testid="headerSignUpButton">Sign up</button></span></p><div class="fg m"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerSignInButton" href="https://medium.com/m/signin?operation=login&amp;redirect=https%3A%2F%2Fakiboy96.medium.com%2Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9&amp;source=post_page---top_nav_layout_nav-----------------------global_nav------------------" rel="noopener follow">Sign in</a></span></p></div></div></div><div class="m" aria-hidden="false"><button class="aj fp an ac r ap fl fq fr fs" aria-label="user options menu" data-testid="headerUserIcon"><div class="m fl"><img alt="" class="m fd bx by bz cx" src="https://miro.medium.com/v2/resize:fill:64:64/1*dmbNkD5D-u45r44go_cf0g.png" width="32" height="32" loading="lazy" role="presentation"/><div class="ft bx m by bz fu o aj fv"></div></div></button></div></div></div><div class="ac"><div class="ca bh"><div class="m"><div class="fw fx fy fz ga m"><div class="ac cb"><div class="ci bh gb gc gd ge"></div></div><article><div class="m"><div class="m"><span class="m"></span><section><div><div class="fu gk gl gm gn go"></div><div class="gp gq gr gs gt"><div class="ac cb"><div class="ci bh gb gc gd ge"><div><h1 id="b51b" class="pw-post-title gu gv gw bf gx gy gz ha hb hc hd he hf hg hh hi hj hk hl hm hn ho hp hq hr hs ht hu hv hw bk" data-testid="storyTitle">Building your Long Term Portfolio using Unsupervised ML with Community Detection Analysis in Python &amp; R</h1><div><div class="speechify-ignore ac cp"><div class="speechify-ignore bh m"><div class="ac hx hy hz ia ib ic id ie if ig ih"><div class="ac r ih"><div class="ac ii"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><a rel="noopener follow" href="/?source=post_page---byline--ca4e0fbb8ca9---------------------------------------" data-discover="true"><div class="m ij ik bx il im"><div class="m fl"><img alt="Aakash Kedia" class="m fd bx by bz cx" src="https://miro.medium.com/v2/da:true/resize:fill:64:64/0*emaZ2wHvCzhl_Apj" width="32" height="32" loading="lazy" data-testid="authorPhoto"/><div class="in bx m by bz fu o io fv"></div></div></div></a></div></div></div></div><span class="bf b bg ab bk"><div class="ip ac r"><div class="ac r iq"><div class="ac r"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><span class="bf b bg ab bk"><a class="ag ah ai fh ak al am an ao ap aq ar as ir" data-testid="authorName" rel="noopener follow" href="/?source=post_page---byline--ca4e0fbb8ca9---------------------------------------" data-discover="true">Aakash Kedia</a></span></div></div></div></div><div class="is bm"></div></div></div></span></div><div class="ac r it"><span class="bf b bg ab du"><div class="ac af"><span data-testid="storyReadTime">8 min read</span><div class="iu iv m" aria-hidden="true"><span class="m" aria-hidden="true"><span class="bf b bg ab du">·</span></span></div><span data-testid="storyPublishDate">Oct 1, 2020</span></div></span></div></div><div class="ac cp iw ix iy iz ja jb jc jd je jf jg jh ji jj jk jl"><div class="i l x fi fj r"><div class="kb m"><div class="ac r kc kd"><div class="pw-multi-vote-icon fl ke kf kg kh"><span data-dd-action-name="Susi presentation tracker clap_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerClapButton" href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fp%2Fca4e0fbb8ca9&amp;operation=register&amp;redirect=https%3A%2F%2Fakiboy96.medium.com%2Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9&amp;user=Aakash+Kedia&amp;userId=e0dbacebbef4&amp;source=---header_actions--ca4e0fbb8ca9---------------------clap_footer------------------" rel="noopener follow"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><div class="ki ap kj kk kl km an kn ko kp kh" role="presentation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="clap"><path fill-rule="evenodd" d="M11.37.828 12 3.282l.63-2.454zM13.916 3.953l1.523-2.112-1.184-.39zM8.589 1.84l1.522 2.112-.337-2.501zM18.523 18.92c-.86.86-1.75 1.246-2.62 1.33a6 6 0 0 0 .407-.372c2.388-2.389 2.86-4.951 1.399-7.623l-.912-1.603-.79-1.672c-.26-.56-.194-.98.203-1.288a.7.7 0 0 1 .546-.132c.283.046.546.231.728.5l2.363 4.157c.976 1.624 1.141 4.237-1.324 6.702m-10.999-.438L3.37 14.328a.828.828 0 0 1 .585-1.408.83.83 0 0 1 .585.242l2.158 2.157a.365.365 0 0 0 .516-.516l-2.157-2.158-1.449-1.449a.826.826 0 0 1 1.167-1.17l3.438 3.44a.363.363 0 0 0 .516 0 .364.364 0 0 0 0-.516L5.293 9.513l-.97-.97a.826.826 0 0 1 0-*********** 0 0 1 1.167 0l.97.968 3.437 3.436a.36.36 0 0 0 .517 0 .366.366 0 0 0 0-.516L6.977 7.83a.82.82 0 0 1-.241-.584.82.82 0 0 1 .824-.826c.219 0 .43.087.584.242l5.787 5.787a.366.366 0 0 0 .587-.415l-1.117-2.363c-.26-.56-.194-.98.204-1.289a.7.7 0 0 1 .546-.132c.283.046.545.232.727.501l2.193 3.86c1.302 2.38.883 4.59-1.277 6.75-1.156 1.156-2.602 1.627-4.19 1.367-1.418-.236-2.866-1.033-4.079-2.246M10.75 5.971l2.12 2.12c-.41.502-.465 1.17-.128 1.89l.22.465-3.523-3.523a.8.8 0 0 1-.097-.368c0-.22.086-.428.241-.584a.847.847 0 0 1 1.167 0m7.355 1.705c-.31-.461-.746-.758-1.23-.837a1.44 1.44 0 0 0-1.11.275c-.312.24-.505.543-.59.881a1.74 1.74 0 0 0-.906-.465 1.47 1.47 0 0 0-.82.106l-2.182-2.182a1.56 1.56 0 0 0-2.2 0 1.54 1.54 0 0 0-.396.701 1.56 1.56 0 0 0-2.21-.01 1.55 1.55 0 0 0-.416.753c-.624-.624-1.649-.624-2.237-.037a1.557 1.557 0 0 0 0 2.2c-.239.1-.501.238-.715.453a1.56 1.56 0 0 0 0 2.2l.516.515a1.556 1.556 0 0 0-.753 2.615L7.01 19c1.32 1.319 2.909 2.189 4.475 2.449q.482.08.971.08c.85 0 1.653-.198 2.393-.579.**************.686.054 1.266 0 2.457-.52 3.505-1.567 2.763-2.763 2.552-5.734 1.439-7.586z" clip-rule="evenodd"></path></svg></div></div></div></div></a></span></div><div class="pw-multi-vote-count m kq kr ks kt ku kv kw"><p class="bf b dv ab du"><span class="kx">--</span></p></div></div></div><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button class="ap ki ky kz ac r fm la lb" aria-label="responses"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="lc"><path d="M18.006 16.803c1.533-1.456 2.234-3.325 2.234-5.321C20.24 7.357 16.709 4 12.191 4S4 7.357 4 11.482c0 4.126 3.674 7.482 8.191 7.482.817 0 1.622-.111 2.393-.327.231.2.48.391.744.559 1.06.693 2.203 1.044 3.399 1.044.224-.008.4-.112.486-.287a.49.49 0 0 0-.042-.518c-.495-.67-.845-1.364-1.04-2.057a4 4 0 0 1-.125-.598zm-3.122 1.055-.067-.223-.315.096a8 8 0 0 1-2.311.338c-4.023 0-7.292-2.955-7.292-6.587 0-3.633 3.269-6.588 7.292-6.588 4.014 0 7.112 2.958 7.112 6.593 0 1.794-.608 3.469-2.027 4.72l-.195.168v.255c0 .056 0 .151.016.295.025.231.081.478.154.733.154.558.398 1.117.722 1.659a5.3 5.3 0 0 1-2.165-.845c-.276-.176-.714-.383-.941-.59z"></path></svg></button></div></div></div></div><div class="ac r jm jn jo jp jq jr js jt ju jv jw jx jy jz ka"><div class="ld l k j e"></div><div class="i l"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><span data-dd-action-name="Susi presentation tracker bookmark_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerBookmarkButton" href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2Fca4e0fbb8ca9&amp;operation=register&amp;redirect=https%3A%2F%2Fakiboy96.medium.com%2Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9&amp;source=---header_actions--ca4e0fbb8ca9---------------------bookmark_footer------------------" rel="noopener follow"><svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="none" viewBox="0 0 25 25" class="du le" aria-label="Add to list bookmark button"><path fill="currentColor" d="M18 2.5a.5.5 0 0 1 1 0V5h2.5a.5.5 0 0 1 0 1H19v2.5a.5.5 0 1 1-1 0V6h-2.5a.5.5 0 0 1 0-1H18zM7 7a1 1 0 0 1 1-1h3.5a.5.5 0 0 0 0-1H8a2 2 0 0 0-2 2v14a.5.5 0 0 0 .805.396L12.5 17l5.695 4.396A.5.5 0 0 0 19 21v-8.5a.5.5 0 0 0-1 0v7.485l-5.195-4.012a.5.5 0 0 0-.61 0L7 19.985z"></path></svg></a></span></div></div></div></div><div class="fd lf cn"><div class="m af"><div class="ac cb"><div class="lg lh li lj lk ll ci bh"><div class="ac"><div class="bm" aria-hidden="false" role="tooltip"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button aria-label="Listen" data-testid="audioPlayButton" class="ag fm ai fh ak al am lm ao ap aq ex ln lo lb lp lq lr ls lt t lu lv lw lx ly lz ma v mb mc md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a9 9 0 1 1 18 0 9 9 0 0 1-18 0m9-10C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m3.376 10.416-4.599 3.066a.5.5 0 0 1-.777-.416V8.934a.5.5 0 0 1 .777-.416l4.599 3.066a.5.5 0 0 1 0 .832" clip-rule="evenodd"></path></svg><div class="k j e"><p class="bf b bg ab du">Listen</p></div></button></div></div></div></div></div></div></div></div></div><div class="bm" aria-hidden="false" aria-describedby="postFooterSocialMenu" aria-labelledby="postFooterSocialMenu"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button aria-controls="postFooterSocialMenu" aria-expanded="false" aria-label="Share Post" data-testid="headerSocialShareButton" class="ag fm ai fh ak al am lm ao ap aq ex ln lo lb lp lq lr ls lt t lu lv lw lx ly lz ma v mb mc md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M15.218 4.931a.4.4 0 0 1-.118.132l.012.006a.45.45 0 0 1-.292.074.5.5 0 0 1-.3-.13l-2.02-2.02v7.07c0 .28-.23.5-.5.5s-.5-.22-.5-.5v-7.04l-2 2a.45.45 0 0 1-.57.04h-.02a.4.4 0 0 1-.16-.3.4.4 0 0 1 .1-.32l2.8-2.8a.5.5 0 0 1 .7 0l2.8 2.79a.42.42 0 0 1 .068.498m-.106.138.008.004v-.01zM16 7.063h1.5a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-11c-1.1 0-2-.9-2-2v-10a2 2 0 0 1 2-2H8a.5.5 0 0 1 .********* 0 0 1 .********* 0 0 1-.********* 0 0 1-.35.15H6.4c-.5 0-.9.4-.9.9v10.2a.9.9 0 0 0 .9.9h11.2c.5 0 .9-.4.9-.9v-10.2c0-.5-.4-.9-.9-.9H16a.5.5 0 0 1 0-1" clip-rule="evenodd"></path></svg><div class="k j e"><p class="bf b bg ab du">Share</p></div></button></div></div></div></div></div></div></div></div></div></div><p id="e6c5" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">We finally get to the <strong class="mg gx">Portfolio Building</strong> where we can try to beat the SPY average returns Year over Yer (YoY). <a class="ag nc" href="https://www.kaggle.com/akiboy96/stock-network-analysis" rel="noopener ugc nofollow" target="_blank">The whole code base can be found here</a>:</p><figure class="nf ng nh ni nj nk nd ne paragraph-image"><div class="nd ne ch"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*cStacbcB7ylqz6gutZvQkQ.png 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*cStacbcB7ylqz6gutZvQkQ.png 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*cStacbcB7ylqz6gutZvQkQ.png 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*cStacbcB7ylqz6gutZvQkQ.png 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*cStacbcB7ylqz6gutZvQkQ.png 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*cStacbcB7ylqz6gutZvQkQ.png 1100w, https://miro.medium.com/v2/resize:fit:1360/format:webp/1*cStacbcB7ylqz6gutZvQkQ.png 1360w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 680px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*cStacbcB7ylqz6gutZvQkQ.png 640w, https://miro.medium.com/v2/resize:fit:720/1*cStacbcB7ylqz6gutZvQkQ.png 720w, https://miro.medium.com/v2/resize:fit:750/1*cStacbcB7ylqz6gutZvQkQ.png 750w, https://miro.medium.com/v2/resize:fit:786/1*cStacbcB7ylqz6gutZvQkQ.png 786w, https://miro.medium.com/v2/resize:fit:828/1*cStacbcB7ylqz6gutZvQkQ.png 828w, https://miro.medium.com/v2/resize:fit:1100/1*cStacbcB7ylqz6gutZvQkQ.png 1100w, https://miro.medium.com/v2/resize:fit:1360/1*cStacbcB7ylqz6gutZvQkQ.png 1360w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 680px"/><img alt="" class="bh ll nl c" width="680" height="510" loading="eager" role="presentation"/></picture></div></figure><blockquote class="nm"><p id="d8f0" class="nn no gw bf np nq nr ns nt nu nv nb du">To build the portfolio based on the correlation of stock prices in the S&amp;P 500, we will use network centrality as a measure to understand how the stocks are better integrated with one and other.</p></blockquote><p id="f5b3" class="pw-post-body-paragraph me mf gw mg b mh nw mj mk ml nx mn mo mp ny mr ms mt nz mv mw mx oa mz na nb gp bk">Using this diagram below you can understand the process better.</p><figure class="nf ng nh ni nj nk nd ne paragraph-image"><div role="button" tabindex="0" class="oc od fl oe bh of"><span class="fu og oh an oi oj ok ol om speechify-ignore">Press enter or click to view image in full size</span><div class="nd ne ob"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*JOtvkmn_br5pP9AMm2jnIg.png 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*JOtvkmn_br5pP9AMm2jnIg.png 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*JOtvkmn_br5pP9AMm2jnIg.png 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*JOtvkmn_br5pP9AMm2jnIg.png 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*JOtvkmn_br5pP9AMm2jnIg.png 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*JOtvkmn_br5pP9AMm2jnIg.png 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*JOtvkmn_br5pP9AMm2jnIg.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*JOtvkmn_br5pP9AMm2jnIg.png 640w, https://miro.medium.com/v2/resize:fit:720/1*JOtvkmn_br5pP9AMm2jnIg.png 720w, https://miro.medium.com/v2/resize:fit:750/1*JOtvkmn_br5pP9AMm2jnIg.png 750w, https://miro.medium.com/v2/resize:fit:786/1*JOtvkmn_br5pP9AMm2jnIg.png 786w, https://miro.medium.com/v2/resize:fit:828/1*JOtvkmn_br5pP9AMm2jnIg.png 828w, https://miro.medium.com/v2/resize:fit:1100/1*JOtvkmn_br5pP9AMm2jnIg.png 1100w, https://miro.medium.com/v2/resize:fit:1400/1*JOtvkmn_br5pP9AMm2jnIg.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="" class="bh ll nl c" width="700" height="395" loading="lazy" role="presentation"/></picture></div></div></figure><p id="83a2" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">We have done everything to required from data gathering, preprocessing to applying unsupervised clustering model to create the communities of stocks in<a class="ag nc" href="https://medium.com/swlh/community-detection-network-analysis-of-the-stock-market-in-python-r-part-1-a8a3366dc5a5" rel="noopener"> <strong class="mg gx">Part</strong> <strong class="mg gx">1</strong> </a>and <a class="ag nc" href="https://medium.com/@akiboy96/community-detection-network-analysis-of-the-stock-market-in-python-r-part-2-9b5a5dbe5d5b" rel="noopener"><strong class="mg gx">Part 2</strong></a> of this story. We will now be using the <strong class="mg gx">Louvain</strong> method as discussed before to calculate centrality of the stocks using 4 very important measures.</p><blockquote class="on oo op"><p id="4426" class="me mf oq mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">For this we built an algorithm that applies equal weightage to the score of the four centralities we considered while building our portfolio. The 4 being:</p><p id="c55a" class="me mf oq mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Degree Centrality — that assigns a score based simply on the number of links held by each node.</p><p id="61fa" class="me mf oq mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Betweenness Centrality — which assigns a score based on the number of times a node lies on the shortest path between other nodes.</p><p id="9408" class="me mf oq mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Closeness Centrality — which scores each node based on their ‘closeness’ to all other nodes in the network.</p><p id="1222" class="me mf oq mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Eigen Centrality — which measures a node’s influence based on the number of links it has to other nodes in the network.</p></blockquote><p id="e939" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Together these centralities give us a score that help us determine the overall influence of a stock on the network. We can see these with the code below:</p><pre class="nf ng nh ni nj or os ot ou aj ov bk"><span id="c168" class="ow ox gw os b oy oz pa m pb pc">stocks_cross_corr, _, _ = calculate_corr(df_stock_prices,1, len(df_stock_prices), &#x27;pearson&#x27;)<br/>stocks_cross_corr = stocks_cross_corr[1]cor_thresold = 0.7<br/>G = build_graph(stocks_cross_corr, cor_thresold)<br/>partition = community.best_partition(G)<br/>modularity = community.modularity(partition, G)<br/>values = [partition.get(node) for node <strong class="os gx">in</strong> G.nodes()]<br/>plt.figure(figsize=(10,10))<br/>nx.draw_spring(G, cmap = plt.get_cmap(&#x27;jet&#x27;), node_color = values, node_size=30, with_labels=False)</span><span id="0083" class="ow ox gw os b oy pd pa m pb pc">print(modularity)<br/>print(&quot;Total number of Communities=&quot;, len(G.nodes()))<br/><br/>dict_betwenness_centrality = nx.betweenness_centrality(G)<br/>dict_degree_centrality = nx.degree_centrality(G)<br/>dict_closeness_centrality = nx.closeness_centrality(G)<br/>dict_eigenvector_centrality = nx.eigenvector_centrality(G)<br/>print(&quot;dict_degree_centrality: &quot;, dict_degree_centrality)<br/>print(&quot;dict_closeness_centrality: &quot;, dict_closeness_centrality)<br/>print(&quot;dict_eigenvector_centrality: &quot;, dict_eigenvector_centrality)<br/>print(&quot;dict_betweenness_centrality: &quot;, dict_betwenness_centrality)<br/></span></pre><figure class="nf ng nh ni nj nk nd ne paragraph-image"><div role="button" tabindex="0" class="oc od fl oe bh of"><span class="fu og oh an oi oj ok ol om speechify-ignore">Press enter or click to view image in full size</span><div class="nd ne pe"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*cBx7WlXo_MYK_xFao1RF_w.png 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*cBx7WlXo_MYK_xFao1RF_w.png 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*cBx7WlXo_MYK_xFao1RF_w.png 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*cBx7WlXo_MYK_xFao1RF_w.png 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*cBx7WlXo_MYK_xFao1RF_w.png 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*cBx7WlXo_MYK_xFao1RF_w.png 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*cBx7WlXo_MYK_xFao1RF_w.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*cBx7WlXo_MYK_xFao1RF_w.png 640w, https://miro.medium.com/v2/resize:fit:720/1*cBx7WlXo_MYK_xFao1RF_w.png 720w, https://miro.medium.com/v2/resize:fit:750/1*cBx7WlXo_MYK_xFao1RF_w.png 750w, https://miro.medium.com/v2/resize:fit:786/1*cBx7WlXo_MYK_xFao1RF_w.png 786w, https://miro.medium.com/v2/resize:fit:828/1*cBx7WlXo_MYK_xFao1RF_w.png 828w, https://miro.medium.com/v2/resize:fit:1100/1*cBx7WlXo_MYK_xFao1RF_w.png 1100w, https://miro.medium.com/v2/resize:fit:1400/1*cBx7WlXo_MYK_xFao1RF_w.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="" class="bh ll nl c" width="700" height="459" loading="lazy" role="presentation"/></picture></div></div></figure><h3 id="0d6e" class="ow ox gw bf pf pg ph dy pi pj pk ea pl mp pm pn po mt pp pq pr mx ps pt pu pv bk">Once the four types of centralities are obtained we will build our own portfolio determining algorithm using these centralities in an equal weightage sense. To do that we combine the dictionary data structure of our 4 centralties and use a for loop to iterate through each stock and add the four different centralities to create a final centrality metric. Since we are using equal weightage the formula would use a simple sum and not be any assigned any weights.</h3><pre class="nf ng nh ni nj or os ot ou aj ov bk"><span id="fcea" class="ow ox gw os b oy oz pa m pb pc"><em class="oq">#Portfolio Formula: </em><br/>c_dict = dict([(k, [dict_betwenness_centrality[k], dict_eigenvector_centrality[k], dict_degree_centrality[k], dict_closeness_centrality[k] ]) for k <strong class="os gx">in</strong> dict_betwenness_centrality])<br/><em class="oq">#print(c_dict)    </em><br/>    <br/>C_total = {}<br/>for key <strong class="os gx">in</strong> c_dict: <br/>    C_total[key] = sum(c_dict[key]) <br/>        <br/><br/>print(&quot;The Centrality total for stocks are:&quot;, C_total)   <br/><br/>newDict = dict(filter(lambda elem: elem[1] &gt; 0, C_total.items()))<br/>print(&quot;Stocks greater than 0.3 centrality are&quot;,newDict)<br/>print(len(newDict))<br/><br/>df_centrality = pd.DataFrame(list(newDict.items()),columns = [&#x27;Symbol&#x27;,&#x27;Centrality&#x27;]) <br/>df_centrality.sort_values(by=&#x27;Centrality&#x27;, ascending=False)<br/><em class="oq">#df_centrality.head(20)</em><br/><em class="oq">#type(df_centrality[&#x27;Centrality&#x27;])</em><br/>df_centrality.to_csv(&#x27;centrality_of_stocks_0.7cor.csv&#x27;,index=False)</span></pre><figure class="nf ng nh ni nj nk nd ne paragraph-image"><div role="button" tabindex="0" class="oc od fl oe bh of"><span class="fu og oh an oi oj ok ol om speechify-ignore">Press enter or click to view image in full size</span><div class="nd ne pw"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*sKMBeKndZl8V-Gzz-kJ15Q.png 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*sKMBeKndZl8V-Gzz-kJ15Q.png 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*sKMBeKndZl8V-Gzz-kJ15Q.png 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*sKMBeKndZl8V-Gzz-kJ15Q.png 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*sKMBeKndZl8V-Gzz-kJ15Q.png 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*sKMBeKndZl8V-Gzz-kJ15Q.png 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*sKMBeKndZl8V-Gzz-kJ15Q.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*sKMBeKndZl8V-Gzz-kJ15Q.png 640w, https://miro.medium.com/v2/resize:fit:720/1*sKMBeKndZl8V-Gzz-kJ15Q.png 720w, https://miro.medium.com/v2/resize:fit:750/1*sKMBeKndZl8V-Gzz-kJ15Q.png 750w, https://miro.medium.com/v2/resize:fit:786/1*sKMBeKndZl8V-Gzz-kJ15Q.png 786w, https://miro.medium.com/v2/resize:fit:828/1*sKMBeKndZl8V-Gzz-kJ15Q.png 828w, https://miro.medium.com/v2/resize:fit:1100/1*sKMBeKndZl8V-Gzz-kJ15Q.png 1100w, https://miro.medium.com/v2/resize:fit:1400/1*sKMBeKndZl8V-Gzz-kJ15Q.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="" class="bh ll nl c" width="700" height="410" loading="lazy" role="presentation"/></picture></div></div></figure><p id="1a47" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">As seen from the image above <strong class="mg gx">Apple (AAPL)</strong> located on the 7th index has a total centrality score of 0.126. This gives us a good idea on how the stock is placed in our network and community, in terms of degrees of connections, betweenness and closeness amongst nodes along with their eigenvalues</p><p id="10fb" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Using the centrality score from our algorithm, we further filtered the stocks by sector so that we could bring diversification in our portfolio as well.Making use of the tidyquant package in <strong class="mg gx">R</strong> we then compared our portfolio to the S&amp;P500’s in the time frame of monthly returns. We were happy to see that our index over the last 5 years has beaten the benchmark S&amp;P 500 and has indeed provided a better return on investment percentage.</p><p id="a87a" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">The whole code base for the portfolio making can be found <a class="ag nc" href="https://www.kaggle.com/akiboy96/portfolio-management" rel="noopener ugc nofollow" target="_blank">here:</a></p><p id="c338" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">We will start by loading the required packages in <strong class="mg gx">R</strong></p><pre class="nf ng nh ni nj or os ot ou aj ov bk"><span id="3ca9" class="ow ox gw os b oy oz pa m pb pc"><em class="oq"># This R environment comes with many helpful analytics packages installed</em><br/><em class="oq"># It is defined by the kaggle/rstats Docker image: https://github.com/kaggle/docker-rstats</em><br/><em class="oq"># For example, here&#x27;s a helpful package to load</em><br/><br/>library(tidyverse)<br/>library(tidyquant)<br/>library(jsonlite)<br/>library(tidyverse)<br/>library(readr)<br/>library(igraph)<br/>library(dplyr)<br/>library(lubridate)<br/>library(data.table)<br/>library(Quandl) <em class="oq"># metapackage of all tidyverse packages</em><br/><br/><em class="oq"># Input data files are available in the read-only &quot;../input/&quot; directory</em><br/><em class="oq"># For example, running this (by clicking run or pressing Shift+Enter) will list all files under the input directory</em><br/><br/>list.files(path = &quot;../input&quot;)<br/><br/><em class="oq"># You can write up to 5GB to the current directory (/kaggle/working/) that gets preserved as output when you create a version using &quot;Save &amp; Run All&quot; </em><br/><em class="oq"># You can also write temporary files to /kaggle/temp/, but they won&#x27;t be saved outside of the current session</em></span></pre><p id="3f3d" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">We will then load the dataset that we made using different correlation thresholds as shown below like <em class="oq">0.5, 0.6 and 0.7</em>. This will be used in a variety of examples as shown below to make your portfolio. These <a class="ag nc" href="https://www.kaggle.com/akiboy96/centrality-of-stocks-network-analysis" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">datasets</strong></a> can be found on kaggle <a class="ag nc" href="https://www.kaggle.com/akiboy96/portfolio-management" rel="noopener ugc nofollow" target="_blank">here</a>:</p><pre class="nf ng nh ni nj or os ot ou aj ov bk"><span id="e4ea" class="ow ox gw os b oy oz pa m pb pc"><em class="oq">#DataLoad </em><br/>stockprices &lt;- read.csv(&quot;../input/usstockprices/stocks_price_final.csv&quot;)<br/>centrality0.5 &lt;- read.csv(&quot;../input/centrality-of-stocks-network-analysis/centrality_of_stocks_0.5cor.csv&quot;)<br/>centrality0.6 &lt;- read.csv(&quot;../input/centrality-of-stocks-network-analysis/centrality_of_stocks_0.6cor.csv&quot;)<br/>centrality0.7 &lt;- read.csv(&quot;../input/centrality-of-stocks-network-analysis/centrality_of_stocks_0.7cor.csv&quot;)</span></pre><p id="918e" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">We will then do some data cleaning by joining stocks with their centralities. Once that is done, we can begin the portfolio generation and comparison to SPY depending on whichever correlation you want of the stocks</p><pre class="nf ng nh ni nj or os ot ou aj ov bk"><span id="49a6" class="ow ox gw os b oy oz pa m pb pc"><em class="oq">#Merges Centtraility values with other potential useful information like market cap and sector for further filtering</em><br/>stocks &lt;- stockprices[!duplicated(stockprices$symbol), ] <em class="oq"># removes duplicate symbols</em><br/>Index0.5cor &lt;- merge(stocks, centrality0.5, by.x = &quot;symbol&quot;, by.y = &quot;Symbol&quot;)<br/>Index0.6cor &lt;- merge(stocks, centrality0.6, by.x = &quot;symbol&quot;, by.y = &quot;Symbol&quot;)<br/>Index0.7cor &lt;- merge(stocks, centrality0.7, by.x = &quot;symbol&quot;, by.y = &quot;Symbol&quot;)<br/><em class="oq">#head(Index0.6cor)</em><br/>IndexGet0.5 &lt;- as.character(Index0.5cor$symbol)<br/>IndexGet0.6 &lt;- as.character(Index0.6cor$symbol)<br/>IndexGet0.7 &lt;- as.character(Index0.7cor$symbol)</span><span id="c18b" class="ow ox gw os b oy pd pa m pb pc"><em class="oq">#Portfolio Testing</em><br/><br/><em class="oq">##0.5 correlation centrality prices</em><br/>stockindex0.5 &lt;- tq_get(IndexGet0.5, get=&quot;stock.prices&quot;, from = &quot;2015-07-01&quot;,warnings = FALSE,<br/>                             stringsAsFactors = FALSE) %&gt;%<br/>  group_by(symbol) %&gt;%<br/>  tq_transmute(select=adjusted,<br/>               mutate_fun=periodReturn,<br/>               period=&quot;monthly&quot;,<br/>               col_rename = &quot;monthly_return&quot;)<br/><br/>stockindex0.5<br/><br/><em class="oq">##0.6 correlation centrality prices</em><br/>stockindex0.6 &lt;- tq_get(IndexGet0.6, get=&quot;stock.prices&quot;, from = &quot;2015-07-01&quot;,warnings = FALSE,<br/>                             stringsAsFactors = FALSE) %&gt;%<br/>  group_by(symbol) %&gt;%<br/>  tq_transmute(select=adjusted,<br/>               mutate_fun=periodReturn,<br/>               period=&quot;monthly&quot;,<br/>               col_rename = &quot;monthly_return&quot;)<br/><br/><br/><em class="oq">##0.7 correlation centrality prices</em><br/>stockindex0.7 &lt;- tq_get(IndexGet0.7, get=&quot;stock.prices&quot;, from = &quot;2015-07-01&quot;,warnings = FALSE,<br/>                             stringsAsFactors = FALSE) %&gt;%<br/>  group_by(symbol) %&gt;%<br/>  tq_transmute(select=adjusted,<br/>               mutate_fun=periodReturn,<br/>               period=&quot;monthly&quot;,<br/>               col_rename = &quot;monthly_return&quot;)<br/><br/><br/><em class="oq">##Base Portfolio to compare </em><br/><br/>baseline_returns_monthly &lt;- &quot;SPY&quot; %&gt;%<br/>    tq_get(get  = &quot;stock.prices&quot;,<br/>           from = &quot;2015-07-01&quot;, warnings = FALSE,stringsAsFactors = FALSE) %&gt;%<br/>    tq_transmute(select     = adjusted, <br/>                 mutate_fun = periodReturn, <br/>                 period     = &quot;monthly&quot;, <br/>                 col_rename = &quot;spy_monthly_return&quot;)<br/><br/>baseline_returns_monthly</span></pre><p id="c6c8" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Portfolio calculation from above method</p><pre class="nf ng nh ni nj or os ot ou aj ov bk"><span id="1870" class="ow ox gw os b oy oz pa m pb pc">portfolio_returns_monthly0.5 &lt;- stockindex0.5 %&gt;%<br/>    tq_portfolio(assets_col  = symbol, <br/>                 returns_col = monthly_return, <br/>                 col_rename  = &quot;portfolio-monthly&quot;)<br/><br/><br/>portfolio_returns_monthly0.6 &lt;- stockindex0.6 %&gt;%<br/>    tq_portfolio(assets_col  = symbol, <br/>                 returns_col = monthly_return, <br/>                 col_rename  = &quot;portfolio-monthly&quot;)<br/><br/><br/>portfolio_returns_monthly0.7 &lt;- stockindex0.7 %&gt;%<br/>    tq_portfolio(assets_col  = symbol, <br/>                 returns_col = monthly_return, <br/>                 col_rename  = &quot;portfolio-monthly&quot;)<br/><br/><br/><em class="oq">#Portfolio Compare</em><br/>stock0.5indexVSSPY &lt;- left_join(portfolio_returns_monthly0.5, <br/>                                   baseline_returns_monthly,<br/>                                   by = &quot;date&quot;)<br/><br/>stock0.6indexVSSPY &lt;- left_join(portfolio_returns_monthly0.6, <br/>                                   baseline_returns_monthly,<br/>                                   by = &quot;date&quot;)<br/><br/>stock0.7indexVSSPY &lt;- left_join(portfolio_returns_monthly0.7, <br/>                                   baseline_returns_monthly,<br/>                                   by = &quot;date&quot;)<br/><em class="oq">#stock0.5indexVSSPY</em><br/><br/>ggplot(stock0.5indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = &quot;blue&quot;)+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = &quot;red&quot;)<br/>ggplot(stock0.6indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = &quot;blue&quot;)+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = &quot;red&quot;)<br/>ggplot(stock0.7indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = &quot;blue&quot;)+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = &quot;red&quot;)</span></pre><p id="49a2" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk"><strong class="mg gx">Playground here to optimize your portfolio! Choose the correlation you want from the datasets above and then further optimize your portfolio by filtering the stocks by sector and their centrality value. This will help you create a niche list of stocks that could potentially beat the</strong><a class="ag nc" href="https://www.investopedia.com/articles/investing/122215/spy-spdr-sp-500-trust-etf.asp#:~:text=SPY%20Performance&amp;text=The%20SPDR%20S%26P%20500%20ETF%20Trust%20(SPY)%20has%20generated%20an,average%20annual%20returns%20of%208.93%25." rel="noopener ugc nofollow" target="_blank"><strong class="mg gx"> SPY returns YoY of 8.93%</strong></a><strong class="mg gx">.</strong></p><pre class="nf ng nh ni nj or os ot ou aj ov bk"><span id="08d1" class="ow ox gw os b oy oz pa m pb pc"><em class="oq">##PLAYGROUND - LETS DO TRIAL AND ERROR HERE TO BEAT THE SPY GRAPH in RETURNS</em><br/><br/>centrality_filter &lt;- Index0.6cor<br/><em class="oq">#filter(Centrality &gt; 0.8 &amp; Centrality &lt; 0.5)</em><br/>centrality_filter<br/><br/>IndexGet0.6 &lt;- as.character(centrality_filter$symbol)<br/><br/>stockindex0.6 &lt;- tq_get(IndexGet0.6, get=&quot;stock.prices&quot;, from = &quot;2015-07-01&quot;,warnings = FALSE,<br/>                             stringsAsFactors = FALSE) %&gt;%<br/>  group_by(symbol) %&gt;%<br/>  tq_transmute(select=adjusted,<br/>               mutate_fun=periodReturn,<br/>               period=&quot;monthly&quot;,<br/>               col_rename = &quot;monthly_return&quot;)<br/><br/>portfolio_returns_monthly0.6 &lt;- stockindex0.6 %&gt;%<br/>    tq_portfolio(assets_col  = symbol, <br/>                 returns_col = monthly_return, <br/>                 col_rename  = &quot;portfolio-monthly&quot;)<br/><br/>stock0.6indexVSSPY &lt;- left_join(portfolio_returns_monthly0.6, <br/>                                   baseline_returns_monthly,<br/>                                   by = &quot;date&quot;)<br/><br/>ggplot(stock0.6indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = &quot;blue&quot;)+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = &quot;red&quot;)</span></pre><blockquote class="nm"><p id="34d5" class="nn no gw bf np nq nr ns nt nu nv nb du">This is the code I wrote to achieve a portfolio that could beat the SPY returns over a 5 year period from July 1st 2015 to July 22nd 2020</p></blockquote><pre class="px py pz qa qb or os ot ou aj ov bk"><span id="2462" class="ow ox gw os b oy oz pa m pb pc">centrality_filter &lt;- Index0.5cor %&gt;%<br/>filter((sector == &quot;Health Care&quot; &amp; Centrality &gt; 0.4) | (sector == &quot;Technology&quot; &amp; Centrality &gt; 0.42) | (sector == &quot;Consumer Services&quot; &amp; Centrality &gt; 0.49) | (sector == &quot;Finance&quot; &amp; Centrality &gt; 0.5)  | (sector == &quot;Transportation&quot; &amp; Centrality &gt; 0.5) | (sector == &quot;Capital Goods&quot; &amp; Centrality &gt; 0.35) | (sector == &quot;Miscellaneous&quot; &amp; Centrality &gt; 0.5) | (sector == &quot;Basic Industries&quot; &amp; Centrality &gt; 0.39) | (sector == &quot;Public Utilities&quot; &amp; Centrality &gt; 0.36) | (sector == &quot;Consumer Durables&quot; &amp; Centrality &gt; 0.3)| (sector == &quot;Consumer Non-Durables&quot; &amp; Centrality &gt; 0.25))<br/>centrality_filter<br/><br/>IndexGet0.5 &lt;- as.character(centrality_filter$symbol)<br/><br/>stockindex0.5 &lt;- tq_get(IndexGet0.5, get=&quot;stock.prices&quot;, from = &quot;2015-07-01&quot;, till = &quot;2020-07-22&quot;,warnings = FALSE,<br/>                             stringsAsFactors = FALSE) %&gt;%<br/>  group_by(symbol) %&gt;%<br/>  tq_transmute(select=adjusted,<br/>               mutate_fun=periodReturn,<br/>               period=&quot;monthly&quot;,<br/>               col_rename = &quot;monthly_return&quot;)<br/><br/>portfolio_returns_monthly0.5 &lt;- stockindex0.5 %&gt;%<br/>    tq_portfolio(assets_col  = symbol, <br/>                 returns_col = monthly_return, <br/>                 col_rename  = &quot;portfolio-monthly&quot;)<br/><br/>stock0.5indexVSSPY &lt;- left_join(portfolio_returns_monthly0.5, <br/>                                   baseline_returns_monthly,<br/>                                   by = &quot;date&quot;)<br/><br/>stock0.5indexVSSPY$`portfolio-monthly` &lt;- stock0.5indexVSSPY$`portfolio-monthly` * 100<br/>stock0.5indexVSSPY$spy_monthly_return &lt;- stock0.5indexVSSPY$spy_monthly_return * 100<br/><br/>stock0.5indexVSSPY &lt;- stock0.5indexVSSPY[-c(14), ]<br/><br/>returns &lt;- ggplot(stock0.5indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = &quot;blue&quot;)+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = &quot;red&quot;) + ggtitle(&quot;Portfolio vs S&amp;P 500 Returns over 5 years&quot;) + labs(y=&quot;Returns Percenatge&quot;, x = &quot;Date&quot;) <br/><br/>plot(returns)</span></pre><figure class="nf ng nh ni nj nk nd ne paragraph-image"><div class="nd ne qc"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*lhR-zvcYQhsOswSL8unAJw.png 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*lhR-zvcYQhsOswSL8unAJw.png 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*lhR-zvcYQhsOswSL8unAJw.png 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*lhR-zvcYQhsOswSL8unAJw.png 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*lhR-zvcYQhsOswSL8unAJw.png 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*lhR-zvcYQhsOswSL8unAJw.png 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*lhR-zvcYQhsOswSL8unAJw.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*lhR-zvcYQhsOswSL8unAJw.png 640w, https://miro.medium.com/v2/resize:fit:720/1*lhR-zvcYQhsOswSL8unAJw.png 720w, https://miro.medium.com/v2/resize:fit:750/1*lhR-zvcYQhsOswSL8unAJw.png 750w, https://miro.medium.com/v2/resize:fit:786/1*lhR-zvcYQhsOswSL8unAJw.png 786w, https://miro.medium.com/v2/resize:fit:828/1*lhR-zvcYQhsOswSL8unAJw.png 828w, https://miro.medium.com/v2/resize:fit:1100/1*lhR-zvcYQhsOswSL8unAJw.png 1100w, https://miro.medium.com/v2/resize:fit:1400/1*lhR-zvcYQhsOswSL8unAJw.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="" class="bh ll nl c" width="700" height="432" loading="lazy" role="presentation"/></picture></div><figcaption class="qd ff qe nd ne qf qg bf b bg ab du">Blue Line is my Portfolio, Red line is SPY</figcaption></figure><p id="d0c3" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">From the plot above we can visualize a more volatile trend for my portfolio which is the blue line as comp to red which’s the SPY returns. But overall, the blue line has a higher Return on Investment (ROI) percentage over 5 years.</p><blockquote class="nm"><p id="7fff" class="nn no gw bf np nq qh qi qj qk ql nb du">The centralities as shown in the above code were chosen based on my predictions for future trends in terms of what sectors will do well and become important to society as a service.</p></blockquote></div></div></div></div></section></div></div></article></div><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="qm qn ac it"><div class="qo ac"><a class="qp aj an ap" href="https://medium.com/tag/stock-market?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><div class="qq fl cx qr gg qs qt bf b bg ab bk ol">Stock Market</div></a></div><div class="qo ac"><a class="qp aj an ap" href="https://medium.com/tag/finance?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><div class="qq fl cx qr gg qs qt bf b bg ab bk ol">Finance</div></a></div><div class="qo ac"><a class="qp aj an ap" href="https://medium.com/tag/network-as-a-service?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><div class="qq fl cx qr gg qs qt bf b bg ab bk ol">Network As A Service</div></a></div><div class="qo ac"><a class="qp aj an ap" href="https://medium.com/tag/r?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><div class="qq fl cx qr gg qs qt bf b bg ab bk ol">R</div></a></div><div class="qo ac"><a class="qp aj an ap" href="https://medium.com/tag/portfolio-management?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><div class="qq fl cx qr gg qs qt bf b bg ab bk ol">Portfolio Management</div></a></div></div></div></div><div class="m"></div><footer class="qu qv qw qx qy ac r qz ra c"><div class="m af"><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="ac cp rb"><div class="ac r kc"><div class="rc m"><span class="m rd re rf f e"><div class="ac r kc kd"><div class="pw-multi-vote-icon fl ke kf kg kh"><span data-dd-action-name="Susi presentation tracker clap_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="footerClapButton" href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fp%2Fca4e0fbb8ca9&amp;operation=register&amp;redirect=https%3A%2F%2Fakiboy96.medium.com%2Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9&amp;user=Aakash+Kedia&amp;userId=e0dbacebbef4&amp;source=---footer_actions--ca4e0fbb8ca9---------------------clap_footer------------------" rel="noopener follow"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><div class="ki ap kj kk kl km an kn ko kp kh" role="presentation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="clap"><path fill-rule="evenodd" d="M11.37.828 12 3.282l.63-2.454zM13.916 3.953l1.523-2.112-1.184-.39zM8.589 1.84l1.522 2.112-.337-2.501zM18.523 18.92c-.86.86-1.75 1.246-2.62 1.33a6 6 0 0 0 .407-.372c2.388-2.389 2.86-4.951 1.399-7.623l-.912-1.603-.79-1.672c-.26-.56-.194-.98.203-1.288a.7.7 0 0 1 .546-.132c.283.046.546.231.728.5l2.363 4.157c.976 1.624 1.141 4.237-1.324 6.702m-10.999-.438L3.37 14.328a.828.828 0 0 1 .585-1.408.83.83 0 0 1 .585.242l2.158 2.157a.365.365 0 0 0 .516-.516l-2.157-2.158-1.449-1.449a.826.826 0 0 1 1.167-1.17l3.438 3.44a.363.363 0 0 0 .516 0 .364.364 0 0 0 0-.516L5.293 9.513l-.97-.97a.826.826 0 0 1 0-*********** 0 0 1 1.167 0l.97.968 3.437 3.436a.36.36 0 0 0 .517 0 .366.366 0 0 0 0-.516L6.977 7.83a.82.82 0 0 1-.241-.584.82.82 0 0 1 .824-.826c.219 0 .43.087.584.242l5.787 5.787a.366.366 0 0 0 .587-.415l-1.117-2.363c-.26-.56-.194-.98.204-1.289a.7.7 0 0 1 .546-.132c.283.046.545.232.727.501l2.193 3.86c1.302 2.38.883 4.59-1.277 6.75-1.156 1.156-2.602 1.627-4.19 1.367-1.418-.236-2.866-1.033-4.079-2.246M10.75 5.971l2.12 2.12c-.41.502-.465 1.17-.128 1.89l.22.465-3.523-3.523a.8.8 0 0 1-.097-.368c0-.22.086-.428.241-.584a.847.847 0 0 1 1.167 0m7.355 1.705c-.31-.461-.746-.758-1.23-.837a1.44 1.44 0 0 0-1.11.275c-.312.24-.505.543-.59.881a1.74 1.74 0 0 0-.906-.465 1.47 1.47 0 0 0-.82.106l-2.182-2.182a1.56 1.56 0 0 0-2.2 0 1.54 1.54 0 0 0-.396.701 1.56 1.56 0 0 0-2.21-.01 1.55 1.55 0 0 0-.416.753c-.624-.624-1.649-.624-2.237-.037a1.557 1.557 0 0 0 0 2.2c-.239.1-.501.238-.715.453a1.56 1.56 0 0 0 0 2.2l.516.515a1.556 1.556 0 0 0-.753 2.615L7.01 19c1.32 1.319 2.909 2.189 4.475 2.449q.482.08.971.08c.85 0 1.653-.198 2.393-.579.**************.686.054 1.266 0 2.457-.52 3.505-1.567 2.763-2.763 2.552-5.734 1.439-7.586z" clip-rule="evenodd"></path></svg></div></div></div></div></a></span></div><div class="pw-multi-vote-count m kq kr ks kt ku kv kw"><p class="bf b dv ab du"><span class="kx">--</span></p></div></div></span><span class="m i h g rg rh"><div class="ac r kc kd"><div class="pw-multi-vote-icon fl ke kf kg kh"><span data-dd-action-name="Susi presentation tracker clap_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="footerClapButton" href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fp%2Fca4e0fbb8ca9&amp;operation=register&amp;redirect=https%3A%2F%2Fakiboy96.medium.com%2Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9&amp;user=Aakash+Kedia&amp;userId=e0dbacebbef4&amp;source=---footer_actions--ca4e0fbb8ca9---------------------clap_footer------------------" rel="noopener follow"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><div class="ki ap kj kk kl km an kn ko kp kh" role="presentation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="clap"><path fill-rule="evenodd" d="M11.37.828 12 3.282l.63-2.454zM13.916 3.953l1.523-2.112-1.184-.39zM8.589 1.84l1.522 2.112-.337-2.501zM18.523 18.92c-.86.86-1.75 1.246-2.62 1.33a6 6 0 0 0 .407-.372c2.388-2.389 2.86-4.951 1.399-7.623l-.912-1.603-.79-1.672c-.26-.56-.194-.98.203-1.288a.7.7 0 0 1 .546-.132c.283.046.546.231.728.5l2.363 4.157c.976 1.624 1.141 4.237-1.324 6.702m-10.999-.438L3.37 14.328a.828.828 0 0 1 .585-1.408.83.83 0 0 1 .585.242l2.158 2.157a.365.365 0 0 0 .516-.516l-2.157-2.158-1.449-1.449a.826.826 0 0 1 1.167-1.17l3.438 3.44a.363.363 0 0 0 .516 0 .364.364 0 0 0 0-.516L5.293 9.513l-.97-.97a.826.826 0 0 1 0-*********** 0 0 1 1.167 0l.97.968 3.437 3.436a.36.36 0 0 0 .517 0 .366.366 0 0 0 0-.516L6.977 7.83a.82.82 0 0 1-.241-.584.82.82 0 0 1 .824-.826c.219 0 .43.087.584.242l5.787 5.787a.366.366 0 0 0 .587-.415l-1.117-2.363c-.26-.56-.194-.98.204-1.289a.7.7 0 0 1 .546-.132c.283.046.545.232.727.501l2.193 3.86c1.302 2.38.883 4.59-1.277 6.75-1.156 1.156-2.602 1.627-4.19 1.367-1.418-.236-2.866-1.033-4.079-2.246M10.75 5.971l2.12 2.12c-.41.502-.465 1.17-.128 1.89l.22.465-3.523-3.523a.8.8 0 0 1-.097-.368c0-.22.086-.428.241-.584a.847.847 0 0 1 1.167 0m7.355 1.705c-.31-.461-.746-.758-1.23-.837a1.44 1.44 0 0 0-1.11.275c-.312.24-.505.543-.59.881a1.74 1.74 0 0 0-.906-.465 1.47 1.47 0 0 0-.82.106l-2.182-2.182a1.56 1.56 0 0 0-2.2 0 1.54 1.54 0 0 0-.396.701 1.56 1.56 0 0 0-2.21-.01 1.55 1.55 0 0 0-.416.753c-.624-.624-1.649-.624-2.237-.037a1.557 1.557 0 0 0 0 2.2c-.239.1-.501.238-.715.453a1.56 1.56 0 0 0 0 2.2l.516.515a1.556 1.556 0 0 0-.753 2.615L7.01 19c1.32 1.319 2.909 2.189 4.475 2.449q.482.08.971.08c.85 0 1.653-.198 2.393-.579.**************.686.054 1.266 0 2.457-.52 3.505-1.567 2.763-2.763 2.552-5.734 1.439-7.586z" clip-rule="evenodd"></path></svg></div></div></div></div></a></span></div><div class="pw-multi-vote-count m kq kr ks kt ku kv kw"><p class="bf b dv ab du"><span class="kx">--</span></p></div></div></span></div><div class="ay ac"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button class="ap ki ky kz ac r fm la lb" aria-label="responses"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="lc"><path d="M18.006 16.803c1.533-1.456 2.234-3.325 2.234-5.321C20.24 7.357 16.709 4 12.191 4S4 7.357 4 11.482c0 4.126 3.674 7.482 8.191 7.482.817 0 1.622-.111 2.393-.327.231.2.48.391.744.559 1.06.693 2.203 1.044 3.399 1.044.224-.008.4-.112.486-.287a.49.49 0 0 0-.042-.518c-.495-.67-.845-1.364-1.04-2.057a4 4 0 0 1-.125-.598zm-3.122 1.055-.067-.223-.315.096a8 8 0 0 1-2.311.338c-4.023 0-7.292-2.955-7.292-6.587 0-3.633 3.269-6.588 7.292-6.588 4.014 0 7.112 2.958 7.112 6.593 0 1.794-.608 3.469-2.027 4.72l-.195.168v.255c0 .056 0 .151.016.295.025.231.081.478.154.733.154.558.398 1.117.722 1.659a5.3 5.3 0 0 1-2.165-.845c-.276-.176-.714-.383-.941-.59z"></path></svg></button></div></div></div></div></div><div class="ac r"><div class="ri m rj"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><span data-dd-action-name="Susi presentation tracker bookmark_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="footerBookmarkButton" href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2Fca4e0fbb8ca9&amp;operation=register&amp;redirect=https%3A%2F%2Fakiboy96.medium.com%2Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9&amp;source=---footer_actions--ca4e0fbb8ca9---------------------bookmark_footer------------------" rel="noopener follow"><svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="none" viewBox="0 0 25 25" class="du le" aria-label="Add to list bookmark button"><path fill="currentColor" d="M18 2.5a.5.5 0 0 1 1 0V5h2.5a.5.5 0 0 1 0 1H19v2.5a.5.5 0 1 1-1 0V6h-2.5a.5.5 0 0 1 0-1H18zM7 7a1 1 0 0 1 1-1h3.5a.5.5 0 0 0 0-1H8a2 2 0 0 0-2 2v14a.5.5 0 0 0 .805.396L12.5 17l5.695 4.396A.5.5 0 0 0 19 21v-8.5a.5.5 0 0 0-1 0v7.485l-5.195-4.012a.5.5 0 0 0-.61 0L7 19.985z"></path></svg></a></span></div></div></div></div><div class="ri m rj"><div class="bm" aria-hidden="false" aria-describedby="postFooterSocialMenu" aria-labelledby="postFooterSocialMenu"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button aria-controls="postFooterSocialMenu" aria-expanded="false" aria-label="Share Post" data-testid="footerSocialShareButton" class="ag fm ai fh ak al am lm ao ap aq ex ln lo lb lp"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M15.218 4.931a.4.4 0 0 1-.118.132l.012.006a.45.45 0 0 1-.292.074.5.5 0 0 1-.3-.13l-2.02-2.02v7.07c0 .28-.23.5-.5.5s-.5-.22-.5-.5v-7.04l-2 2a.45.45 0 0 1-.57.04h-.02a.4.4 0 0 1-.16-.3.4.4 0 0 1 .1-.32l2.8-2.8a.5.5 0 0 1 .7 0l2.8 2.79a.42.42 0 0 1 .068.498m-.106.138.008.004v-.01zM16 7.063h1.5a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-11c-1.1 0-2-.9-2-2v-10a2 2 0 0 1 2-2H8a.5.5 0 0 1 .********* 0 0 1 .********* 0 0 1-.********* 0 0 1-.35.15H6.4c-.5 0-.9.4-.9.9v10.2a.9.9 0 0 0 .9.9h11.2c.5 0 .9-.4.9-.9v-10.2c0-.5-.4-.9-.9-.9H16a.5.5 0 0 1 0-1" clip-rule="evenodd"></path></svg></button></div></div></div></div></div></div></div></div></div></div></footer><div class="rk m"><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="ac ig ie ic rl rm"><div class="rn ro rp rq rr rs rt ru rv rw ac cp"><div class="i l"><a tabindex="0" rel="noopener follow" href="/?source=post_page---post_author_info--ca4e0fbb8ca9---------------------------------------" data-discover="true"><div class="m fl"><img alt="Aakash Kedia" class="m fd bx rx ry cx" src="https://miro.medium.com/v2/resize:fill:96:96/0*emaZ2wHvCzhl_Apj" width="48" height="48" loading="lazy"/><div class="ft bx m rx ry fu o aj rz"></div></div></a></div><div class="k j e"><a tabindex="0" rel="noopener follow" href="/?source=post_page---post_author_info--ca4e0fbb8ca9---------------------------------------" data-discover="true"><div class="m fl"><img alt="Aakash Kedia" class="m fd bx sa sb cx" src="https://miro.medium.com/v2/resize:fill:128:128/0*emaZ2wHvCzhl_Apj" width="64" height="64" loading="lazy"/><div class="ft bx m sa sb fu o aj rz"></div></div></a></div><div class="k j e sc rj"><div class="ac"></div></div></div><div class="ac co ca"><div class="sd se sf sg sh m"><a class="ag ah ai ak al am an ao ap aq ar as at au ac r" rel="noopener follow" href="/?source=post_page---post_author_info--ca4e0fbb8ca9---------------------------------------" data-discover="true"><h2 class="pw-author-name bf sj sk sl sm sn so sp mp pn po mt pq pr mx pt pu bk"><span class="gp si">Written by <!-- -->Aakash Kedia</span></h2></a><div class="qo ac ii"><div class="m rj"><span class="pw-follower-count bf b bg ab du"><a class="ag ah ai fh ak al am an ao ap aq ar as ir" rel="noopener follow" href="/followers?source=post_page---post_author_info--ca4e0fbb8ca9---------------------------------------" data-discover="true">19 followers</a></span></div><div class="bf b bg ab du ac pb"><span class="sq m" aria-hidden="true"><span class="bf b bg ab du">·</span></span><a class="ag ah ai fh ak al am an ao ap aq ar as ir" rel="noopener follow" href="/following?source=post_page---post_author_info--ca4e0fbb8ca9---------------------------------------" data-discover="true">45 following</a></div></div><div class="sr m"><p class="bf b bg ab bk"><span class="gp">CS, music and football in no particular order</span></p></div></div></div><div class="i l"><div class="ac"></div></div></div></div></div></div><div class="ss m"><div class="st bh s rk"></div><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="ac r cp"><h2 class="bf sj su sv pi sw sx pl sy sz ta tb tc td te tf tg bk">No responses yet</h2><div class="ac th"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><a class="ti tj" href="https://policy.medium.com/medium-rules-30e5502c4eb4?source=post_page---post_responses--ca4e0fbb8ca9---------------------------------------" rel="noopener follow" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" aria-label="Shield with a checkmark" viewBox="0 0 25 25"><path fill-rule="evenodd" d="M11.987 5.036a.754.754 0 0 1 .914-.01c.972.721 1.767 1.218 2.6 1.543.828.322 1.719.485 2.887.505a.755.755 0 0 1 .741.757c-.018 3.623-.43 6.256-1.449 8.21-1.034 1.984-2.662 3.209-4.966 4.083a.75.75 0 0 1-.537-.003c-2.243-.874-3.858-2.095-4.897-4.074-1.024-1.951-1.457-4.583-1.476-8.216a.755.755 0 0 1 .741-.757c1.195-.02 2.1-.182 2.923-.503.827-.322 1.6-.815 2.519-1.535m.468.903c-.897.69-1.717 1.21-2.623 1.564-.898.35-1.856.527-3.026.565.037 3.45.469 5.817 1.36 7.515.884 1.684 2.25 2.762 4.284 3.571 2.092-.81 3.465-1.89 4.344-3.575.886-1.698 1.299-4.065 1.334-7.512-1.149-.039-2.091-.217-2.99-.567-.906-.353-1.745-.873-2.683-1.561m-.009 9.155a2.672 2.672 0 1 0 0-5.344 2.672 2.672 0 0 0 0 5.344m0 1a3.672 3.672 0 1 0 0-7.344 3.672 3.672 0 0 0 0 7.344m-1.813-3.777.525-.526.916.917 1.623-1.625.526.526-2.149 2.152z" clip-rule="evenodd"></path></svg></a></div></div></div></div></div><div class="tk tl tm tn to m"></div></div></div></div><div class="tp tq tr ts tt m bw"><div class="i l k"><div class="st bh tu tv"></div><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="tw ac kc it"><div class="tx ty m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://help.medium.com/hc/en-us?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Help</p></a></div><div class="tx ty m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://medium.statuspage.io/?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Status</p></a></div><div class="tx ty m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://medium.com/about?autoplay=1&amp;source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">About</p></a></div><div class="tx ty m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://medium.com/jobs-at-medium/work-at-medium-959d1a85284e?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Careers</p></a></div><div class="tx ty m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="mailto:<EMAIL>" rel="noopener follow"><p class="bf b dv ab du">Press</p></a></div><div class="tx ty m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://blog.medium.com/?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Blog</p></a></div><div class="tx ty m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://policy.medium.com/medium-privacy-policy-f03bf92035c9?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Privacy</p></a></div><div class="tx ty m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://policy.medium.com/medium-rules-30e5502c4eb4?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Rules</p></a></div><div class="tx ty m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://policy.medium.com/medium-terms-of-service-9db0094a1e0f?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Terms</p></a></div><div class="tx m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://speechify.com/medium?source=post_page-----ca4e0fbb8ca9---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Text to speech</p></a></div></div></div></div></div></div></div></div></div></div></div></div><script>window.__BUILD_ID__="main-20250829-185947-c929e6f698"</script><script>window.__GRAPHQL_URI__ = "https://akiboy96.medium.com/_/graphql"</script><script>window.__PRELOADED_STATE__ = {"algolia":{"queries":{}},"cache":{"experimentGroupSet":true,"reason":"","group":"enabled","tags":["group-edgeCachePosts","post-ca4e0fbb8ca9","user-e0dbacebbef4","group-newLoNonMocUpsellExperimentGroup-group2"],"serverVariantState":"44136fa355b3678a1146ad16f7e8649e94fb4fc21fe77e8310c060f61caaff8a","middlewareEnabled":true,"cacheStatus":"DYNAMIC","shouldUseCache":true,"vary":[],"pubFeaturingPostPageLabelEnabled":false,"shouldFollowPostQueryEnabled":false,"newLoNonMocUpsellExperimentGroup":"group2"},"client":{"hydrated":false,"isUs":false,"isNativeMedium":false,"isSafariMobile":false,"isSafari":false,"isFirefox":false,"routingEntity":{"type":"USER","id":"e0dbacebbef4","explicit":true},"viewerIsBot":false},"debug":{"requestId":"a9296891-4796-4cce-a3f8-9b38bb4f269f","requestTag":"","hybridDevServices":[],"originalSpanCarrier":{"traceparent":"00-57ffb9f023d09ce0b3b0aa3d0a031f76-c15451d9acf44f54-01"}},"multiVote":{"clapsPerPost":{}},"navigation":{"branch":{"show":null,"hasRendered":null,"blockedByCTA":false},"hideGoogleOneTap":false,"hasRenderedAlternateUserBanner":null,"currentLocation":"https:\u002F\u002Fakiboy96.medium.com\u002Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9","host":"akiboy96.medium.com","hostname":"akiboy96.medium.com","referrer":"","hasSetReferrer":false,"susiModal":{"step":null,"operation":"register"},"postRead":false,"partnerProgram":{"selectedCountryCode":null},"staticRouterContext":{"route":{"name":"ShowPostUnderUser"},"statusCode":200},"toastQueue":[],"currentToast":null,"queryString":"","currentHash":""},"config":{"nodeEnv":"production","version":"main-20250829-185947-c929e6f698","target":"production","productName":"Medium","publicUrl":"https:\u002F\u002Fcdn-client.medium.com\u002Flite","authDomain":"medium.com","authGoogleClientId":"216296035834-k1k6qe060s2tp2a2jam4ljdcms00sttg.apps.googleusercontent.com","favicon":"production","iosAppId":"828256236","glyphUrl":"https:\u002F\u002Fglyph.medium.com","branchKey":"key_live_ofxXr2qTrrU9NqURK8ZwEhknBxiI6KBm","algolia":{"appId":"MQ57UUUQZ2","apiKeySearch":"********************************","indexPrefix":"medium_","host":"-dsn.algolia.net"},"recaptchaKey":"6Lfc37IUAAAAAKGGtC6rLS13R1Hrw_BqADfS1LRk","recaptcha3Key":"6Lf8R9wUAAAAABMI_85Wb8melS7Zj6ziuf99Yot5","recaptchaEnterpriseKeyId":"6Le-uGgpAAAAAPprRaokM8AKthQ9KNGdoxaGUvVp","datadog":{"applicationId":"6702d87d-a7e0-42fe-bbcb-95b469547ea0","clientToken":"pub853ea8d17ad6821d9f8f11861d23dfed","rumToken":"pubf9cc52896502b9413b68ba36fc0c7162","context":{"deployment":{"target":"production","tag":"main-20250829-185947-c929e6f698","commit":"c929e6f698a22d0c3be06d191c5c594e2e313e6d"}},"datacenter":"us"},"googleAdsCode":"AW-17106321204","googleAnalyticsCode":"G-7JY7T788PK","googlePay":{"apiVersion":"2","apiVersionMinor":"0","merchantId":"BCR2DN6TV7EMTGBM","merchantName":"Medium","instanceMerchantId":"13685562959212738550"},"applePay":{"version":3},"signInWallCustomDomainCollectionIds":["3a8144eabfe3","336d898217ee","61061eb0c96b","138adf9c44c","819cc2aaeee0"],"mediumMastodonDomainName":"me.dm","mediumOwnedAndOperatedCollectionIds":["8a9336e5bb4","b7e45b22fec3","193b68bd4fba","8d6b8a439e32","54c98c43354d","3f6ecf56618","d944778ce714","92d2092dc598","ae2a65f35510","1285ba81cada","544c7006046e","fc8964313712","40187e704f1c","88d9857e584e","7b6769f2748b","bcc38c8f6edf","cef6983b292","cb8577c9149e","444d13b52878","713d7dbc99b0","ef8e90590e66","191186aaafa0","55760f21cdc5","9dc80918cc93","bdc4052bbdba","8ccfed20cbb2"],"tierOneDomains":["medium.com","thebolditalic.com","arcdigital.media","towardsdatascience.com","uxdesign.cc","codeburst.io","psiloveyou.xyz","writingcooperative.com","entrepreneurshandbook.co","prototypr.io","betterhumans.coach.me","theascent.pub"],"topicsToFollow":["d61cf867d93f","8a146bc21b28","1eca0103fff3","4d562ee63426","aef1078a3ef5","e15e46793f8d","6158eb913466","55f1c20aba7a","3d18b94f6858","4861fee224fd","63c6f1f93ee","1d98b3a9a871","decb52b64abf","ae5d4995e225","830cded25262"],"topicToTagMappings":{"accessibility":"accessibility","addiction":"addiction","android-development":"android-development","art":"art","artificial-intelligence":"artificial-intelligence","astrology":"astrology","basic-income":"basic-income","beauty":"beauty","biotech":"biotech","blockchain":"blockchain","books":"books","business":"business","cannabis":"cannabis","cities":"cities","climate-change":"climate-change","comics":"comics","coronavirus":"coronavirus","creativity":"creativity","cryptocurrency":"cryptocurrency","culture":"culture","cybersecurity":"cybersecurity","data-science":"data-science","design":"design","digital-life":"digital-life","disability":"disability","economy":"economy","education":"education","equality":"equality","family":"family","feminism":"feminism","fiction":"fiction","film":"film","fitness":"fitness","food":"food","freelancing":"freelancing","future":"future","gadgets":"gadgets","gaming":"gaming","gun-control":"gun-control","health":"health","history":"history","humor":"humor","immigration":"immigration","ios-development":"ios-development","javascript":"javascript","justice":"justice","language":"language","leadership":"leadership","lgbtqia":"lgbtqia","lifestyle":"lifestyle","machine-learning":"machine-learning","makers":"makers","marketing":"marketing","math":"math","media":"media","mental-health":"mental-health","mindfulness":"mindfulness","money":"money","music":"music","neuroscience":"neuroscience","nonfiction":"nonfiction","outdoors":"outdoors","parenting":"parenting","pets":"pets","philosophy":"philosophy","photography":"photography","podcasts":"podcast","poetry":"poetry","politics":"politics","privacy":"privacy","product-management":"product-management","productivity":"productivity","programming":"programming","psychedelics":"psychedelics","psychology":"psychology","race":"race","relationships":"relationships","religion":"religion","remote-work":"remote-work","san-francisco":"san-francisco","science":"science","self":"self","self-driving-cars":"self-driving-cars","sexuality":"sexuality","social-media":"social-media","society":"society","software-engineering":"software-engineering","space":"space","spirituality":"spirituality","sports":"sports","startups":"startup","style":"style","technology":"technology","transportation":"transportation","travel":"travel","true-crime":"true-crime","tv":"tv","ux":"ux","venture-capital":"venture-capital","visual-design":"visual-design","work":"work","world":"world","writing":"writing"},"defaultImages":{"avatar":{"imageId":"1*dmbNkD5D-u45r44go_cf0g.png","height":150,"width":150},"orgLogo":{"imageId":"7*V1_7XP4snlmqrc_0Njontw.png","height":110,"width":500},"postLogo":{"imageId":"167cff2a3d17ac1e64d0762539978f2d54c0058886e8b3c8a03a725a83012ec0","height":630,"width":1200},"postPreviewImage":{"imageId":"bc1f8416df0cad099e43cda2872716e5864f18a73bda2a7547ea082aca9b5632","height":630,"width":1200}},"embeddedPostIds":{"coronavirus":"cd3010f9d81f"},"sharedCdcMessaging":{"COVID_APPLICABLE_TAG_SLUGS":[],"COVID_APPLICABLE_TOPIC_NAMES":[],"COVID_APPLICABLE_TOPIC_NAMES_FOR_TOPIC_PAGE":[],"COVID_MESSAGES":{"tierA":{"text":"For more information on the novel coronavirus and Covid-19, visit cdc.gov.","markups":[{"start":66,"end":73,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]},"tierB":{"text":"Anyone can publish on Medium per our Policies, but we don’t fact-check every story. For more info about the coronavirus, see cdc.gov.","markups":[{"start":37,"end":45,"href":"https:\u002F\u002Fhelp.medium.com\u002Fhc\u002Fen-us\u002Fcategories\u002F201931128-Policies-Safety"},{"start":125,"end":132,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]},"paywall":{"text":"This article has been made free for everyone, thanks to Medium Members. For more information on the novel coronavirus and Covid-19, visit cdc.gov.","markups":[{"start":56,"end":70,"href":"https:\u002F\u002Fmedium.com\u002Fmembership"},{"start":138,"end":145,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]},"unbound":{"text":"This article is free for everyone, thanks to Medium Members. For more information on the novel coronavirus and Covid-19, visit cdc.gov.","markups":[{"start":45,"end":59,"href":"https:\u002F\u002Fmedium.com\u002Fmembership"},{"start":127,"end":134,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]}},"COVID_BANNER_POST_ID_OVERRIDE_WHITELIST":["3b31a67bff4a"]},"sharedVoteMessaging":{"TAGS":["politics","election-2020","government","us-politics","election","2020-presidential-race","trump","donald-trump","democrats","republicans","congress","republican-party","democratic-party","biden","joe-biden","maga"],"TOPICS":["politics","election"],"MESSAGE":{"text":"Find out more about the U.S. election results here.","markups":[{"start":46,"end":50,"href":"https:\u002F\u002Fcookpolitical.com\u002F2020-national-popular-vote-tracker"}]},"EXCLUDE_POSTS":["397ef29e3ca5"]},"embedPostRules":[],"recircOptions":{"v1":{"limit":3},"v2":{"limit":8}},"braintreeClientKey":"production_zjkj96jm_m56f8fqpf7ngnrd4","braintree":{"enabled":true,"merchantId":"m56f8fqpf7ngnrd4","merchantAccountId":{"usd":"AMediumCorporation_instant","eur":"amediumcorporation_EUR","cad":"amediumcorporation_CAD"},"publicKey":"ds2nn34bg2z7j5gd","braintreeEnvironment":"production","dashboardUrl":"https:\u002F\u002Fwww.braintreegateway.com\u002Fmerchants","gracePeriodDurationInDays":14,"mediumMembershipPlanId":{"monthly":"ce105f8c57a3","monthlyV2":"e8a5e126-792b-4ee6-8fba-d574c1b02fc5","monthlyWithTrial":"d5ee3dbe3db8","monthlyPremium":"fa741a9b47a2","yearly":"a40ad4a43185","yearlyV2":"3815d7d6-b8ca-4224-9b8c-182f9047866e","yearlyStaff":"d74fb811198a","yearlyWithTrial":"b3bc7350e5c7","yearlyPremium":"e21bd2c12166","monthlyOneYearFree":"e6c0637a-2bad-4171-ab4f-3c268633d83c","monthly25PercentOffFirstYear":"235ecc62-0cdb-49ae-9378-726cd21c504b","monthly20PercentOffFirstYear":"ba518864-9c13-4a99-91ca-411bf0cac756","monthly15PercentOffFirstYear":"594c029b-9f89-43d5-88f8-8173af4e070e","monthly10PercentOffFirstYear":"c6c7bc9a-40f2-4b51-8126-e28511d5bdb0","monthlyForStudents":"629ebe51-da7d-41fd-8293-34cd2f2030a8","yearlyOneYearFree":"78ba7be9-0d9f-4ece-aa3e-b54b826f2bf1","yearly25PercentOffFirstYear":"2dbb010d-bb8f-4eeb-ad5c-a08509f42d34","yearly20PercentOffFirstYear":"47565488-435b-47f8-bf93-40d5fbe0ebc8","yearly15PercentOffFirstYear":"8259809b-0881-47d9-acf7-6c001c7f720f","yearly10PercentOffFirstYear":"9dd694fb-96e1-472c-8d9e-3c868d5c1506","yearlyForStudents":"e29345ef-ab1c-4234-95c5-70e50fe6bc23","monthlyCad":"p52orjkaceei","yearlyCad":"h4q9g2up9ktt"},"braintreeDiscountId":{"oneMonthFree":"MONTHS_FREE_01","threeMonthsFree":"MONTHS_FREE_03","sixMonthsFree":"MONTHS_FREE_06","fiftyPercentOffOneYear":"FIFTY_PERCENT_OFF_ONE_YEAR"},"3DSecureVersion":"2","defaultCurrency":"usd","providerPlanIdCurrency":{"4ycw":"usd","rz3b":"usd","3kqm":"usd","jzw6":"usd","c2q2":"usd","nnsw":"usd","q8qw":"usd","d9y6":"usd","fx7w":"cad","nwf2":"cad"}},"paypalClientId":"AXj1G4fotC2GE8KzWX9mSxCH1wmPE3nJglf4Z2ig_amnhvlMVX87otaq58niAg9iuLktVNF_1WCMnN7v","paypal":{"host":"https:\u002F\u002Fapi.paypal.com:443","clientMode":"production","serverMode":"live","webhookId":"4G466076A0294510S","monthlyPlan":{"planId":"P-9WR0658853113943TMU5FDQA","name":"Medium Membership (Monthly) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"yearlyPlan":{"planId":"P-7N8963881P8875835MU5JOPQ","name":"Medium Membership (Annual) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"oneYearGift":{"name":"Medium Membership (1 Year, Digital Gift Code)","description":"Unlimited access to the best and brightest stories on Medium. Gift codes can be redeemed at medium.com\u002Fredeem.","price":"50.00","currency":"USD","sku":"membership-gift-1-yr"},"oldMonthlyPlan":{"planId":"P-96U02458LM656772MJZUVH2Y","name":"Medium Membership (Monthly)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"oldYearlyPlan":{"planId":"P-59P80963JF186412JJZU3SMI","name":"Medium Membership (Annual)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"monthlyPlanWithTrial":{"planId":"P-66C21969LR178604GJPVKUKY","name":"Medium Membership (Monthly) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"yearlyPlanWithTrial":{"planId":"P-6XW32684EX226940VKCT2MFA","name":"Medium Membership (Annual) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"oldMonthlyPlanNoSetupFee":{"planId":"P-4N046520HR188054PCJC7LJI","name":"Medium Membership (Monthly)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"oldYearlyPlanNoSetupFee":{"planId":"P-7A4913502Y5181304CJEJMXQ","name":"Medium Membership (Annual)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"sdkUrl":"https:\u002F\u002Fwww.paypal.com\u002Fsdk\u002Fjs"},"stripePublishableKey":"pk_live_7FReX44VnNIInZwrIIx6ghjl","log":{"json":true,"level":"info"},"imageUploadMaxSizeMb":25,"staffPicks":{"title":"Staff Picks","catalogId":"c7bc6e1ee00f"}},"session":{"xsrf":""}}</script><script>window.__APOLLO_STATE__ = {"ROOT_QUERY":{"__typename":"Query","viewer":null,"collectionByDomainOrSlug({\"domainOrSlug\":\"akiboy96.medium.com\"})":null,"postResult({\"id\":\"ca4e0fbb8ca9\"})":{"__ref":"Post:ca4e0fbb8ca9"}},"LinkedAccounts:e0dbacebbef4":{"__typename":"LinkedAccounts","mastodon":null,"id":"e0dbacebbef4"},"User:e0dbacebbef4":{"__typename":"User","id":"e0dbacebbef4","linkedAccounts":{"__ref":"LinkedAccounts:e0dbacebbef4"},"isSuspended":false,"name":"Aakash Kedia","imageId":"0*emaZ2wHvCzhl_Apj","customDomainState":{"__typename":"CustomDomainState","live":{"__typename":"CustomDomain","domain":"akiboy96.medium.com"}},"hasSubdomain":true,"username":"akiboy96","verifications":{"__typename":"VerifiedInfo","isBookAuthor":false},"newsletterV3":null,"socialStats":{"__typename":"SocialStats","followerCount":19,"followingCount":18,"collectionFollowingCount":27},"bio":"CS, music and football in no particular order","membership":null,"allowNotes":true,"viewerEdge":{"__ref":"UserViewerEdge:userId:e0dbacebbef4-viewerId:lo_40f07550e3be"},"twitterScreenName":""},"Paragraph:89668139779a_0":{"__typename":"Paragraph","id":"89668139779a_0","name":"b51b","type":"H3","href":null,"layout":null,"metadata":null,"text":"Building your Long Term Portfolio using Unsupervised ML with Community Detection Analysis in Python & R","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_1":{"__typename":"Paragraph","id":"89668139779a_1","name":"e6c5","type":"P","href":null,"layout":null,"metadata":null,"text":"We finally get to the Portfolio Building where we can try to beat the SPY average returns Year over Yer (YoY). The whole code base can be found here:","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":111,"end":148,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fakiboy96\u002Fstock-network-analysis","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":22,"end":40,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*cStacbcB7ylqz6gutZvQkQ.png":{"__typename":"ImageMetadata","id":"1*cStacbcB7ylqz6gutZvQkQ.png","originalHeight":510,"originalWidth":680,"focusPercentX":null,"focusPercentY":null,"alt":null},"Paragraph:89668139779a_2":{"__typename":"Paragraph","id":"89668139779a_2","name":"942f","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*cStacbcB7ylqz6gutZvQkQ.png"},"text":"","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_3":{"__typename":"Paragraph","id":"89668139779a_3","name":"d8f0","type":"PQ","href":null,"layout":null,"metadata":null,"text":"To build the portfolio based on the correlation of stock prices in the S&P 500, we will use network centrality as a measure to understand how the stocks are better integrated with one and other.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_4":{"__typename":"Paragraph","id":"89668139779a_4","name":"f5b3","type":"P","href":null,"layout":null,"metadata":null,"text":"Using this diagram below you can understand the process better.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*JOtvkmn_br5pP9AMm2jnIg.png":{"__typename":"ImageMetadata","id":"1*JOtvkmn_br5pP9AMm2jnIg.png","originalHeight":516,"originalWidth":916,"focusPercentX":null,"focusPercentY":null,"alt":null},"Paragraph:89668139779a_5":{"__typename":"Paragraph","id":"89668139779a_5","name":"94f1","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*JOtvkmn_br5pP9AMm2jnIg.png"},"text":"","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_6":{"__typename":"Paragraph","id":"89668139779a_6","name":"83a2","type":"P","href":null,"layout":null,"metadata":null,"text":"We have done everything to required from data gathering, preprocessing to applying unsupervised clustering model to create the communities of stocks in Part 1 and Part 2 of this story. We will now be using the Louvain method as discussed before to calculate centrality of the stocks using 4 very important measures.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":151,"end":159,"href":"https:\u002F\u002Fmedium.com\u002Fswlh\u002Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-1-a8a3366dc5a5","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":163,"end":169,"href":"https:\u002F\u002Fmedium.com\u002F@akiboy96\u002Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-2-9b5a5dbe5d5b","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":152,"end":156,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":157,"end":158,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":163,"end":169,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":210,"end":217,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_7":{"__typename":"Paragraph","id":"89668139779a_7","name":"4426","type":"BQ","href":null,"layout":null,"metadata":null,"text":"For this we built an algorithm that applies equal weightage to the score of the four centralities we considered while building our portfolio. The 4 being:","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_8":{"__typename":"Paragraph","id":"89668139779a_8","name":"c55a","type":"BQ","href":null,"layout":null,"metadata":null,"text":"Degree Centrality — that assigns a score based simply on the number of links held by each node.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_9":{"__typename":"Paragraph","id":"89668139779a_9","name":"61fa","type":"BQ","href":null,"layout":null,"metadata":null,"text":"Betweenness Centrality — which assigns a score based on the number of times a node lies on the shortest path between other nodes.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_10":{"__typename":"Paragraph","id":"89668139779a_10","name":"9408","type":"BQ","href":null,"layout":null,"metadata":null,"text":"Closeness Centrality — which scores each node based on their ‘closeness’ to all other nodes in the network.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_11":{"__typename":"Paragraph","id":"89668139779a_11","name":"1222","type":"BQ","href":null,"layout":null,"metadata":null,"text":"Eigen Centrality — which measures a node’s influence based on the number of links it has to other nodes in the network.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_12":{"__typename":"Paragraph","id":"89668139779a_12","name":"e939","type":"P","href":null,"layout":null,"metadata":null,"text":"Together these centralities give us a score that help us determine the overall influence of a stock on the network. We can see these with the code below:","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_13":{"__typename":"Paragraph","id":"89668139779a_13","name":"c168","type":"PRE","href":null,"layout":null,"metadata":null,"text":"stocks_cross_corr, _, _ = calculate_corr(df_stock_prices,1, len(df_stock_prices), 'pearson')\nstocks_cross_corr = stocks_cross_corr[1]cor_thresold = 0.7\nG = build_graph(stocks_cross_corr, cor_thresold)\npartition = community.best_partition(G)\nmodularity = community.modularity(partition, G)\nvalues = [partition.get(node) for node in G.nodes()]\nplt.figure(figsize=(10,10))\nnx.draw_spring(G, cmap = plt.get_cmap('jet'), node_color = values, node_size=30, with_labels=False)","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":328,"end":330,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_14":{"__typename":"Paragraph","id":"89668139779a_14","name":"0083","type":"PRE","href":null,"layout":null,"metadata":null,"text":"print(modularity)\nprint(\"Total number of Communities=\", len(G.nodes()))\n\ndict_betwenness_centrality = nx.betweenness_centrality(G)\ndict_degree_centrality = nx.degree_centrality(G)\ndict_closeness_centrality = nx.closeness_centrality(G)\ndict_eigenvector_centrality = nx.eigenvector_centrality(G)\nprint(\"dict_degree_centrality: \", dict_degree_centrality)\nprint(\"dict_closeness_centrality: \", dict_closeness_centrality)\nprint(\"dict_eigenvector_centrality: \", dict_eigenvector_centrality)\nprint(\"dict_betweenness_centrality: \", dict_betwenness_centrality)\n","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*cBx7WlXo_MYK_xFao1RF_w.png":{"__typename":"ImageMetadata","id":"1*cBx7WlXo_MYK_xFao1RF_w.png","originalHeight":626,"originalWidth":955,"focusPercentX":null,"focusPercentY":null,"alt":null},"Paragraph:89668139779a_15":{"__typename":"Paragraph","id":"89668139779a_15","name":"dbbf","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*cBx7WlXo_MYK_xFao1RF_w.png"},"text":"","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_16":{"__typename":"Paragraph","id":"89668139779a_16","name":"0d6e","type":"H4","href":null,"layout":null,"metadata":null,"text":"Once the four types of centralities are obtained we will build our own portfolio determining algorithm using these centralities in an equal weightage sense. To do that we combine the dictionary data structure of our 4 centralties and use a for loop to iterate through each stock and add the four different centralities to create a final centrality metric. Since we are using equal weightage the formula would use a simple sum and not be any assigned any weights.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_17":{"__typename":"Paragraph","id":"89668139779a_17","name":"fcea","type":"PRE","href":null,"layout":null,"metadata":null,"text":"#Portfolio Formula: \nc_dict = dict([(k, [dict_betwenness_centrality[k], dict_eigenvector_centrality[k], dict_degree_centrality[k], dict_closeness_centrality[k] ]) for k in dict_betwenness_centrality])\n#print(c_dict)    \n    \nC_total = {}\nfor key in c_dict: \n    C_total[key] = sum(c_dict[key]) \n        \n\nprint(\"The Centrality total for stocks are:\", C_total)   \n\nnewDict = dict(filter(lambda elem: elem[1] \u003E 0, C_total.items()))\nprint(\"Stocks greater than 0.3 centrality are\",newDict)\nprint(len(newDict))\n\ndf_centrality = pd.DataFrame(list(newDict.items()),columns = ['Symbol','Centrality']) \ndf_centrality.sort_values(by='Centrality', ascending=False)\n#df_centrality.head(20)\n#type(df_centrality['Centrality'])\ndf_centrality.to_csv('centrality_of_stocks_0.7cor.csv',index=False)","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":169,"end":171,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":246,"end":248,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":0,"end":20,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":201,"end":219,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":654,"end":677,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":678,"end":712,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*sKMBeKndZl8V-Gzz-kJ15Q.png":{"__typename":"ImageMetadata","id":"1*sKMBeKndZl8V-Gzz-kJ15Q.png","originalHeight":603,"originalWidth":1032,"focusPercentX":null,"focusPercentY":null,"alt":null},"Paragraph:89668139779a_18":{"__typename":"Paragraph","id":"89668139779a_18","name":"e61f","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*sKMBeKndZl8V-Gzz-kJ15Q.png"},"text":"","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_19":{"__typename":"Paragraph","id":"89668139779a_19","name":"1a47","type":"P","href":null,"layout":null,"metadata":null,"text":"As seen from the image above Apple (AAPL) located on the 7th index has a total centrality score of 0.126. This gives us a good idea on how the stock is placed in our network and community, in terms of degrees of connections, betweenness and closeness amongst nodes along with their eigenvalues","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":29,"end":41,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_20":{"__typename":"Paragraph","id":"89668139779a_20","name":"10fb","type":"P","href":null,"layout":null,"metadata":null,"text":"Using the centrality score from our algorithm, we further filtered the stocks by sector so that we could bring diversification in our portfolio as well.Making use of the tidyquant package in R we then compared our portfolio to the S&P500’s in the time frame of monthly returns. We were happy to see that our index over the last 5 years has beaten the benchmark S&P 500 and has indeed provided a better return on investment percentage.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":191,"end":192,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_21":{"__typename":"Paragraph","id":"89668139779a_21","name":"a87a","type":"P","href":null,"layout":null,"metadata":null,"text":"The whole code base for the portfolio making can be found here:","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":58,"end":63,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fakiboy96\u002Fportfolio-management","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_22":{"__typename":"Paragraph","id":"89668139779a_22","name":"c338","type":"P","href":null,"layout":null,"metadata":null,"text":"We will start by loading the required packages in R","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":50,"end":51,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_23":{"__typename":"Paragraph","id":"89668139779a_23","name":"3ca9","type":"PRE","href":null,"layout":null,"metadata":null,"text":"# This R environment comes with many helpful analytics packages installed\n# It is defined by the kaggle\u002Frstats Docker image: https:\u002F\u002Fgithub.com\u002Fkaggle\u002Fdocker-rstats\n# For example, here's a helpful package to load\n\nlibrary(tidyverse)\nlibrary(tidyquant)\nlibrary(jsonlite)\nlibrary(tidyverse)\nlibrary(readr)\nlibrary(igraph)\nlibrary(dplyr)\nlibrary(lubridate)\nlibrary(data.table)\nlibrary(Quandl) # metapackage of all tidyverse packages\n\n# Input data files are available in the read-only \"..\u002Finput\u002F\" directory\n# For example, running this (by clicking run or pressing Shift+Enter) will list all files under the input directory\n\nlist.files(path = \"..\u002Finput\")\n\n# You can write up to 5GB to the current directory (\u002Fkaggle\u002Fworking\u002F) that gets preserved as output when you create a version using \"Save & Run All\" \n# You can also write temporary files to \u002Fkaggle\u002Ftemp\u002F, but they won't be saved outside of the current session","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":0,"end":73,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":74,"end":164,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":165,"end":212,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":390,"end":429,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":431,"end":502,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":503,"end":618,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":651,"end":800,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":801,"end":910,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_24":{"__typename":"Paragraph","id":"89668139779a_24","name":"3f3d","type":"P","href":null,"layout":null,"metadata":null,"text":"We will then load the dataset that we made using different correlation thresholds as shown below like 0.5, 0.6 and 0.7. This will be used in a variety of examples as shown below to make your portfolio. These datasets can be found on kaggle here:","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":208,"end":216,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fakiboy96\u002Fcentrality-of-stocks-network-analysis","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":240,"end":244,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fakiboy96\u002Fportfolio-management","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":208,"end":216,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":102,"end":118,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_25":{"__typename":"Paragraph","id":"89668139779a_25","name":"e4ea","type":"PRE","href":null,"layout":null,"metadata":null,"text":"#DataLoad \nstockprices \u003C- read.csv(\"..\u002Finput\u002Fusstockprices\u002Fstocks_price_final.csv\")\ncentrality0.5 \u003C- read.csv(\"..\u002Finput\u002Fcentrality-of-stocks-network-analysis\u002Fcentrality_of_stocks_0.5cor.csv\")\ncentrality0.6 \u003C- read.csv(\"..\u002Finput\u002Fcentrality-of-stocks-network-analysis\u002Fcentrality_of_stocks_0.6cor.csv\")\ncentrality0.7 \u003C- read.csv(\"..\u002Finput\u002Fcentrality-of-stocks-network-analysis\u002Fcentrality_of_stocks_0.7cor.csv\")","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":0,"end":10,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_26":{"__typename":"Paragraph","id":"89668139779a_26","name":"918e","type":"P","href":null,"layout":null,"metadata":null,"text":"We will then do some data cleaning by joining stocks with their centralities. Once that is done, we can begin the portfolio generation and comparison to SPY depending on whichever correlation you want of the stocks","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_27":{"__typename":"Paragraph","id":"89668139779a_27","name":"49a6","type":"PRE","href":null,"layout":null,"metadata":null,"text":"#Merges Centtraility values with other potential useful information like market cap and sector for further filtering\nstocks \u003C- stockprices[!duplicated(stockprices$symbol), ] # removes duplicate symbols\nIndex0.5cor \u003C- merge(stocks, centrality0.5, by.x = \"symbol\", by.y = \"Symbol\")\nIndex0.6cor \u003C- merge(stocks, centrality0.6, by.x = \"symbol\", by.y = \"Symbol\")\nIndex0.7cor \u003C- merge(stocks, centrality0.7, by.x = \"symbol\", by.y = \"Symbol\")\n#head(Index0.6cor)\nIndexGet0.5 \u003C- as.character(Index0.5cor$symbol)\nIndexGet0.6 \u003C- as.character(Index0.6cor$symbol)\nIndexGet0.7 \u003C- as.character(Index0.7cor$symbol)","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":0,"end":116,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":174,"end":201,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":436,"end":454,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_28":{"__typename":"Paragraph","id":"89668139779a_28","name":"c18b","type":"PRE","href":null,"layout":null,"metadata":null,"text":"#Portfolio Testing\n\n##0.5 correlation centrality prices\nstockindex0.5 \u003C- tq_get(IndexGet0.5, get=\"stock.prices\", from = \"2015-07-01\",warnings = FALSE,\n                             stringsAsFactors = FALSE) %\u003E%\n  group_by(symbol) %\u003E%\n  tq_transmute(select=adjusted,\n               mutate_fun=periodReturn,\n               period=\"monthly\",\n               col_rename = \"monthly_return\")\n\nstockindex0.5\n\n##0.6 correlation centrality prices\nstockindex0.6 \u003C- tq_get(IndexGet0.6, get=\"stock.prices\", from = \"2015-07-01\",warnings = FALSE,\n                             stringsAsFactors = FALSE) %\u003E%\n  group_by(symbol) %\u003E%\n  tq_transmute(select=adjusted,\n               mutate_fun=periodReturn,\n               period=\"monthly\",\n               col_rename = \"monthly_return\")\n\n\n##0.7 correlation centrality prices\nstockindex0.7 \u003C- tq_get(IndexGet0.7, get=\"stock.prices\", from = \"2015-07-01\",warnings = FALSE,\n                             stringsAsFactors = FALSE) %\u003E%\n  group_by(symbol) %\u003E%\n  tq_transmute(select=adjusted,\n               mutate_fun=periodReturn,\n               period=\"monthly\",\n               col_rename = \"monthly_return\")\n\n\n##Base Portfolio to compare \n\nbaseline_returns_monthly \u003C- \"SPY\" %\u003E%\n    tq_get(get  = \"stock.prices\",\n           from = \"2015-07-01\", warnings = FALSE,stringsAsFactors = FALSE) %\u003E%\n    tq_transmute(select     = adjusted, \n                 mutate_fun = periodReturn, \n                 period     = \"monthly\", \n                 col_rename = \"spy_monthly_return\")\n\nbaseline_returns_monthly","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":0,"end":18,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":20,"end":55,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":400,"end":435,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":766,"end":801,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":1132,"end":1160,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_29":{"__typename":"Paragraph","id":"89668139779a_29","name":"c6c8","type":"P","href":null,"layout":null,"metadata":null,"text":"Portfolio calculation from above method","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_30":{"__typename":"Paragraph","id":"89668139779a_30","name":"1870","type":"PRE","href":null,"layout":null,"metadata":null,"text":"portfolio_returns_monthly0.5 \u003C- stockindex0.5 %\u003E%\n    tq_portfolio(assets_col  = symbol, \n                 returns_col = monthly_return, \n                 col_rename  = \"portfolio-monthly\")\n\n\nportfolio_returns_monthly0.6 \u003C- stockindex0.6 %\u003E%\n    tq_portfolio(assets_col  = symbol, \n                 returns_col = monthly_return, \n                 col_rename  = \"portfolio-monthly\")\n\n\nportfolio_returns_monthly0.7 \u003C- stockindex0.7 %\u003E%\n    tq_portfolio(assets_col  = symbol, \n                 returns_col = monthly_return, \n                 col_rename  = \"portfolio-monthly\")\n\n\n#Portfolio Compare\nstock0.5indexVSSPY \u003C- left_join(portfolio_returns_monthly0.5, \n                                   baseline_returns_monthly,\n                                   by = \"date\")\n\nstock0.6indexVSSPY \u003C- left_join(portfolio_returns_monthly0.6, \n                                   baseline_returns_monthly,\n                                   by = \"date\")\n\nstock0.7indexVSSPY \u003C- left_join(portfolio_returns_monthly0.7, \n                                   baseline_returns_monthly,\n                                   by = \"date\")\n#stock0.5indexVSSPY\n\nggplot(stock0.5indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = \"blue\")+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = \"red\")\nggplot(stock0.6indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = \"blue\")+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = \"red\")\nggplot(stock0.7indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = \"blue\")+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = \"red\")","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":576,"end":594,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":1113,"end":1132,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_31":{"__typename":"Paragraph","id":"89668139779a_31","name":"49a2","type":"P","href":null,"layout":null,"metadata":null,"text":"Playground here to optimize your portfolio! Choose the correlation you want from the datasets above and then further optimize your portfolio by filtering the stocks by sector and their centrality value. This will help you create a niche list of stocks that could potentially beat the SPY returns YoY of 8.93%.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":283,"end":308,"href":"https:\u002F\u002Fwww.investopedia.com\u002Farticles\u002Finvesting\u002F122215\u002Fspy-spdr-sp-500-trust-etf.asp#:~:text=SPY%20Performance&text=The%20SPDR%20S%26P%20500%20ETF%20Trust%20(SPY)%20has%20generated%20an,average%20annual%20returns%20of%208.93%25.","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":0,"end":309,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_32":{"__typename":"Paragraph","id":"89668139779a_32","name":"08d1","type":"PRE","href":null,"layout":null,"metadata":null,"text":"##PLAYGROUND - LETS DO TRIAL AND ERROR HERE TO BEAT THE SPY GRAPH in RETURNS\n\ncentrality_filter \u003C- Index0.6cor\n#filter(Centrality \u003E 0.8 & Centrality \u003C 0.5)\ncentrality_filter\n\nIndexGet0.6 \u003C- as.character(centrality_filter$symbol)\n\nstockindex0.6 \u003C- tq_get(IndexGet0.6, get=\"stock.prices\", from = \"2015-07-01\",warnings = FALSE,\n                             stringsAsFactors = FALSE) %\u003E%\n  group_by(symbol) %\u003E%\n  tq_transmute(select=adjusted,\n               mutate_fun=periodReturn,\n               period=\"monthly\",\n               col_rename = \"monthly_return\")\n\nportfolio_returns_monthly0.6 \u003C- stockindex0.6 %\u003E%\n    tq_portfolio(assets_col  = symbol, \n                 returns_col = monthly_return, \n                 col_rename  = \"portfolio-monthly\")\n\nstock0.6indexVSSPY \u003C- left_join(portfolio_returns_monthly0.6, \n                                   baseline_returns_monthly,\n                                   by = \"date\")\n\nggplot(stock0.6indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = \"blue\")+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = \"red\")","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":0,"end":76,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":111,"end":155,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_33":{"__typename":"Paragraph","id":"89668139779a_33","name":"34d5","type":"PQ","href":null,"layout":null,"metadata":null,"text":"This is the code I wrote to achieve a portfolio that could beat the SPY returns over a 5 year period from July 1st 2015 to July 22nd 2020","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_34":{"__typename":"Paragraph","id":"89668139779a_34","name":"2462","type":"PRE","href":null,"layout":null,"metadata":null,"text":"centrality_filter \u003C- Index0.5cor %\u003E%\nfilter((sector == \"Health Care\" & Centrality \u003E 0.4) | (sector == \"Technology\" & Centrality \u003E 0.42) | (sector == \"Consumer Services\" & Centrality \u003E 0.49) | (sector == \"Finance\" & Centrality \u003E 0.5)  | (sector == \"Transportation\" & Centrality \u003E 0.5) | (sector == \"Capital Goods\" & Centrality \u003E 0.35) | (sector == \"Miscellaneous\" & Centrality \u003E 0.5) | (sector == \"Basic Industries\" & Centrality \u003E 0.39) | (sector == \"Public Utilities\" & Centrality \u003E 0.36) | (sector == \"Consumer Durables\" & Centrality \u003E 0.3)| (sector == \"Consumer Non-Durables\" & Centrality \u003E 0.25))\ncentrality_filter\n\nIndexGet0.5 \u003C- as.character(centrality_filter$symbol)\n\nstockindex0.5 \u003C- tq_get(IndexGet0.5, get=\"stock.prices\", from = \"2015-07-01\", till = \"2020-07-22\",warnings = FALSE,\n                             stringsAsFactors = FALSE) %\u003E%\n  group_by(symbol) %\u003E%\n  tq_transmute(select=adjusted,\n               mutate_fun=periodReturn,\n               period=\"monthly\",\n               col_rename = \"monthly_return\")\n\nportfolio_returns_monthly0.5 \u003C- stockindex0.5 %\u003E%\n    tq_portfolio(assets_col  = symbol, \n                 returns_col = monthly_return, \n                 col_rename  = \"portfolio-monthly\")\n\nstock0.5indexVSSPY \u003C- left_join(portfolio_returns_monthly0.5, \n                                   baseline_returns_monthly,\n                                   by = \"date\")\n\nstock0.5indexVSSPY$`portfolio-monthly` \u003C- stock0.5indexVSSPY$`portfolio-monthly` * 100\nstock0.5indexVSSPY$spy_monthly_return \u003C- stock0.5indexVSSPY$spy_monthly_return * 100\n\nstock0.5indexVSSPY \u003C- stock0.5indexVSSPY[-c(14), ]\n\nreturns \u003C- ggplot(stock0.5indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = \"blue\")+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = \"red\") + ggtitle(\"Portfolio vs S&P 500 Returns over 5 years\") + labs(y=\"Returns Percenatge\", x = \"Date\") \n\nplot(returns)","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*lhR-zvcYQhsOswSL8unAJw.png":{"__typename":"ImageMetadata","id":"1*lhR-zvcYQhsOswSL8unAJw.png","originalHeight":432,"originalWidth":700,"focusPercentX":null,"focusPercentY":null,"alt":null},"Paragraph:89668139779a_35":{"__typename":"Paragraph","id":"89668139779a_35","name":"f598","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*lhR-zvcYQhsOswSL8unAJw.png"},"text":"Blue Line is my Portfolio, Red line is SPY","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_36":{"__typename":"Paragraph","id":"89668139779a_36","name":"d0c3","type":"P","href":null,"layout":null,"metadata":null,"text":"From the plot above we can visualize a more volatile trend for my portfolio which is the blue line as comp to red which’s the SPY returns. But overall, the blue line has a higher Return on Investment (ROI) percentage over 5 years.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:89668139779a_37":{"__typename":"Paragraph","id":"89668139779a_37","name":"7fff","type":"PQ","href":null,"layout":null,"metadata":null,"text":"The centralities as shown in the above code were chosen based on my predictions for future trends in terms of what sectors will do well and become important to society as a service.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"UserViewerEdge:userId:e0dbacebbef4-viewerId:lo_40f07550e3be":{"__typename":"UserViewerEdge","id":"userId:e0dbacebbef4-viewerId:lo_40f07550e3be","isMuting":false},"PostViewerEdge:postId:ca4e0fbb8ca9-viewerId:lo_40f07550e3be":{"__typename":"PostViewerEdge","shouldIndexPostForExternalSearch":true,"id":"postId:ca4e0fbb8ca9-viewerId:lo_40f07550e3be"},"Tag:stock-market":{"__typename":"Tag","id":"stock-market","displayTitle":"Stock Market","normalizedTagSlug":"stock-market"},"Tag:finance":{"__typename":"Tag","id":"finance","displayTitle":"Finance","normalizedTagSlug":"finance"},"Tag:network-as-a-service":{"__typename":"Tag","id":"network-as-a-service","displayTitle":"Network As A Service","normalizedTagSlug":"network-as-a-service"},"Tag:r":{"__typename":"Tag","id":"r","displayTitle":"R","normalizedTagSlug":"r"},"Tag:portfolio-management":{"__typename":"Tag","id":"portfolio-management","displayTitle":"Portfolio Management","normalizedTagSlug":"portfolio-management"},"User:d43c46db5b92":{"__typename":"User","id":"d43c46db5b92"},"Collection:f5af2b715248":{"__typename":"Collection","id":"f5af2b715248","creator":{"__ref":"User:d43c46db5b92"},"name":"The Startup"},"Post:ca4e0fbb8ca9":{"__typename":"Post","id":"ca4e0fbb8ca9","collection":null,"content({\"postMeteringOptions\":{\"referrer\":\"\"}})":{"__typename":"PostContent","isLockedPreviewOnly":false,"bodyModel":{"__typename":"RichText","sections":[{"__typename":"Section","name":"5a5a","startIndex":0,"textLayout":null,"imageLayout":null,"backgroundImage":null,"videoLayout":null,"backgroundVideo":null}],"paragraphs":[{"__ref":"Paragraph:89668139779a_0"},{"__ref":"Paragraph:89668139779a_1"},{"__ref":"Paragraph:89668139779a_2"},{"__ref":"Paragraph:89668139779a_3"},{"__ref":"Paragraph:89668139779a_4"},{"__ref":"Paragraph:89668139779a_5"},{"__ref":"Paragraph:89668139779a_6"},{"__ref":"Paragraph:89668139779a_7"},{"__ref":"Paragraph:89668139779a_8"},{"__ref":"Paragraph:89668139779a_9"},{"__ref":"Paragraph:89668139779a_10"},{"__ref":"Paragraph:89668139779a_11"},{"__ref":"Paragraph:89668139779a_12"},{"__ref":"Paragraph:89668139779a_13"},{"__ref":"Paragraph:89668139779a_14"},{"__ref":"Paragraph:89668139779a_15"},{"__ref":"Paragraph:89668139779a_16"},{"__ref":"Paragraph:89668139779a_17"},{"__ref":"Paragraph:89668139779a_18"},{"__ref":"Paragraph:89668139779a_19"},{"__ref":"Paragraph:89668139779a_20"},{"__ref":"Paragraph:89668139779a_21"},{"__ref":"Paragraph:89668139779a_22"},{"__ref":"Paragraph:89668139779a_23"},{"__ref":"Paragraph:89668139779a_24"},{"__ref":"Paragraph:89668139779a_25"},{"__ref":"Paragraph:89668139779a_26"},{"__ref":"Paragraph:89668139779a_27"},{"__ref":"Paragraph:89668139779a_28"},{"__ref":"Paragraph:89668139779a_29"},{"__ref":"Paragraph:89668139779a_30"},{"__ref":"Paragraph:89668139779a_31"},{"__ref":"Paragraph:89668139779a_32"},{"__ref":"Paragraph:89668139779a_33"},{"__ref":"Paragraph:89668139779a_34"},{"__ref":"Paragraph:89668139779a_35"},{"__ref":"Paragraph:89668139779a_36"},{"__ref":"Paragraph:89668139779a_37"}]},"validatedShareKey":"","shareKeyCreator":null},"creator":{"__ref":"User:e0dbacebbef4"},"inResponseToEntityType":null,"isLocked":false,"isMarkedPaywallOnly":false,"lockedSource":"LOCKED_POST_SOURCE_NONE","mediumUrl":"https:\u002F\u002Fakiboy96.medium.com\u002Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9","primaryTopic":null,"topics":[{"__typename":"Topic","slug":"data-science"},{"__typename":"Topic","slug":"programming"}],"isLimitedState":false,"isPublished":true,"allowResponses":true,"responsesLocked":false,"visibility":"PUBLIC","latestPublishedVersion":"89668139779a","postResponses":{"__typename":"PostResponses","count":0},"responseDistribution":"NOT_DISTRIBUTED","clapCount":71,"title":"Community Detection & Network Analysis of the Stock Market in Python & R — Part 3","isSeries":false,"sequence":null,"uniqueSlug":"community-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9","socialTitle":"","socialDek":"","canonicalUrl":"https:\u002F\u002Fakiboy96.medium.com\u002Fswlh\u002Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9","metaDescription":"","latestPublishedAt":1602632586722,"readingTime":7.018238993710692,"previewContent":{"__typename":"PreviewContent","subtitle":"We finally get to the Portfolio Building where we can try to beat the SPY benchmark returns  Year over Yer (YoY). The whole code base can…"},"previewImage":{"__ref":"ImageMetadata:1*lhR-zvcYQhsOswSL8unAJw.png"},"isShortform":false,"seoMetaTags":{"__typename":"SEOMetaTags","jsonLd":"{\"@context\":\"https:\u002F\u002Fschema.org\",\"@id\":\"https:\u002F\u002Fmedium.com\u002Fswlh\u002Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9\",\"@type\":\"SocialMediaPosting\",\"image\":[\"https:\u002F\u002Fmiro.medium.com\u002F1*lhR-zvcYQhsOswSL8unAJw.png\"],\"url\":\"https:\u002F\u002Fmedium.com\u002Fswlh\u002Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9\",\"dateCreated\":\"2020-10-01T22:39:02Z\",\"datePublished\":\"2020-10-01T22:39:02Z\",\"dateModified\":\"2020-10-13T23:43:06Z\",\"headline\":\"Building your Long Term Portfolio using Unsupervised ML with Community Detection Analysis in Python…\",\"name\":\"Building your Long Term Portfolio using Unsupervised ML with Community Detection Analysis in Python…\",\"description\":\"Building your Long Term Portfolio using Unsupervised ML with Community Detection Analysis in Python \\u0026 R\\nWe finally get to the Portfolio Building where we can try to beat the SPY average returns Year …\",\"identifier\":\"ca4e0fbb8ca9\",\"author\":{\"@context\":\"https:\u002F\u002Fschema.org\",\"@id\":\"https:\u002F\u002Fmedium.com\u002F@akiboy96\",\"@type\":\"Person\",\"identifier\":\"akiboy96\",\"name\":\"Aakash Kedia\",\"url\":\"https:\u002F\u002Fmedium.com\u002F@akiboy96\"},\"creator\":{\"@context\":\"https:\u002F\u002Fschema.org\",\"@id\":\"https:\u002F\u002Fmedium.com\u002F@akiboy96\",\"@type\":\"Person\",\"identifier\":\"akiboy96\",\"name\":\"Aakash Kedia\",\"url\":\"https:\u002F\u002Fmedium.com\u002F@akiboy96\"},\"publisher\":{\"@context\":\"https:\u002F\u002Fschema.org\",\"@type\":\"Organization\",\"@id\":\"https:\u002F\u002Fmedium.com\u002Fswlh\",\"name\":\"The Startup\",\"description\":\"Get smarter at building your thing. Follow to join The Startup’s +8 million monthly readers \\u0026 +772K followers.\",\"url\":\"https:\u002F\u002Fmedium.com\u002Fswlh\",\"sameAs\":[\"https:\u002F\u002Ftwitter.com\u002FGrowthSupply\"],\"logo\":{\"@type\":\"ImageObject\",\"width\":1832,\"height\":1832,\"url\":\"https:\u002F\u002Fmiro.medium.com\u002Fv2\u002Fresize:fit:1832\u002F1%2ApKOfOAOvx-fWzfITATgGRg.jpeg\"}},\"mainEntityOfPage\":\"https:\u002F\u002Fmedium.com\u002Fswlh\u002Fcommunity-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9\",\"isAccessibleForFree\":true}"},"seoDescription":"","shortformType":"SHORTFORM_TYPE_LINK","firstPublishedAt":1601591942394,"viewerEdge":{"__ref":"PostViewerEdge:postId:ca4e0fbb8ca9-viewerId:lo_40f07550e3be"},"seoTitle":"","isSuspended":false,"license":"ALL_RIGHTS_RESERVED","tags":[{"__ref":"Tag:stock-market"},{"__ref":"Tag:finance"},{"__ref":"Tag:network-as-a-service"},{"__ref":"Tag:r"},{"__ref":"Tag:portfolio-management"}],"isFeaturedInPublishedPublication":false,"isNewsletter":false,"statusForCollection":"PENDING","pendingCollection":{"__ref":"Collection:f5af2b715248"},"detectedLanguage":"en","wordCount":1639,"layerCake":0}}</script><script>window.__MIDDLEWARE_STATE__={"session":{"xsrf":""},"cache":{"cacheStatus":"MISS"}}</script><script src="https://cdn-client.medium.com/lite/static/js/manifest.91087f21.js"></script><script src="https://cdn-client.medium.com/lite/static/js/723.093de8f1.js"></script><script src="https://cdn-client.medium.com/lite/static/js/main.668f262a.js"></script><script src="https://cdn-client.medium.com/lite/static/js/instrumentation.47ae8b31.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/reporting.851fdaca.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/5052.eb638269.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/683.abfef39e.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/6618.4aea0357.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/2050.3c25fb60.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3326.9712e10d.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7566.fa51707d.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7908.908acb8a.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3927.2f9f3eed.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/8640.0d3bced2.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9967.f31ca2af.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9214.a792bbcc.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/1214.9ae8faaf.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/556.d95c90dd.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7381.c53435ab.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9768.a85f5560.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/5522.3fb24bd9.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/792.f17e92fb.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3507.8b27b9e8.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7561.fc7962fc.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/4929.75144692.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/6834.6c66e3cc.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/1887.f9daf0b6.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7979.35c5b2af.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7975.3f8d607c.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3877.96683729.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9256.629cdc7e.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/8768.62c3639c.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/144.f38d4759.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3666.6579eeda.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/1069.6236ad3b.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/PostPage.MainContent.e31fff2e.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/2698.9eecb474.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3974.ee0dd7bf.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/2527.358dc2fb.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/PostResponsesContent.3c0c12ee.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/responses.editor.d61aa7c4.chunk.js"></script>
<script id="__LOADABLE_REQUIRED_CHUNKS__" type="application/json">[]</script>
<script id="__LOADABLE_REQUIRED_CHUNKS___ext" type="application/json">{"namedChunks":[]}</script><script>window.main();</script><script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'9777545d8a17a316',t:'MTc1NjU4ODgwMC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body></html>