[{"title": "PortAnalyticsAdvanced/lab_23.ipynb at master", "url": "https://github.com/suhasghorp/PortAnalyticsAdvanced/blob/master/lab_23.ipynb", "engines": ["google"]}, {"title": "A Hybrid Approach for Generating Investor Views in Black ...", "url": "https://github.com/Yebei-Rong/A-Hybrid-Approach-for-Generating-Investor-Views-in-Black-<PERSON>tterman-Model", "engines": ["google"]}, {"title": "Gavin-OP/fina4380-project", "url": "https://github.com/Gavin-OP/fina4380-project", "engines": ["google"]}, {"title": "Portfolio Optimization on S&P 500 Subset", "url": "https://github.com/FrancescoCettolin/portfolio-optimization", "engines": ["google"]}, {"title": "Portfolio Optimization with Quadratic Transaction Costs", "url": "https://github.com/mingboiz/portfolio_optimization", "engines": ["google"]}, {"title": "xixi0222/Stock-Risk-Return-Analysis", "url": "https://github.com/xixi0222/Stock-Risk-Return-Analysis", "engines": ["google"]}, {"title": "nilaj-c/Portfolio-risk-return", "url": "https://github.com/nilaj-c/Portfolio-risk-return", "engines": ["google"]}, {"title": "salmany/Algorithmic-Trading-using-Gated-Recurrrent-Unit- ...", "url": "https://github.com/salmany/Algorithmic-Trading-using-Gated-Recurrrent-Unit-Neural-Networks", "engines": ["google"]}, {"title": "options-strategies", "url": "https://github.com/topics/options-strategies?l=python&o=desc", "engines": ["google"]}]