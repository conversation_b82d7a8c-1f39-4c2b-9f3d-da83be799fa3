{"title": "Sales Prediction and Sustainable Sales Recommendations", "source_url": "https://yashkhandelwal07.medium.com/data-science-hackathons-winners-guide-part-1-fecbf3b2c023", "thesis": "The strategy involves predicting sales of a mega mart using machine learning models, specifically focusing on regression tasks. The approach includes meticulous data preparation, feature engineering, and ensemble modeling techniques to enhance predictive accuracy.", "universe": "Sales data from a mega mart with 87,864 training observations and 37,656 test observations", "rebalancing": "Not applicable for this dataset as it is a static prediction task", "signals": [{"name": "Outlier Detection", "definition": "Applying the Interquartile Range (IQR) method to identify and handle outliers by capping extreme values within acceptable range."}, {"name": "Null Value Imputation", "definition": "Checking for null values in the dataset; no imputation needed as none were found."}, {"name": "Categorical Encoding", "definition": "Using one-hot encoding for categorical variables with no inherent order and ordinal encoding for those with a clear order."}, {"name": "Feature Engineering", "definition": "Creating new features that enhance model performance, such as calculating years from present based on outlet year."}, {"name": "Permutation Feature Importance", "definition": "Measuring the impact of each feature by randomly shuffling values and observing changes in model accuracy."}]}