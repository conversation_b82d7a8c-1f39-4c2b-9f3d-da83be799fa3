
import os
import sys
import json
import math
import warnings
from dataclasses import dataclass, asdict
from typing import List, Optional, Dict, Tuple

import numpy as np
import pandas as pd

from sklearn.linear_model import Ridge
from sklearn.ensemble import HistGradientBoostingRegressor
from sklearn.metrics import mean_squared_error

warnings.filterwarnings("ignore", category=UserWarning)
pd.set_option("mode.chained_assignment", None)

# ------------------------------
# Config
# ------------------------------
QUICK_TEST = False  # Set to False for full 2010-2023 OOS
TOP_N = 80          # must be between 50 and 100 per competition guidelines
SEED = 42

# ------------------------------
# Utils
# ------------------------------
def find_file(candidates: List[str]) -> Optional[str]:
    for p in candidates:
        if p and os.path.exists(p):
            return p
    return None

def month_start(dt: pd.Timestamp) -> pd.Timestamp:
    return pd.Timestamp(year=dt.year, month=dt.month, day=1)

def annualize_return(monthly_ret: float) -> float:
    return (1 + monthly_ret) ** 12 - 1

def annualize_std(monthly_std: float) -> float:
    return monthly_std * np.sqrt(12.0)

def max_drawdown_from_returns(series: pd.Series) -> float:
    # series: monthly simple returns
    cum = (1 + series).cumprod()
    peaks = cum.cummax()
    dd = (cum / peaks) - 1.0
    return dd.min()  # negative number

@dataclass
class Metrics:
    sharpe_ann: float
    max_1m_loss: float
    max_drawdown: float
    alpha_monthly: Optional[float]
    alpha_ann: Optional[float]
    alpha_tstat: Optional[float]
    info_ratio_ann: Optional[float]
    avg_ret_ann: float
    vol_ann: float
    turnover: float

def compute_alpha_stats(port_df: pd.DataFrame):
    '''
    Requires columns: ['year','month','port_ret'] and market data with 'mkt_rf' merged.
    If mkt data missing, return Nones gracefully.
    '''
    if 'mkt_rf' not in port_df.columns:
        return None, None, None, None

    # OLS with heteroskedasticity-robust t-stats (simple White) as a fallback if statsmodels is not present
    try:
        import statsmodels.api as sm
    except Exception:
        # Basic OLS (no HAC/White) fallback
        X = port_df[['mkt_rf']].values
        X = np.hstack([np.ones((len(X), 1)), X])
        y = port_df['port_ret'].values
        beta = np.linalg.lstsq(X, y, rcond=None)[0]
        resid = y - X @ beta
        s2 = (resid**2).sum() / (len(y) - X.shape[1])
        cov_beta = s2 * np.linalg.inv(X.T @ X)
        se_alpha = np.sqrt(cov_beta[0,0])
        t_alpha = beta[0] / (se_alpha + 1e-12)
        alpha_monthly = beta[0]
        return alpha_monthly, alpha_monthly*12.0, t_alpha, None

    # statsmodels path
    X = sm.add_constant(port_df['mkt_rf'].values, has_constant='add')
    y = port_df['port_ret'].values
    model = sm.OLS(y, X).fit(cov_type='HAC', cov_kwds={'maxlags':3})
    alpha_monthly = float(model.params[0])
    t_alpha = float(model.tvalues[0])
    mse = float(model.mse_resid)
    info_ratio_ann = (alpha_monthly / np.sqrt(mse)) * np.sqrt(12.0)
    return alpha_monthly, alpha_monthly*12.0, t_alpha, info_ratio_ann

def robust_rank_to_unit(x: pd.Series) -> pd.Series:
    '''
    Cross-sectional rank -> [-1, 1] with median fill
    '''
    med = x.median(skipna=True)
    x2 = x.fillna(med).rank(method="dense")
    maxv = x2.max()
    if pd.isna(maxv) or maxv <= 1:
        return pd.Series(np.zeros(len(x2)), index=x.index)
    x_scaled = ((x2 - 1) / (maxv - 1)) * 2 - 1
    return x_scaled

def detect_feature_cols(df: pd.DataFrame, target_col="stock_exret") -> List[str]:
    '''
    If factor_char_list.csv absent, auto-detect numeric features excluding identifiers & target.
    '''
    blacklist = set([
        target_col, "ret_eom","year","month","permno","date","rf",
        "stock_ticker","cusip","comp_name","shrcd","exchcd",
    ])
    numeric_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]
    feats = [c for c in numeric_cols if c not in blacklist]
    return feats

def month_key(ts: pd.Timestamp) -> Tuple[int,int]:
    return (ts.year, ts.month)

def compute_turnover(monthly_holdings: Dict[Tuple[int,int], List[int]]) -> float:
    months = sorted(list(monthly_holdings.keys()))
    if len(months) < 2:
        return 0.0
    changes = []
    for i in range(1, len(months)):
        prev = set(monthly_holdings[months[i-1]])
        cur = set(monthly_holdings[months[i]])
        if len(prev) == 0:
            continue
        # one-way turnover = fraction replaced
        changes.append(1.0 - len(prev & cur) / float(len(prev)))
    return float(np.mean(changes)) if changes else 0.0

# ------------------------------
# Load data
# ------------------------------
def load_core_data():
    # Candidate locations
    data_path = find_file([
        "./asset/hackathon_sample_v2.csv",
        os.getenv("HACKATHON_DATA_PATH"),
        "./hackathon_sample_v2.csv",
        "./data/hackathon_sample_v2.csv",
        "/mnt/data/hackathon_sample_v2.csv",
    ])
    if data_path is None:
        raise FileNotFoundError("hackathon_sample_v2.csv not found. Place it in current dir or set HACKATHON_DATA_PATH.")

    df = pd.read_csv(data_path, low_memory=False, parse_dates=['date'])
    if 'year' not in df.columns or 'month' not in df.columns:
        df['year'] = df['date'].dt.year
        df['month'] = df['date'].dt.month

    # Factor list (optional)
    factor_path = find_file([
        "./asset/factor_char_list.csv",
        os.getenv("FACTOR_LIST_PATH"),
        "./factor_char_list.csv",
        "./data/factor_char_list.csv",
        "/mnt/data/factor_char_list.csv",
    ])
    if factor_path is not None:
        fac = pd.read_csv(factor_path)
        # Accept either 'variable' or 'Variable' column names robustly
        cand_cols = [c for c in fac.columns if c.lower() == "variable"]
        if cand_cols:
            varcol = cand_cols[0]
            features = [str(v) for v in fac[varcol].dropna().unique().tolist() if v in df.columns]
        else:
            features = detect_feature_cols(df)
    else:
        features = detect_feature_cols(df)

    # Ensure required fields
    required = ['date','year','month','permno','stock_exret']
    for r in required:
        if r not in df.columns:
            raise ValueError(f"Required column missing: {r}")

    # Drop rows with missing target
    df = df[df['stock_exret'].notna()].copy()

    # Cross-sectional transform to [-1,1] monthly
    # (we do *not* touch the target)
    monthly_groups = []
    for dt, g in df.groupby('date', sort=True):
        gg = g.copy()
        for f in features:
            if f not in gg.columns:
                continue
            gg[f] = robust_rank_to_unit(gg[f])
        monthly_groups.append(gg)
    data = pd.concat(monthly_groups, ignore_index=True)

    # Sort data
    data = data.sort_values(['date','permno']).reset_index(drop=True)

    # report
    print(f"Loaded {len(data):,} rows with {len(features)} features from {data['date'].min().date()} to {data['date'].max().date()}")
    return data, features

# ------------------------------
# Model training (expanding window)
# ------------------------------
def train_predict_monthly(data: pd.DataFrame, features: List[str]):
    '''
    Expanding window:
      - 8y train, 2y val, predict next year monthly
      - Rolling yearly
    Returns a dataframe with columns: date,year,month,permno,stock_exret, pred_ridge, pred_hgbr
    '''
    start = pd.Timestamp("2000-01-01")
    end_oos_end = pd.Timestamp("2024-01-01")

    out_rows = []
    counter = 0
    oos_years_done = 0
    while (start + pd.DateOffset(years=11 + counter)) <= end_oos_end:
        cutoff_train_end = start + pd.DateOffset(years=8 + counter)
        cutoff_val_end   = start + pd.DateOffset(years=10 + counter)
        cutoff_test_end  = start + pd.DateOffset(years=11 + counter)

        train = data[(data['date'] >= start) & (data['date'] < cutoff_train_end)]
        val   = data[(data['date'] >= cutoff_train_end) & (data['date'] < cutoff_val_end)]
        test  = data[(data['date'] >= cutoff_val_end) & (data['date'] < cutoff_test_end)]

        if len(test) == 0 or len(train) == 0 or len(val) == 0:
            counter += 1
            continue

        # Prepare matrices
        X_tr = train[features].values
        y_tr = train['stock_exret'].values
        X_va = val[features].values
        y_va = val['stock_exret'].values
        X_te = test[features].values

        # De-mean y (ridge without intercept) to mimic provided baseline
        y_tr_mean = y_tr.mean()
        y_tr_dm = y_tr - y_tr_mean

        # Ridge: tune alpha on validation
        alphas = np.logspace(-3, 3, num=10)
        best_a, best_mse = None, 1e9
        for a in alphas:
            rr = Ridge(alpha=a, fit_intercept=False, random_state=SEED)
            rr.fit(X_tr, y_tr_dm)
            pred_va = rr.predict(X_va) + y_tr_mean
            mse = mean_squared_error(y_va, pred_va)
            if mse < best_mse:
                best_mse, best_a = mse, a
        ridge = Ridge(alpha=best_a, fit_intercept=False, random_state=SEED)
        ridge.fit(X_tr, y_tr_dm)
        pred_ridge = ridge.predict(X_te) + y_tr_mean

        # HistGradientBoosting: small grid
        hg_grid = [
            dict(max_depth=3, learning_rate=0.05, max_iter=300),
            dict(max_depth=5, learning_rate=0.05, max_iter=400),
            dict(max_depth=None, learning_rate=0.05, max_iter=300),
            dict(max_depth=3, learning_rate=0.1, max_iter=300),
        ]
        best_hg, best_mse = None, 1e9
        for params in hg_grid:
            hg = HistGradientBoostingRegressor(
                random_state=SEED,
                max_depth=params["max_depth"],
                learning_rate=params["learning_rate"],
                max_iter=params["max_iter"],
            )
            hg.fit(X_tr, y_tr)
            pred_va = hg.predict(X_va)
            mse = mean_squared_error(y_va, pred_va)
            if mse < best_mse:
                best_mse, best_hg = mse, params
        hg = HistGradientBoostingRegressor(random_state=SEED, **best_hg)
        hg.fit(X_tr, y_tr)
        pred_hgbr = hg.predict(X_te)

        reg_pred = test[['date','year','month','permno','stock_exret']].copy()
        reg_pred['pred_ridge'] = pred_ridge
        reg_pred['pred_hgbr']  = pred_hgbr
        out_rows.append(reg_pred)

        counter += 1
        oos_years_done += 1
        if QUICK_TEST and oos_years_done >= 2:
            # stop after 2 OOS years in smoke test mode
            break

    if not out_rows:
        raise RuntimeError("No OOS predictions produced. Check date ranges in the data.")

    out_df = pd.concat(out_rows, ignore_index=True)
    return out_df, {"oos_years": oos_years_done}

# ------------------------------
# Portfolio construction
# ------------------------------
def build_portfolio(pred_df: pd.DataFrame, model_cols: List[str]):
    # Ensemble by rank-average for stability
    pred_df = pred_df.copy()
    pred_df['score'] = 0.0
    for m in model_cols:
        pred_df[f'rank_{m}'] = pred_df.groupby(['year','month'])[m].rank(method='average')
        pred_df['score'] += pred_df[f'rank_{m}']
    pred_df['score'] = pred_df['score'] / float(len(model_cols))
    # Rank by score descending -> top
    pred_df['rank_final'] = pred_df.groupby(['year','month'])['score'].rank(ascending=False, method='first')

    # Select top-N each month
    holdings = {}
    long_rows = []
    for (y,m), g in pred_df.groupby(['year','month']):
        gg = g.sort_values('rank_final').head(TOP_N)
        holdings[(y,m)] = gg['permno'].astype(int).tolist()
        # Equal-weight long-only monthly return
        long_ret = gg['stock_exret'].mean()
        long_rows.append(dict(year=y, month=m, port_ret=long_ret))

    port = pd.DataFrame(long_rows).sort_values(['year','month']).reset_index(drop=True)

    # Compute turnover
    turnover = compute_turnover(holdings)
    return port, {"turnover": float(turnover), "holdings_count": len(holdings)}

# ------------------------------
# Optional: merge market data for alpha
# ------------------------------
def merge_market_data(port: pd.DataFrame) -> pd.DataFrame:
    mkt_path = find_file([
        os.getenv("MKT_FILE_PATH"),
        "./mkt_ind.csv",
        "./data/mkt_ind.csv",
        "/mnt/data/mkt_ind.csv",
    ])
    if mkt_path is None:
        print("Note: mkt_ind.csv not found; alpha metrics will be None.")
        return port
    mkt = pd.read_csv(mkt_path)
    # Expect columns: year, month, mkt_rf (excess market return)
    need = {'year','month','mkt_rf'}
    if not need.issubset(set(mkt.columns)):
        print("Note: mkt_ind.csv present but missing required columns {year, month, mkt_rf}. Skipping alpha.")
        return port
    merged = pd.merge(port, mkt[['year','month','mkt_rf']], on=['year','month'], how='left')
    return merged

# ------------------------------
# Orchestrate
# ------------------------------
def main(out_dir="./"):
    os.makedirs(out_dir, exist_ok=True)
    data, features = load_core_data()
    preds, train_info = train_predict_monthly(data, features)
    # Save predictions for transparency
    preds_out = os.path.join(out_dir, "predictions_oos.csv")
    preds.to_csv(preds_out, index=False)
    print(f"Wrote OOS predictions -> {preds_out}")

    # Build long-only portfolio
    port, port_info = build_portfolio(preds, model_cols=['pred_ridge','pred_hgbr'])
    port = merge_market_data(port)

    # Metrics
    sharpe_ann = (port['port_ret'].mean() / (port['port_ret'].std() + 1e-12)) * np.sqrt(12.0) if len(port) > 1 else 0.0
    max_1m_loss = float(port['port_ret'].min()) if len(port) else 0.0
    # drawdown
    def _max_dd(s):
        cum = (1 + s).cumprod()
        peaks = cum.cummax()
        dd = (cum/peaks) - 1.0
        return dd.min()
    max_dd = float(_max_dd(port['port_ret'])) if len(port) else 0.0
    avg_ret_ann = float((1 + port['port_ret'].mean()) ** 12 - 1) if len(port) else 0.0
    vol_ann = float(port['port_ret'].std() * np.sqrt(12.0)) if len(port) else 0.0

    alpha_m, alpha_a, alpha_t, info_ratio = compute_alpha_stats(port)

    metrics = Metrics(
        sharpe_ann=float(sharpe_ann),
        max_1m_loss=float(max_1m_loss),
        max_drawdown=float(max_dd),
        alpha_monthly=None if alpha_m is None else float(alpha_m),
        alpha_ann=None if alpha_a is None else float(alpha_a),
        alpha_tstat=None if alpha_t is None else float(alpha_t),
        info_ratio_ann=None if info_ratio is None else float(info_ratio),
        avg_ret_ann=float(avg_ret_ann),
        vol_ann=float(vol_ann),
        turnover=float(port_info.get("turnover", 0.0)),
    )

    # Save monthly returns
    port_out = os.path.join(out_dir, "portfolio_monthly.csv")
    port.to_csv(port_out, index=False)
    print(f"Wrote portfolio monthly returns -> {port_out}")

    # Save JSON metrics
    metrics_out = os.path.join(out_dir, "metrics.json")
    with open(metrics_out, "w") as f:
        json.dump({"metrics": asdict(metrics), "info": {"train": train_info, "portfolio": port_info}}, f, indent=2)
    print(f"Wrote metrics -> {metrics_out}")

if __name__ == "__main__":
    out_dir = sys.argv[1] if len(sys.argv) > 1 else "./pred"
    main(out_dir)
