[{"title": "Picking Efficient Portfolios from 3,171 US Common Stocks with New Quantum and Classical Solvers", "url": "http://arxiv.org/abs/2011.01308v1", "engines": ["arxiv"]}, {"title": "Supervised classification-based stock prediction and portfolio optimization", "url": "http://arxiv.org/abs/1406.0824v1", "engines": ["arxiv"]}, {"title": "Fine-Tuning Large Language Models for Stock Return Prediction Using Newsflow", "url": "http://arxiv.org/abs/2407.18103v2", "engines": ["arxiv"]}, {"title": "Optimum Risk Portfolio and Eigen Portfolio: A Comparative Analysis Using Selected Stocks from the Indian Stock Market", "url": "http://arxiv.org/abs/2107.11371v1", "engines": ["arxiv"]}, {"title": "Performance Evaluation of Equal-Weight Portfolio and Optimum Risk Portfolio on Indian Stocks", "url": "http://arxiv.org/abs/2309.13696v1", "engines": ["arxiv"]}, {"title": "Portfolio Optimization: A Comparative Study", "url": "http://arxiv.org/abs/2307.05048v1", "engines": ["arxiv"]}, {"title": "Designing an Optimal Portfolio for Iran's Stock Market with Genetic Algorithm using Neural Network Prediction of Risk and Return Stocks", "url": "http://arxiv.org/abs/1903.06632v1", "engines": ["arxiv"]}, {"title": "Portfolio Theory, Information Theory and Tsallis Statistics", "url": "http://arxiv.org/abs/1811.07237v2", "engines": ["arxiv"]}, {"title": "A Comparative Study of Hierarchical Risk Parity Portfolio and Eigen Portfolio on the NIFTY 50 Stocks", "url": "http://arxiv.org/abs/2210.00984v1", "engines": ["arxiv"]}, {"title": "Finalists' strategies", "url": "https://fiamtl.com/2024-events/hackathon-2024/finalists-strategies/", "engines": ["google"]}, {"title": "Optimizing Portfolios with the Mean Variance Method in ...", "url": "https://www.analyticsvidhya.com/blog/2023/06/optimizing-portfolios-with-the-mean-variance-method-in-python/", "engines": ["google"]}, {"title": "An Intelligent Portfolio Construction Technique Based on ...", "url": "https://ieeexplore.ieee.org/iel8/6287639/10820123/10892141.pdf", "engines": ["google"]}, {"title": "XGBoost Parameters Tuning: A Complete Guide with ...", "url": "https://www.analyticsvidhya.com/blog/2016/03/complete-guide-parameter-tuning-xgboost-with-codes-python/", "engines": ["google"]}, {"title": "Investment Portfolio Optimization using Machine Learning (2)", "url": "https://www.scribd.com/document/855006473/Investment-Portfolio-Optimization-using-Machine-Learning-2", "engines": ["google"]}, {"title": "50 ML Projects To Strengthen Your Portfolio and Get You ...", "url": "https://www.projectpro.io/article/ml-projects-ideas-with-source-code/474", "engines": ["google"]}, {"title": "IntPort: An Intelligent Portfolio Construction Technique ...", "url": "https://www.researchgate.net/publication/389163142_IntPort_An_Intelligent_Portfolio_Construction_Technique_Based_on_Financial_Forecasting_by_Statistical_Average_Method", "engines": ["google"]}, {"title": "Using Machine Learning to predict US electricity demand", "url": "https://medium.com/@tdahya2/predicting-us-electricity-usage-a-comparison-of-two-approaches-34303b81a4aa", "engines": ["google"]}, {"title": "Kaggle Winning Solutions: AI Trends & Insights", "url": "https://www.kaggle.com/code/tahaalselwii/kaggle-winning-solutions-ai-trends-insights", "engines": ["google"]}, {"title": "Precise Stock Price Prediction for Robust Portfolio Design from Selected Sectors of the Indian Stock Market", "url": "http://arxiv.org/abs/2201.05570v1", "engines": ["arxiv"]}, {"title": "21+ Hackathon Project Ideas No One Else Dares to Try", "url": "https://www.upgrad.com/blog/hackathon-project-ideas/", "engines": ["google"]}]