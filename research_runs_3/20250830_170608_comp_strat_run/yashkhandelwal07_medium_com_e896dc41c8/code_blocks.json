["# Assuming df is the DataFrame \nQ1 = df[‘Sales’].quantile(0.25) \nQ3 = df[‘Sales’].quantile(0.75) \nIQR = Q3 — Q1 \nlower_bound = Q1–1.5 * IQR \nupper_bound = Q3 + 1.5 * IQR \ndf[‘Sales’] = df[‘Sales’].apply(lambda x: upper_bound if x > upper_bound else (lower_bound if x < lower_bound else x))", "# One Hot Encoding \ndf_encoded = pd.get_dummies(df, columns=[‘categorical_feature’], drop_first=True) # Ordinal Encoding \nordinal_mapping = {‘low’: 0, ‘medium’: 1, ‘high’: 2} \ndf[‘ordinal_feature’] = df[‘ordinal_feature’].map(ordinal_mapping)", "# Assuming df is your DataFrame\ndf[‘new_feature’] = df[‘feature1’] * df[‘feature2’]", "from sklearn.inspection import permutation_importance \n# Assuming model is your trained machine learning model \nperm_importance = permutation_importance(model, X_test, y_test, n_repeats=10, random_state=42) \nfeature_importance = perm_importance.importances_mean", "from sklearn.model_selection import train_test_split \n# Assuming X is your feature matrix and y is your target variable \nX_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42) #import csv12345python", "#Code for Stacking Ensembling\nfrom sklearn.ensemble import StackingRegressor \nfrom sklearn.linear_model import LinearRegression \nfrom sklearn.ensemble import RandomForestRegressor\nfrom sklearn.tree import DecisionTreeRegressor \n# Assuming base models are your individual regression models \nbase_models = [(‘linear’, LinearRegression()), (‘rf’, RandomForestRegressor()), (‘tree’, DecisionTreeRegressor())] \nstacking_model = StackingRegressor(estimators=base_models, final_estimator=LinearRegression()) \nstacking_model.fit(X_train, y_train)", "#Code for Weighted Average Ensemble\n# Assuming model1, model2, model3 are your regression models\nweights = [0.4, 0.3, 0.3] \n# Adjust weights based on model performance\npredictions_model1 = model1.predict(X_test) \npredictions_model2 = model2.predict(X_test) \npredictions_model3 = model3.predict(X_test) \nweighted_average_predictions = (weights[0] * predictions_model1 + weights[1] * predictions_model2 + weights[2] * predictions_model3) / sum(weights)"]