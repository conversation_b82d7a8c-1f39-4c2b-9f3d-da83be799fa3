{"title": "Alpha Strategy Backtest System", "source_url": "https://github.com/yuba316/Alpha_Strategy_BackTest_System", "thesis": "Develop a daily return prediction model for 100 U.S. equities and develop a trading strategy based on alpha factors, including beta, momentum, volatility, price-volume correlation, reverse drawdown, and volume spike. The thesis involves using machine learning algorithms to achieve nonlinear regression of tomorrow's return on today's factor scores and transforming the rank into position weights for each stock.", "universe": "100 U.S. equities", "rebalancing": "daily", "signals": [{"name": "Beta", "definition": "The slope of the rolling linear regression between the stock return and the market return with a 1-year window and a 1-quarter half-life."}, {"name": "Momentum", "definition": "The exponential moving average of the stock’s log return with a 1-year window and a 1-month lag."}, {"name": "Volatility", "definition": "The exponential moving standard deviation of the stock return with a 1-year window and a 2-month half-life."}, {"name": "Price-Volume Correlation", "definition": "The rolling covariance of the cross-section rank of the stock’s adjusted price and volume with a 1-month window."}, {"name": "Reverse Drawdown", "definition": "The rolling max percentage drawdown of the stock’s adjusted price with a 1-month window."}, {"name": "Volume Spike", "definition": "Daily volume divided by its 5-day moving average."}]}