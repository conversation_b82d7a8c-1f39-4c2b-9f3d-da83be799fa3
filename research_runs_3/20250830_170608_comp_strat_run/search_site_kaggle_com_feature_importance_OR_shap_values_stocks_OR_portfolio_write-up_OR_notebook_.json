[{"title": "Jane Street: EDA of day 0 and feature importance", "url": "https://www.kaggle.com/code/carlmcbrideellis/jane-street-eda-of-day-0-and-feature-importance", "engines": ["google"]}, {"title": "Stock Portfolio Diversification using ML", "url": "https://www.kaggle.com/code/amlgroupproject/stock-portfolio-diversification-using-ml/notebook?scriptVersionId=3362142", "engines": ["google"]}, {"title": "Machine Learning for Stocks Trading", "url": "https://www.kaggle.com/code/ibrahimkaratas/machine-learning-for-stocks-trading/comments", "engines": ["google"]}, {"title": "Stock Prediction - LSTM", "url": "https://www.kaggle.com/code/malingarajapaksha/stock-prediction-lstm", "engines": ["google"]}, {"title": "Jane Street: EDA of day 0 and feature importance", "url": "https://www.kaggle.com/carlmcbrideellis/jane-street-eda-of-day-0-and-feature-importance/code", "engines": ["google"]}, {"title": "XGBoost to Predict Stock Price change", "url": "https://www.kaggle.com/code/stacknishant/xgboost-to-predict-stock-price-change", "engines": ["google"]}, {"title": "XGBoost explainability with SHAP", "url": "https://www.kaggle.com/code/bryanb/xgboost-explainability-with-shap", "engines": ["google"]}, {"title": "stock analysis and prediction", "url": "https://www.kaggle.com/code/sarahyeboahm/stock-analysis-and-prediction", "engines": ["google"]}, {"title": "Optiver GPU Accelerated LOFO Feature Importance", "url": "https://www.kaggle.com/code/aerdem4/optiver-gpu-accelerated-lofo-feature-importance/input", "engines": ["google"]}, {"title": "[LSTM] S&P500 Stocks Time Series Forecasting", "url": "https://www.kaggle.com/code/yassinesfaihi/lstm-stock-time-series-forecasting", "engines": ["google"]}]