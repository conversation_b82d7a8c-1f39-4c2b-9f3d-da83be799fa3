Please send pull request if you want to add more competition solutions.
For Comment and Suggestions: Discusssion Thread:
- 5th Place Solution (Explanation)
- 7th Place Solution (Explanation)
- 10th Place Solution (Explanation)
- 11th Place Solution (Explanation)
- 19th Place Solution (Explanation)
- 21th Place Solution (Explanation)(code)
- 1st Place Solution (Explanation)
- 2nd Place Solution (Explanation)(code)
- 5th Place Solution (Explanation)
- 7th Place Solution (Explanation)
- 9th Place Solution (Explanation)
- 10th Place Solution (Explanation)
- 29th Place Solution (Explanation)(code)
- 2nd Place Solution (Explanation)(code)
- 3rd Place Solution (Explanation & code)
- 6th Place Solution (Explanation & code)
- 8th Place Solution (Explanation & code)
- 10th Place Solution (Explanation & code)
- Place Solution (Explanation & code)
- 1st Place Solution (Explanation)
- 2nd Place Solution (Explanation)(code)(Blog)
- 3rd Place Solution - R language (Explanation & code)
- 4th Place Solution (Explanation)
- 5th Place Solution (Explanation)(code)
- 8th Place Solution (Explanation)(code)
- 11th Place Solution (Explanation)(code)
- 2nd Place Solution (Explanation)
- 3rd Place Solution (Explanation)
- 4th Place Solution (Explanation)(code)(Kaggle Kernel)
- 8th Place Solution (Explanation)(code)
- 1st Place Solution (Explanation)
- 2nd Place Solution (Explanation)
- 3rd Place Solution (Explanation)(code)
- 4th Place Solution (Explanation)(code)
- 7th Place Solution (Explanation)
- 1st Place Solution (Explanation)
- 2nd Place Solution (Explanation)
- 3rd Place Solution (Explanation)
- 7th Place Solution (Explanation)
- Some interesting solutions from the web (write-up)
- 24 Place Solution (Explanation)(code)
- 1st Place Solution (Explanation)
- 2nd Place Solution (Explanation)
- 3rd Place Solution (Explanation)
- 27 Place Solution (Explanation)(code)
- Collection of winning solutions (write-up)
- 1st Place Solution (Explanation)(code)
- 28th Place Solution (Explanation & code)
- 71th Place Solution (code)
- Another Solution (code)
- 49th Place Solution (Explanation & code)
- 91th Place Solution (Explanation & code)
- 113th Place Solution (Explanation & code)
- 119th Place Solution(Explanation & code)
- Post-competition thoughts(write-up)
- 1st Place Solution (Explanation)(code)
- 2nd Place Solution (Explanation)(code)
- 3rd Place Solution (Explanation)
- 6th Place Solution (Explanation)(code)
- Tips from the winning solutions (write-up)
- General Approach (write-up)
- 1st Place Solution (Explanation)
- 3rd Place Solution (Explanation)(code)
- 1st Place Solution (Explanation)
- 2nd Place Solution (Explanation)(code)
- 3rd Place Solution (Explanation & code)
- 4th Place Solution (Explanation)
- 1st Place Solution (Explanation)(code)
- 3rd Place Solution (Explanation)
- Place Solution (Explanation)(code)
- Place Solution (Explanation)(code)
- Top solutions (write-up)
- 1st Place Solution (Explanation)(code)
- 2nd Place Solution (Explanation)
- 3rd Place Solution (Explanation)
- 5th Place Solution (Explanation)
- Gold Medal Solutions (write-up)
- 1st Place Solution (Explanation)
- 5th Place Solution (Explanation)(Video Tutorial with Eng subtitles)
- 7th Place Solution (Explanation)
- 8th Place Solution (Explanation)
- 9th Place Solution (Explanation)
- 1st Place Solution (Explanation)(code)
- 2nd Place Solution (Explanation)(code)
- 5th Place Solution (Explanation)
- 6th Place Solution (Explanation)
- 7th Place Solution (Explanation)
- 1st Place Solution (Explanation)
- 2nd Place Solution (Explanation)
- 4th Place Solution (Explanation)
- 5th Place Solution (Explanation)
- 7h Place Solution (Explanation)(code)
- 8th Place Solution (Explanation)(code)
- 🏅Gold Medal Solutions list 🏅
- 1st Place Solution (Explanation)(code)
- 2nd Place Solution(Explanation)
- 3rd Place Solution(Explanation)
- 1st Place Solution (Explanation)(code)
- 4th Place Solution (Explanation)(code)
- 9th Place Solution (Explanation)(code)
- 1st Place Solution (Explanation)
- 6th Place Solution (Explanation)
- 8th Place Solution (Explanation)(code)
- 9th Place Solution (Explanation)
- Few lessons learned (4th place) (Explanation)
- 1st Place Solution (Explanation)(code)
- 2nd Place Solution (Explanation & code)
- 3rd Place Solution (Explanation)(code)
- 4th Place Solution (Explanation)(code)
- P1st lace Solution (Explanation)
- Place Solution (Explanation)(code)
- Place Solution (Explanation)(code)
- Place Solution (Explanation)(code)
- Place Solution (Explanation)(code)
- Gold Medal Solutions
- 14th Place Solution (code)
- 25th Place Solution (code)
- 27th Place Solution (code)
- 29th Place Solution (code)
- 1st Place Solution (code)
- 2nd Place Solution (code)
- 3rd Place Solution (code)
- 5th Place Solution (Explanation)(code)
- 5th Place Solution (code)
- 11th Place Solution (Explanation & code)
- 3rd Place Solution (code)
- 8th Place Solution (Explanation & code)
- 3rd Place Solution (code)
- 11th Place Solution (Explanation & code)
- 1st Place Solution (Explanation)
- 3rd Place Solution (Explanation)
- Score: 73.4% Solution (Explanation & code)
- Score 66%: Solution (Explanation & code)