<!doctype html><html lang="en"><head><title data-rh="true">Rock, Paper, Scissors, and the Stock Market | by <PERSON><PERSON><PERSON> | TDS Archive | Medium</title><meta data-rh="true" charset="utf-8"/><meta data-rh="true" name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1,maximum-scale=1"/><meta data-rh="true" name="theme-color" content="#000000"/><meta data-rh="true" name="twitter:app:name:iphone" content="Medium"/><meta data-rh="true" name="twitter:app:id:iphone" content="828256236"/><meta data-rh="true" property="al:ios:app_name" content="Medium"/><meta data-rh="true" property="al:ios:app_store_id" content="828256236"/><meta data-rh="true" property="al:android:package" content="com.medium.reader"/><meta data-rh="true" property="fb:app_id" content="542599432471018"/><meta data-rh="true" property="og:site_name" content="Medium"/><meta data-rh="true" name="apple-itunes-app" content="app-id=828256236, app-argument=/data-science/rock-paper-scissors-and-the-stock-market-f1ad81785a76, affiliate-data=pt=698524&amp;ct=smart_app_banner&amp;mt=8"/><meta data-rh="true" property="og:type" content="article"/><meta data-rh="true" property="article:published_time" content="2021-10-08T19:16:29.089Z"/><meta data-rh="true" name="title" content="Rock, Paper, Scissors, and the Stock Market | by Seouk Jun Kim | TDS Archive | Medium"/><meta data-rh="true" property="og:title" content="Rock, Paper, Scissors, and the Stock Market"/><meta data-rh="true" property="al:android:url" content="medium://p/f1ad81785a76"/><meta data-rh="true" property="al:ios:url" content="medium://p/f1ad81785a76"/><meta data-rh="true" property="al:android:app_name" content="Medium"/><meta data-rh="true" name="description" content="You would not normally relate rock, paper, scissors (RPS for short) to the stock market. It actually seems absurd to do so. And granted, a single game of RPS, or just a series of a thousand games of…"/><meta data-rh="true" property="og:description" content="Recounting my experience in the Rock, Paper, Scissors Kaggle competition, and the resemblance of the competition structure to the stock…"/><meta data-rh="true" property="og:url" content="https://medium.com/data-science/rock-paper-scissors-and-the-stock-market-f1ad81785a76"/><meta data-rh="true" property="al:web:url" content="https://medium.com/data-science/rock-paper-scissors-and-the-stock-market-f1ad81785a76"/><meta data-rh="true" property="og:image" content="https://miro.medium.com/v2/resize:fit:1200/1*hwW2M6hn4fd98QlEmbxmog.jpeg"/><meta data-rh="true" property="article:author" content="https://kdanielive.medium.com"/><meta data-rh="true" name="author" content="Seouk Jun Kim"/><meta data-rh="true" name="robots" content="index,noarchive,follow,max-image-preview:large"/><meta data-rh="true" name="referrer" content="unsafe-url"/><meta data-rh="true" property="twitter:title" content="Rock, Paper, Scissors, and the Stock Market"/><meta data-rh="true" name="twitter:site" content="@Medium"/><meta data-rh="true" name="twitter:app:url:iphone" content="medium://p/f1ad81785a76"/><meta data-rh="true" property="twitter:description" content="Recounting my experience in the Rock, Paper, Scissors Kaggle competition, and the resemblance of the competition structure to the stock…"/><meta data-rh="true" name="twitter:image:src" content="https://miro.medium.com/v2/resize:fit:1200/1*hwW2M6hn4fd98QlEmbxmog.jpeg"/><meta data-rh="true" name="twitter:card" content="summary_large_image"/><meta data-rh="true" name="twitter:label1" content="Reading time"/><meta data-rh="true" name="twitter:data1" content="12 min read"/><link data-rh="true" rel="icon" href="https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19"/><link data-rh="true" rel="search" type="application/opensearchdescription+xml" title="Medium" href="/osd.xml"/><link data-rh="true" rel="apple-touch-icon" sizes="152x152" href="https://miro.medium.com/v2/resize:fill:304:304/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="apple-touch-icon" sizes="120x120" href="https://miro.medium.com/v2/resize:fill:240:240/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="apple-touch-icon" sizes="76x76" href="https://miro.medium.com/v2/resize:fill:152:152/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="apple-touch-icon" sizes="60x60" href="https://miro.medium.com/v2/resize:fill:120:120/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="mask-icon" href="https://miro.medium.com/v2/resize:fill:1000:1000/7*GAOKVe--MXbEJmV9230oOQ.png" color="#171717"/><link data-rh="true" rel="preconnect" href="https://glyph.medium.com" crossOrigin=""/><link data-rh="true" rel="manifest" href="/manifest.json"/><link data-rh="true" rel="preconnect" href="https://www.google.com"/><link data-rh="true" rel="preconnect" href="https://www.gstatic.com" crossOrigin=""/><link data-rh="true" id="glyph_preload_link" rel="preload" as="style" type="text/css" href="https://glyph.medium.com/css/unbound.css"/><link data-rh="true" id="glyph_link" rel="stylesheet" type="text/css" href="https://glyph.medium.com/css/unbound.css"/><link data-rh="true" rel="author" href="https://kdanielive.medium.com"/><link data-rh="true" rel="canonical" href="https://medium.com/data-science/rock-paper-scissors-and-the-stock-market-f1ad81785a76"/><link data-rh="true" rel="alternate" href="android-app://com.medium.reader/https/medium.com/p/f1ad81785a76"/><script data-rh="true" type="application/ld+json">{"@context":"https://schema.org","@id":"https://medium.com/data-science/rock-paper-scissors-and-the-stock-market-f1ad81785a76","@type":"SocialMediaPosting","image":["https://miro.medium.com/1*hwW2M6hn4fd98QlEmbxmog.jpeg"],"url":"https://medium.com/data-science/rock-paper-scissors-and-the-stock-market-f1ad81785a76","dateCreated":"2021-03-13T09:09:25Z","datePublished":"2021-03-13T09:09:25Z","dateModified":"2021-10-08T19:16:29Z","headline":"Rock, Paper, Scissors, and the Stock Market","name":"Rock, Paper, Scissors, and the Stock Market","description":"Rock, Paper, Scissors, and the Stock Market\nRecounting my experience in the Rock, Paper, Scissors Kaggle competition, and the resemblance of the competition structure to the stock market.\nYou would …","identifier":"f1ad81785a76","author":{"@context":"https://schema.org","@id":"https://medium.com/@kdanielive","@type":"Person","identifier":"kdanielive","name":"Seouk Jun Kim","url":"https://medium.com/@kdanielive"},"creator":{"@context":"https://schema.org","@id":"https://medium.com/@kdanielive","@type":"Person","identifier":"kdanielive","name":"Seouk Jun Kim","url":"https://medium.com/@kdanielive"},"publisher":{"@context":"https://schema.org","@type":"Organization","@id":"https://medium.com/data-science","name":"TDS Archive","description":"An archive of data science, data analytics, data engineering, machine learning, and artificial intelligence writing from the former Towards Data Science Medium publication.","url":"https://medium.com/data-science","logo":{"@type":"ImageObject","width":692,"height":642,"url":"https://miro.medium.com/v2/resize:fit:692/1%2AJEuS4KBdakUcjg9sC7Wo4A.png"}},"mainEntityOfPage":"https://medium.com/data-science/rock-paper-scissors-and-the-stock-market-f1ad81785a76","isAccessibleForFree":true}</script><script data-rh="true" src="https://www.google.com/recaptcha/enterprise.js?render=6Le-uGgpAAAAAPprRaokM8AKthQ9KNGdoxaGUvVp" async="true"></script><style type="text/css" data-fela-rehydration="556" data-fela-type="STATIC">html{box-sizing:border-box;-webkit-text-size-adjust:100%}*, *:before, *:after{box-sizing:inherit}body{margin:0;padding:0;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;color:rgba(0,0,0,0.8);position:relative;min-height:100vh}h1, h2, h3, h4, h5, h6, dl, dd, ol, ul, menu, figure, blockquote, p, pre, form{margin:0}menu, ol, ul{padding:0;list-style:none;list-style-image:none}main{display:block}a{color:inherit;text-decoration:none}a, button, input{-webkit-tap-highlight-color:transparent}img, svg{vertical-align:middle}button{background:transparent;overflow:visible}button, input, optgroup, select, textarea{margin:0}:root{--reach-tabs:1;--reach-menu-button:1}#speechify-root{font-family:Sohne, sans-serif}div[data-popper-reference-hidden="true"]{visibility:hidden;pointer-events:none}.grecaptcha-badge{visibility:hidden}
/*XCode style (c) Angel Garcia <<EMAIL>>*/.hljs {background: #fff;color: black;
}/* Gray DOCTYPE selectors like WebKit */
.xml .hljs-meta {color: #c0c0c0;
}.hljs-comment,
.hljs-quote {color: #007400;
}.hljs-tag,
.hljs-attribute,
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-name {color: #aa0d91;
}.hljs-variable,
.hljs-template-variable {color: #3F6E74;
}.hljs-code,
.hljs-string,
.hljs-meta .hljs-string {color: #c41a16;
}.hljs-regexp,
.hljs-link {color: #0E0EFF;
}.hljs-title,
.hljs-symbol,
.hljs-bullet,
.hljs-number {color: #1c00cf;
}.hljs-section,
.hljs-meta {color: #643820;
}.hljs-title.class_,
.hljs-class .hljs-title,
.hljs-type,
.hljs-built_in,
.hljs-params {color: #5c2699;
}.hljs-attr {color: #836C28;
}.hljs-subst {color: #000;
}.hljs-formula {background-color: #eee;font-style: italic;
}.hljs-addition {background-color: #baeeba;
}.hljs-deletion {background-color: #ffc8bd;
}.hljs-selector-id,
.hljs-selector-class {color: #9b703f;
}.hljs-doctag,
.hljs-strong {font-weight: bold;
}.hljs-emphasis {font-style: italic;
}
</style><style type="text/css" data-fela-rehydration="556" data-fela-type="KEYFRAME">@-webkit-keyframes k1{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}@-moz-keyframes k1{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}@keyframes k1{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}</style><style type="text/css" data-fela-rehydration="556" data-fela-type="RULE">.a{font-family:medium-content-sans-serif-font, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif}.b{font-weight:400}.c{background-color:rgba(255, 255, 255, 1)}.d{display:none}.m{display:block}.n{position:sticky}.o{top:0}.p{z-index:500}.q{padding:0 24px}.r{align-items:center}.s{border-bottom:solid 1px #F2F2F2}.z{height:41px}.ab{line-height:20px}.ac{display:flex}.ae{height:57px}.af{flex:1 0 auto}.ag{color:inherit}.ah{fill:inherit}.ai{font-size:inherit}.aj{border:none}.ak{font-family:inherit}.al{letter-spacing:inherit}.am{font-weight:inherit}.an{padding:0}.ao{margin:0}.ap{cursor:pointer}.aq:disabled{cursor:not-allowed}.ar:disabled{color:#6B6B6B}.as:disabled{fill:#6B6B6B}.av{width:auto}.aw path{fill:#242424}.ax{height:25px}.ay{margin-left:24px}.az{border-radius:20px}.ba{width:240px}.bb{background:#F9F9F9}.bc path{fill:#6B6B6B}.be{outline:none}.bf{font-family:sohne, "Helvetica Neue", Helvetica, Arial, sans-serif}.bg{font-size:14px}.bh{width:100%}.bi{padding:10px 20px 10px 0}.bj{background-color:transparent}.bk{color:#242424}.bl::placeholder{color:#6B6B6B}.bm{display:inline-block}.bn{margin-left:12px}.bo{margin-right:12px}.bp{border-radius:4px}.bq{height:24px}.bw{background-color:#F9F9F9}.bx{border-radius:50%}.by{height:32px}.bz{width:32px}.ca{flex:1 1 auto}.cb{justify-content:center}.ch{max-width:680px}.ci{min-width:0}.cj{animation:k1 1.2s ease-in-out infinite}.ck{height:100vh}.cl{margin-bottom:16px}.cm{margin-top:48px}.cn{align-items:flex-start}.co{flex-direction:column}.cp{justify-content:space-between}.cq{margin-bottom:24px}.cw{width:80%}.cx{background-color:#F2F2F2}.dd{height:44px}.de{width:44px}.df{margin:auto 0}.dg{margin-bottom:4px}.dh{height:16px}.di{width:120px}.dj{width:80px}.dp{margin-bottom:8px}.dq{width:96%}.dr{width:98%}.ds{width:81%}.dt{margin-left:8px}.du{color:#6B6B6B}.dv{font-size:13px}.dw{height:100%}.ep{color:#FFFFFF}.eq{fill:#FFFFFF}.er{background:#1A8917}.es{border-color:#1A8917}.ew:disabled{cursor:inherit !important}.ex:disabled{opacity:0.3}.ey:disabled:hover{background:#1A8917}.ez:disabled:hover{border-color:#1A8917}.fa{border-radius:99em}.fb{border-width:1px}.fc{border-style:solid}.fd{box-sizing:border-box}.fe{text-decoration:none}.ff{text-align:center}.fg{margin-left:16px}.fh{border:inherit}.fk{margin-right:32px}.fl{position:relative}.fm{fill:#6B6B6B}.fp{background:transparent}.fq svg{margin-left:4px}.fr svg{fill:#6B6B6B}.ft{box-shadow:inset 0 0 0 1px rgba(0, 0, 0, 0.05)}.fu{position:absolute}.fw{max-width:100%}.fx{overflow:hidden}.fy{text-overflow:ellipsis}.fz{white-space:nowrap}.ga{border-bottom:1px solid #F2F2F2}.gb{height:3px}.gc{background-color:#355876}.gd{max-width:1192px}.gj{font-weight:500}.gx{margin:0 8px}.gy{display:inline}.gz{font-size:16px}.ha{line-height:24px}.hb{pointer-events:none}.hc{will-change:opacity, transform}.hd{width:calc(100% - 0px)}.hg{opacity:0}.hh{transform:translateY(89px)}.hi{width:148px}.hj{border-radius:2px}.hk{height:38px}.hl{width:38px}.hn{margin-top:16px}.ho{word-break:break-word}.hu{margin:0 24px}.hy{background:rgba(255, 255, 255, 1)}.hz{border:1px solid #F2F2F2}.ia{box-shadow:0 1px 4px #F2F2F2}.ib{max-height:100vh}.ic{overflow-y:auto}.id{left:0}.ie{top:calc(100vh + 100px)}.if{bottom:calc(100vh + 100px)}.ig{width:10px}.ih{word-wrap:break-word}.ii:after{display:block}.ij:after{content:""}.ik:after{clear:both}.il{line-height:1.23}.im{letter-spacing:0}.in{font-style:normal}.io{font-weight:700}.jj{margin-bottom:-0.27em}.jk{line-height:1.394}.kk{gap:12px}.kl{align-items:baseline}.km{width:36px}.kn{height:36px}.ko{border:2px solid rgba(255, 255, 255, 1)}.kp{z-index:0}.kq{box-shadow:none}.kr{border:1px solid rgba(0, 0, 0, 0.05)}.ks{margin-bottom:2px}.kt{flex-wrap:nowrap}.kv{width:12px}.kw{flex-wrap:wrap}.kx{padding-left:8px}.ky{padding-right:8px}.lz> *{flex-shrink:0}.ma{overflow-x:scroll}.mb::-webkit-scrollbar{display:none}.mc{scrollbar-width:none}.md{-ms-overflow-style:none}.me{width:74px}.mf{flex-direction:row}.mg{z-index:2}.mh{margin-right:4px}.mk{-webkit-user-select:none}.ml{border:0}.mm{fill:rgba(117, 117, 117, 1)}.mp{outline:0}.mq{user-select:none}.mr> svg{pointer-events:none}.na{cursor:progress}.nb{opacity:1}.nc{padding:4px 0}.nf{margin-top:0px}.ng{width:16px}.ni{display:inline-flex}.no{padding:8px 2px}.np svg{color:#6B6B6B}.og{margin-left:auto}.oh{margin-right:auto}.oi{max-width:4624px}.oo{clear:both}.oq{cursor:zoom-in}.or{z-index:auto}.ot{width:1px}.ou{height:1px}.ov{margin:-1px}.ow{clip:rect(0, 0, 0, 0)}.ox{border-width:0}.oy{height:auto}.oz{margin-top:10px}.pa{max-width:728px}.pd{text-decoration:underline}.pe{line-height:1.58}.pf{letter-spacing:-0.004em}.pg{font-family:source-serif-pro, Georgia, Cambria, "Times New Roman", Times, serif}.pw{margin-bottom:-0.46em}.px{line-height:1.12}.py{letter-spacing:-0.022em}.pz{font-weight:600}.qq{margin-bottom:-0.28em}.qr{line-height:1.18}.qx{margin-bottom:-0.31em}.rd{box-shadow:inset 3px 0 0 0 #242424}.re{padding-left:23px}.rf{margin-left:-20px}.rg{font-style:italic}.rh{margin-top:32px}.ri{margin-bottom:14px}.rj{padding-top:24px}.rk{padding-bottom:10px}.rl{background-color:#000000}.rm{width:3px}.rn{margin-right:20px}.ro{margin-bottom:26px}.rp{margin-top:6px}.rq{margin-top:8px}.rr{margin-right:8px}.rs{padding:8px 16px}.rt{border-radius:100px}.ru{transition:background 300ms ease}.rw{border-top:none}.rx{margin-bottom:50px}.ry{height:52px}.rz{max-height:52px}.sa{box-sizing:content-box}.sb{position:static}.sc{z-index:1}.se{max-width:155px}.sk{flex:0 0 auto}.sl{margin-bottom:64px}.sm{margin-bottom:48px}.sz{height:48px}.ta{width:48px}.tb{height:64px}.tc{width:64px}.td{align-self:flex-end}.tj{padding-right:4px}.tq{white-space:pre-wrap}.tr{margin-bottom:54px}.ts{height:0px}.tt{gap:18px}.tu{fill:rgba(61, 61, 61, 1)}.ug{border-bottom:solid 1px #E5E5E5}.uh{margin-top:72px}.ui{padding:24px 0}.uj{margin-bottom:0px}.uk{margin-right:16px}.at:hover:not(:disabled){color:rgba(25, 25, 25, 1)}.au:hover:not(:disabled){fill:rgba(25, 25, 25, 1)}.et:hover{background:#156D12}.eu:hover{border-color:#156D12}.ev:hover{cursor:pointer}.fn:hover{color:#242424}.fo:hover{fill:#242424}.fs:hover svg{fill:#242424}.fv:hover{background-color:rgba(0, 0, 0, 0.1)}.hm:hover{background-color:none}.ku:hover{text-decoration:underline}.mo:hover{fill:rgba(8, 8, 8, 1)}.nd:hover{fill:#000000}.ne:hover p{color:#000000}.nh:hover{color:#000000}.nq:hover svg{color:#000000}.rv:hover{background-color:#F2F2F2}.tv:hover{fill:rgba(25, 25, 25, 1)}.bd:focus-within path{fill:#242424}.mn:focus{fill:rgba(8, 8, 8, 1)}.nr:focus svg{color:#000000}.os:focus{transform:scale(1.01)}.ms:active{border-style:none}</style><style type="text/css" data-fela-rehydration="556" data-fela-type="RULE" media="all and (min-width: 1080px)">.e{display:none}.bv{width:64px}.cg{margin:0 64px}.cv{height:48px}.dc{margin-bottom:52px}.do{margin-bottom:48px}.ef{font-size:14px}.eg{line-height:20px}.em{font-size:13px}.eo{padding:5px 12px}.fj{display:flex}.gi{max-width:250px}.gu{font-size:20px}.gv{line-height:24px}.gw{letter-spacing:0}.ht{margin-bottom:50px}.hx{max-width:680px}.jf{font-size:42px}.jg{margin-top:1.19em}.jh{line-height:52px}.ji{letter-spacing:-0.011em}.jx{font-size:22px}.jy{margin-top:0.92em}.jz{line-height:28px}.ki{align-items:center}.kj{flex-direction:row}.ll{border-top:solid 1px #F2F2F2}.lm{border-bottom:solid 1px #F2F2F2}.ln{margin:32px 0 0}.lo{padding:3px 8px}.lx> *{margin-right:24px}.ly> :last-child{margin-right:0}.mz{margin-top:0px}.nn{margin:0}.on{margin-top:56px}.pt{margin-top:2.14em}.pu{line-height:32px}.pv{letter-spacing:-0.003em}.qm{font-size:24px}.qn{margin-top:1.95em}.qo{line-height:30px}.qp{letter-spacing:-0.016em}.qw{margin-top:1.72em}.rc{margin-top:0.94em}.sj{display:inline-block}.sp{margin-bottom:0}.sq{margin-right:20px}.te{max-width:500px}.ua{margin:40px 0 0}.uf{padding-top:72px}</style><style type="text/css" data-fela-rehydration="556" data-fela-type="RULE" media="all and (max-width: 1079.98px)">.f{display:none}.my{margin-top:0px}.pb{margin-left:auto}.pc{text-align:center}.si{display:inline-block}</style><style type="text/css" data-fela-rehydration="556" data-fela-type="RULE" media="all and (max-width: 903.98px)">.g{display:none}.mx{margin-top:0px}.sh{display:inline-block}</style><style type="text/css" data-fela-rehydration="556" data-fela-type="RULE" media="all and (max-width: 727.98px)">.h{display:none}.mv{margin-top:0px}.mw{margin-right:0px}.sg{display:inline-block}</style><style type="text/css" data-fela-rehydration="556" data-fela-type="RULE" media="all and (max-width: 551.98px)">.i{display:none}.t{display:flex}.u{justify-content:space-between}.br{width:24px}.cc{margin:0 24px}.cr{height:40px}.cy{margin-bottom:44px}.dk{margin-bottom:32px}.dx{font-size:13px}.dy{line-height:20px}.eh{padding:0px 8px 1px}.ge{max-width:150px}.gk{font-size:16px}.gl{letter-spacing:0}.hp{margin-bottom:2px}.ip{font-size:32px}.iq{margin-top:1.01em}.ir{line-height:38px}.is{letter-spacing:-0.014em}.jl{font-size:18px}.jm{margin-top:0.79em}.jn{line-height:24px}.ka{align-items:flex-start}.kb{flex-direction:column-reverse}.kz{margin:24px -24px 0}.la{padding:0}.lp> *{margin-right:8px}.lq> :last-child{margin-right:24px}.mi{margin-left:0px}.mt{margin-top:0px}.mu{margin-right:0px}.nj{margin:0}.ns{border:1px solid #F2F2F2}.nt{border-radius:99em}.nu{padding:0px 16px 0px 12px}.nv{height:38px}.nw{align-items:center}.ny svg{margin-right:8px}.oj{margin-top:40px}.ph{margin-top:1.56em}.pi{line-height:28px}.pj{letter-spacing:-0.003em}.qa{font-size:20px}.qb{margin-top:1.2em}.qs{margin-top:1.23em}.qy{margin-top:0.67em}.sf{display:inline-block}.so{flex-direction:column}.sx{margin-bottom:20px}.sy{margin-right:0}.ti{max-width:100%}.tk{font-size:24px}.tl{line-height:30px}.tm{letter-spacing:-0.016em}.tw{margin:32px 0 0}.ub{padding-top:48px}.nx:hover{border-color:#E5E5E5}</style><style type="text/css" data-fela-rehydration="556" data-fela-type="RULE" media="all and (min-width: 904px) and (max-width: 1079.98px)">.j{display:none}.bu{width:64px}.cf{margin:0 64px}.cu{height:48px}.db{margin-bottom:52px}.dn{margin-bottom:48px}.ed{font-size:14px}.ee{line-height:20px}.ek{font-size:13px}.el{padding:5px 12px}.fi{display:flex}.gh{max-width:250px}.gr{font-size:20px}.gs{line-height:24px}.gt{letter-spacing:0}.hs{margin-bottom:50px}.hw{max-width:680px}.jb{font-size:42px}.jc{margin-top:1.19em}.jd{line-height:52px}.je{letter-spacing:-0.011em}.ju{font-size:22px}.jv{margin-top:0.92em}.jw{line-height:28px}.kg{align-items:center}.kh{flex-direction:row}.lh{border-top:solid 1px #F2F2F2}.li{border-bottom:solid 1px #F2F2F2}.lj{margin:32px 0 0}.lk{padding:3px 8px}.lv> *{margin-right:24px}.lw> :last-child{margin-right:0}.nm{margin:0}.om{margin-top:56px}.pq{margin-top:2.14em}.pr{line-height:32px}.ps{letter-spacing:-0.003em}.qi{font-size:24px}.qj{margin-top:1.95em}.qk{line-height:30px}.ql{letter-spacing:-0.016em}.qv{margin-top:1.72em}.rb{margin-top:0.94em}.sr{margin-bottom:0}.ss{margin-right:20px}.tf{max-width:500px}.tz{margin:40px 0 0}.ue{padding-top:72px}</style><style type="text/css" data-fela-rehydration="556" data-fela-type="RULE" media="all and (min-width: 728px) and (max-width: 903.98px)">.k{display:none}.x{display:flex}.y{justify-content:space-between}.bt{width:64px}.ce{margin:0 48px}.ct{height:48px}.da{margin-bottom:52px}.dm{margin-bottom:48px}.eb{font-size:13px}.ec{line-height:20px}.ej{padding:0px 8px 1px}.gg{max-width:250px}.go{font-size:20px}.gp{line-height:24px}.gq{letter-spacing:0}.hr{margin-bottom:50px}.hv{max-width:680px}.ix{font-size:42px}.iy{margin-top:1.19em}.iz{line-height:52px}.ja{letter-spacing:-0.011em}.jr{font-size:22px}.js{margin-top:0.92em}.jt{line-height:28px}.ke{align-items:center}.kf{flex-direction:row}.ld{border-top:solid 1px #F2F2F2}.le{border-bottom:solid 1px #F2F2F2}.lf{margin:32px 0 0}.lg{padding:3px 8px}.lt> *{margin-right:24px}.lu> :last-child{margin-right:0}.nl{margin:0}.ol{margin-top:56px}.pn{margin-top:2.14em}.po{line-height:32px}.pp{letter-spacing:-0.003em}.qe{font-size:24px}.qf{margin-top:1.95em}.qg{line-height:30px}.qh{letter-spacing:-0.016em}.qu{margin-top:1.72em}.ra{margin-top:0.94em}.st{margin-bottom:0}.su{margin-right:20px}.tg{max-width:500px}.ty{margin:40px 0 0}.ud{padding-top:72px}</style><style type="text/css" data-fela-rehydration="556" data-fela-type="RULE" media="all and (min-width: 552px) and (max-width: 727.98px)">.l{display:none}.v{display:flex}.w{justify-content:space-between}.bs{width:24px}.cd{margin:0 24px}.cs{height:40px}.cz{margin-bottom:44px}.dl{margin-bottom:32px}.dz{font-size:13px}.ea{line-height:20px}.ei{padding:0px 8px 1px}.gf{max-width:150px}.gm{font-size:16px}.gn{letter-spacing:0}.hq{margin-bottom:2px}.it{font-size:32px}.iu{margin-top:1.01em}.iv{line-height:38px}.iw{letter-spacing:-0.014em}.jo{font-size:18px}.jp{margin-top:0.79em}.jq{line-height:24px}.kc{align-items:flex-start}.kd{flex-direction:column-reverse}.lb{margin:24px 0 0}.lc{padding:0}.lr> *{margin-right:8px}.ls> :last-child{margin-right:8px}.mj{margin-left:0px}.nk{margin:0}.nz{border:1px solid #F2F2F2}.oa{border-radius:99em}.ob{padding:0px 16px 0px 12px}.oc{height:38px}.od{align-items:center}.of svg{margin-right:8px}.ok{margin-top:40px}.pk{margin-top:1.56em}.pl{line-height:28px}.pm{letter-spacing:-0.003em}.qc{font-size:20px}.qd{margin-top:1.2em}.qt{margin-top:1.23em}.qz{margin-top:0.67em}.sn{flex-direction:column}.sv{margin-bottom:20px}.sw{margin-right:0}.th{max-width:100%}.tn{font-size:24px}.to{line-height:30px}.tp{letter-spacing:-0.016em}.tx{margin:32px 0 0}.uc{padding-top:48px}.oe:hover{border-color:#E5E5E5}</style><style type="text/css" data-fela-rehydration="556" data-fela-type="RULE" media="print">.sd{display:none}</style><style type="text/css" data-fela-rehydration="556" data-fela-type="RULE" media="(prefers-reduced-motion: no-preference)">.he{transition:opacity 200ms}.op{transition:transform 300ms cubic-bezier(0.2, 0, 0.2, 1)}</style><style type="text/css" data-fela-rehydration="556" data-fela-type="RULE" media="all and (max-width: 1232px)">.hf{display:none}</style></head><body><div id="root"><div class="a b c"><a href="/sitemap/sitemap.xml" class="d">Sitemap</a><div class="e f g h i j k l"></div><script>document.domain = document.domain;</script><div class="m c"><div class="m n o p c"><div class="q r s t u v w x y j e z ab"><a class="du ah dv bf al b an ao ap aq ar as at au t v x j e r dw ab" href="https://rsci.app.link/?%24canonical_url=https%3A%2F%2Fmedium.com%2Fp%2Ff1ad81785a76&amp;%7Efeature=LoOpenInAppButton&amp;%7Echannel=ShowPostUnderCollection&amp;%7Estage=mobileNavBar&amp;source=post_page---top_nav_layout_nav-----------------------------------------" rel="noopener follow">Open in app<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="none" viewBox="0 0 10 10" class="dt"><path fill="currentColor" d="M.985 8.485a.375.375 0 1 0 .53.53zM8.75 1.25h.375A.375.375 0 0 0 8.75.875zM8.375 6.5a.375.375 0 1 0 .75 0zM3.5.875a.375.375 0 1 0 0 .75zm-1.985 8.14 7.5-7.5-.53-.53-7.5 7.5zm6.86-7.765V6.5h.75V1.25zM3.5 1.625h5.25v-.75H3.5z"></path></svg></a><div class="ac r"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><button class="bf b dx dy eh dz ea ei eb ec ej ek ee el em eg eo ep eq er es et eu ev ew ex ey ez fa fb fc fd bm fe ff" data-testid="headerSignUpButton">Sign up</button></span></p><div class="fg m"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerSignInButton" rel="noopener follow" href="/m/signin?operation=login&amp;redirect=https%3A%2F%2Fmedium.com%2Fdata-science%2Frock-paper-scissors-and-the-stock-market-f1ad81785a76&amp;source=post_page---top_nav_layout_nav-----------------------global_nav------------------" data-discover="true">Sign in</a></span></p></div></div></div><div class="q r s ac ae"><div class="ac r af"><a class="ag ah ai aj ak al am an ao ap aq ar as at au ac" aria-label="Homepage" data-testid="headerMediumLogo" rel="noopener follow" href="/?source=post_page---top_nav_layout_nav-----------------------------------------" data-discover="true"><svg xmlns="http://www.w3.org/2000/svg" width="719" height="160" fill="none" aria-labelledby="wordmark-medium-desc" viewBox="0 0 719 160" class="av aw ax"><desc id="wordmark-medium-desc">Medium Logo</desc><path fill="#242424" d="m174.104 9.734.215-.047V8.02H130.39L89.6 103.89 48.81 8.021H1.472v1.666l.212.047c8.018 1.81 12.09 4.509 12.09 14.242V137.93c0 9.734-4.087 12.433-12.106 14.243l-.212.047v1.671h32.118v-1.665l-.213-.048c-8.018-1.809-12.089-4.509-12.089-14.242V30.586l52.399 123.305h2.972l53.925-126.743V140.75c-.687 7.688-4.721 10.062-11.982 11.701l-.215.05v1.652h55.948v-1.652l-.215-.05c-7.269-1.639-11.4-4.013-12.087-11.701l-.037-116.774h.037c0-9.733 4.071-12.432 12.087-14.242m25.555 75.488c.915-20.474 8.268-35.252 20.606-35.507 3.806.063 6.998 1.312 9.479 3.714 5.272 5.118 7.751 15.812 7.368 31.793zm-.553 5.77h65.573v-.275c-.186-15.656-4.721-27.834-13.466-36.196-7.559-7.227-18.751-11.203-30.507-11.203h-.263c-6.101 0-13.584 1.48-18.909 4.16-6.061 2.807-11.407 7.003-15.855 12.511-7.161 8.874-11.499 20.866-12.554 34.343q-.05.606-.092 1.212a50 50 0 0 0-.065 1.151 85.807 85.807 0 0 0-.094 5.689c.71 30.524 17.198 54.917 46.483 54.917 25.705 0 40.675-18.791 44.407-44.013l-1.886-.664c-6.557 13.556-18.334 21.771-31.738 20.769-18.297-1.369-32.314-19.922-31.042-42.395m139.722 41.359c-2.151 5.101-6.639 7.908-12.653 7.908s-11.513-4.129-15.418-11.63c-4.197-8.053-6.405-19.436-6.405-32.92 0-28.067 8.729-46.22 22.24-46.22 5.657 0 10.111 2.807 12.236 7.704zm43.499 20.008c-8.019-1.897-12.089-4.722-12.089-14.951V1.309l-48.716 14.353v1.757l.299-.024c6.72-.543 11.278.386 13.925 2.83 2.072 1.915 3.082 4.853 3.082 8.987v18.66c-4.803-3.067-10.516-4.56-17.448-4.56-14.059 0-26.909 5.92-36.176 16.672-9.66 11.205-14.767 26.518-14.767 44.278-.003 31.72 15.612 53.039 38.851 53.039 13.595 0 24.533-7.449 29.54-20.013v16.865h43.711v-1.746zM424.1 19.819c0-9.904-7.468-17.374-17.375-17.374-9.859 0-17.573 7.632-17.573 17.374s7.721 17.374 17.573 17.374c9.907 0 17.375-7.47 17.375-17.374m11.499 132.546c-8.019-1.897-12.089-4.722-12.089-14.951h-.035V43.635l-43.714 12.551v1.705l.263.024c9.458.842 12.047 4.1 12.047 15.152v81.086h43.751v-1.746zm112.013 0c-8.018-1.897-12.089-4.722-12.089-14.951V43.635l-41.621 12.137v1.71l.246.026c7.733.813 9.967 4.257 9.967 15.36v59.279c-2.578 5.102-7.415 8.131-13.274 8.336-9.503 0-14.736-6.419-14.736-18.073V43.638l-43.714 12.55v1.703l.262.024c9.459.84 12.05 4.097 12.05 15.152v50.17a56.3 56.3 0 0 0 .91 10.444l.787 3.423c3.701 13.262 13.398 20.197 28.59 20.197 12.868 0 24.147-7.966 29.115-20.43v17.311h43.714v-1.747zm169.818 1.788v-1.749l-.213-.05c-8.7-2.006-12.089-5.789-12.089-13.49v-63.79c0-19.89-11.171-31.761-29.883-31.761-13.64 0-25.141 7.882-29.569 20.16-3.517-13.01-13.639-20.16-28.606-20.16-13.146 0-23.449 6.938-27.869 18.657V43.643L545.487 55.68v1.715l.263.024c9.345.829 12.047 4.181 12.047 14.95v81.784h40.787v-1.746l-.215-.053c-6.941-1.631-9.181-4.606-9.181-12.239V66.998c1.836-4.289 5.537-9.37 12.853-9.37 9.086 0 13.692 6.296 13.692 18.697v77.828h40.797v-1.746l-.215-.053c-6.94-1.631-9.18-4.606-9.18-12.239V75.066a42 42 0 0 0-.578-7.26c1.947-4.661 5.86-10.177 13.475-10.177 9.214 0 13.691 6.114 13.691 18.696v77.828z"></path></svg></a><div class="ay i"><div class="ac aj az ba bb r bc bd"><div class="bm" aria-hidden="false" aria-describedby="searchResults" aria-labelledby="searchResults" aria-haspopup="listbox" role="listbox"></div><div class="bn bo ac"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M4.092 11.06a6.95 6.95 0 1 1 13.9 0 6.95 6.95 0 0 1-13.9 0m6.95-8.05a8.05 8.05 0 1 0 5.13 14.26l3.75 3.75a.56.56 0 1 0 .79-.79l-3.73-3.73A8.05 8.05 0 0 0 11.042 3z" clip-rule="evenodd"></path></svg></div><input role="combobox" aria-controls="searchResults" aria-expanded="false" aria-label="search" data-testid="headerSearchInput" tabindex="0" class="aj be bf bg ab bh bi bj bk bl" placeholder="Search" value=""/></div></div></div><div class="i l x fi fj"><div class="fk ac"><span data-dd-action-name="Susi presentation tracker new_post_topnav"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerWriteButton" rel="noopener follow" href="/m/signin?operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fnew-story&amp;source=---top_nav_layout_nav-----------------------new_post_topnav------------------" data-discover="true"><div class="bf b bg ab du fl fm ac r fn fo"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" aria-label="Write"><path fill="currentColor" d="M14 4a.5.5 0 0 0 0-1zm7 6a.5.5 0 0 0-1 0zm-7-7H4v1h10zM3 4v16h1V4zm1 17h16v-1H4zm17-1V10h-1v10zm-1 1a1 1 0 0 0 1-1h-1zM3 20a1 1 0 0 0 1 1v-1zM4 3a1 1 0 0 0-1 1h1z"></path><path stroke="currentColor" d="m17.5 4.5-8.458 8.458a.25.25 0 0 0-.06.098l-.824 2.47a.25.25 0 0 0 .316.316l2.47-.823a.25.25 0 0 0 .098-.06L19.5 6.5m-2-2 2.323-2.323a.25.25 0 0 1 .354 0l1.646 1.646a.25.25 0 0 1 0 .354L19.5 6.5m-2-2 2 2"></path></svg><div class="dt m">Write</div></div></a></span></div></div><div class="l k j e"><div class="fk ac"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerSearchButton" rel="noopener follow" href="/search?source=post_page---top_nav_layout_nav-----------------------------------------" data-discover="true"><div class="bf b bg ab du fl fm ac r fn fo"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" aria-label="Search"><path fill="currentColor" fill-rule="evenodd" d="M4.092 11.06a6.95 6.95 0 1 1 13.9 0 6.95 6.95 0 0 1-13.9 0m6.95-8.05a8.05 8.05 0 1 0 5.13 14.26l3.75 3.75a.56.56 0 1 0 .79-.79l-3.73-3.73A8.05 8.05 0 0 0 11.042 3z" clip-rule="evenodd"></path></svg></div></a></div></div><div class="fk i l k"><div class="ac r"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><button class="bf b dx dy eh dz ea ei eb ec ej ek ee el em eg eo ep eq er es et eu ev ew ex ey ez fa fb fc fd bm fe ff" data-testid="headerSignUpButton">Sign up</button></span></p><div class="fg m"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerSignInButton" rel="noopener follow" href="/m/signin?operation=login&amp;redirect=https%3A%2F%2Fmedium.com%2Fdata-science%2Frock-paper-scissors-and-the-stock-market-f1ad81785a76&amp;source=post_page---top_nav_layout_nav-----------------------global_nav------------------" data-discover="true">Sign in</a></span></p></div></div></div><div class="m" aria-hidden="false"><button class="aj fp an ac r ap fl fq fr fs" aria-label="user options menu" data-testid="headerUserIcon"><div class="m fl"><img alt="" class="m fd bx by bz cx" src="https://miro.medium.com/v2/resize:fill:64:64/1*dmbNkD5D-u45r44go_cf0g.png" width="32" height="32" loading="lazy" role="presentation"/><div class="ft bx m by bz fu o aj fv"></div></div></button></div></div></div><div class="ac"><div class="ca bh"><div class="m"><div><div class="ga bh m"><div class="gb bh gc"></div><div class="ac cb"><div class="cc cd ce cf cg gd ci bh"><div class="ae ac r"><div class="ge gf gg gh gi m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://medium.com/data-science?source=post_page---publication_nav-7f60cf5620c9-f1ad81785a76---------------------------------------" rel="noopener follow"><h2 class="bf gj gk dy gl gm ea gn go gp gq gr gs gt gu gv gw bk"><div class="fw fx fy fz">TDS Archive</div></h2></a></div><div class="t v k j e"><span class="gx gy" aria-hidden="true"><span class="bf b gz ha bk">·</span></span><p class="bf b gz ha bk"></p></div></div></div></div></div></div><div class="hb fu hc hd o he hf hg hh"><div class="ac cb"><div class="cc cd ce cf cg gd ci bh"><div class="hi m"><div class="hb"><div class="ac cn co"><a href="https://medium.com/data-science?source=post_page---post_publication_sidebar-7f60cf5620c9-f1ad81785a76---------------------------------------" rel="noopener follow"><div class="fl"><img alt="TDS Archive" class="cx hj m hl hk" src="https://miro.medium.com/v2/resize:fill:76:76/1*JEuS4KBdakUcjg9sC7Wo4A.png" width="38" height="38" loading="lazy"/><div class="hj m hk hl fu o ft hm"></div></div></a><div class="hn m"></div><p class="bf b bg ab du"><span class="ho">An archive of data science, data analytics, data engineering, machine learning, and artificial intelligence writing from the former Towards Data Science Medium publication.</span></p><div class="hn m"></div><p class="bf b bg ab bk"></p></div></div></div></div></div></div><div class="hp hq hr hs ht m"><div class="ac cb"><div class="ci bh hu hv hw hx"></div></div><article><div class="m"><div class="m"><span class="m"></span><section><div><div class="fu id ie if ig hb"></div><div class="ho ih ii ij ik"><div class="ac cb"><div class="ci bh hu hv hw hx"><div><h1 id="9fff" class="pw-post-title il im in bf io ip iq ir is it iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj bk" data-testid="storyTitle">Rock, Paper, Scissors, and the Stock Market</h1></div><div><h2 id="1dfe" class="pw-subtitle-paragraph jk im in bf b jl jm jn jo jp jq jr js jt ju jv jw jx jy jz cq du">Recounting my experience in the Rock, Paper, Scissors Kaggle competition, and the resemblance of the competition structure to the stock market.</h2><div><div class="speechify-ignore ac cp"><div class="speechify-ignore bh m"><div class="ac ka kb kc kd ke kf kg kh ki kj kk"><div class="ac r kk"><div class="ac kl"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><a rel="noopener follow" href="/@kdanielive?source=post_page---byline--f1ad81785a76---------------------------------------" data-discover="true"><div class="m km kn bx ko kp"><div class="m fl"><img alt="Seouk Jun Kim" class="m fd bx by bz cx" src="https://miro.medium.com/v2/resize:fill:64:64/1*<EMAIL>" width="32" height="32" loading="lazy" data-testid="authorPhoto"/><div class="kq bx m by bz fu o kr fv"></div></div></div></a></div></div></div></div><span class="bf b bg ab bk"><div class="ks ac r"><div class="ac r kt"><div class="ac r"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><span class="bf b bg ab bk"><a class="ag ah ai fh ak al am an ao ap aq ar as ku" data-testid="authorName" rel="noopener follow" href="/@kdanielive?source=post_page---byline--f1ad81785a76---------------------------------------" data-discover="true">Seouk Jun Kim</a></span></div></div></div></div><div class="kv bm"></div></div></div></span></div><div class="ac r kw"><span class="bf b bg ab du"><div class="ac af"><span data-testid="storyReadTime">12 min read</span><div class="kx ky m" aria-hidden="true"><span class="m" aria-hidden="true"><span class="bf b bg ab du">·</span></span></div><span data-testid="storyPublishDate">Mar 13, 2021</span></div></span></div></div><div class="ac cp kz la lb lc ld le lf lg lh li lj lk ll lm ln lo"><div class="i l x fi fj r"><div class="me m"><div class="ac r mf mg"><div class="pw-multi-vote-icon fl mh mi mj mk"><span data-dd-action-name="Susi presentation tracker clap_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerClapButton" rel="noopener follow" href="/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fdata-science%2Ff1ad81785a76&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fdata-science%2Frock-paper-scissors-and-the-stock-market-f1ad81785a76&amp;user=Seouk+Jun+Kim&amp;userId=b50aa26e058d&amp;source=---header_actions--f1ad81785a76---------------------clap_footer------------------" data-discover="true"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><div class="ml ap mm mn mo mp an mq mr ms mk" role="presentation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="clap"><path fill-rule="evenodd" d="M11.37.828 12 3.282l.63-2.454zM13.916 3.953l1.523-2.112-1.184-.39zM8.589 1.84l1.522 2.112-.337-2.501zM18.523 18.92c-.86.86-1.75 1.246-2.62 1.33a6 6 0 0 0 .407-.372c2.388-2.389 2.86-4.951 1.399-7.623l-.912-1.603-.79-1.672c-.26-.56-.194-.98.203-1.288a.7.7 0 0 1 .546-.132c.283.046.546.231.728.5l2.363 4.157c.976 1.624 1.141 4.237-1.324 6.702m-10.999-.438L3.37 14.328a.828.828 0 0 1 .585-1.408.83.83 0 0 1 .585.242l2.158 2.157a.365.365 0 0 0 .516-.516l-2.157-2.158-1.449-1.449a.826.826 0 0 1 1.167-1.17l3.438 3.44a.363.363 0 0 0 .516 0 .364.364 0 0 0 0-.516L5.293 9.513l-.97-.97a.826.826 0 0 1 0-*********** 0 0 1 1.167 0l.97.968 3.437 3.436a.36.36 0 0 0 .517 0 .366.366 0 0 0 0-.516L6.977 7.83a.82.82 0 0 1-.241-.584.82.82 0 0 1 .824-.826c.219 0 .43.087.584.242l5.787 5.787a.366.366 0 0 0 .587-.415l-1.117-2.363c-.26-.56-.194-.98.204-1.289a.7.7 0 0 1 .546-.132c.283.046.545.232.727.501l2.193 3.86c1.302 2.38.883 4.59-1.277 6.75-1.156 1.156-2.602 1.627-4.19 1.367-1.418-.236-2.866-1.033-4.079-2.246M10.75 5.971l2.12 2.12c-.41.502-.465 1.17-.128 1.89l.22.465-3.523-3.523a.8.8 0 0 1-.097-.368c0-.22.086-.428.241-.584a.847.847 0 0 1 1.167 0m7.355 1.705c-.31-.461-.746-.758-1.23-.837a1.44 1.44 0 0 0-1.11.275c-.312.24-.505.543-.59.881a1.74 1.74 0 0 0-.906-.465 1.47 1.47 0 0 0-.82.106l-2.182-2.182a1.56 1.56 0 0 0-2.2 0 1.54 1.54 0 0 0-.396.701 1.56 1.56 0 0 0-2.21-.01 1.55 1.55 0 0 0-.416.753c-.624-.624-1.649-.624-2.237-.037a1.557 1.557 0 0 0 0 2.2c-.239.1-.501.238-.715.453a1.56 1.56 0 0 0 0 2.2l.516.515a1.556 1.556 0 0 0-.753 2.615L7.01 19c1.32 1.319 2.909 2.189 4.475 2.449q.482.08.971.08c.85 0 1.653-.198 2.393-.579.**************.686.054 1.266 0 2.457-.52 3.505-1.567 2.763-2.763 2.552-5.734 1.439-7.586z" clip-rule="evenodd"></path></svg></div></div></div></div></a></span></div><div class="pw-multi-vote-count m mt mu mv mw mx my mz"><p class="bf b dv ab du"><span class="na">--</span></p></div></div></div><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button class="ap ml nb nc ac r fm nd ne" aria-label="responses"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="nf"><path d="M18.006 16.803c1.533-1.456 2.234-3.325 2.234-5.321C20.24 7.357 16.709 4 12.191 4S4 7.357 4 11.482c0 4.126 3.674 7.482 8.191 7.482.817 0 1.622-.111 2.393-.327.231.2.48.391.744.559 1.06.693 2.203 1.044 3.399 1.044.224-.008.4-.112.486-.287a.49.49 0 0 0-.042-.518c-.495-.67-.845-1.364-1.04-2.057a4 4 0 0 1-.125-.598zm-3.122 1.055-.067-.223-.315.096a8 8 0 0 1-2.311.338c-4.023 0-7.292-2.955-7.292-6.587 0-3.633 3.269-6.588 7.292-6.588 4.014 0 7.112 2.958 7.112 6.593 0 1.794-.608 3.469-2.027 4.72l-.195.168v.255c0 .056 0 .151.016.295.025.231.081.478.154.733.154.558.398 1.117.722 1.659a5.3 5.3 0 0 1-2.165-.845c-.276-.176-.714-.383-.941-.59z"></path></svg></button></div></div></div></div><div class="ac r lp lq lr ls lt lu lv lw lx ly lz ma mb mc md"><div class="ng l k j e"></div><div class="i l"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><span data-dd-action-name="Susi presentation tracker bookmark_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerBookmarkButton" rel="noopener follow" href="/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2Ff1ad81785a76&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fdata-science%2Frock-paper-scissors-and-the-stock-market-f1ad81785a76&amp;source=---header_actions--f1ad81785a76---------------------bookmark_footer------------------" data-discover="true"><svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="none" viewBox="0 0 25 25" class="du nh" aria-label="Add to list bookmark button"><path fill="currentColor" d="M18 2.5a.5.5 0 0 1 1 0V5h2.5a.5.5 0 0 1 0 1H19v2.5a.5.5 0 1 1-1 0V6h-2.5a.5.5 0 0 1 0-1H18zM7 7a1 1 0 0 1 1-1h3.5a.5.5 0 0 0 0-1H8a2 2 0 0 0-2 2v14a.5.5 0 0 0 .805.396L12.5 17l5.695 4.396A.5.5 0 0 0 19 21v-8.5a.5.5 0 0 0-1 0v7.485l-5.195-4.012a.5.5 0 0 0-.61 0L7 19.985z"></path></svg></a></span></div></div></div></div><div class="fd ni cn"><div class="m af"><div class="ac cb"><div class="nj nk nl nm nn fw ci bh"><div class="ac"><div class="bm" aria-hidden="false" role="tooltip"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button aria-label="Listen" data-testid="audioPlayButton" class="ag fm ai fh ak al am no ao ap aq ex np nq ne nr ns nt nu nv t nw nx ny nz oa ob oc v od oe of"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a9 9 0 1 1 18 0 9 9 0 0 1-18 0m9-10C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m3.376 10.416-4.599 3.066a.5.5 0 0 1-.777-.416V8.934a.5.5 0 0 1 .777-.416l4.599 3.066a.5.5 0 0 1 0 .832" clip-rule="evenodd"></path></svg><div class="k j e"><p class="bf b bg ab du">Listen</p></div></button></div></div></div></div></div></div></div></div></div><div class="bm" aria-hidden="false" aria-describedby="postFooterSocialMenu" aria-labelledby="postFooterSocialMenu"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button aria-controls="postFooterSocialMenu" aria-expanded="false" aria-label="Share Post" data-testid="headerSocialShareButton" class="ag fm ai fh ak al am no ao ap aq ex np nq ne nr ns nt nu nv t nw nx ny nz oa ob oc v od oe of"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M15.218 4.931a.4.4 0 0 1-.118.132l.012.006a.45.45 0 0 1-.292.074.5.5 0 0 1-.3-.13l-2.02-2.02v7.07c0 .28-.23.5-.5.5s-.5-.22-.5-.5v-7.04l-2 2a.45.45 0 0 1-.57.04h-.02a.4.4 0 0 1-.16-.3.4.4 0 0 1 .1-.32l2.8-2.8a.5.5 0 0 1 .7 0l2.8 2.79a.42.42 0 0 1 .068.498m-.106.138.008.004v-.01zM16 7.063h1.5a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-11c-1.1 0-2-.9-2-2v-10a2 2 0 0 1 2-2H8a.5.5 0 0 1 .********* 0 0 1 .********* 0 0 1-.********* 0 0 1-.35.15H6.4c-.5 0-.9.4-.9.9v10.2a.9.9 0 0 0 .9.9h11.2c.5 0 .9-.4.9-.9v-10.2c0-.5-.4-.9-.9-.9H16a.5.5 0 0 1 0-1" clip-rule="evenodd"></path></svg><div class="k j e"><p class="bf b bg ab du">Share</p></div></button></div></div></div></div></div></div></div></div></div></div><figure class="oj ok ol om on oo og oh paragraph-image"><div role="button" tabindex="0" class="op oq fl or bh os"><span class="fu ot ou an ov fx ow fz ox speechify-ignore">Press enter or click to view image in full size</span><div class="og oh oi"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*hwW2M6hn4fd98QlEmbxmog.jpeg 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*hwW2M6hn4fd98QlEmbxmog.jpeg 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*hwW2M6hn4fd98QlEmbxmog.jpeg 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*hwW2M6hn4fd98QlEmbxmog.jpeg 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*hwW2M6hn4fd98QlEmbxmog.jpeg 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*hwW2M6hn4fd98QlEmbxmog.jpeg 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*hwW2M6hn4fd98QlEmbxmog.jpeg 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*hwW2M6hn4fd98QlEmbxmog.jpeg 640w, https://miro.medium.com/v2/resize:fit:720/1*hwW2M6hn4fd98QlEmbxmog.jpeg 720w, https://miro.medium.com/v2/resize:fit:750/1*hwW2M6hn4fd98QlEmbxmog.jpeg 750w, https://miro.medium.com/v2/resize:fit:786/1*hwW2M6hn4fd98QlEmbxmog.jpeg 786w, https://miro.medium.com/v2/resize:fit:828/1*hwW2M6hn4fd98QlEmbxmog.jpeg 828w, https://miro.medium.com/v2/resize:fit:1100/1*hwW2M6hn4fd98QlEmbxmog.jpeg 1100w, https://miro.medium.com/v2/resize:fit:1400/1*hwW2M6hn4fd98QlEmbxmog.jpeg 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="" class="bh fw oy c" width="700" height="525" loading="eager" role="presentation"/></picture></div></div><figcaption class="oz ff pa og oh pb pc bf b bg ab du">Photo by <a class="ag pd" href="https://unsplash.com/@imanitor?utm_source=unsplash&amp;utm_medium=referral&amp;utm_content=creditCopyText" rel="noopener ugc nofollow" target="_blank">Fadilah N. Imani</a> on <a class="ag pd" rel="noopener ugc nofollow" href="/s/photos/rock-paper-scissors?utm_source=unsplash&amp;utm_medium=referral&amp;utm_content=creditCopyText" target="_blank" data-discover="true">Unsplash</a></figcaption></figure><p id="7d8a" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">You would not normally relate rock, paper, scissors (RPS for short) to the stock market. It actually seems absurd to do so. And granted, a single game of RPS, or just a series of a thousand games of RPS has no relation whatsoever to the stock market.</p><p id="bb59" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">But when RPS is put in a competition format where various algorithms battle against each other for points in the leaderboard,<a class="ag pd" href="https://www.kaggle.com/c/rock-paper-scissors/discussion/215477" rel="noopener ugc nofollow" target="_blank"> my theory is that there are some notable resemblances to the stock market</a>.</p><p id="23b6" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">More precisely, Kaggle’s recent competition on RPS that started in November, 2020, and ended in February, 2021 seems to have some characteristics of our usual stock market, and I personally achieved 32nd rank (top 2%) by using portfolio optimization theory to take advantage of this similarity.</p><p id="d28e" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Note that much of this article is based on intuition; there is no statistical evidence to back up my arguments. But the article should be informative for those interested in market efficiency, Kaggle competitions, portfolio optimization theory, etc.</p><h2 id="6549" class="px py in bf pz qa qb jn gl qc qd jq gn qe qf qg qh qi qj qk ql qm qn qo qp qq bk">Data Science in Rock, Paper, Scissors Context</h2><h3 id="6f85" class="qr py in bf pz gk qs dy gl gm qt ea gn go qu gp gq gr qv gs gt gu qw gv gw qx bk">Kaggle’s RPS Competition</h3><p id="ce83" class="pw-post-body-paragraph pe pf in pg b jl qy pi pj jo qz pl pm go ra po pp gr rb pr ps gu rc pu pv pw ho bk">Four months ago, probably near the start of November of 2020, Kaggle launched the RPS competition. Below is an abbreviated excerpt from the <a class="ag pd" href="https://www.kaggle.com/c/rock-paper-scissors/overview/description" rel="noopener ugc nofollow" target="_blank">competition description page</a>.</p><blockquote class="rd re rf"><p id="f48f" class="pe pf rg pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Rock, Paper, Scissors (sometimes called roshambo) has been a staple to settle playground disagreements or determine who gets to ride in the front seat on a road trip…</p><p id="3c00" class="pe pf rg pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Studies have shown that a Rock, Paper, Scissors AI can consistently beat human opponents. With previous games as input, it studies patterns to understand a player’s tendencies. But what happens when we expand the simple “Best-of-3” game to be “Best-of-1000”? How well can artificial intelligence perform?</p><p id="baae" class="pe pf rg pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">In this simulation competition, you will create an AI to play against others in many rounds of this classic game… It’s possible to greatly outperform a random player when the matches involve non-random agents. A strong AI can consistently beat predictable AI…</p></blockquote><p id="a898" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">As explained above, the goal of each participant in the competition is to submit an algorithm that plays 1000 consecutive RPS matches against another bot (submitted by some other participant). An algorithm wins that episode (=match of 1000 games) if it wins by more than 20 points, with 1 point rewarded for a win, 0 for a draw, and -1 for a loss. And for each victory in an episode, the algorithm is rewarded learderboard points according to the <a class="ag pd" href="https://www.kaggle.com/c/rock-paper-scissors/overview/evaluation" rel="noopener ugc nofollow" target="_blank">competition’s evaluation metric</a>.</p><p id="249a" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">At first sight, this seems rather pointless. The intuitive rational move in an RPS game is to play random, no debate. If we were to play a single episode against any bot, we should by all means play random.</p><p id="7400" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">But the point of the competition lies in this part of the description:</p><blockquote class="rd re rf"><p id="73cb" class="pe pf rg pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk"><em class="in">It’s possible to greatly outperform a random player when the matches involve non-random agents.</em></p></blockquote><p id="6074" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">If there are even a few dozens of non-random bots in the competition, then another bot that can exploit this non-randomness will have a higher chance of outperforming all other random bots as long as there is a sufficient number of matches. This is because a superior non-random algorithm will likely tie against other random bots, but win against inferior non-random algorithms, gaining extra points in the leaderboard that random bots cannot gain.</p><p id="01b6" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">So while the Nash equilibrium dictates that one should play random under the assumption that every other bot acts rationally, the very fact that not all bots are rational allows for non-random agents to outperform in the leaderboard.</p><p id="f0ad" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Already we see a resemblance to the stock market. If all market participants acted rationally with given information, then the stock market would be extremely efficient (=unpredictable). It is the existing irrationality of market participants that gives birth to exploitable market inefficiencies for excess return (although this part could be debatable after various risk-adjustment).</p><h3 id="9d16" class="qr py in bf pz gk qs dy gl gm qt ea gn go qu gp gq gr qv gs gt gu qw gv gw qx bk">Previous RPS Competitions</h3><p id="9ae9" class="pw-post-body-paragraph pe pf in pg b jl qy pi pj jo qz pl pm go ra po pp gr rb pr ps gu rc pu pv pw ho bk">Believe it or not there had been several RPS competitions before Kaggle. The most recent one is the RPSC<a class="ag pd" href="http://www.rpscontest.com/" rel="noopener ugc nofollow" target="_blank">ontest </a>website. Although there were no prizes given out to the monthly winners, people actively participated in the contest, which led to the birth of some truly remarkable algorithms.</p><p id="f978" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">In Kaggle discussions, there were other frequently mentioned algorithms, such as <em class="rg">Greenberg</em> and <em class="rg">Iocaine Powder. </em>These algorithms performed in competitions hosted even before RPSContest, in the year 2001 and before. The best detailed descriptions of these algorithms can be found <a class="ag pd" href="http://webdocs.cs.ualberta.ca/~darse/rsbpc.html" rel="noopener ugc nofollow" target="_blank">here</a>, but to briefly put the logic in context, these highly successful algorithms utilized memory-searching algorithms with multiple layers of meta strategy that decided on whether to make the move that beats the predicted move from past history, or the move that beats that move, or the move that beats the move that beats that move, etc.</p><p id="fddb" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">To put the strength of these algorithms in context, <em class="rg">Iocaine Powder </em>and <em class="rg">Greenberg</em> can easily beat machine learning based algorithms such as decision tree and multi-layer perceptrons (feed-forward neural network). In fact, using machine learning algorithms requires extra care to avoid over-fitting, as other memory-searching algorithms and statistical models react much quicker to change in opponent’s strategy, which is essential in securing a +20 points victory.</p><h2 id="92bb" class="px py in bf pz qa qb jn gl qc qd jq gn qe qf qg qh qi qj qk ql qm qn qo qp qq bk">Market Efficiency and Data Science in the Stock Market Context</h2><h3 id="9f62" class="qr py in bf pz gk qs dy gl gm qt ea gn go qu gp gq gr qv gs gt gu qw gv gw qx bk">Efficient Market?</h3><p id="4f0b" class="pw-post-body-paragraph pe pf in pg b jl qy pi pj jo qz pl pm go ra po pp gr rb pr ps gu rc pu pv pw ho bk">Okay, so now that we’ve talked about the place of RPS in the traditional data science community, let’s examine what it means to have an <a class="ag pd" href="https://www.investopedia.com/terms/m/marketefficiency.asp" rel="noopener ugc nofollow" target="_blank">efficient market</a>.</p><p id="25a3" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">A fully efficient market incorporates all relevant information into its market price instantly. This means that, as the market price already reflects the asset’s true value, there are no opportunities to find “under-valued” or “over-valued” assets. By this definition, it is impossible to consistently outperform the market — all those with long track records of superior performance are just fortunate.</p><p id="a3e7" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">There are different degrees of proposed market efficiency. The weak form suggests that past price movements are inconsequential in predicting future returns. In other words, trend-based trading or technical analysis based on price movements alone are not sufficient to consistently outperform the market. This has been one of the focal points of researches on cryptocurrency market efficiency in relatively recent academics, which could be interesting to read papers on.</p><p id="3880" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">The semi-strong market efficiency states that the market price quickly responds to all publicly available information. In this perspective, even fundamental analysis is deemed obsolete. The only way to beat the market in this way will be to gain non-public information, which hangs dangerously on the line of illegal insider trading.</p><p id="3e67" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">The strong form argues that the market reflects all information — both public and undisclosed — instantly, meaning that outperforming the market becomes a nigh impossibility.</p><p id="490f" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">There however had been certain classes of stocks that seem to outperform the market. To incorporate these anomalies in the efficient market context, we can draw on <a class="ag pd" href="https://onlinelibrary.wiley.com/doi/full/10.1111/j.1540-6261.1992.tb04398.x" rel="noopener ugc nofollow" target="_blank">Fama’s thee factor model</a>, which shows that when adjusted to various risks based on certain factors, the seemingly superior stock returns could be explained (Fama later expands this model to incorporate two more factors, but these factors alone explain the majority of anomalies in expected returns).</p><p id="ea09" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">It seems that with the exception of academia, it is generally agreed upon that while the market is generally efficient, it still displays inefficiencies, especially in short time-periods — hence the adoption of <em class="rg">Efficiently Inefficient</em> as the title of Lasse Heje Pedersen’s famous introductory book on hedge funds and active investing.</p><h3 id="16dd" class="qr py in bf pz gk qs dy gl gm qt ea gn go qu gp gq gr qv gs gt gu qw gv gw qx bk">Then What is the Place of Data Science in Beating the Market?</h3><p id="4b0a" class="pw-post-body-paragraph pe pf in pg b jl qy pi pj jo qz pl pm go ra po pp gr rb pr ps gu rc pu pv pw ho bk">As only a new student in this field, I probably do not qualify to answer the question above, but I’ll take a shot at it anyways.</p><p id="a62c" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">The tricky part of using machine learning algorithms to predict the stock market is that it is very easy to overfit. The evidence is practically everywhere on Medium. More than half of relatively old articles on “predicting the stock market” fall into this trap. The easiest ones to spot are those that use LSTMs on recent <em class="rg">price</em> data to predict the next price, which just ends up being a time lag prediction (basically, every day’s price forecast is the previous day’s price; interestingly this is consistent with the <em class="rg">Martingale Property</em>, but you do not need LSTMs to make use of this property).</p><p id="f7a2" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Granted, the top quant hedge funds are probably using ML algorithms for full benefit in beating the market by taking extra care to avoid overfitting, but for a regular retail investor, the cost of taking such extra caution probably outweighs the benefits. The fact is that a simple statistical model with enough, real diversification shows surprisingly strong performance. And while using additional data science techniques such as sentiment analysis, google trend analysis, and deep neural networks could provide a small edge, that effort is better spent minimizing market friction, providing extra diversification, lowering server costs, etc.</p><p id="8e01" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Of course, there are certain exceptions such as <a class="ag pd" href="https://www.qraftec.com/" rel="noopener ugc nofollow" target="_blank">QRAFT</a>, a Korean startup recently drawing a lot of attention, but even here you should notice that the startup’s most valuable assets are not only its deep reinforcement learning based ETFs, but also AXE (ML based order execution system) and Kirin API (automated data cleaning module).</p><p id="b083" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">To summarize, data science does have a place in the active investing world, but while its impact on stock return prediction is unclear, its positive influence on other aspects of trading such as order execution seems much clear-cut.</p><h2 id="3552" class="px py in bf pz qa qb jn gl qc qd jq gn qe qf qg qh qi qj qk ql qm qn qo qp qq bk">Non-random RPS Competition and the Inefficient Market</h2><h3 id="8182" class="qr py in bf pz gk qs dy gl gm qt ea gn go qu gp gq gr qv gs gt gu qw gv gw qx bk">Drawing the Analogy</h3><p id="b9d8" class="pw-post-body-paragraph pe pf in pg b jl qy pi pj jo qz pl pm go ra po pp gr rb pr ps gu rc pu pv pw ho bk">You’re probably wondering now how all the information above come together to form the conclusion the Kaggle’s RPS competition resembles the stock market.</p><p id="06df" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Well, let’s consider the semi-strong form of market efficiency. Here it is argued that whenever there is a disclosed event that changes the value of an asset, the market price instantly reflects that information, propelled by market participants who act on the opportunity to make profit.</p><p id="e592" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">One way of seeing how this works, is to consider what would happen if it is guaranteed that market efficiency holds true. If that is the case, then there is no incentive for any market participant to risk losing the transaction cost by actively trading. The best move would be to only take the systematic risk by investing in passive ETFs.</p><p id="3796" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">However, this is usually not the case. Investors, whether institutional or individual, trade actively under the assumption that certain inefficiencies exist for exploitation. If any inefficiency exists, then this active movement removes that inefficiency from the market as investors greedily take opportunity from the wrong pricing, quickly reverting the price towards its correct value. The return that an investor gains for such activity can be thought of as the reward for re-instilling efficiency to the market.</p><p id="9705" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Interestingly this form of active investing that restores efficiency is also the very source of inefficiency.</p><p id="eca3" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Now let’s go back to Kaggle’s RPS competition. Under Nash equilibrium one should only submit random agents and hope to win. However, in this case we know for a fact that there are non-random, irrational agents participating in the competition. Under this new information, the best move becomes to introduce similarly non-random (inefficient) agents that could hopefully beat all other non-random agents. If you succeed in coming up with such algorithm, then you revert the performance of the other non-random agents to the random mean (restoring efficiency), and gain points in the leaderboard as the reward. At the same time, because you submitted a non-random agent, you also risk introducing inefficiency to the market unless another better bot emerges.</p><p id="e3ca" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">In Kaggle’s RPS competition, past algorithms like<em class="rg"> Iocaine Powder</em> and <em class="rg">Greenberg</em> performed very poorly. Why? While an obvious reason is that almost every other top-performing agent has a mirror version of such algorithms to defeat them 100% of the time, the other reason is that because so many copies of such successful-in-past-but-publicly-available algorithms are already submitted, bots that would lose to such algorithms already hang too low on the leaderboard. In other words, all inefficiencies in the competition that <em class="rg">Iocaine Powder</em> or <em class="rg">Greenberg </em>could feed on are already resolved by the collective crowd.</p><p id="539d" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">This is very similar to what happens when a successful trading strategy gets publicly known in the investing world. In the very beginning utilizing option pricing using Black-Scholes model, arbitrage opportunities like pair-trading, and contrarian strategies were all pretty lucrative because limited number of market participants employed them. By the market crashes in the 2000s, however, extreme leverage became essential to attain similar levels of return.</p><p id="5513" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">I can be frowned upon for drafting these conclusions without any real data to back them up. After all, it is the spirit of Kaggle and data science community in general to actually extract data and draw concrete conclusions. But the analogy drawn here does bring an interesting view on the nature of the competition.</p><h3 id="d841" class="qr py in bf pz gk qs dy gl gm qt ea gn go qu gp gq gr qv gs gt gu qw gv gw qx bk">Portfolio Optimization as the Ensemble Methodology</h3><p id="0cd9" class="pw-post-body-paragraph pe pf in pg b jl qy pi pj jo qz pl pm go ra po pp gr rb pr ps gu rc pu pv pw ho bk">With less than a couple of weeks until the competition deadline, it became increasingly clear that an ensemble is probably the way to go in order to be successful. While there was a remarkable reveal of <a class="ag pd" href="https://www.kaggle.com/c/rock-paper-scissors/discussion/210305" rel="noopener ugc nofollow" target="_blank">a simple, non-ensemble agent</a> that showed superb performance, <a class="ag pd" href="https://www.kaggle.com/c/rock-paper-scissors/discussion/201683" rel="noopener ugc nofollow" target="_blank">most of the discussions</a> leaned towards coming up with a reliable ensemble methodology.</p><p id="1a67" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Personally, as I was fascinated by the similarity of the competition to the stock market, I desperately wanted to make use of knowledge in the quantitative finance field that I had been wrestling with for the past year.</p><p id="50b1" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">A detailed write-up of my thoughts is well documented <a class="ag pd" href="https://www.kaggle.com/c/rock-paper-scissors/discussion/216358" rel="noopener ugc nofollow" target="_blank">here</a>, but to add on to what is said in the linked thread, let me concretely explain how I set up the problem.</p><p id="d7bd" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Regardless of whether you make a deterministic or stochastic choice at each step of an episode, the fact is that when trying to incorporate information from multiple algorithms, you are going to weight the decision of those algorithms for the final decision. If algorithm A has produced excellent results against the current opponent, then it is most likely that you should stick with the recommended move of algorithm A; if algorithm A recommends scissors, you should pick scissors. In such case, the weight map would look like: {0 : 0.02, 1 : 0.18, 2 : 0.80}, in which 0, 1, 2 correspond to rock, paper, scissors, respectively. With 80% of the weights concentrated on 2, the ensemble would pick scissors as the next move.</p><p id="24bc" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">So in some aspect, picking weights for algorithms to use for the current step is similar to how one would choose portfolio weights for different assets during a re-balancing period. If an algorithm(=asset) performed well recently, you would be likely to give it more weight. Then we can think of each step in the episode as a natural time-step for re-balancing our portfolio.</p><p id="6e0b" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Now, most of the publicly available RPS algorithms use the same core logic; it was frequently discussed in the competition threads that it is essential to remove redundant agents in order to improve the performance of an ensemble. Well, if we were to incentivize algorithms with low correlation with others, then there is no need for manual hand-picking.</p><p id="7d87" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Fortunately, our famous <a class="ag pd" href="http://www.columbia.edu/~mh2078/FoundationsFE/MeanVariance-CAPM.pdf" rel="noopener ugc nofollow" target="_blank">mean-variance portfolio optimization theory</a> gives us the means to do exactly that. I don’t want to get too deep into the portfolio optimization theory, as there are murky areas that I am still not so sure about, but the important takeaway is that as long as expected returns of target assets are excellent, the resulting portfolio will also be excellent in terms of risk-adjusted returns: the portfolio will rarely lose.</p><p id="3248" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Regrettably, Kaggle only allows a maximum of 1 second to decide on the next move, so I was not able to get an ensemble model that examines the efficient frontier to find the point of maximum Sharpe. Had that been possible, I dare presume I might have finished higher in the leaderboard.</p><p id="e580" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Wait, though. How do I know that this ensemble method was indeed successful in the competition? After all, a lucky random agent could wander very high up in the leaderboard, due to the nature of RPS. My defense would be that I had 8 of these portfolio ensemble agents in the medal zone by the end of the competition, and that this ensemble method has proved much more successful than multi-armed bandit with beta distribution in local testing (and in the leaderboard).</p><h2 id="09c1" class="px py in bf pz qa qb jn gl qc qd jq gn qe qf qg qh qi qj qk ql qm qn qo qp qq bk">In Conclusion</h2><p id="206b" class="pw-post-body-paragraph pe pf in pg b jl qy pi pj jo qz pl pm go ra po pp gr rb pr ps gu rc pu pv pw ho bk">Once you register all the facts, it probably is not a shock that RPS competition resembles a stock market in some aspects. Both could be described as systems that are completely random in the long run, yet somewhat predictable in the short term.</p><p id="c30d" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">What RPS competition really showed me, however, is that using complicated methods like LSTMs to bash through all the random noise is usually very pointless. The fact is that there does not exist any such strong signals in past score trends (=past price trends) that give meaningful information about the future. Instead of worrying about tuning hyper-parameters, more effort could be better spent on trimming down execution time of algorithms (=reducing market friction?) or coming up with and adding more effective algorithms (=adding diversification).</p><p id="9032" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk">Also, math is king. I really recommend checking out <a class="ag pd" href="https://www.kaggle.com/c/rock-paper-scissors/discussion/210305" rel="noopener ugc nofollow" target="_blank">this thread</a>. It shows how effective a simple mathematical solution could be.</p></div></div></div><div class="ac cb rh ri rj rk" role="separator"><span class="rl bx bm gb rm rn"></span><span class="rl bx bm gb rm rn"></span><span class="rl bx bm gb rm"></span></div><div class="ho ih ii ij ik"><div class="ac cb"><div class="ci bh hu hv hw hx"><p id="6e2b" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk"><em class="rg">Thanks to Kaggle staff for hosting this fun competition, great gratitude to people like Stas Sl, Taaha Khan, SuperAnt, Tony Robinson, etc, who made so much meaningful intellectual contributions to the community.</em></p></div></div></div><div class="ac cb rh ri rj rk" role="separator"><span class="rl bx bm gb rm rn"></span><span class="rl bx bm gb rm rn"></span><span class="rl bx bm gb rm"></span></div><div class="ho ih ii ij ik"><div class="ac cb"><div class="ci bh hu hv hw hx"><p id="b3eb" class="pw-post-body-paragraph pe pf in pg b jl ph pi pj jo pk pl pm go pn po pp gr pq pr ps gu pt pu pv pw ho bk"><strong class="pg io"><em class="rg">Note from Towards Data Science’s editors:</em></strong><em class="rg"> While we allow independent authors to publish articles in accordance with our </em><a class="ag pd" href="https://towardsdatascience.com/questions-96667b06af5" rel="noopener" target="_blank"><em class="rg">rules and guidelines</em></a><em class="rg">, we do not endorse each author’s contribution. You should not rely on an author’s works without seeking professional advice. See our </em><a class="ag pd" href="https://towardsdatascience.com/readers-terms-b5d780a700a4" rel="noopener" target="_blank"><em class="rg">Reader Terms</em></a><em class="rg"> for details.</em></p></div></div></div></div></section></div></div></article></div><div class="ac cb"><div class="ci bh hu hv hw hx"><div class="ro rp ac kw"><div class="rq ac"><a class="rr aj an ap" rel="noopener follow" href="/tag/stock-market?source=post_page-----f1ad81785a76---------------------------------------" data-discover="true"><div class="rs fl cx rt hz ru rv bf b bg ab bk fz">Stock Market</div></a></div><div class="rq ac"><a class="rr aj an ap" rel="noopener follow" href="/tag/quantitative-finance?source=post_page-----f1ad81785a76---------------------------------------" data-discover="true"><div class="rs fl cx rt hz ru rv bf b bg ab bk fz">Quantitative Finance</div></a></div><div class="rq ac"><a class="rr aj an ap" rel="noopener follow" href="/tag/portfolio-management?source=post_page-----f1ad81785a76---------------------------------------" data-discover="true"><div class="rs fl cx rt hz ru rv bf b bg ab bk fz">Portfolio Management</div></a></div><div class="rq ac"><a class="rr aj an ap" rel="noopener follow" href="/tag/kaggle?source=post_page-----f1ad81785a76---------------------------------------" data-discover="true"><div class="rs fl cx rt hz ru rv bf b bg ab bk fz">Kaggle</div></a></div><div class="rq ac"><a class="rr aj an ap" rel="noopener follow" href="/tag/rock-paper-scissors?source=post_page-----f1ad81785a76---------------------------------------" data-discover="true"><div class="rs fl cx rt hz ru rv bf b bg ab bk fz">Rock Paper Scissors</div></a></div></div></div></div><div class="m"></div><footer class="rw rx ry rz sa ac r sb sc c"><div class="m af"><div class="ac cb"><div class="ci bh hu hv hw hx"><div class="ac cp sd"><div class="ac r mf"><div class="se m"><span class="m sf sg sh f e"><div class="ac r mf mg"><div class="pw-multi-vote-icon fl mh mi mj mk"><span data-dd-action-name="Susi presentation tracker clap_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="footerClapButton" rel="noopener follow" href="/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fdata-science%2Ff1ad81785a76&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fdata-science%2Frock-paper-scissors-and-the-stock-market-f1ad81785a76&amp;user=Seouk+Jun+Kim&amp;userId=b50aa26e058d&amp;source=---footer_actions--f1ad81785a76---------------------clap_footer------------------" data-discover="true"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><div class="ml ap mm mn mo mp an mq mr ms mk" role="presentation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="clap"><path fill-rule="evenodd" d="M11.37.828 12 3.282l.63-2.454zM13.916 3.953l1.523-2.112-1.184-.39zM8.589 1.84l1.522 2.112-.337-2.501zM18.523 18.92c-.86.86-1.75 1.246-2.62 1.33a6 6 0 0 0 .407-.372c2.388-2.389 2.86-4.951 1.399-7.623l-.912-1.603-.79-1.672c-.26-.56-.194-.98.203-1.288a.7.7 0 0 1 .546-.132c.283.046.546.231.728.5l2.363 4.157c.976 1.624 1.141 4.237-1.324 6.702m-10.999-.438L3.37 14.328a.828.828 0 0 1 .585-1.408.83.83 0 0 1 .585.242l2.158 2.157a.365.365 0 0 0 .516-.516l-2.157-2.158-1.449-1.449a.826.826 0 0 1 1.167-1.17l3.438 3.44a.363.363 0 0 0 .516 0 .364.364 0 0 0 0-.516L5.293 9.513l-.97-.97a.826.826 0 0 1 0-*********** 0 0 1 1.167 0l.97.968 3.437 3.436a.36.36 0 0 0 .517 0 .366.366 0 0 0 0-.516L6.977 7.83a.82.82 0 0 1-.241-.584.82.82 0 0 1 .824-.826c.219 0 .43.087.584.242l5.787 5.787a.366.366 0 0 0 .587-.415l-1.117-2.363c-.26-.56-.194-.98.204-1.289a.7.7 0 0 1 .546-.132c.283.046.545.232.727.501l2.193 3.86c1.302 2.38.883 4.59-1.277 6.75-1.156 1.156-2.602 1.627-4.19 1.367-1.418-.236-2.866-1.033-4.079-2.246M10.75 5.971l2.12 2.12c-.41.502-.465 1.17-.128 1.89l.22.465-3.523-3.523a.8.8 0 0 1-.097-.368c0-.22.086-.428.241-.584a.847.847 0 0 1 1.167 0m7.355 1.705c-.31-.461-.746-.758-1.23-.837a1.44 1.44 0 0 0-1.11.275c-.312.24-.505.543-.59.881a1.74 1.74 0 0 0-.906-.465 1.47 1.47 0 0 0-.82.106l-2.182-2.182a1.56 1.56 0 0 0-2.2 0 1.54 1.54 0 0 0-.396.701 1.56 1.56 0 0 0-2.21-.01 1.55 1.55 0 0 0-.416.753c-.624-.624-1.649-.624-2.237-.037a1.557 1.557 0 0 0 0 2.2c-.239.1-.501.238-.715.453a1.56 1.56 0 0 0 0 2.2l.516.515a1.556 1.556 0 0 0-.753 2.615L7.01 19c1.32 1.319 2.909 2.189 4.475 2.449q.482.08.971.08c.85 0 1.653-.198 2.393-.579.**************.686.054 1.266 0 2.457-.52 3.505-1.567 2.763-2.763 2.552-5.734 1.439-7.586z" clip-rule="evenodd"></path></svg></div></div></div></div></a></span></div><div class="pw-multi-vote-count m mt mu mv mw mx my mz"><p class="bf b dv ab du"><span class="na">--</span></p></div></div></span><span class="m i h g si sj"><div class="ac r mf mg"><div class="pw-multi-vote-icon fl mh mi mj mk"><span data-dd-action-name="Susi presentation tracker clap_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="footerClapButton" rel="noopener follow" href="/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fdata-science%2Ff1ad81785a76&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fdata-science%2Frock-paper-scissors-and-the-stock-market-f1ad81785a76&amp;user=Seouk+Jun+Kim&amp;userId=b50aa26e058d&amp;source=---footer_actions--f1ad81785a76---------------------clap_footer------------------" data-discover="true"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><div class="ml ap mm mn mo mp an mq mr ms mk" role="presentation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="clap"><path fill-rule="evenodd" d="M11.37.828 12 3.282l.63-2.454zM13.916 3.953l1.523-2.112-1.184-.39zM8.589 1.84l1.522 2.112-.337-2.501zM18.523 18.92c-.86.86-1.75 1.246-2.62 1.33a6 6 0 0 0 .407-.372c2.388-2.389 2.86-4.951 1.399-7.623l-.912-1.603-.79-1.672c-.26-.56-.194-.98.203-1.288a.7.7 0 0 1 .546-.132c.283.046.546.231.728.5l2.363 4.157c.976 1.624 1.141 4.237-1.324 6.702m-10.999-.438L3.37 14.328a.828.828 0 0 1 .585-1.408.83.83 0 0 1 .585.242l2.158 2.157a.365.365 0 0 0 .516-.516l-2.157-2.158-1.449-1.449a.826.826 0 0 1 1.167-1.17l3.438 3.44a.363.363 0 0 0 .516 0 .364.364 0 0 0 0-.516L5.293 9.513l-.97-.97a.826.826 0 0 1 0-*********** 0 0 1 1.167 0l.97.968 3.437 3.436a.36.36 0 0 0 .517 0 .366.366 0 0 0 0-.516L6.977 7.83a.82.82 0 0 1-.241-.584.82.82 0 0 1 .824-.826c.219 0 .43.087.584.242l5.787 5.787a.366.366 0 0 0 .587-.415l-1.117-2.363c-.26-.56-.194-.98.204-1.289a.7.7 0 0 1 .546-.132c.283.046.545.232.727.501l2.193 3.86c1.302 2.38.883 4.59-1.277 6.75-1.156 1.156-2.602 1.627-4.19 1.367-1.418-.236-2.866-1.033-4.079-2.246M10.75 5.971l2.12 2.12c-.41.502-.465 1.17-.128 1.89l.22.465-3.523-3.523a.8.8 0 0 1-.097-.368c0-.22.086-.428.241-.584a.847.847 0 0 1 1.167 0m7.355 1.705c-.31-.461-.746-.758-1.23-.837a1.44 1.44 0 0 0-1.11.275c-.312.24-.505.543-.59.881a1.74 1.74 0 0 0-.906-.465 1.47 1.47 0 0 0-.82.106l-2.182-2.182a1.56 1.56 0 0 0-2.2 0 1.54 1.54 0 0 0-.396.701 1.56 1.56 0 0 0-2.21-.01 1.55 1.55 0 0 0-.416.753c-.624-.624-1.649-.624-2.237-.037a1.557 1.557 0 0 0 0 2.2c-.239.1-.501.238-.715.453a1.56 1.56 0 0 0 0 2.2l.516.515a1.556 1.556 0 0 0-.753 2.615L7.01 19c1.32 1.319 2.909 2.189 4.475 2.449q.482.08.971.08c.85 0 1.653-.198 2.393-.579.**************.686.054 1.266 0 2.457-.52 3.505-1.567 2.763-2.763 2.552-5.734 1.439-7.586z" clip-rule="evenodd"></path></svg></div></div></div></div></a></span></div><div class="pw-multi-vote-count m mt mu mv mw mx my mz"><p class="bf b dv ab du"><span class="na">--</span></p></div></div></span></div><div class="ay ac"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button class="ap ml nb nc ac r fm nd ne" aria-label="responses"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="nf"><path d="M18.006 16.803c1.533-1.456 2.234-3.325 2.234-5.321C20.24 7.357 16.709 4 12.191 4S4 7.357 4 11.482c0 4.126 3.674 7.482 8.191 7.482.817 0 1.622-.111 2.393-.327.231.2.48.391.744.559 1.06.693 2.203 1.044 3.399 1.044.224-.008.4-.112.486-.287a.49.49 0 0 0-.042-.518c-.495-.67-.845-1.364-1.04-2.057a4 4 0 0 1-.125-.598zm-3.122 1.055-.067-.223-.315.096a8 8 0 0 1-2.311.338c-4.023 0-7.292-2.955-7.292-6.587 0-3.633 3.269-6.588 7.292-6.588 4.014 0 7.112 2.958 7.112 6.593 0 1.794-.608 3.469-2.027 4.72l-.195.168v.255c0 .056 0 .151.016.295.025.231.081.478.154.733.154.558.398 1.117.722 1.659a5.3 5.3 0 0 1-2.165-.845c-.276-.176-.714-.383-.941-.59z"></path></svg></button></div></div></div></div></div><div class="ac r"><div class="rn m sk"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><span data-dd-action-name="Susi presentation tracker bookmark_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="footerBookmarkButton" rel="noopener follow" href="/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2Ff1ad81785a76&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fdata-science%2Frock-paper-scissors-and-the-stock-market-f1ad81785a76&amp;source=---footer_actions--f1ad81785a76---------------------bookmark_footer------------------" data-discover="true"><svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="none" viewBox="0 0 25 25" class="du nh" aria-label="Add to list bookmark button"><path fill="currentColor" d="M18 2.5a.5.5 0 0 1 1 0V5h2.5a.5.5 0 0 1 0 1H19v2.5a.5.5 0 1 1-1 0V6h-2.5a.5.5 0 0 1 0-1H18zM7 7a1 1 0 0 1 1-1h3.5a.5.5 0 0 0 0-1H8a2 2 0 0 0-2 2v14a.5.5 0 0 0 .805.396L12.5 17l5.695 4.396A.5.5 0 0 0 19 21v-8.5a.5.5 0 0 0-1 0v7.485l-5.195-4.012a.5.5 0 0 0-.61 0L7 19.985z"></path></svg></a></span></div></div></div></div><div class="rn m sk"><div class="bm" aria-hidden="false" aria-describedby="postFooterSocialMenu" aria-labelledby="postFooterSocialMenu"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button aria-controls="postFooterSocialMenu" aria-expanded="false" aria-label="Share Post" data-testid="footerSocialShareButton" class="ag fm ai fh ak al am no ao ap aq ex np nq ne nr"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M15.218 4.931a.4.4 0 0 1-.118.132l.012.006a.45.45 0 0 1-.292.074.5.5 0 0 1-.3-.13l-2.02-2.02v7.07c0 .28-.23.5-.5.5s-.5-.22-.5-.5v-7.04l-2 2a.45.45 0 0 1-.57.04h-.02a.4.4 0 0 1-.16-.3.4.4 0 0 1 .1-.32l2.8-2.8a.5.5 0 0 1 .7 0l2.8 2.79a.42.42 0 0 1 .068.498m-.106.138.008.004v-.01zM16 7.063h1.5a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-11c-1.1 0-2-.9-2-2v-10a2 2 0 0 1 2-2H8a.5.5 0 0 1 .********* 0 0 1 .********* 0 0 1-.********* 0 0 1-.35.15H6.4c-.5 0-.9.4-.9.9v10.2a.9.9 0 0 0 .9.9h11.2c.5 0 .9-.4.9-.9v-10.2c0-.5-.4-.9-.9-.9H16a.5.5 0 0 1 0-1" clip-rule="evenodd"></path></svg></button></div></div></div></div></div></div></div></div></div></div></footer><div class="sl m"><div class="ac cb"><div class="ci bh hu hv hw hx"><div class="sm m"><div class="ac kj kh kf sn so"><div class="sp sq sr ss st su sv sw sx sy ac cp"><div class="i l"><a href="https://medium.com/data-science?source=post_page---post_publication_info--f1ad81785a76---------------------------------------" rel="noopener follow"><div class="fl"><img alt="TDS Archive" class="cx hj m ta sz" src="https://miro.medium.com/v2/resize:fill:96:96/1*JEuS4KBdakUcjg9sC7Wo4A.png" width="48" height="48" loading="lazy"/><div class="hj m sz ta fu o ft hm"></div></div></a></div><div class="k j e"><a href="https://medium.com/data-science?source=post_page---post_publication_info--f1ad81785a76---------------------------------------" rel="noopener follow"><div class="fl"><img alt="TDS Archive" class="cx hj m tc tb" src="https://miro.medium.com/v2/resize:fill:128:128/1*JEuS4KBdakUcjg9sC7Wo4A.png" width="64" height="64" loading="lazy"/><div class="hj m tb tc fu o ft hm"></div></div></a></div><div class="k j e td sk"><div class="ac"></div></div></div><div class="ac co ca"><div class="te tf tg th ti m"><a class="ag ah ai ak al am an ao ap aq ar as at au ac r" href="https://medium.com/data-science?source=post_page---post_publication_info--f1ad81785a76---------------------------------------" rel="noopener follow"><h2 class="pw-author-name bf gj tk tl tm tn to tp go gp gq gr gs gt gu gv gw bk"><span class="ho tj">Published in <!-- -->TDS Archive</span></h2></a><div class="rq ac kl"><div class="m sk"><span class="pw-follower-count bf b bg ab du"><a class="ag ah ai fh ak al am an ao ap aq ar as ku" rel="noopener follow" href="/data-science/followers?source=post_page---post_publication_info--f1ad81785a76---------------------------------------" data-discover="true">829K followers</a></span></div><div class="bf b bg ab du ac tq"><span class="gx m" aria-hidden="true"><span class="bf b bg ab du">·</span></span><a class="ag ah ai fh ak al am an ao ap aq ar as ku" rel="noopener follow" href="/data-science/diy-ai-how-to-build-a-linear-regression-model-from-scratch-7b4cc0efd235?source=post_page---post_publication_info--f1ad81785a76---------------------------------------" data-discover="true">Last published <span>Feb 3, 2025</span></a></div></div><div class="hn m"><p class="bf b bg ab bk"><span class="ho">An archive of data science, data analytics, data engineering, machine learning, and artificial intelligence writing from the former Towards Data Science Medium publication.</span></p></div></div></div><div class="i l"><div class="ac"></div></div></div></div><div class="ac kj kh kf sn so"><div class="sp sq sr ss st su sv sw sx sy ac cp"><div class="i l"><a tabindex="0" rel="noopener follow" href="/@kdanielive?source=post_page---post_author_info--f1ad81785a76---------------------------------------" data-discover="true"><div class="m fl"><img alt="Seouk Jun Kim" class="m fd bx sz ta cx" src="https://miro.medium.com/v2/resize:fill:96:96/1*<EMAIL>" width="48" height="48" loading="lazy"/><div class="ft bx m sz ta fu o aj hm"></div></div></a></div><div class="k j e"><a tabindex="0" rel="noopener follow" href="/@kdanielive?source=post_page---post_author_info--f1ad81785a76---------------------------------------" data-discover="true"><div class="m fl"><img alt="Seouk Jun Kim" class="m fd bx tb tc cx" src="https://miro.medium.com/v2/resize:fill:128:128/1*<EMAIL>" width="64" height="64" loading="lazy"/><div class="ft bx m tb tc fu o aj hm"></div></div></a></div><div class="k j e td sk"><div class="ac"></div></div></div><div class="ac co ca"><div class="te tf tg th ti m"><a class="ag ah ai ak al am an ao ap aq ar as at au ac r" rel="noopener follow" href="/@kdanielive?source=post_page---post_author_info--f1ad81785a76---------------------------------------" data-discover="true"><h2 class="pw-author-name bf gj tk tl tm tn to tp go gp gq gr gs gt gu gv gw bk"><span class="ho tj">Written by <!-- -->Seouk Jun Kim</span></h2></a><div class="rq ac kl"><div class="m sk"><span class="pw-follower-count bf b bg ab du"><a class="ag ah ai fh ak al am an ao ap aq ar as ku" rel="noopener follow" href="/@kdanielive/followers?source=post_page---post_author_info--f1ad81785a76---------------------------------------" data-discover="true">35 followers</a></span></div><div class="bf b bg ab du ac tq"><span class="gx m" aria-hidden="true"><span class="bf b bg ab du">·</span></span><a class="ag ah ai fh ak al am an ao ap aq ar as ku" rel="noopener follow" href="/@kdanielive/following?source=post_page---post_author_info--f1ad81785a76---------------------------------------" data-discover="true">27 following</a></div></div><div class="hn m"><p class="bf b bg ab bk">Columbia University student, CS. Former iOS developer, presently pursuing career in data science. <a class="ag ah ai fh ak al am an ao ap aq ar as pd ih ho" href="https://www.linkedin.com/in/seouk-jun-kim-a74921184/" rel="noopener  ugc nofollow">https://www.linkedin.com/in/seouk-jun-kim-a74921184/</a></p></div></div></div><div class="i l"><div class="ac"></div></div></div></div></div></div><div class="tr m"><div class="ts bh s sl"></div><div class="ac cb"><div class="ci bh hu hv hw hx"><div class="ac r cp"><h2 class="bf gj qa jn gl qc jq gn qe qg qh qi qk ql qm qo qp bk">No responses yet</h2><div class="ac tt"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><a class="tu tv" href="https://policy.medium.com/medium-rules-30e5502c4eb4?source=post_page---post_responses--f1ad81785a76---------------------------------------" rel="noopener follow" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" aria-label="Shield with a checkmark" viewBox="0 0 25 25"><path fill-rule="evenodd" d="M11.987 5.036a.754.754 0 0 1 .914-.01c.972.721 1.767 1.218 2.6 1.543.828.322 1.719.485 2.887.505a.755.755 0 0 1 .741.757c-.018 3.623-.43 6.256-1.449 8.21-1.034 1.984-2.662 3.209-4.966 4.083a.75.75 0 0 1-.537-.003c-2.243-.874-3.858-2.095-4.897-4.074-1.024-1.951-1.457-4.583-1.476-8.216a.755.755 0 0 1 .741-.757c1.195-.02 2.1-.182 2.923-.503.827-.322 1.6-.815 2.519-1.535m.468.903c-.897.69-1.717 1.21-2.623 1.564-.898.35-1.856.527-3.026.565.037 3.45.469 5.817 1.36 7.515.884 1.684 2.25 2.762 4.284 3.571 2.092-.81 3.465-1.89 4.344-3.575.886-1.698 1.299-4.065 1.334-7.512-1.149-.039-2.091-.217-2.99-.567-.906-.353-1.745-.873-2.683-1.561m-.009 9.155a2.672 2.672 0 1 0 0-5.344 2.672 2.672 0 0 0 0 5.344m0 1a3.672 3.672 0 1 0 0-7.344 3.672 3.672 0 0 0 0 7.344m-1.813-3.777.525-.526.916.917 1.623-1.625.526.526-2.149 2.152z" clip-rule="evenodd"></path></svg></a></div></div></div></div></div><div class="tw tx ty tz ua m"></div></div></div></div><div class="ub uc ud ue uf m bw"><div class="i l k"><div class="ts bh ug uh"></div><div class="ac cb"><div class="ci bh hu hv hw hx"><div class="ui ac mf kw"><div class="uj uk m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://help.medium.com/hc/en-us?source=post_page-----f1ad81785a76---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Help</p></a></div><div class="uj uk m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://medium.statuspage.io/?source=post_page-----f1ad81785a76---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Status</p></a></div><div class="uj uk m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" rel="noopener follow" href="/about?autoplay=1&amp;source=post_page-----f1ad81785a76---------------------------------------" data-discover="true"><p class="bf b dv ab du">About</p></a></div><div class="uj uk m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" rel="noopener follow" href="/jobs-at-medium/work-at-medium-959d1a85284e?source=post_page-----f1ad81785a76---------------------------------------" data-discover="true"><p class="bf b dv ab du">Careers</p></a></div><div class="uj uk m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="mailto:<EMAIL>" rel="noopener follow"><p class="bf b dv ab du">Press</p></a></div><div class="uj uk m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://blog.medium.com/?source=post_page-----f1ad81785a76---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Blog</p></a></div><div class="uj uk m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://policy.medium.com/medium-privacy-policy-f03bf92035c9?source=post_page-----f1ad81785a76---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Privacy</p></a></div><div class="uj uk m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://policy.medium.com/medium-rules-30e5502c4eb4?source=post_page-----f1ad81785a76---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Rules</p></a></div><div class="uj uk m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://policy.medium.com/medium-terms-of-service-9db0094a1e0f?source=post_page-----f1ad81785a76---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Terms</p></a></div><div class="uj m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://speechify.com/medium?source=post_page-----f1ad81785a76---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Text to speech</p></a></div></div></div></div></div></div></div></div></div></div></div></div><script>window.__BUILD_ID__="main-********-185947-c929e6f698"</script><script>window.__GRAPHQL_URI__ = "https://medium.com/_/graphql"</script><script>window.__PRELOADED_STATE__ = {"algolia":{"queries":{}},"cache":{"experimentGroupSet":true,"reason":"","group":"enabled","tags":["group-edgeCachePosts","post-f1ad81785a76","user-b50aa26e058d","collection-7f60cf5620c9","group-newLoNonMocUpsellExperimentGroup-group2"],"serverVariantState":"44136fa355b3678a1146ad16f7e8649e94fb4fc21fe77e8310c060f61caaff8a","middlewareEnabled":true,"cacheStatus":"DYNAMIC","shouldUseCache":true,"vary":[],"pubFeaturingPostPageLabelEnabled":false,"shouldFollowPostQueryEnabled":false,"newLoNonMocUpsellExperimentGroup":"group2"},"client":{"hydrated":false,"isUs":false,"isNativeMedium":false,"isSafariMobile":false,"isSafari":false,"isFirefox":false,"routingEntity":{"type":"DEFAULT","explicit":false},"viewerIsBot":false},"debug":{"requestId":"b02488f3-8484-47ec-9f68-f91fabe18750","requestTag":"","hybridDevServices":[],"originalSpanCarrier":{"traceparent":"00-9829e79878cbdfb30012bbc28a632084-298304d0575c6b35-01"}},"multiVote":{"clapsPerPost":{}},"navigation":{"branch":{"show":null,"hasRendered":null,"blockedByCTA":false},"hideGoogleOneTap":false,"hasRenderedAlternateUserBanner":null,"currentLocation":"https:\u002F\u002Fmedium.com\u002Fdata-science\u002Frock-paper-scissors-and-the-stock-market-f1ad81785a76","host":"medium.com","hostname":"medium.com","referrer":"","hasSetReferrer":false,"susiModal":{"step":null,"operation":"register"},"postRead":false,"partnerProgram":{"selectedCountryCode":null},"staticRouterContext":{"route":{"name":"ShowPostUnderCollection"},"statusCode":200},"toastQueue":[],"currentToast":null,"queryString":"","currentHash":""},"config":{"nodeEnv":"production","version":"main-********-185947-c929e6f698","target":"production","productName":"Medium","publicUrl":"https:\u002F\u002Fcdn-client.medium.com\u002Flite","authDomain":"medium.com","authGoogleClientId":"216296035834-k1k6qe060s2tp2a2jam4ljdcms00sttg.apps.googleusercontent.com","favicon":"production","iosAppId":"828256236","glyphUrl":"https:\u002F\u002Fglyph.medium.com","branchKey":"key_live_ofxXr2qTrrU9NqURK8ZwEhknBxiI6KBm","algolia":{"appId":"MQ57UUUQZ2","apiKeySearch":"********************************","indexPrefix":"medium_","host":"-dsn.algolia.net"},"recaptchaKey":"6Lfc37IUAAAAAKGGtC6rLS13R1Hrw_BqADfS1LRk","recaptcha3Key":"6Lf8R9wUAAAAABMI_85Wb8melS7Zj6ziuf99Yot5","recaptchaEnterpriseKeyId":"6Le-uGgpAAAAAPprRaokM8AKthQ9KNGdoxaGUvVp","datadog":{"applicationId":"6702d87d-a7e0-42fe-bbcb-95b469547ea0","clientToken":"pub853ea8d17ad6821d9f8f11861d23dfed","rumToken":"pubf9cc52896502b9413b68ba36fc0c7162","context":{"deployment":{"target":"production","tag":"main-********-185947-c929e6f698","commit":"c929e6f698a22d0c3be06d191c5c594e2e313e6d"}},"datacenter":"us"},"googleAdsCode":"AW-17106321204","googleAnalyticsCode":"G-7JY7T788PK","googlePay":{"apiVersion":"2","apiVersionMinor":"0","merchantId":"BCR2DN6TV7EMTGBM","merchantName":"Medium","instanceMerchantId":"13685562959212738550"},"applePay":{"version":3},"signInWallCustomDomainCollectionIds":["3a8144eabfe3","336d898217ee","61061eb0c96b","138adf9c44c","819cc2aaeee0"],"mediumMastodonDomainName":"me.dm","mediumOwnedAndOperatedCollectionIds":["8a9336e5bb4","b7e45b22fec3","193b68bd4fba","8d6b8a439e32","54c98c43354d","3f6ecf56618","d944778ce714","92d2092dc598","ae2a65f35510","1285ba81cada","544c7006046e","fc8964313712","40187e704f1c","88d9857e584e","7b6769f2748b","bcc38c8f6edf","cef6983b292","cb8577c9149e","444d13b52878","713d7dbc99b0","ef8e90590e66","191186aaafa0","55760f21cdc5","9dc80918cc93","bdc4052bbdba","8ccfed20cbb2"],"tierOneDomains":["medium.com","thebolditalic.com","arcdigital.media","towardsdatascience.com","uxdesign.cc","codeburst.io","psiloveyou.xyz","writingcooperative.com","entrepreneurshandbook.co","prototypr.io","betterhumans.coach.me","theascent.pub"],"topicsToFollow":["d61cf867d93f","8a146bc21b28","1eca0103fff3","4d562ee63426","aef1078a3ef5","e15e46793f8d","6158eb913466","55f1c20aba7a","3d18b94f6858","4861fee224fd","63c6f1f93ee","1d98b3a9a871","decb52b64abf","ae5d4995e225","830cded25262"],"topicToTagMappings":{"accessibility":"accessibility","addiction":"addiction","android-development":"android-development","art":"art","artificial-intelligence":"artificial-intelligence","astrology":"astrology","basic-income":"basic-income","beauty":"beauty","biotech":"biotech","blockchain":"blockchain","books":"books","business":"business","cannabis":"cannabis","cities":"cities","climate-change":"climate-change","comics":"comics","coronavirus":"coronavirus","creativity":"creativity","cryptocurrency":"cryptocurrency","culture":"culture","cybersecurity":"cybersecurity","data-science":"data-science","design":"design","digital-life":"digital-life","disability":"disability","economy":"economy","education":"education","equality":"equality","family":"family","feminism":"feminism","fiction":"fiction","film":"film","fitness":"fitness","food":"food","freelancing":"freelancing","future":"future","gadgets":"gadgets","gaming":"gaming","gun-control":"gun-control","health":"health","history":"history","humor":"humor","immigration":"immigration","ios-development":"ios-development","javascript":"javascript","justice":"justice","language":"language","leadership":"leadership","lgbtqia":"lgbtqia","lifestyle":"lifestyle","machine-learning":"machine-learning","makers":"makers","marketing":"marketing","math":"math","media":"media","mental-health":"mental-health","mindfulness":"mindfulness","money":"money","music":"music","neuroscience":"neuroscience","nonfiction":"nonfiction","outdoors":"outdoors","parenting":"parenting","pets":"pets","philosophy":"philosophy","photography":"photography","podcasts":"podcast","poetry":"poetry","politics":"politics","privacy":"privacy","product-management":"product-management","productivity":"productivity","programming":"programming","psychedelics":"psychedelics","psychology":"psychology","race":"race","relationships":"relationships","religion":"religion","remote-work":"remote-work","san-francisco":"san-francisco","science":"science","self":"self","self-driving-cars":"self-driving-cars","sexuality":"sexuality","social-media":"social-media","society":"society","software-engineering":"software-engineering","space":"space","spirituality":"spirituality","sports":"sports","startups":"startup","style":"style","technology":"technology","transportation":"transportation","travel":"travel","true-crime":"true-crime","tv":"tv","ux":"ux","venture-capital":"venture-capital","visual-design":"visual-design","work":"work","world":"world","writing":"writing"},"defaultImages":{"avatar":{"imageId":"1*dmbNkD5D-u45r44go_cf0g.png","height":150,"width":150},"orgLogo":{"imageId":"7*V1_7XP4snlmqrc_0Njontw.png","height":110,"width":500},"postLogo":{"imageId":"167cff2a3d17ac1e64d0762539978f2d54c0058886e8b3c8a03a725a83012ec0","height":630,"width":1200},"postPreviewImage":{"imageId":"bc1f8416df0cad099e43cda2872716e5864f18a73bda2a7547ea082aca9b5632","height":630,"width":1200}},"embeddedPostIds":{"coronavirus":"cd3010f9d81f"},"sharedCdcMessaging":{"COVID_APPLICABLE_TAG_SLUGS":[],"COVID_APPLICABLE_TOPIC_NAMES":[],"COVID_APPLICABLE_TOPIC_NAMES_FOR_TOPIC_PAGE":[],"COVID_MESSAGES":{"tierA":{"text":"For more information on the novel coronavirus and Covid-19, visit cdc.gov.","markups":[{"start":66,"end":73,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]},"tierB":{"text":"Anyone can publish on Medium per our Policies, but we don’t fact-check every story. For more info about the coronavirus, see cdc.gov.","markups":[{"start":37,"end":45,"href":"https:\u002F\u002Fhelp.medium.com\u002Fhc\u002Fen-us\u002Fcategories\u002F201931128-Policies-Safety"},{"start":125,"end":132,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]},"paywall":{"text":"This article has been made free for everyone, thanks to Medium Members. For more information on the novel coronavirus and Covid-19, visit cdc.gov.","markups":[{"start":56,"end":70,"href":"https:\u002F\u002Fmedium.com\u002Fmembership"},{"start":138,"end":145,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]},"unbound":{"text":"This article is free for everyone, thanks to Medium Members. For more information on the novel coronavirus and Covid-19, visit cdc.gov.","markups":[{"start":45,"end":59,"href":"https:\u002F\u002Fmedium.com\u002Fmembership"},{"start":127,"end":134,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]}},"COVID_BANNER_POST_ID_OVERRIDE_WHITELIST":["3b31a67bff4a"]},"sharedVoteMessaging":{"TAGS":["politics","election-2020","government","us-politics","election","2020-presidential-race","trump","donald-trump","democrats","republicans","congress","republican-party","democratic-party","biden","joe-biden","maga"],"TOPICS":["politics","election"],"MESSAGE":{"text":"Find out more about the U.S. election results here.","markups":[{"start":46,"end":50,"href":"https:\u002F\u002Fcookpolitical.com\u002F2020-national-popular-vote-tracker"}]},"EXCLUDE_POSTS":["397ef29e3ca5"]},"embedPostRules":[],"recircOptions":{"v1":{"limit":3},"v2":{"limit":8}},"braintreeClientKey":"production_zjkj96jm_m56f8fqpf7ngnrd4","braintree":{"enabled":true,"merchantId":"m56f8fqpf7ngnrd4","merchantAccountId":{"usd":"AMediumCorporation_instant","eur":"amediumcorporation_EUR","cad":"amediumcorporation_CAD"},"publicKey":"ds2nn34bg2z7j5gd","braintreeEnvironment":"production","dashboardUrl":"https:\u002F\u002Fwww.braintreegateway.com\u002Fmerchants","gracePeriodDurationInDays":14,"mediumMembershipPlanId":{"monthly":"ce105f8c57a3","monthlyV2":"e8a5e126-792b-4ee6-8fba-d574c1b02fc5","monthlyWithTrial":"d5ee3dbe3db8","monthlyPremium":"fa741a9b47a2","yearly":"a40ad4a43185","yearlyV2":"3815d7d6-b8ca-4224-9b8c-182f9047866e","yearlyStaff":"d74fb811198a","yearlyWithTrial":"b3bc7350e5c7","yearlyPremium":"e21bd2c12166","monthlyOneYearFree":"e6c0637a-2bad-4171-ab4f-3c268633d83c","monthly25PercentOffFirstYear":"235ecc62-0cdb-49ae-9378-726cd21c504b","monthly20PercentOffFirstYear":"ba518864-9c13-4a99-91ca-411bf0cac756","monthly15PercentOffFirstYear":"594c029b-9f89-43d5-88f8-8173af4e070e","monthly10PercentOffFirstYear":"c6c7bc9a-40f2-4b51-8126-e28511d5bdb0","monthlyForStudents":"629ebe51-da7d-41fd-8293-34cd2f2030a8","yearlyOneYearFree":"78ba7be9-0d9f-4ece-aa3e-b54b826f2bf1","yearly25PercentOffFirstYear":"2dbb010d-bb8f-4eeb-ad5c-a08509f42d34","yearly20PercentOffFirstYear":"47565488-435b-47f8-bf93-40d5fbe0ebc8","yearly15PercentOffFirstYear":"8259809b-0881-47d9-acf7-6c001c7f720f","yearly10PercentOffFirstYear":"9dd694fb-96e1-472c-8d9e-3c868d5c1506","yearlyForStudents":"e29345ef-ab1c-4234-95c5-70e50fe6bc23","monthlyCad":"p52orjkaceei","yearlyCad":"h4q9g2up9ktt"},"braintreeDiscountId":{"oneMonthFree":"MONTHS_FREE_01","threeMonthsFree":"MONTHS_FREE_03","sixMonthsFree":"MONTHS_FREE_06","fiftyPercentOffOneYear":"FIFTY_PERCENT_OFF_ONE_YEAR"},"3DSecureVersion":"2","defaultCurrency":"usd","providerPlanIdCurrency":{"4ycw":"usd","rz3b":"usd","3kqm":"usd","jzw6":"usd","c2q2":"usd","nnsw":"usd","q8qw":"usd","d9y6":"usd","fx7w":"cad","nwf2":"cad"}},"paypalClientId":"AXj1G4fotC2GE8KzWX9mSxCH1wmPE3nJglf4Z2ig_amnhvlMVX87otaq58niAg9iuLktVNF_1WCMnN7v","paypal":{"host":"https:\u002F\u002Fapi.paypal.com:443","clientMode":"production","serverMode":"live","webhookId":"4G466076A0294510S","monthlyPlan":{"planId":"P-9WR0658853113943TMU5FDQA","name":"Medium Membership (Monthly) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"yearlyPlan":{"planId":"P-7N8963881P8875835MU5JOPQ","name":"Medium Membership (Annual) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"oneYearGift":{"name":"Medium Membership (1 Year, Digital Gift Code)","description":"Unlimited access to the best and brightest stories on Medium. Gift codes can be redeemed at medium.com\u002Fredeem.","price":"50.00","currency":"USD","sku":"membership-gift-1-yr"},"oldMonthlyPlan":{"planId":"P-96U02458LM656772MJZUVH2Y","name":"Medium Membership (Monthly)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"oldYearlyPlan":{"planId":"P-59P80963JF186412JJZU3SMI","name":"Medium Membership (Annual)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"monthlyPlanWithTrial":{"planId":"P-66C21969LR178604GJPVKUKY","name":"Medium Membership (Monthly) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"yearlyPlanWithTrial":{"planId":"P-6XW32684EX226940VKCT2MFA","name":"Medium Membership (Annual) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"oldMonthlyPlanNoSetupFee":{"planId":"P-4N046520HR188054PCJC7LJI","name":"Medium Membership (Monthly)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"oldYearlyPlanNoSetupFee":{"planId":"P-7A4913502Y5181304CJEJMXQ","name":"Medium Membership (Annual)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"sdkUrl":"https:\u002F\u002Fwww.paypal.com\u002Fsdk\u002Fjs"},"stripePublishableKey":"pk_live_7FReX44VnNIInZwrIIx6ghjl","log":{"json":true,"level":"info"},"imageUploadMaxSizeMb":25,"staffPicks":{"title":"Staff Picks","catalogId":"c7bc6e1ee00f"}},"session":{"xsrf":""}}</script><script>window.__APOLLO_STATE__ = {"ROOT_QUERY":{"__typename":"Query","viewer":null,"collectionByDomainOrSlug({\"domainOrSlug\":\"data-science\"})":{"__ref":"Collection:7f60cf5620c9"},"postResult({\"id\":\"f1ad81785a76\"})":{"__ref":"Post:f1ad81785a76"}},"ImageMetadata:":{"__typename":"ImageMetadata","id":""},"Collection:7f60cf5620c9":{"__typename":"Collection","id":"7f60cf5620c9","favicon":{"__ref":"ImageMetadata:"},"domain":null,"slug":"data-science","googleAnalyticsId":null,"name":"TDS Archive","avatar":{"__ref":"ImageMetadata:1*JEuS4KBdakUcjg9sC7Wo4A.png"},"description":"An archive of data science, data analytics, data engineering, machine learning, and artificial intelligence writing from the former Towards Data Science Medium publication.","subscriberCount":829155,"latestPostsConnection({\"paging\":{\"limit\":1}})":{"__typename":"PostConnection","posts":[{"__ref":"Post:7b4cc0efd235"}]},"compatV3":{"__ref":"Publication:7f60cf5620c9"},"viewerEdge":{"__ref":"CollectionViewerEdge:collectionId:7f60cf5620c9-viewerId:lo_40f07550e3be"},"twitterUsername":null,"facebookPageId":null},"ImageMetadata:1*JEuS4KBdakUcjg9sC7Wo4A.png":{"__typename":"ImageMetadata","id":"1*JEuS4KBdakUcjg9sC7Wo4A.png"},"User:3da2be442381":{"__typename":"User","id":"3da2be442381","customDomainState":null,"hasSubdomain":false,"username":"jaingle77"},"Post:7b4cc0efd235":{"__typename":"Post","id":"7b4cc0efd235","firstPublishedAt":1738608884674,"creator":{"__ref":"User:3da2be442381"},"collection":{"__ref":"Collection:7f60cf5620c9"},"isSeries":false,"mediumUrl":"https:\u002F\u002Fmedium.com\u002Fdata-science\u002Fdiy-ai-how-to-build-a-linear-regression-model-from-scratch-7b4cc0efd235","sequence":null,"uniqueSlug":"diy-ai-how-to-build-a-linear-regression-model-from-scratch-7b4cc0efd235"},"Publication:7f60cf5620c9":{"__typename":"Publication","id":"7f60cf5620c9","theme":{"__typename":"PublicationTheme","accentColor":{"__typename":"ColorValue","rgb":"#355876"}}},"LinkedAccounts:b50aa26e058d":{"__typename":"LinkedAccounts","mastodon":null,"id":"b50aa26e058d"},"User:b50aa26e058d":{"__typename":"User","id":"b50aa26e058d","linkedAccounts":{"__ref":"LinkedAccounts:b50aa26e058d"},"isSuspended":false,"name":"Seouk Jun Kim","imageId":"1*<EMAIL>","customDomainState":{"__typename":"CustomDomainState","live":{"__typename":"CustomDomain","domain":"kdanielive.medium.com"}},"hasSubdomain":true,"username":"kdanielive","verifications":{"__typename":"VerifiedInfo","isBookAuthor":false},"newsletterV3":null,"socialStats":{"__typename":"SocialStats","followerCount":35,"followingCount":20,"collectionFollowingCount":7},"bio":"Columbia University student, CS. Former iOS developer, presently pursuing career in data science. https:\u002F\u002Fwww.linkedin.com\u002Fin\u002Fseouk-jun-kim-a74921184\u002F","membership":null,"allowNotes":true,"viewerEdge":{"__ref":"UserViewerEdge:userId:b50aa26e058d-viewerId:lo_40f07550e3be"},"twitterScreenName":""},"Topic:40c8e34e04ce":{"__typename":"Topic","slug":"economy","id":"40c8e34e04ce","name":"Economy"},"Paragraph:615592cf295_0":{"__typename":"Paragraph","id":"615592cf295_0","name":"9fff","type":"H3","href":null,"layout":null,"metadata":null,"text":"Rock, Paper, Scissors, and the Stock Market","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_1":{"__typename":"Paragraph","id":"615592cf295_1","name":"1dfe","type":"H4","href":null,"layout":null,"metadata":null,"text":"Recounting my experience in the Rock, Paper, Scissors Kaggle competition, and the resemblance of the competition structure to the stock market.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*hwW2M6hn4fd98QlEmbxmog.jpeg":{"__typename":"ImageMetadata","id":"1*hwW2M6hn4fd98QlEmbxmog.jpeg","originalHeight":3468,"originalWidth":4624,"focusPercentX":null,"focusPercentY":null,"alt":null},"Paragraph:615592cf295_2":{"__typename":"Paragraph","id":"615592cf295_2","name":"0e43","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*hwW2M6hn4fd98QlEmbxmog.jpeg"},"text":"Photo by Fadilah N. Imani on Unsplash","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":9,"end":25,"href":"https:\u002F\u002Funsplash.com\u002F@imanitor?utm_source=unsplash&utm_medium=referral&utm_content=creditCopyText","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":29,"end":37,"href":"\u002Fs\u002Fphotos\u002Frock-paper-scissors?utm_source=unsplash&utm_medium=referral&utm_content=creditCopyText","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_3":{"__typename":"Paragraph","id":"615592cf295_3","name":"7d8a","type":"P","href":null,"layout":null,"metadata":null,"text":"You would not normally relate rock, paper, scissors (RPS for short) to the stock market. It actually seems absurd to do so. And granted, a single game of RPS, or just a series of a thousand games of RPS has no relation whatsoever to the stock market.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_4":{"__typename":"Paragraph","id":"615592cf295_4","name":"bb59","type":"P","href":null,"layout":null,"metadata":null,"text":"But when RPS is put in a competition format where various algorithms battle against each other for points in the leaderboard, my theory is that there are some notable resemblances to the stock market.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":125,"end":199,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fc\u002Frock-paper-scissors\u002Fdiscussion\u002F215477","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_5":{"__typename":"Paragraph","id":"615592cf295_5","name":"23b6","type":"P","href":null,"layout":null,"metadata":null,"text":"More precisely, Kaggle’s recent competition on RPS that started in November, 2020, and ended in February, 2021 seems to have some characteristics of our usual stock market, and I personally achieved 32nd rank (top 2%) by using portfolio optimization theory to take advantage of this similarity.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_6":{"__typename":"Paragraph","id":"615592cf295_6","name":"d28e","type":"P","href":null,"layout":null,"metadata":null,"text":"Note that much of this article is based on intuition; there is no statistical evidence to back up my arguments. But the article should be informative for those interested in market efficiency, Kaggle competitions, portfolio optimization theory, etc.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_7":{"__typename":"Paragraph","id":"615592cf295_7","name":"6549","type":"H3","href":null,"layout":null,"metadata":null,"text":"Data Science in Rock, Paper, Scissors Context","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_8":{"__typename":"Paragraph","id":"615592cf295_8","name":"6f85","type":"H4","href":null,"layout":null,"metadata":null,"text":"Kaggle’s RPS Competition","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_9":{"__typename":"Paragraph","id":"615592cf295_9","name":"ce83","type":"P","href":null,"layout":null,"metadata":null,"text":"Four months ago, probably near the start of November of 2020, Kaggle launched the RPS competition. Below is an abbreviated excerpt from the competition description page.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":140,"end":168,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fc\u002Frock-paper-scissors\u002Foverview\u002Fdescription","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_10":{"__typename":"Paragraph","id":"615592cf295_10","name":"f48f","type":"BQ","href":null,"layout":null,"metadata":null,"text":"Rock, Paper, Scissors (sometimes called roshambo) has been a staple to settle playground disagreements or determine who gets to ride in the front seat on a road trip…","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_11":{"__typename":"Paragraph","id":"615592cf295_11","name":"3c00","type":"BQ","href":null,"layout":null,"metadata":null,"text":"Studies have shown that a Rock, Paper, Scissors AI can consistently beat human opponents. With previous games as input, it studies patterns to understand a player’s tendencies. But what happens when we expand the simple “Best-of-3” game to be “Best-of-1000”? How well can artificial intelligence perform?","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_12":{"__typename":"Paragraph","id":"615592cf295_12","name":"baae","type":"BQ","href":null,"layout":null,"metadata":null,"text":"In this simulation competition, you will create an AI to play against others in many rounds of this classic game… It’s possible to greatly outperform a random player when the matches involve non-random agents. A strong AI can consistently beat predictable AI…","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_13":{"__typename":"Paragraph","id":"615592cf295_13","name":"a898","type":"P","href":null,"layout":null,"metadata":null,"text":"As explained above, the goal of each participant in the competition is to submit an algorithm that plays 1000 consecutive RPS matches against another bot (submitted by some other participant). An algorithm wins that episode (=match of 1000 games) if it wins by more than 20 points, with 1 point rewarded for a win, 0 for a draw, and -1 for a loss. And for each victory in an episode, the algorithm is rewarded learderboard points according to the competition’s evaluation metric.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":447,"end":478,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fc\u002Frock-paper-scissors\u002Foverview\u002Fevaluation","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_14":{"__typename":"Paragraph","id":"615592cf295_14","name":"249a","type":"P","href":null,"layout":null,"metadata":null,"text":"At first sight, this seems rather pointless. The intuitive rational move in an RPS game is to play random, no debate. If we were to play a single episode against any bot, we should by all means play random.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_15":{"__typename":"Paragraph","id":"615592cf295_15","name":"7400","type":"P","href":null,"layout":null,"metadata":null,"text":"But the point of the competition lies in this part of the description:","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_16":{"__typename":"Paragraph","id":"615592cf295_16","name":"73cb","type":"BQ","href":null,"layout":null,"metadata":null,"text":"It’s possible to greatly outperform a random player when the matches involve non-random agents.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":0,"end":95,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_17":{"__typename":"Paragraph","id":"615592cf295_17","name":"6074","type":"P","href":null,"layout":null,"metadata":null,"text":"If there are even a few dozens of non-random bots in the competition, then another bot that can exploit this non-randomness will have a higher chance of outperforming all other random bots as long as there is a sufficient number of matches. This is because a superior non-random algorithm will likely tie against other random bots, but win against inferior non-random algorithms, gaining extra points in the leaderboard that random bots cannot gain.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_18":{"__typename":"Paragraph","id":"615592cf295_18","name":"01b6","type":"P","href":null,"layout":null,"metadata":null,"text":"So while the Nash equilibrium dictates that one should play random under the assumption that every other bot acts rationally, the very fact that not all bots are rational allows for non-random agents to outperform in the leaderboard.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_19":{"__typename":"Paragraph","id":"615592cf295_19","name":"f0ad","type":"P","href":null,"layout":null,"metadata":null,"text":"Already we see a resemblance to the stock market. If all market participants acted rationally with given information, then the stock market would be extremely efficient (=unpredictable). It is the existing irrationality of market participants that gives birth to exploitable market inefficiencies for excess return (although this part could be debatable after various risk-adjustment).","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_20":{"__typename":"Paragraph","id":"615592cf295_20","name":"9d16","type":"H4","href":null,"layout":null,"metadata":null,"text":"Previous RPS Competitions","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_21":{"__typename":"Paragraph","id":"615592cf295_21","name":"9ae9","type":"P","href":null,"layout":null,"metadata":null,"text":"Believe it or not there had been several RPS competitions before Kaggle. The most recent one is the RPSContest website. Although there were no prizes given out to the monthly winners, people actively participated in the contest, which led to the birth of some truly remarkable algorithms.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":104,"end":111,"href":"http:\u002F\u002Fwww.rpscontest.com\u002F","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_22":{"__typename":"Paragraph","id":"615592cf295_22","name":"f978","type":"P","href":null,"layout":null,"metadata":null,"text":"In Kaggle discussions, there were other frequently mentioned algorithms, such as Greenberg and Iocaine Powder. These algorithms performed in competitions hosted even before RPSContest, in the year 2001 and before. The best detailed descriptions of these algorithms can be found here, but to briefly put the logic in context, these highly successful algorithms utilized memory-searching algorithms with multiple layers of meta strategy that decided on whether to make the move that beats the predicted move from past history, or the move that beats that move, or the move that beats the move that beats that move, etc.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":278,"end":282,"href":"http:\u002F\u002Fwebdocs.cs.ualberta.ca\u002F~darse\u002Frsbpc.html","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":81,"end":90,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":95,"end":111,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_23":{"__typename":"Paragraph","id":"615592cf295_23","name":"fddb","type":"P","href":null,"layout":null,"metadata":null,"text":"To put the strength of these algorithms in context, Iocaine Powder and Greenberg can easily beat machine learning based algorithms such as decision tree and multi-layer perceptrons (feed-forward neural network). In fact, using machine learning algorithms requires extra care to avoid over-fitting, as other memory-searching algorithms and statistical models react much quicker to change in opponent’s strategy, which is essential in securing a +20 points victory.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":52,"end":67,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":71,"end":80,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_24":{"__typename":"Paragraph","id":"615592cf295_24","name":"92bb","type":"H3","href":null,"layout":null,"metadata":null,"text":"Market Efficiency and Data Science in the Stock Market Context","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_25":{"__typename":"Paragraph","id":"615592cf295_25","name":"9f62","type":"H4","href":null,"layout":null,"metadata":null,"text":"Efficient Market?","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_26":{"__typename":"Paragraph","id":"615592cf295_26","name":"4f0b","type":"P","href":null,"layout":null,"metadata":null,"text":"Okay, so now that we’ve talked about the place of RPS in the traditional data science community, let’s examine what it means to have an efficient market.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":136,"end":152,"href":"https:\u002F\u002Fwww.investopedia.com\u002Fterms\u002Fm\u002Fmarketefficiency.asp","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_27":{"__typename":"Paragraph","id":"615592cf295_27","name":"25a3","type":"P","href":null,"layout":null,"metadata":null,"text":"A fully efficient market incorporates all relevant information into its market price instantly. This means that, as the market price already reflects the asset’s true value, there are no opportunities to find “under-valued” or “over-valued” assets. By this definition, it is impossible to consistently outperform the market — all those with long track records of superior performance are just fortunate.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_28":{"__typename":"Paragraph","id":"615592cf295_28","name":"a3e7","type":"P","href":null,"layout":null,"metadata":null,"text":"There are different degrees of proposed market efficiency. The weak form suggests that past price movements are inconsequential in predicting future returns. In other words, trend-based trading or technical analysis based on price movements alone are not sufficient to consistently outperform the market. This has been one of the focal points of researches on cryptocurrency market efficiency in relatively recent academics, which could be interesting to read papers on.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_29":{"__typename":"Paragraph","id":"615592cf295_29","name":"3880","type":"P","href":null,"layout":null,"metadata":null,"text":"The semi-strong market efficiency states that the market price quickly responds to all publicly available information. In this perspective, even fundamental analysis is deemed obsolete. The only way to beat the market in this way will be to gain non-public information, which hangs dangerously on the line of illegal insider trading.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_30":{"__typename":"Paragraph","id":"615592cf295_30","name":"3e67","type":"P","href":null,"layout":null,"metadata":null,"text":"The strong form argues that the market reflects all information — both public and undisclosed — instantly, meaning that outperforming the market becomes a nigh impossibility.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_31":{"__typename":"Paragraph","id":"615592cf295_31","name":"490f","type":"P","href":null,"layout":null,"metadata":null,"text":"There however had been certain classes of stocks that seem to outperform the market. To incorporate these anomalies in the efficient market context, we can draw on Fama’s thee factor model, which shows that when adjusted to various risks based on certain factors, the seemingly superior stock returns could be explained (Fama later expands this model to incorporate two more factors, but these factors alone explain the majority of anomalies in expected returns).","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":164,"end":188,"href":"https:\u002F\u002Fonlinelibrary.wiley.com\u002Fdoi\u002Ffull\u002F10.1111\u002Fj.1540-6261.1992.tb04398.x","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_32":{"__typename":"Paragraph","id":"615592cf295_32","name":"ea09","type":"P","href":null,"layout":null,"metadata":null,"text":"It seems that with the exception of academia, it is generally agreed upon that while the market is generally efficient, it still displays inefficiencies, especially in short time-periods — hence the adoption of Efficiently Inefficient as the title of Lasse Heje Pedersen’s famous introductory book on hedge funds and active investing.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":211,"end":234,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_33":{"__typename":"Paragraph","id":"615592cf295_33","name":"16dd","type":"H4","href":null,"layout":null,"metadata":null,"text":"Then What is the Place of Data Science in Beating the Market?","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_34":{"__typename":"Paragraph","id":"615592cf295_34","name":"4b0a","type":"P","href":null,"layout":null,"metadata":null,"text":"As only a new student in this field, I probably do not qualify to answer the question above, but I’ll take a shot at it anyways.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_35":{"__typename":"Paragraph","id":"615592cf295_35","name":"a62c","type":"P","href":null,"layout":null,"metadata":null,"text":"The tricky part of using machine learning algorithms to predict the stock market is that it is very easy to overfit. The evidence is practically everywhere on Medium. More than half of relatively old articles on “predicting the stock market” fall into this trap. The easiest ones to spot are those that use LSTMs on recent price data to predict the next price, which just ends up being a time lag prediction (basically, every day’s price forecast is the previous day’s price; interestingly this is consistent with the Martingale Property, but you do not need LSTMs to make use of this property).","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":323,"end":328,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":518,"end":537,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_36":{"__typename":"Paragraph","id":"615592cf295_36","name":"f7a2","type":"P","href":null,"layout":null,"metadata":null,"text":"Granted, the top quant hedge funds are probably using ML algorithms for full benefit in beating the market by taking extra care to avoid overfitting, but for a regular retail investor, the cost of taking such extra caution probably outweighs the benefits. The fact is that a simple statistical model with enough, real diversification shows surprisingly strong performance. And while using additional data science techniques such as sentiment analysis, google trend analysis, and deep neural networks could provide a small edge, that effort is better spent minimizing market friction, providing extra diversification, lowering server costs, etc.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_37":{"__typename":"Paragraph","id":"615592cf295_37","name":"8e01","type":"P","href":null,"layout":null,"metadata":null,"text":"Of course, there are certain exceptions such as QRAFT, a Korean startup recently drawing a lot of attention, but even here you should notice that the startup’s most valuable assets are not only its deep reinforcement learning based ETFs, but also AXE (ML based order execution system) and Kirin API (automated data cleaning module).","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":48,"end":53,"href":"https:\u002F\u002Fwww.qraftec.com\u002F","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_38":{"__typename":"Paragraph","id":"615592cf295_38","name":"b083","type":"P","href":null,"layout":null,"metadata":null,"text":"To summarize, data science does have a place in the active investing world, but while its impact on stock return prediction is unclear, its positive influence on other aspects of trading such as order execution seems much clear-cut.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_39":{"__typename":"Paragraph","id":"615592cf295_39","name":"3552","type":"H3","href":null,"layout":null,"metadata":null,"text":"Non-random RPS Competition and the Inefficient Market","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_40":{"__typename":"Paragraph","id":"615592cf295_40","name":"8182","type":"H4","href":null,"layout":null,"metadata":null,"text":"Drawing the Analogy","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_41":{"__typename":"Paragraph","id":"615592cf295_41","name":"b9d8","type":"P","href":null,"layout":null,"metadata":null,"text":"You’re probably wondering now how all the information above come together to form the conclusion the Kaggle’s RPS competition resembles the stock market.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_42":{"__typename":"Paragraph","id":"615592cf295_42","name":"06df","type":"P","href":null,"layout":null,"metadata":null,"text":"Well, let’s consider the semi-strong form of market efficiency. Here it is argued that whenever there is a disclosed event that changes the value of an asset, the market price instantly reflects that information, propelled by market participants who act on the opportunity to make profit.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_43":{"__typename":"Paragraph","id":"615592cf295_43","name":"e592","type":"P","href":null,"layout":null,"metadata":null,"text":"One way of seeing how this works, is to consider what would happen if it is guaranteed that market efficiency holds true. If that is the case, then there is no incentive for any market participant to risk losing the transaction cost by actively trading. The best move would be to only take the systematic risk by investing in passive ETFs.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_44":{"__typename":"Paragraph","id":"615592cf295_44","name":"3796","type":"P","href":null,"layout":null,"metadata":null,"text":"However, this is usually not the case. Investors, whether institutional or individual, trade actively under the assumption that certain inefficiencies exist for exploitation. If any inefficiency exists, then this active movement removes that inefficiency from the market as investors greedily take opportunity from the wrong pricing, quickly reverting the price towards its correct value. The return that an investor gains for such activity can be thought of as the reward for re-instilling efficiency to the market.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_45":{"__typename":"Paragraph","id":"615592cf295_45","name":"9705","type":"P","href":null,"layout":null,"metadata":null,"text":"Interestingly this form of active investing that restores efficiency is also the very source of inefficiency.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_46":{"__typename":"Paragraph","id":"615592cf295_46","name":"eca3","type":"P","href":null,"layout":null,"metadata":null,"text":"Now let’s go back to Kaggle’s RPS competition. Under Nash equilibrium one should only submit random agents and hope to win. However, in this case we know for a fact that there are non-random, irrational agents participating in the competition. Under this new information, the best move becomes to introduce similarly non-random (inefficient) agents that could hopefully beat all other non-random agents. If you succeed in coming up with such algorithm, then you revert the performance of the other non-random agents to the random mean (restoring efficiency), and gain points in the leaderboard as the reward. At the same time, because you submitted a non-random agent, you also risk introducing inefficiency to the market unless another better bot emerges.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_47":{"__typename":"Paragraph","id":"615592cf295_47","name":"e3ca","type":"P","href":null,"layout":null,"metadata":null,"text":"In Kaggle’s RPS competition, past algorithms like Iocaine Powder and Greenberg performed very poorly. Why? While an obvious reason is that almost every other top-performing agent has a mirror version of such algorithms to defeat them 100% of the time, the other reason is that because so many copies of such successful-in-past-but-publicly-available algorithms are already submitted, bots that would lose to such algorithms already hang too low on the leaderboard. In other words, all inefficiencies in the competition that Iocaine Powder or Greenberg could feed on are already resolved by the collective crowd.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":49,"end":64,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":69,"end":78,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":524,"end":538,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":542,"end":552,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_48":{"__typename":"Paragraph","id":"615592cf295_48","name":"539d","type":"P","href":null,"layout":null,"metadata":null,"text":"This is very similar to what happens when a successful trading strategy gets publicly known in the investing world. In the very beginning utilizing option pricing using Black-Scholes model, arbitrage opportunities like pair-trading, and contrarian strategies were all pretty lucrative because limited number of market participants employed them. By the market crashes in the 2000s, however, extreme leverage became essential to attain similar levels of return.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_49":{"__typename":"Paragraph","id":"615592cf295_49","name":"5513","type":"P","href":null,"layout":null,"metadata":null,"text":"I can be frowned upon for drafting these conclusions without any real data to back them up. After all, it is the spirit of Kaggle and data science community in general to actually extract data and draw concrete conclusions. But the analogy drawn here does bring an interesting view on the nature of the competition.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_50":{"__typename":"Paragraph","id":"615592cf295_50","name":"d841","type":"H4","href":null,"layout":null,"metadata":null,"text":"Portfolio Optimization as the Ensemble Methodology","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_51":{"__typename":"Paragraph","id":"615592cf295_51","name":"0cd9","type":"P","href":null,"layout":null,"metadata":null,"text":"With less than a couple of weeks until the competition deadline, it became increasingly clear that an ensemble is probably the way to go in order to be successful. While there was a remarkable reveal of a simple, non-ensemble agent that showed superb performance, most of the discussions leaned towards coming up with a reliable ensemble methodology.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":203,"end":231,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fc\u002Frock-paper-scissors\u002Fdiscussion\u002F210305","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":264,"end":287,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fc\u002Frock-paper-scissors\u002Fdiscussion\u002F201683","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_52":{"__typename":"Paragraph","id":"615592cf295_52","name":"1a67","type":"P","href":null,"layout":null,"metadata":null,"text":"Personally, as I was fascinated by the similarity of the competition to the stock market, I desperately wanted to make use of knowledge in the quantitative finance field that I had been wrestling with for the past year.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_53":{"__typename":"Paragraph","id":"615592cf295_53","name":"50b1","type":"P","href":null,"layout":null,"metadata":null,"text":"A detailed write-up of my thoughts is well documented here, but to add on to what is said in the linked thread, let me concretely explain how I set up the problem.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":54,"end":58,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fc\u002Frock-paper-scissors\u002Fdiscussion\u002F216358","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_54":{"__typename":"Paragraph","id":"615592cf295_54","name":"d7bd","type":"P","href":null,"layout":null,"metadata":null,"text":"Regardless of whether you make a deterministic or stochastic choice at each step of an episode, the fact is that when trying to incorporate information from multiple algorithms, you are going to weight the decision of those algorithms for the final decision. If algorithm A has produced excellent results against the current opponent, then it is most likely that you should stick with the recommended move of algorithm A; if algorithm A recommends scissors, you should pick scissors. In such case, the weight map would look like: {0 : 0.02, 1 : 0.18, 2 : 0.80}, in which 0, 1, 2 correspond to rock, paper, scissors, respectively. With 80% of the weights concentrated on 2, the ensemble would pick scissors as the next move.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_55":{"__typename":"Paragraph","id":"615592cf295_55","name":"24bc","type":"P","href":null,"layout":null,"metadata":null,"text":"So in some aspect, picking weights for algorithms to use for the current step is similar to how one would choose portfolio weights for different assets during a re-balancing period. If an algorithm(=asset) performed well recently, you would be likely to give it more weight. Then we can think of each step in the episode as a natural time-step for re-balancing our portfolio.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_56":{"__typename":"Paragraph","id":"615592cf295_56","name":"6e0b","type":"P","href":null,"layout":null,"metadata":null,"text":"Now, most of the publicly available RPS algorithms use the same core logic; it was frequently discussed in the competition threads that it is essential to remove redundant agents in order to improve the performance of an ensemble. Well, if we were to incentivize algorithms with low correlation with others, then there is no need for manual hand-picking.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_57":{"__typename":"Paragraph","id":"615592cf295_57","name":"7d87","type":"P","href":null,"layout":null,"metadata":null,"text":"Fortunately, our famous mean-variance portfolio optimization theory gives us the means to do exactly that. I don’t want to get too deep into the portfolio optimization theory, as there are murky areas that I am still not so sure about, but the important takeaway is that as long as expected returns of target assets are excellent, the resulting portfolio will also be excellent in terms of risk-adjusted returns: the portfolio will rarely lose.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":24,"end":67,"href":"http:\u002F\u002Fwww.columbia.edu\u002F~mh2078\u002FFoundationsFE\u002FMeanVariance-CAPM.pdf","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_58":{"__typename":"Paragraph","id":"615592cf295_58","name":"3248","type":"P","href":null,"layout":null,"metadata":null,"text":"Regrettably, Kaggle only allows a maximum of 1 second to decide on the next move, so I was not able to get an ensemble model that examines the efficient frontier to find the point of maximum Sharpe. Had that been possible, I dare presume I might have finished higher in the leaderboard.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_59":{"__typename":"Paragraph","id":"615592cf295_59","name":"e580","type":"P","href":null,"layout":null,"metadata":null,"text":"Wait, though. How do I know that this ensemble method was indeed successful in the competition? After all, a lucky random agent could wander very high up in the leaderboard, due to the nature of RPS. My defense would be that I had 8 of these portfolio ensemble agents in the medal zone by the end of the competition, and that this ensemble method has proved much more successful than multi-armed bandit with beta distribution in local testing (and in the leaderboard).","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_60":{"__typename":"Paragraph","id":"615592cf295_60","name":"09c1","type":"H3","href":null,"layout":null,"metadata":null,"text":"In Conclusion","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_61":{"__typename":"Paragraph","id":"615592cf295_61","name":"206b","type":"P","href":null,"layout":null,"metadata":null,"text":"Once you register all the facts, it probably is not a shock that RPS competition resembles a stock market in some aspects. Both could be described as systems that are completely random in the long run, yet somewhat predictable in the short term.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_62":{"__typename":"Paragraph","id":"615592cf295_62","name":"c30d","type":"P","href":null,"layout":null,"metadata":null,"text":"What RPS competition really showed me, however, is that using complicated methods like LSTMs to bash through all the random noise is usually very pointless. The fact is that there does not exist any such strong signals in past score trends (=past price trends) that give meaningful information about the future. Instead of worrying about tuning hyper-parameters, more effort could be better spent on trimming down execution time of algorithms (=reducing market friction?) or coming up with and adding more effective algorithms (=adding diversification).","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_63":{"__typename":"Paragraph","id":"615592cf295_63","name":"9032","type":"P","href":null,"layout":null,"metadata":null,"text":"Also, math is king. I really recommend checking out this thread. It shows how effective a simple mathematical solution could be.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":52,"end":63,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fc\u002Frock-paper-scissors\u002Fdiscussion\u002F210305","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_64":{"__typename":"Paragraph","id":"615592cf295_64","name":"6e2b","type":"P","href":null,"layout":null,"metadata":null,"text":"Thanks to Kaggle staff for hosting this fun competition, great gratitude to people like Stas Sl, Taaha Khan, SuperAnt, Tony Robinson, etc, who made so much meaningful intellectual contributions to the community.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":0,"end":211,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:615592cf295_65":{"__typename":"Paragraph","id":"615592cf295_65","name":"b3eb","type":"P","href":null,"layout":null,"metadata":null,"text":"Note from Towards Data Science’s editors: While we allow independent authors to publish articles in accordance with our rules and guidelines, we do not endorse each author’s contribution. You should not rely on an author’s works without seeking professional advice. See our Reader Terms for details.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":120,"end":140,"href":"https:\u002F\u002Ftowardsdatascience.com\u002Fquestions-96667b06af5","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":274,"end":286,"href":"https:\u002F\u002Ftowardsdatascience.com\u002Freaders-terms-b5d780a700a4","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":0,"end":41,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":0,"end":299,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"CollectionViewerEdge:collectionId:7f60cf5620c9-viewerId:lo_40f07550e3be":{"__typename":"CollectionViewerEdge","id":"collectionId:7f60cf5620c9-viewerId:lo_40f07550e3be","isEditor":false,"isMuting":false},"UserViewerEdge:userId:b50aa26e058d-viewerId:lo_40f07550e3be":{"__typename":"UserViewerEdge","id":"userId:b50aa26e058d-viewerId:lo_40f07550e3be","isMuting":false},"PostViewerEdge:postId:f1ad81785a76-viewerId:lo_40f07550e3be":{"__typename":"PostViewerEdge","shouldIndexPostForExternalSearch":true,"id":"postId:f1ad81785a76-viewerId:lo_40f07550e3be"},"Tag:stock-market":{"__typename":"Tag","id":"stock-market","displayTitle":"Stock Market","normalizedTagSlug":"stock-market"},"Tag:quantitative-finance":{"__typename":"Tag","id":"quantitative-finance","displayTitle":"Quantitative Finance","normalizedTagSlug":"quantitative-finance"},"Tag:portfolio-management":{"__typename":"Tag","id":"portfolio-management","displayTitle":"Portfolio Management","normalizedTagSlug":"portfolio-management"},"Tag:kaggle":{"__typename":"Tag","id":"kaggle","displayTitle":"Kaggle","normalizedTagSlug":"kaggle"},"Tag:rock-paper-scissors":{"__typename":"Tag","id":"rock-paper-scissors","displayTitle":"Rock Paper Scissors","normalizedTagSlug":"rock-paper-scissors"},"Post:f1ad81785a76":{"__typename":"Post","id":"f1ad81785a76","collection":{"__ref":"Collection:7f60cf5620c9"},"content({\"postMeteringOptions\":{\"referrer\":\"\"}})":{"__typename":"PostContent","isLockedPreviewOnly":false,"bodyModel":{"__typename":"RichText","sections":[{"__typename":"Section","name":"a230","startIndex":0,"textLayout":null,"imageLayout":null,"backgroundImage":null,"videoLayout":null,"backgroundVideo":null},{"__typename":"Section","name":"1404","startIndex":64,"textLayout":null,"imageLayout":null,"backgroundImage":null,"videoLayout":null,"backgroundVideo":null},{"__typename":"Section","name":"c337","startIndex":65,"textLayout":null,"imageLayout":null,"backgroundImage":null,"videoLayout":null,"backgroundVideo":null}],"paragraphs":[{"__ref":"Paragraph:615592cf295_0"},{"__ref":"Paragraph:615592cf295_1"},{"__ref":"Paragraph:615592cf295_2"},{"__ref":"Paragraph:615592cf295_3"},{"__ref":"Paragraph:615592cf295_4"},{"__ref":"Paragraph:615592cf295_5"},{"__ref":"Paragraph:615592cf295_6"},{"__ref":"Paragraph:615592cf295_7"},{"__ref":"Paragraph:615592cf295_8"},{"__ref":"Paragraph:615592cf295_9"},{"__ref":"Paragraph:615592cf295_10"},{"__ref":"Paragraph:615592cf295_11"},{"__ref":"Paragraph:615592cf295_12"},{"__ref":"Paragraph:615592cf295_13"},{"__ref":"Paragraph:615592cf295_14"},{"__ref":"Paragraph:615592cf295_15"},{"__ref":"Paragraph:615592cf295_16"},{"__ref":"Paragraph:615592cf295_17"},{"__ref":"Paragraph:615592cf295_18"},{"__ref":"Paragraph:615592cf295_19"},{"__ref":"Paragraph:615592cf295_20"},{"__ref":"Paragraph:615592cf295_21"},{"__ref":"Paragraph:615592cf295_22"},{"__ref":"Paragraph:615592cf295_23"},{"__ref":"Paragraph:615592cf295_24"},{"__ref":"Paragraph:615592cf295_25"},{"__ref":"Paragraph:615592cf295_26"},{"__ref":"Paragraph:615592cf295_27"},{"__ref":"Paragraph:615592cf295_28"},{"__ref":"Paragraph:615592cf295_29"},{"__ref":"Paragraph:615592cf295_30"},{"__ref":"Paragraph:615592cf295_31"},{"__ref":"Paragraph:615592cf295_32"},{"__ref":"Paragraph:615592cf295_33"},{"__ref":"Paragraph:615592cf295_34"},{"__ref":"Paragraph:615592cf295_35"},{"__ref":"Paragraph:615592cf295_36"},{"__ref":"Paragraph:615592cf295_37"},{"__ref":"Paragraph:615592cf295_38"},{"__ref":"Paragraph:615592cf295_39"},{"__ref":"Paragraph:615592cf295_40"},{"__ref":"Paragraph:615592cf295_41"},{"__ref":"Paragraph:615592cf295_42"},{"__ref":"Paragraph:615592cf295_43"},{"__ref":"Paragraph:615592cf295_44"},{"__ref":"Paragraph:615592cf295_45"},{"__ref":"Paragraph:615592cf295_46"},{"__ref":"Paragraph:615592cf295_47"},{"__ref":"Paragraph:615592cf295_48"},{"__ref":"Paragraph:615592cf295_49"},{"__ref":"Paragraph:615592cf295_50"},{"__ref":"Paragraph:615592cf295_51"},{"__ref":"Paragraph:615592cf295_52"},{"__ref":"Paragraph:615592cf295_53"},{"__ref":"Paragraph:615592cf295_54"},{"__ref":"Paragraph:615592cf295_55"},{"__ref":"Paragraph:615592cf295_56"},{"__ref":"Paragraph:615592cf295_57"},{"__ref":"Paragraph:615592cf295_58"},{"__ref":"Paragraph:615592cf295_59"},{"__ref":"Paragraph:615592cf295_60"},{"__ref":"Paragraph:615592cf295_61"},{"__ref":"Paragraph:615592cf295_62"},{"__ref":"Paragraph:615592cf295_63"},{"__ref":"Paragraph:615592cf295_64"},{"__ref":"Paragraph:615592cf295_65"}]},"validatedShareKey":"","shareKeyCreator":null},"creator":{"__ref":"User:b50aa26e058d"},"inResponseToEntityType":null,"isLocked":false,"isMarkedPaywallOnly":false,"lockedSource":"LOCKED_POST_SOURCE_NONE","mediumUrl":"https:\u002F\u002Fmedium.com\u002Fdata-science\u002Frock-paper-scissors-and-the-stock-market-f1ad81785a76","primaryTopic":{"__ref":"Topic:40c8e34e04ce"},"topics":[{"__typename":"Topic","slug":"artificial-intelligence"},{"__typename":"Topic","slug":"economy"},{"__typename":"Topic","slug":"data-science"}],"isLimitedState":false,"isPublished":true,"allowResponses":true,"responsesLocked":false,"visibility":"PUBLIC","latestPublishedVersion":"615592cf295","postResponses":{"__typename":"PostResponses","count":0},"responseDistribution":"NOT_DISTRIBUTED","clapCount":22,"title":"Rock, Paper, Scissors, and the Stock Market","isSeries":false,"sequence":null,"uniqueSlug":"rock-paper-scissors-and-the-stock-market-f1ad81785a76","socialTitle":"","socialDek":"","canonicalUrl":"https:\u002F\u002Fmedium.com\u002Fdata-science\u002Frock-paper-scissors-and-the-stock-market-f1ad81785a76","metaDescription":"","latestPublishedAt":1633720589089,"readingTime":11.50566037735849,"previewContent":{"__typename":"PreviewContent","subtitle":"Recounting my experience in the Rock, Paper, Scissors Kaggle competition, and the resemblance of the competition structure to the stock…"},"previewImage":{"__ref":"ImageMetadata:1*hwW2M6hn4fd98QlEmbxmog.jpeg"},"isShortform":false,"seoMetaTags":{"__typename":"SEOMetaTags","jsonLd":"{\"@context\":\"https:\u002F\u002Fschema.org\",\"@id\":\"https:\u002F\u002Fmedium.com\u002Fdata-science\u002Frock-paper-scissors-and-the-stock-market-f1ad81785a76\",\"@type\":\"SocialMediaPosting\",\"image\":[\"https:\u002F\u002Fmiro.medium.com\u002F1*hwW2M6hn4fd98QlEmbxmog.jpeg\"],\"url\":\"https:\u002F\u002Fmedium.com\u002Fdata-science\u002Frock-paper-scissors-and-the-stock-market-f1ad81785a76\",\"dateCreated\":\"2021-03-13T09:09:25Z\",\"datePublished\":\"2021-03-13T09:09:25Z\",\"dateModified\":\"2021-10-08T19:16:29Z\",\"headline\":\"Rock, Paper, Scissors, and the Stock Market\",\"name\":\"Rock, Paper, Scissors, and the Stock Market\",\"description\":\"Rock, Paper, Scissors, and the Stock Market\\nRecounting my experience in the Rock, Paper, Scissors Kaggle competition, and the resemblance of the competition structure to the stock market.\\nYou would …\",\"identifier\":\"f1ad81785a76\",\"author\":{\"@context\":\"https:\u002F\u002Fschema.org\",\"@id\":\"https:\u002F\u002Fmedium.com\u002F@kdanielive\",\"@type\":\"Person\",\"identifier\":\"kdanielive\",\"name\":\"Seouk Jun Kim\",\"url\":\"https:\u002F\u002Fmedium.com\u002F@kdanielive\"},\"creator\":{\"@context\":\"https:\u002F\u002Fschema.org\",\"@id\":\"https:\u002F\u002Fmedium.com\u002F@kdanielive\",\"@type\":\"Person\",\"identifier\":\"kdanielive\",\"name\":\"Seouk Jun Kim\",\"url\":\"https:\u002F\u002Fmedium.com\u002F@kdanielive\"},\"publisher\":{\"@context\":\"https:\u002F\u002Fschema.org\",\"@type\":\"Organization\",\"@id\":\"https:\u002F\u002Fmedium.com\u002Fdata-science\",\"name\":\"TDS Archive\",\"description\":\"An archive of data science, data analytics, data engineering, machine learning, and artificial intelligence writing from the former Towards Data Science Medium publication.\",\"url\":\"https:\u002F\u002Fmedium.com\u002Fdata-science\",\"logo\":{\"@type\":\"ImageObject\",\"width\":692,\"height\":642,\"url\":\"https:\u002F\u002Fmiro.medium.com\u002Fv2\u002Fresize:fit:692\u002F1%2AJEuS4KBdakUcjg9sC7Wo4A.png\"}},\"mainEntityOfPage\":\"https:\u002F\u002Fmedium.com\u002Fdata-science\u002Frock-paper-scissors-and-the-stock-market-f1ad81785a76\",\"isAccessibleForFree\":true}"},"seoDescription":"","shortformType":"SHORTFORM_TYPE_LINK","firstPublishedAt":1615626565774,"viewerEdge":{"__ref":"PostViewerEdge:postId:f1ad81785a76-viewerId:lo_40f07550e3be"},"seoTitle":"","isSuspended":false,"license":"ALL_RIGHTS_RESERVED","tags":[{"__ref":"Tag:stock-market"},{"__ref":"Tag:quantitative-finance"},{"__ref":"Tag:portfolio-management"},{"__ref":"Tag:kaggle"},{"__ref":"Tag:rock-paper-scissors"}],"isFeaturedInPublishedPublication":false,"isNewsletter":false,"statusForCollection":"APPROVED","pendingCollection":null,"detectedLanguage":"en","wordCount":2996,"layerCake":3}}</script><script>window.__MIDDLEWARE_STATE__={"session":{"xsrf":""},"cache":{"cacheStatus":"MISS"}}</script><script src="https://cdn-client.medium.com/lite/static/js/manifest.91087f21.js"></script><script src="https://cdn-client.medium.com/lite/static/js/723.093de8f1.js"></script><script src="https://cdn-client.medium.com/lite/static/js/main.668f262a.js"></script><script src="https://cdn-client.medium.com/lite/static/js/instrumentation.47ae8b31.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/reporting.851fdaca.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/5052.eb638269.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/683.abfef39e.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/6618.4aea0357.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/2050.3c25fb60.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3326.9712e10d.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7566.fa51707d.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7908.908acb8a.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3927.2f9f3eed.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/8640.0d3bced2.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9967.f31ca2af.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9214.a792bbcc.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/1214.9ae8faaf.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/556.d95c90dd.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7381.c53435ab.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9768.a85f5560.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/5522.3fb24bd9.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/792.f17e92fb.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3507.8b27b9e8.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7561.fc7962fc.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/4929.75144692.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/6834.6c66e3cc.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/1887.f9daf0b6.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7979.35c5b2af.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7975.3f8d607c.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3877.96683729.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9256.629cdc7e.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/8768.62c3639c.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/144.f38d4759.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3666.6579eeda.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/1069.6236ad3b.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/PostPage.MainContent.e31fff2e.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/2698.9eecb474.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3974.ee0dd7bf.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/2527.358dc2fb.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/PostResponsesContent.3c0c12ee.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/responses.editor.d61aa7c4.chunk.js"></script>
<script id="__LOADABLE_REQUIRED_CHUNKS__" type="application/json">[]</script>
<script id="__LOADABLE_REQUIRED_CHUNKS___ext" type="application/json">{"namedChunks":[]}</script><script>window.main();</script><script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'977752c93c98a2c2',t:'MTc1NjU4ODczNS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body></html>