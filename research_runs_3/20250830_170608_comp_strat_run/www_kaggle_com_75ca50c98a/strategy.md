# The Moomins - My perhaps over-complicated LSTM solution

**Source:** https://www.kaggle.com/competitions/jane-street-market-prediction/writeups/the-moomins-my-perhaps-over-complicated-lstm-solut


## Thesis

This write-up details an approach to the Jane Street Market Prediction competition using a Long Short-Term Memory (LSTM) model. The LSTM is trained on historical data to predict daily actions for trading, aiming to maximize the Sharpe ratio of the portfolio. The model incorporates various features including technical indicators and alternative data sources such as news sentiment analysis.

## Universe & Rebalancing

**Universe:** The universe consists of a set of assets traded in the Jane Street Market Prediction competition, which includes stocks, ETFs, and other financial instruments.
**Rebalancing:** daily


## Signals

- **LSTM Predicted Action:** A daily prediction generated by an LSTM model indicating whether to buy (1), hold (0), or sell (-1) a given asset based on historical market data and alternative data.
- **Technical Indicators:** Various technical indicators such as moving averages, RSI, MACD, etc., calculated from the price history of assets to inform trading decisions.
- **News Sentiment Score:** A sentiment score derived from analyzing news articles and social media posts related to each asset, used as an additional input feature for the LSTM model.
