Everyday I will backtest a different quant strategy for the markets!
The main metric I will be using to measure a strategy is the sharpe ratio. What is the sharpe ratio?
The sharpe ratio is the average return of a strategy divided by its risk. Meaning if a strategy has high returns, and low risk, then it will have a high sharpe ratio. Why this metric and not total profit? Because total profit only keeps track of returns of a strategy, not risk, while sharpe ratio is calculated with both, allowing you to sleep better at night, instead of wondering if you are going to make 100% or lose 100% tomorrow.
- Either download the notebook or copy the code into a Python script
- Remove the end date parameter from yf.download()
- Print the last row of
weighted_signal
usingprint(weighted_signal.iloc[-1])
(I will update the notebooks to already have this) - Go long the positive weights and (if the strategy is L/S,) short the negative weights
First I will the evaluate the sharpe of each lookback of a strategy in a 5-fold cross validation to determine the best* parameters, then I will plot the cumulative return of the lookback with the highest sharpe. These backtests DO NOT take fees/slippage into account, in my opinion this isnt a problem for the long-only strategies, but needs to be considered for long/short strategies.
- TS = Time Series
- L\S = Long Short
- XS = Cross Sectional
- Vol = Volatility
- MOM = Momentum
- STR = Short-term Reversal
Code was provided by @quant_arb on Twitter, I added the cross-validation to try to better pick parameters.