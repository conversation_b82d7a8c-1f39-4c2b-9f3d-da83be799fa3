# A Comprehensive Approach to Construct a Portfolio: Factor Model, Bayesian Shrinkage, Black-Litterman Model, and Smart Beta

**Source:** https://github.com/Gavin-OP/fina4380-project


## Thesis

The primary objective of our project is to maximize the portfolio return by stock selection and allocation. We employ a dual momentum method for initial stock selection, apply Bayesian shrinkage and Black-Litterman Model for factor returns re-evaluation, utilize Smart Beta for weight calculation, and backtest using Backtrader and QuantStats frameworks. The strategy aims to capture momentum effects and adapt to changing market conditions through monthly rebalancing.

## Universe & Rebalancing

**Universe:** S&P 500 index
**Rebalancing:** monthly


## Signals

- **Dual Momentum Method:** Combines absolute and relative momentum to identify assets with strong performance potential.
- **Bayesian Shrinkage:** Re-evaluates posterior predictive moments of stock returns using the newest information and investor's view, incorporating a six-month formation period for stock selection.
- **Black-Litterman Model:** Incorporates forward-looking analyses on return and covariance matrix of underlying factors via second layer Bayesian update.
- **Smart Beta Weight Calculation:** Calculates the weight of each stock within our portfolio using methods such as Risk Parity, Maximum Diversification Ratio (MDR), Global Minimum Variance (GMV), and Maximum Sharpe Ratio (MSR).
