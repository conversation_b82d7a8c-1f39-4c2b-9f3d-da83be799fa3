# researcher_crawl_loop_competitions.py
# Purpose: Crawl for past portfolio/algo competitions (<PERSON>ggle, EDU, blogs),
# extract page text + code, and ask an LLM (Ollama) for a STRICT JSON strategy
# -> then write a MINIMAL researcher-style Markdown:
#    Thesis, Universe, Rebalancing, Signals

import os, re, sys, json, time, queue, hashlib, logging, random, shutil
from typing import List, Dict, Optional
from datetime import datetime
from urllib.parse import urlparse, urljoin

import requests
from requests.adapters import HTTPAdapter, Retry
from bs4 import BeautifulSoup

# HTML + PDF extraction
import trafilatura
try:
    from pdfminer.high_level import extract_text as pdf_extract_text  # optional
except Exception:
    pdf_extract_text = None  # gracefully disable PDFs if not installed

# --- Config ---
SEARXNG_URL = "http://localhost:8888/search"
OLLAMA_URL  = "http://localhost:11434/api/chat"  # change if different
USER_AGENT  = "HackathonResearchBot/1.2 (+https://example.local)"
OUT_ROOT    = os.path.abspath("./research_runs_3")
MAX_LINK_DEPTH = 1                 # follow in-domain links up to this depth
MAX_PAGES_PER_QUERY = 20           # safety cap per query
REQUEST_TIMEOUT = 30
SLEEP_BETWEEN_REQUESTS = (0.7, 1.8)  # random seconds (min,max)
OLLAMA_MODEL = None

GLOBAL_CONTEXT = (
    "You are an equity portfolio strategy analyst. Read sources about past "
    "portfolio/algorithmic competitions (Kaggle, university challenges, blogs). "
    "Extract the core TRADING STRATEGY in a consistent, backtestable, quantitative form."
)

# STRICT schema for the LLM (only the fields we need in MD)
LLM_SCHEMA_PROMPT = (
    "Return STRICT JSON with the following keys (no extra keys):\n"
    "{\n"
    '  "title": str,\n'
    '  "source_url": str,\n'
    '  "thesis": str,\n'
    '  "universe": str,\n'
    '  "rebalancing": str,  // one of: daily|weekly|biweekly|monthly|quarterly|semiannual|annual|unknown\n'
    '  "signals": [\n'
    '    {\n'
    '      "name": str,\n'
    '      "definition": str,               // plain English, precise\n'
    '      "formula_latex": str,            // math if present, else "unknown"\n'
    '      "pseudo": str,                   // step-by-step pseudo-code to compute the signal\n'
    '      "parameters": {                  // hyperparameters with defaults/ranges if stated\n'
    '        "lookback": str,\n'
    '        "smoothing": str,\n'
    '        "thresholds": str,\n'
    '        "other": str\n'
    '      },\n'
    '      "required_fields": [str]         // exact dataset column names if present; else best explicit names\n'
    '    }\n'
    '  ],\n'
    '  "backtest_spec": {                   // how the portfolio was actually formed\n'
    '    "formation_period": str,           // e.g. t uses data up to t-1m\n'
    '    "holding_period": str,             // e.g. 1m, 1w, 1d\n'
    '    "long_short": str,                 // e.g. long-only, market-neutral L/S\n'
    '    "weighting": str,                  // e.g. equal-weight, z-score rank weight, mean-variance\n'
    '    "transaction_costs": str,          // e.g. 10bps per trade or unknown\n'
    '    "universe_filters": str,           // e.g. top 1000 by mcap; remove illiquid; etc.\n'
    '    "neutralization": str,             // e.g. beta/sector/industry neutral, or none\n'
    '    "rebalance_time_tz": str           // e.g. market close NY; unknown if unstated\n'
    '  },\n'
    '  "evidence": [                        // ground facts with quotes and character spans\n'
    '    {"quote": str, "char_start": int, "char_end": int}\n'
    '  ],\n'
    '  "unknowns": [str]                    // list any required items not found in the text/code\n'
    "}\n"
    "- Your answers MUST be grounded in the provided page text/code. If something is not present, write 'unknown'.\n"
    "- NEVER invent numbers or parameters. Prefer quoting exact phrases (add them to 'evidence').\n"
    "- For 'rebalancing', pick the cadence used for portfolio formation; if unclear, set 'unknown'.\n"
    "- Keep signal 'pseudo' specific enough that a dev could implement without the page.\n"
)

def extract_first_json(text: str) -> Optional[dict]:
    m = re.search(r"\{[\s\S]*\}\s*$", text.strip())
    if not m: 
        m = re.search(r"\{[\s\S]*\}", text)  # fallback
    if not m:
        return None
    try:
        return json.loads(m.group(0))
    except Exception:
        return None

REQUIRED_KEYS = {
    "title", "source_url", "thesis", "universe", "rebalancing",
    "signals", "backtest_spec", "evidence", "unknowns"
}

def enforce_schema(obj: dict) -> dict:
    if not isinstance(obj, dict):
        return {}
    fixed = {k: obj.get(k, "unknown") for k in REQUIRED_KEYS}
    # Ensure types
    fixed["signals"] = fixed.get("signals") or []
    if not isinstance(fixed["signals"], list):
        fixed["signals"] = []
    for sig in fixed["signals"]:
        sig.setdefault("name", "unknown")
        sig.setdefault("definition", "unknown")
        sig.setdefault("formula_latex", "unknown")
        sig.setdefault("pseudo", "unknown")
        sig.setdefault("parameters", {})
        sig["parameters"].setdefault("lookback", "unknown")
        sig["parameters"].setdefault("smoothing", "unknown")
        sig["parameters"].setdefault("thresholds", "unknown")
        sig["parameters"].setdefault("other", "unknown")
        sig.setdefault("required_fields", [])
    fixed["backtest_spec"] = fixed.get("backtest_spec") or {}
    for k in ["formation_period","holding_period","long_short","weighting",
              "transaction_costs","universe_filters","neutralization","rebalance_time_tz"]:
        fixed["backtest_spec"].setdefault(k, "unknown")
    fixed["evidence"] = fixed.get("evidence") or []
    fixed["unknowns"] = fixed.get("unknowns") or []
    # Normalize rebalancing
    rb = (fixed.get("rebalancing") or "").lower()
    allowed = {"daily","weekly","biweekly","monthly","quarterly","semiannual","annual","unknown"}
    fixed["rebalancing"] = rb if rb in allowed else detect_rebalancing(str(obj)) or "unknown"
    return fixed

def repair_json_with_llm(bad_text: str, model: str) -> Optional[dict]:
    prompt = (
        "The following content was supposed to be STRICT JSON per the schema I gave you, "
        "but it is invalid or contains extra text. Return ONLY valid JSON that matches the schema, "
        "with unknowns explicitly marked as 'unknown'.\n\n"
        f"{bad_text}"
    )
    raw = ollama_generate(prompt, model=model, stream=False)
    obj = extract_first_json(raw)
    if obj:
        return enforce_schema(obj)
    return None

# --- Logging setup (console + file) ---
def setup_logging(run_dir):
    log = logging.getLogger("research_loop")
    log.setLevel(logging.INFO)
    fmt = logging.Formatter("%(asctime)s | %(levelname)s | %(message)s")

    # avoid duplicate handlers if reloaded
    if not any(isinstance(h, logging.StreamHandler) for h in log.handlers):
        ch = logging.StreamHandler(sys.stdout)
        ch.setLevel(logging.INFO); ch.setFormatter(fmt)
        log.addHandler(ch)

    fh = logging.FileHandler(os.path.join(run_dir, "research.log"), encoding="utf-8")
    fh.setLevel(logging.INFO); fh.setFormatter(fmt)
    log.addHandler(fh)
    return log

# --- HTTP session with retries ---
def make_session():
    s = requests.Session()
    s.headers.update({"User-Agent": USER_AGENT})
    retries = Retry(total=4, backoff_factor=0.6, status_forcelist=[429, 500, 502, 503, 504])
    s.mount("http://", HTTPAdapter(max_retries=retries))
    s.mount("https://", HTTPAdapter(max_retries=retries))
    return s

# --- Utilities ---
def safe_name(s: str) -> str:
    s = re.sub(r"[^\w\-]+", "_", s)
    return s[:140]

def sha1(s: str) -> str:
    return hashlib.sha1(s.encode("utf-8", "ignore")).hexdigest()[:10]

def sleep_jitter():
    time.sleep(random.uniform(*SLEEP_BETWEEN_REQUESTS))

def is_pdf(resp: requests.Response, url: str) -> bool:
    ctype = resp.headers.get("Content-Type", "").lower()
    return ("application/pdf" in ctype) or url.lower().endswith(".pdf")

def same_domain(u1, u2):
    return urlparse(u1).netloc == urlparse(u2).netloc

class ChatMemory:
    def __init__(self, max_history: int = 10):
        self.history: List[Dict[str, str]] = []
        self.max_history = max_history
    
    def add(self, role: str, content: str) -> None:
        self.history.append({"role": role, "content": content})
        # keep last N messages
        if len(self.history) > self.max_history:
            # drop earliest
            self.history = self.history[-self.max_history:]
    
    def get_messages(self) -> List[Dict[str, str]]:
        return list(self.history)  # shallow copy
    
    def clear(self) -> None:
        self.history = []

def strip_reasoning_tags(text: str) -> str:
    """
    Some reasoning models (e.g., DeepSeek-R1) wrap internal thoughts in <think>...</think>.
    If you don't want to show that to users, strip it out safely.
    """
    # Remove any <think>...</think> blocks
    return re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL).strip()

# --- Extraction ---
def extract_html_text(html: str, base_url: str) -> str:
    extracted = trafilatura.extract(html, url=base_url, include_links=False, favor_precision=True)
    if extracted and extracted.strip():
        return extracted.strip()
    soup = BeautifulSoup(html, "html.parser")
    for s in soup(["script", "style", "noscript"]): s.extract()
    text = soup.get_text(separator="\n")
    return "\n".join([ln.strip() for ln in text.splitlines() if ln.strip()])

def extract_pdf_text(content: bytes) -> str:
    if pdf_extract_text is None:
        return ""  # pdfminer not available; skip PDF text
    from tempfile import NamedTemporaryFile
    from pdfminer.pdfparser import PDFSyntaxError
    with NamedTemporaryFile(suffix=".pdf", delete=False) as tmp:
        tmp.write(content)
        tmp_path = tmp.name
    try:
        txt = pdf_extract_text(tmp_path) or ""
        return txt.strip()
    except PDFSyntaxError as e:
        print(f"PDFSyntaxError for {tmp_path}: {e}")
        return ""
    finally:
        try:
            os.remove(tmp_path)
        except Exception:
            pass

# --- Fetch & parse one URL ---
def fetch_and_parse(session, url, out_dir, log, depth=0):
    try:
        sleep_jitter()
        resp = session.get(url, timeout=REQUEST_TIMEOUT)
    except Exception as e:
        log.warning(f"[fetch] {url} failed: {e}")
        return None

    if resp.status_code != 200:
        log.info(f"[fetch] {url} -> HTTP {resp.status_code}")
        return None

    page_id = f"{safe_name(urlparse(url).netloc)}_{sha1(url)}"
    page_dir = os.path.join(out_dir, page_id)
    os.makedirs(page_dir, exist_ok=True)

    raw_path = os.path.join(page_dir, "raw.bin")
    with open(raw_path, "wb") as f:
        f.write(resp.content)

    if is_pdf(resp, url):
        log.info(f"[parse] PDF {url}")
        text = extract_pdf_text(resp.content)
        kind = "pdf"
        html_for_code = ""
    else:
        log.info(f"[parse] HTML {url}")
        text = extract_html_text(resp.text, url)
        kind = "html"
        html_for_code = resp.text

    text_path = os.path.join(page_dir, "extracted.txt")
    with open(text_path, "w", encoding="utf-8") as f:
        f.write(text or "")

    # Pull code blocks (HTML <pre>/<code> + Markdown ```)
    code_blocks = []
    if html_for_code:
        soup = BeautifulSoup(html_for_code, "html.parser")
        for tag in soup.find_all(["code", "pre"]):
            t = tag.get_text("\n").strip()
            if t and len(t.split()) > 6:
                code_blocks.append(t[:16000])  # increased from 5000
    md_fenced = re.findall(r"```[a-zA-Z0-9_\-]*\n([\s\S]*?)```", text or "")
    for blk in md_fenced:
        b = blk.strip()
        if b:
            code_blocks.append(b[:5000])

    meta = {
        "url": url,
        "kind": kind,
        "depth": depth,
        "bytes": len(resp.content),
        "saved": {"raw": raw_path, "text": text_path},
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "code_blocks_found": len(code_blocks)
    }
    with open(os.path.join(page_dir, "meta.json"), "w", encoding="utf-8") as f:
        json.dump(meta, f, indent=2)

    codes_path = os.path.join(page_dir, "code_blocks.json")
    with open(codes_path, "w", encoding="utf-8") as f:
        json.dump(code_blocks, f, indent=2)

    # Collect in-domain links
    links = []
    if kind == "html":
        soup = BeautifulSoup(html_for_code, "html.parser")
        for a in soup.find_all("a", href=True):
            nxt = urljoin(url, a["href"])
            if nxt.startswith("http") and same_domain(nxt, url):
                links.append(nxt)
        links = list(dict.fromkeys(links))

    return {"id": page_id, "dir": page_dir, "url": url, "text": text, "links": links, "kind": kind, "code_blocks": code_blocks}

# --- SearXNG search ---
def searxng_search(session, query, categories=None, engines=None, size=20):
    params = {
        "q": query,
        "format": "json",
        "pageno": 1,
        "language": "en",
        "safesearch": 1
    }
    if categories: params["categories"] = ",".join(categories)
    if engines: params["engines"] = ",".join(engines)

    r = session.get(SEARXNG_URL, params=params, timeout=REQUEST_TIMEOUT)
    r.raise_for_status()
    data = r.json()
    results = data.get("results", [])
    return [
        {"title": it.get("title"), "url": it.get("url"), "engines": it.get("engines", [])}
        for it in results if it.get("url", "").startswith("http")
    ][:size]

# --- Cadence detection (fallback if LLM omits it) ---
def detect_rebalancing(text: str) -> str:
    t = (text or "").lower()
    if "daily" in t or "every day" in t:
        return "daily"
    if "weekly" in t or "every week" in t:
        return "weekly"
    if "biweekly" in t or "bi-weekly" in t:
        return "biweekly"
    if "monthly" in t or "every month" in t:
        return "monthly"
    if "quarterly" in t or "every quarter" in t or " q1" in t or " q2" in t or " q3" in t or " q4" in t:
        return "quarterly"
    if "annually" in t or "yearly" in t or "every year" in t:
        return "annual"
    if "semiannual" in t or "semi-annual" in t or "twice a year" in t:
        return "semiannual"
    idx = t.find("rebalance")
    if idx != -1:
        chunk = t[max(0, idx-80): idx+80]
        for word, label in [("daily","daily"),("weekly","weekly"),("monthly","monthly"),("quarterly","quarterly"),("annually","annual")]:
            if word in chunk:
                return label
    return ""

# --- LLM call ---

def ollama_generate(
    prompt: str,
    *,
    model: str = "deepseek-r1:14b",      # works for llama3.1 too
    chat_memory: Optional[ChatMemory] = None, # pass your ChatMemory instance
    system_prompt: Optional[str] = None, # optional system message
    stream: bool = False,
    temperature: float = 0.2,
    top_p: float = 0.9,
    num_ctx: int = 8192,
    strip_reasoning: bool = True,        # hide <think>…</think> from the final output
    timeout: int = 180,
) -> str:
    # Assemble messages with optional system prompt + prior turns + new user turn
    messages: List[Dict[str, str]] = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    if chat_memory:
        messages.extend(chat_memory.get_messages())
    messages.append({"role": "user", "content": prompt})

    payload = {
        "model": model,
        "messages": messages,
        "stream": stream,
        "options": {
            "temperature": temperature,
            "top_p": top_p,
            "num_ctx": num_ctx,
        },
    }

    r = requests.post(OLLAMA_URL, json=payload, timeout=timeout)
    r.raise_for_status()

    if stream:
        chunks: List[str] = []
        for line in r.iter_lines():
            if not line:
                continue
            obj = json.loads(line.decode("utf-8"))
            # streamed shape: {"message": {"role": "assistant", "content": "..."}, "done": false}
            if "message" in obj and "content" in obj["message"]:
                piece = obj["message"]["content"]
                chunks.append(piece)
                # (optional: print(piece, end="", flush=True))  # live stream
        response_text = "".join(chunks)
    else:
        data = r.json()
        # non-stream shape: {"message": {"role":"assistant","content":"..."}, ...}
        response_text = data.get("message", {}).get("content", "")

    if strip_reasoning:
        response_text = strip_reasoning_tags(response_text)

    # Update memory automatically
    if chat_memory:
        chat_memory.add("user", prompt)
        chat_memory.add("assistant", response_text)

    return response_text


# --- LLM strategy extraction (STRICT JSON) ---
def process_text_in_chunks(text: str, chunk_size: int, model: str, process_fn) -> str:
    """
    Process long text by breaking it into overlapping chunks and combining results.
    """
    if len(text) <= chunk_size:
        return process_fn(text, model)
        
    chunks = []
    overlap = chunk_size // 10  # 10% overlap between chunks
    start = 0
    
    while start < len(text):
        end = start + chunk_size
        chunk = text[start:end]
        result = process_fn(chunk, model)
        if result:
            chunks.append(result)
        start = end - overlap
    
    return " ".join(chunks)

def llm_strategy_json(page_text: str, source_url: str, code_blocks: list, model=OLLAMA_MODEL, chat_memory: ChatMemory = None) -> dict:
    """
    2-pass, grounded extraction:
      Pass 1: Extract per chunk with evidence and unknowns (NO guessing).
      Merge:   Keep most detailed fields; union signals; accumulate evidence.
      Pass 2:  Fill gaps by explicitly asking for missing items using only provided text/code.
      Validate: Enforce schema + final fallback to detect_rebalancing.
    """
    def ask_chunk(text_chunk: str) -> Optional[dict]:
        prompt = (
            f"{GLOBAL_CONTEXT}\n\n"
            f"SOURCE URL: {source_url}\n\n"
            "Read the page text (and any code) and extract the TRADING STRATEGY as STRICT JSON.\n"
            f"{LLM_SCHEMA_PROMPT}\n\n"
            "PAGE TEXT START\n"
            f"{text_chunk}\n"
            "PAGE TEXT END\n\n"
        )
        if code_blocks:
            prompt += "CODE SNIPPETS (may be partial):\n" + "\n\n".join(code_blocks[:5]) + "\n\n"
        prompt += "Return ONLY the JSON. No prose."
        raw = ollama_generate(prompt, model=model, stream=False, chat_memory=chat_memory)
        obj = extract_first_json(raw)
        if not obj:
            obj = repair_json_with_llm(raw, model)
        return enforce_schema(obj) if obj else None

    # --- Pass 1: chunk + merge
    merged = {
        "title": "Unknown Strategy",
        "source_url": source_url,
        "thesis": "",
        "universe": "",
        "rebalancing": "unknown",
        "signals": [],
        "backtest_spec": {},
        "evidence": [],
        "unknowns": []
    }
    signals_seen = set()

    for i in range(0, len(page_text), 28000):
        chunk = page_text[i:i+28000]
        res = ask_chunk(chunk)
        if not res:
            continue

        # prefer longer/more specific strings
        if len(res.get("title","")) > len(merged.get("title","")): merged["title"] = res["title"]
        if len(res.get("thesis","")) > len(merged.get("thesis","")): merged["thesis"] = res["thesis"]
        if len(res.get("universe","")) > len(merged.get("universe","")): merged["universe"] = res["universe"]

        # rebalancing: prefer non-unknown
        rb = res.get("rebalancing","unknown")
        if rb != "unknown": merged["rebalancing"] = rb

        # merge backtest_spec field-by-field
        merged.setdefault("backtest_spec", {})
        for k,v in (res.get("backtest_spec") or {}).items():
            if merged["backtest_spec"].get(k,"") in ["","unknown"] and v not in ["", "unknown"]:
                merged["backtest_spec"][k] = v

        # merge signals (by name + definition fingerprint)
        for s in res.get("signals", []) or []:
            key = (s.get("name","").strip().lower(), s.get("definition","").strip().lower())
            if key in signals_seen:
                continue
            signals_seen.add(key)
            merged["signals"].append(s)

        # accumulate evidence & unknowns
        merged["evidence"].extend(res.get("evidence", []) or [])
        merged["unknowns"].extend(res.get("unknowns", []) or [])

    # --- Pass 2: If anything critical is unknown, ask a targeted fill-in (still grounded)
    needed = []
    if merged["rebalancing"] == "unknown": needed.append("rebalancing")
    if not merged["universe"]: needed.append("universe")
    if not merged["signals"]: needed.append("signals")
    # also check backtest fields commonly missing
    for k in ["formation_period","holding_period","long_short","weighting"]:
        if (merged.get("backtest_spec", {}).get(k, "unknown") == "unknown"):
            needed.append(k)

    if needed:
        prompt = (
            f"From ONLY the provided text/code below, try to fill these items if truly present "
            f"(otherwise reply 'unknown' for each): {sorted(set(needed))}.\n"
            f"Return STRICT JSON with ONLY these keys and values (no explanations).\n\n"
            "PAGE TEXT START\n"
            f"{page_text[:52000]}\n"
            "PAGE TEXT END\n\n"
        )
        if code_blocks:
            prompt += "CODE SNIPPETS:\n" + "\n\n".join(code_blocks[:5]) + "\n\n"
        raw = ollama_generate(prompt, model=model, stream=False, chat_memory=chat_memory)
        patch = extract_first_json(raw) or repair_json_with_llm(raw, model)
        if isinstance(patch, dict):
            # fill what we asked for
            if "rebalancing" in patch and patch["rebalancing"] != "unknown":
                merged["rebalancing"] = patch["rebalancing"]
            if "universe" in patch and patch["universe"]:
                merged["universe"] = patch["universe"]
            if "signals" in patch and isinstance(patch["signals"], list) and patch["signals"]:
                for s in patch["signals"]:
                    key = (s.get("name","").strip().lower(), s.get("definition","").strip().lower())
                    if key not in signals_seen:
                        signals_seen.add(key); merged["signals"].append(s)
            for k in ["formation_period","holding_period","long_short","weighting"]:
                v = patch.get(k)
                if v and v != "unknown":
                    merged.setdefault("backtest_spec", {})[k] = v

    # Final sanitation + last-resort rebalancing detection
    merged = enforce_schema(merged)
    if merged.get("rebalancing","unknown") == "unknown":
        guess = detect_rebalancing(page_text)
        if guess: merged["rebalancing"] = guess

    # De-duplicate evidence/unknowns
    dedup = {(e.get("quote",""), e.get("char_start",-1), e.get("char_end",-1)) for e in merged["evidence"]}
    merged["evidence"] = [{"quote": q, "char_start": s, "char_end": e} for (q,s,e) in dedup if q]
    merged["unknowns"] = sorted(set(merged["unknowns"]))

    return merged


# --- Minimal researcher-style Markdown (only what you asked for) ---
def render_strategy_md_min(s: dict) -> str:
    title = s.get("title", "Unknown Strategy")
    url = s.get("source_url", "")
    thesis = s.get("thesis", "")
    universe = s.get("universe", "")
    rebal = s.get("rebalancing", s.get("frequency", "unknown"))
    signals = s.get("signals", []) or []

    lines = []
    lines.append(f"# {title}\n")
    if url:
        lines.append(f"**Source:** {url}\n")

    if thesis:
        lines.append("\n## Thesis\n")
        lines.append(thesis.strip())

    lines.append("\n## Universe & Rebalancing\n")
    uline = universe.strip() if universe else "(unspecified)"
    rline = rebal.strip() if isinstance(rebal, str) else ", ".join(rebal)
    lines.append(f"**Universe:** {uline}")
    lines.append(f"**Rebalancing:** {rline if rline else '(unspecified)'}\n")

    lines.append("\n## Signals\n")
    if signals:
        for sig in signals:
            nm = sig.get("name", "signal")
            df = sig.get("definition", "(definition missing)")
            fx = sig.get("formula_latex", "unknown")
            ps = sig.get("pseudo", "unknown")
            params = sig.get("parameters", {})
            lookback = params.get("lookback","unknown")
            smoothing = params.get("smoothing","unknown")
            thresholds = params.get("thresholds","unknown")
            req = sig.get("required_fields", [])
            lines.append(f"- **{nm}:** {df}")
            if fx and fx != "unknown":
                lines.append(f"  - *Formula:* ${fx}$")
            if ps and ps != "unknown":
                lines.append(f"  - *Pseudo:* {ps}")
            lines.append(f"  - *Params:* lookback={lookback}; smoothing={smoothing}; thresholds={thresholds}")
            if req:
                lines.append(f"  - *Required fields:* {', '.join(req)}")
    else:
        lines.append("- _No explicit signals found._")

    return "\n".join(lines) + "\n"


# --- Research loop ---
def research_loop(seed_queries, run_tag=None, model=OLLAMA_MODEL, competition_goal="find the optimal portfolio optimization strategy with explicit rebalance cadence"):
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_name = f"{ts}_{run_tag or 'comp_runs'}"
    run_dir = os.path.join(OUT_ROOT, run_name)
    os.makedirs(run_dir, exist_ok=True)
    log = setup_logging(run_dir)
    sess = make_session()

    log.info(f"Run dir: {run_dir}")
    log.info("Starting competition-focused research loop (SearXNG -> fetch -> parse -> LLM strategy JSON)")

    visited = set()
    queue_items = queue.Queue()

    for q in seed_queries:
        queue_items.put(("search", 0, {"query": q}))

    pages_processed = 0
    strategies = []

    # Initialize chat memory
    chat_memory = ChatMemory(max_history=5)
    chat_memory.add("system", GLOBAL_CONTEXT)
    chat_memory.add("system", f"Competition Goal: {competition_goal}")

    while not queue_items.empty():
        task, depth, payload = queue_items.get()

        if task == "search":
            q = payload["query"]
            log.info(f"[search] {q}")
            results = searxng_search(sess, q, categories=["general", "science"])
            with open(os.path.join(run_dir, f"search_{safe_name(q)}.json"), "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2)
            for res in results[:MAX_PAGES_PER_QUERY]:
                url = res["url"]
                if url not in visited:
                    queue_items.put(("visit", 0, {"url": url}))

        elif task == "visit":
            url = payload["url"]
            if url in visited:
                continue
            visited.add(url)
            out = fetch_and_parse(sess, url, run_dir, log, depth=depth)
            if not out:
                continue
            pages_processed += 1

            if out["text"]:
                # Check if the page is relevant to the competition goal.
                # You may obtain the competition goal dynamically from a PDF instead
                if not check_relevance(out["text"], competition_goal, model=model, chat_memory=chat_memory):
                    log.info(f"[skip] {url} deemed not relevant to competition.")
                    shutil.rmtree(out["dir"])
                    continue

                strat = llm_strategy_json(out["text"], url, out.get("code_blocks", []), model=model, chat_memory=chat_memory)
                # Save JSON
                strat_path = os.path.join(out["dir"], "strategy.json")
                with open(strat_path, "w", encoding="utf-8") as f:
                    json.dump(strat, f, indent=2)
                # Save minimal MD
                md = render_strategy_md_min(strat)
                md_path = os.path.join(out["dir"], "strategy.md")
                with open(md_path, "w", encoding="utf-8") as f:
                    f.write(md)
                strategies.append(strat)

            # Dynamic crawl decision: use LLM to decide if we should follow links
            if should_follow_links(out["text"], competition_goal, model=model):
                for nxt in out["links"][:10]:
                    if nxt not in visited:
                        queue_items.put(("visit", depth + 1, {"url": nxt}))

            if pages_processed % 50 == 0:
                log.info(f"[progress] processed {pages_processed} pages so far.")

    # Rollup
    summary_path = os.path.join(run_dir, "all_strategies.json")
    with open(summary_path, "w", encoding="utf-8") as f:
        json.dump(strategies, f, indent=2)

    log.info(f"Done. Pages processed: {pages_processed}. Strategies saved: {len(strategies)}. Artifacts in {run_dir}")

def generate_competition_seed_queries(goal: str, model=OLLAMA_MODEL) -> list:
    import json, re
    prompt = (
        f"Given the following competition goal: '{goal}', "
        "generate a list of effective search queries to find optimal portfolio strategies for algorithmic competitions. "
        "Return ONLY a JSON array of query strings with absolutely no additional text, think blocks, or commentary."
    )
    response = ollama_generate(prompt, model=model, stream=False)
    
    # Extract only the JSON array, ignoring any think blocks or other text
    try:
        m = re.search(r'\[.*?\]', response, re.DOTALL)
        if m:
            queries = json.loads(m.group(0))
            if isinstance(queries, list):
                print(f"[INFO] Generated queries: {queries}")
                return queries
    except Exception as e:
        print(f"[ERROR] Failed to parse queries: {e}")
        print(f"[DEBUG] Raw response: {response}")
    
    return []

# --- Updated default_competition_seed_queries ---
def default_competition_seed_queries(goal=None, model=OLLAMA_MODEL):
    import os
    print("[INFO] Starting default_competition_seed_queries")
    if not goal:
        pdf_goal_path = os.path.join(os.getcwd(), "McGill-FIAM Asset Management Hackathon Instructions.pdf")
        print(f"[INFO] Attempting to extract goal from PDF: {pdf_goal_path}")
        goal = find_competition_goal_from_pdf(pdf_goal_path, model=model)
        if goal:
            print(f"[INFO] Extracted competition goal: {goal}")
        else:
            print("[WARN] Failed to extract goal from PDF, using fallback goal")
            goal = "find the optimal portfolio optimization strategy with explicit rebalance cadence"
    else:
        print(f"[INFO] Using provided competition goal: {goal}")
    queries = generate_competition_seed_queries(goal, model=model)
    print(f"[INFO] Seed queries generated: {queries}")
    return queries, goal

# --- Updated find_competition_goal_from_pdf ---
def find_competition_goal_from_pdf(pdf_path: str, model=OLLAMA_MODEL) -> str:
    import os
    print(f"[INFO] Entered find_competition_goal_from_pdf with file: {pdf_path}")
    if not os.path.exists(pdf_path):
        print(f"[ERROR] PDF not found: {pdf_path}")
        return ""
    with open(pdf_path, "rb") as f:
        content = f.read()
    text = extract_pdf_text(content)
    print(f"[INFO] Extracted PDF text length: {len(text)}")
    if not text:
        print(f"[WARN] No text extracted from PDF: {pdf_path}")
        return ""
        
    if len(text) > 4000:
        print("[INFO] PDF text too long, summarizing in chunks")
        goal = summarize_pdf_goal(text, model=model)
        print(f"[INFO] Summarized competition goal: {goal}")
        return goal
    else:
        prompt = (
            "Extract the main competition goal from the following full text of an asset management hackathon PDF. "
            "Return ONLY the goal as a concise sentence with no additional commentary.\n\n"
            f"{text}"
        )
        goal = ollama_generate(prompt, model=model, stream=False)
        print(f"[INFO] LLM returned competition goal: {goal}")
        return goal.strip()

# --- Updated summarize_pdf_goal ---
def summarize_pdf_goal(text: str, chunk_size: int = 16000, model=OLLAMA_MODEL) -> str:
    print("[INFO] Starting summarize_pdf_goal")
    # Split text into chunks
    chunks = [text[i:i+chunk_size] for i in range(0, len(text), chunk_size)]
    print(f"[INFO] PDF text split into {len(chunks)} chunks")
    
    # Just collect relevant text from each chunk without interpretation
    partial_texts = []
    for idx, chunk in enumerate(chunks):
        prompt = (
            "From the following text chunk, extract any text related to competition instructions, "
            "goals, or requirements. Return ONLY the relevant text, no interpretation.\n\n"
            f"{chunk}"
        )
        result = ollama_generate(prompt, model=model)
        cleaned = result.strip()
        if cleaned:
            partial_texts.append(cleaned)
            print(f"[DEBUG] Chunk {idx+1}/{len(chunks)} extracted text length: {len(cleaned)}")
    
    # Combine all extracted texts and then find the competition goal
    combined_text = " ".join(partial_texts)
    final_prompt = (
        "Based on the following combined text from a hackathon PDF, "
        "what is the main competition goal? Return ONLY a single concise sentence "
        "describing the core goal.\n\n"
        f"{combined_text}"
    )
    final_goal = ollama_generate(final_prompt, model=model, stream=False)
    print(f"[INFO] Final competition goal: '{final_goal.strip()}'")
    return final_goal.strip()
    
def check_relevance(page_text: str, competition_goal: str, model=OLLAMA_MODEL, chat_memory=None) -> bool:
    CHUNK = 16000
    if chat_memory:
        chat_memory.clear()
    for i in range(0, len(page_text), CHUNK):
        chunk = page_text[i:i+CHUNK]
        prompt = (
            "Answer strictly 'yes' or 'no'. The page is relevant if it contains a concrete portfolio/trading "
            "strategy, features/signals, model training/inference for equities, or explicit backtest details.\n\n"
            f"Competition Goal: {competition_goal}\n\n"
            f"PAGE SNIPPET:\n{chunk}\n"
        )
        ans = ollama_generate(prompt, model=model, stream=False, chat_memory=None).strip().lower()
        if ans.startswith("yes"):
            return True
    return False

def should_follow_links(page_text: str, competition_goal: str, model=OLLAMA_MODEL) -> bool:
    """
    Ask the LLM whether it makes sense to follow the links on this page
    based on its content and the competition goal.
    Returns True if yes, False otherwise.
    """
    prompt = (
        "Based on the following page snippet and the competition goal, "
        "should we follow the links found on this page to potentially discover more relevant content? "
        "Answer only 'yes' or 'no'.\n\n"
        f"Competition Goal: {competition_goal}\n\n"
        f"Page snippet: {page_text[:8000]}\n\n"
    )
    answer = ollama_generate(prompt, model=model, stream=False)
    return answer.strip().lower().startswith("yes")

if __name__ == "__main__":
    OLLAMA_MODEL = "qwen2.5:14b-instruct"
    # seeds, goal = default_competition_seed_queries(model=OLLAMA_MODEL)
    seeds = [
        'site:kaggle.com ("1st place" OR "gold medal" OR "winning solution") (portfolio OR stock OR equities OR alpha OR factor) (write-up OR notebook OR code)',
        'site:kaggle.com/competitions ("stock" OR "equities" OR "portfolio") (solution OR strategy) (discussion OR notebook)',
        'site:kaggle.com ("top solution" OR "winning approach") (time series OR forecasting) (feature engineering OR signals)',
        'site:github.com ("kaggle" OR "competition") ("winning solution" OR "1st place" OR "gold") (finance OR stock OR portfolio OR alpha)',
        'site:github.com ("long-short" OR "factor model" OR "alpha signals") (backtest OR backtesting) (Sharpe OR drawdown)',
        'site:medium.com OR site:substack.com ("kaggle" OR "quant competition") ("winning solution" OR "postmortem" OR "write-up") (stock OR portfolio)',
        'site:ssrn.com OR site:arxiv.org (hackathon OR competition) (portfolio OR stock OR alpha) ("winning" OR "top") (code OR github)',
        'site:cs.mit.edu OR site:harvard.edu OR site:cmu.edu OR site:stanford.edu ("quant" OR "finance") (hackathon OR competition) (solution OR strategy OR report)',
        '("student quant competition" OR "algorithmic trading competition") ("winning solution" OR "champion") (strategy OR code OR github)',
        'site:kaggle.com ("wavelet" OR "EMD" OR "SSA" OR "XGBoost" OR "LightGBM") (stocks OR portfolio) (notebook OR code)',
        'site:github.com ("mean-variance" OR "risk parity" OR "Black-Litterman") (implementation OR backtest) (stocks OR equities)',
        'site:kaggle.com ("feature importance" OR "shap values") (stocks OR portfolio) (write-up OR notebook)',
        'site:github.com ("earnings surprise" OR "eps surprise") (prediction OR alpha) (backtest OR pipeline)',
        'site:kaggle.com ("monthly rebalancing" OR "weekly rebalancing" OR "daily rebalancing") (portfolio OR long-short) (notebook OR code)',
        'site:github.com ("momentum" OR "value" OR "quality" OR "low volatility") (cross-sectional OR factor) (backtest)',
        'site:kaggle.com ("pair trading" OR "statistical arbitrage") (solution OR notebook OR discussion)',
        'site:github.com ("options" AND stocks) (predictability OR implied volatility) (alpha OR strategy) (backtest)',
        'site:paperswithcode.com (stock OR portfolio) (backtesting OR strategy) (code available)'
    ]
    goal = (
        "Find optimal coding strategies for portfolio management that leverage machine learning and alternative data."
    )
    research_loop(seeds, run_tag="comp_strat_run", model=OLLAMA_MODEL, competition_goal=goal)