

<!DOCTYPE html>
<html lang="en">

<head>
  <title>&#x1F916; Trading with Machine Learning: Classification | Kaggle</title>
  <meta charset="utf-8" />
    <meta name="robots" content="index, follow" />
  <meta name="description" content="Explore and run machine learning code with Kaggle Notebooks | Using data from No attached data sources" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, minimum-scale=1.0">
  <meta name="theme-color" content="#008ABC" />
  <script nonce="Zn/uCgv3TTH6nNl7yedD0A==" type="text/javascript">
    window["pageRequestStartTime"] = *************;
    window["pageRequestEndTime"] = *************;
    window["initialPageLoadStartTime"] = new Date().getTime();
  </script>
  <script nonce="Zn/uCgv3TTH6nNl7yedD0A==" id="gsi-client" src="https://accounts.google.com/gsi/client" async defer></script>
  <script nonce="Zn/uCgv3TTH6nNl7yedD0A==">window.KAGGLE_JUPYTERLAB_PATH = "/static/assets/jupyterlab-v4/jupyterlab-index-da3e27246649f6384bc4.html";</script>
  <link rel="preconnect" href="https://www.google-analytics.com" crossorigin="anonymous" /><link rel="preconnect" href="https://stats.g.doubleclick.net" /><link rel="preconnect" href="https://storage.googleapis.com" /><link rel="preconnect" href="https://apis.google.com" />
    <link href="/static/images/favicon.ico" rel="shortcut icon" type="image/x-icon" id="dynamic-favicon" />
  <link rel="manifest" href="/static/json/manifest.json" crossorigin="use-credentials">


  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

  <link href="https://fonts.googleapis.com/css?family=Inter:400,400i,500,500i,600,600i,700,700i&display=swap"
    rel="preload" as="style" />
  <link href="https://fonts.googleapis.com/css2?family=Google+Symbols:FILL@0..1&display=block"
    rel="preload" as="style" />
  <link href="https://fonts.googleapis.com/css?family=Inter:400,400i,500,500i,600,600i,700,700i&display=swap"
    rel="stylesheet" media="print" id="async-google-font-1" />
  <link href="https://fonts.googleapis.com/css2?family=Google+Symbols:FILL@0..1&display=block"
    rel="stylesheet" media="print" id="async-google-font-2" />
  <script nonce="Zn/uCgv3TTH6nNl7yedD0A==" type="text/javascript">
    const styleSheetIds = ["async-google-font-1", "async-google-font-2"];
    styleSheetIds.forEach(function (id) {
      document.getElementById(id).addEventListener("load", function() {
        this.media = "all";
      });
    });
  </script>


    <link rel="canonical" href="/code/lusfernandotorres/trading-with-machine-learning-classification" />
    <link rel="stylesheet" type="text/css" href="/static/assets/app.css?v=2d4e7ec4eb689d926191" />

  
    
 
      <script nonce="Zn/uCgv3TTH6nNl7yedD0A==">
        try{(function(a,s,y,n,c,h,i,d,e){d=s.createElement("style");
        d.appendChild(s.createTextNode(""));s.head.appendChild(d);d=d.sheet;
        y=y.map(x => d.insertRule(x + "{ opacity: 0 !important }"));
        h.start=1*new Date;h.end=i=function(){y.forEach(x => x<d.cssRules.length ? d.deleteRule(x) : {})};
        (a[n]=a[n]||[]).hide=h;setTimeout(function(){i();h.end=null},c);h.timeout=c;
        })(window,document,['.site-header-react__nav'],'dataLayer',2000,{'GTM-52LNT9S':true});}catch(ex){}
    </script>
    <script nonce="Zn/uCgv3TTH6nNl7yedD0A==">
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', 'G-T7QHS60L4Q', {
            'optimize_id': 'GTM-52LNT9S',
            'displayFeaturesTask': null,
            'send_page_view': false,
            'content_group1': 'Notebooks'
        });
    </script>
    <script nonce="Zn/uCgv3TTH6nNl7yedD0A==" async src="https://www.googletagmanager.com/gtag/js?id=G-T7QHS60L4Q"></script>

  
    
    <meta name="og:url" content="https://kaggle.com/code/lusfernandotorres/trading-with-machine-learning-classification" />
    <meta property="og:title" content="&#x1F916; Trading with Machine Learning: Classification" />
    <meta property="og:description" content="Explore and run machine learning code with Kaggle Notebooks | Using data from No attached data sources" />
    <meta property="og:type" content="website" />
    <meta name="og:image" content="https://storage.googleapis.com/kaggle-avatars/thumbnails/8860864-kg.png?t=2023-10-19-14-24-21" />

    <meta name="twitter:card" content="summary" />
    <meta name="twitter:site" content="@kaggledatasets" />


  <meta name="twitter:site" content="@Kaggle" /> 
  
    
    <script nonce="Zn/uCgv3TTH6nNl7yedD0A==" type="application/ld+json">{"@context":"http://schema.org/","@type":"Article","author":{"@type":"Person","name":"lusfernandotorres","sameAs":"/lusfernandotorres"},"headline":"🤖 Trading with Machine Learning: Classification","url":"https://www.kaggle.com/code/lusfernandotorres/trading-with-machine-learning-classification","dateModified":"2023-06-04T17:49:45.48Z","datePublished":"2023-06-04T17:49:45.48Z","mainEntityOfPage":{"@type":"WebPage","@id":"https://www.kaggle.com/code/lusfernandotorres/trading-with-machine-learning-classification"},"publisher":{"@type":"Organization","name":"Kaggle","sameAs":"https://www.kaggle.com","logo":{"type":"ImageObject","url":"https://www.kaggle.com/static/images/site-logo.png"}},"image":["https://storage.googleapis.com/kaggle-avatars/thumbnails/8860864-kg.png?t=2023-10-19-14-24-21"],"description":""}</script>


  
    
        <link rel="alternate" type="application/json+oembed"
          href="https://www.kaggle.com/oembed/kernel?url=https%3A%2F%2Fwww.kaggle.com%2Fcode%2Flusfernandotorres%2Ftrading-with-machine-learning-classification%3FscriptVersionId%3D132280564"
          title="&#x1F916; Trading with Machine Learning: Classification" />


  
    


    <script nonce="Zn/uCgv3TTH6nNl7yedD0A==">window['useKaggleAnalytics'] = true;</script>

  <script id="gapi-target" nonce="Zn/uCgv3TTH6nNl7yedD0A==" src="https://apis.google.com/js/api.js" defer
    async></script>
  <script nonce="Zn/uCgv3TTH6nNl7yedD0A==" src="/static/assets/runtime.js?v=97eb874934c17ed518f0"></script>
  <script nonce="Zn/uCgv3TTH6nNl7yedD0A==" src="/static/assets/vendor.js?v=93540707f793d66d045a"></script>
  <script nonce="Zn/uCgv3TTH6nNl7yedD0A==" src="/static/assets/app.js?v=02cd0871599dce515974"></script>
    <script nonce="Zn/uCgv3TTH6nNl7yedD0A==" type="text/javascript">
      window.kaggleStackdriverConfig = {
        key: 'AIzaSyA4eNqUdRRskJsCZWVz-qL655Xa5JEMreE',
        projectId: 'kaggle-161607',
        service: 'web-fe',
        version: 'ci',
        userId: '0'
      }
    </script>
</head>
<body>
  <div id="root">
    










<script nonce="Zn/uCgv3TTH6nNl7yedD0A==" type="text/x-mathjax-config">
    MathJax.Hub.Config({
    jax: ['input/TeX', 'output/SVG'],
    "HTML-CSS": {
    preferredFont: "TeX",
    availableFonts: ["STIX", "TeX"],
    linebreaks: {
    automatic: true
    },
    EqnChunk: (MathJax.Hub.Browser.isMobile ? 10 : 50)
    },
    tex2jax: {
    inlineMath: [["\\(", "\\)"], ["\\\\(", "\\\\)"]],
    displayMath: [["$$", "$$"], ["\\[", "\\]"]],
    processEscapes: true,
    ignoreClass: "tex2jax_ignore|dno"
    },
    TeX: {
    noUndefined: {
    attributes: {
    mathcolor: "red",
    mathbackground: "#FFEEEE",
    mathsize: "90%"
    }
    }
    },
    Macros: {
    href: "{}"
    },
    skipStartupTypeset: true,
    messageStyle: "none",
    extensions: ["Safe.js"],
    });
</script>
<script type="text/javascript" nonce="Zn/uCgv3TTH6nNl7yedD0A==">
  window.addEventListener("DOMContentLoaded", () => {
    const head = document.getElementsByTagName("head")[0];
    const lib = document.createElement("script");
    lib.type = "text/javascript";
    // Always use the production asset in local dev, which is served from GCS. We tried to proxy and / or serve this
    // in a better way in localhost, but it didn't work out. See b/328073416#comment8 for details.
    const forceProdHost = window.location.hostname === "localhost";
    lib.src = `${forceProdHost ? "https://www.kaggle.com" : ""}/static/mathjax/2.7.9/MathJax.js?config=TeX-AMS_SVG`;
    head.appendChild(lib);
  });
</script>





  </div>
</body>
</html>
