# Sales Prediction and Sustainable Sales Recommendations

**Source:** https://yashkhandelwal07.medium.com/data-science-hackathons-winners-guide-part-1-fecbf3b2c023


## Thesis

The strategy involves predicting sales of a mega mart using machine learning models, specifically focusing on regression tasks. The approach includes meticulous data preparation, feature engineering, and ensemble modeling techniques to enhance predictive accuracy.

## Universe & Rebalancing

**Universe:** Sales data from a mega mart with 87,864 training observations and 37,656 test observations
**Rebalancing:** Not applicable for this dataset as it is a static prediction task


## Signals

- **Outlier Detection:** Applying the Interquartile Range (IQR) method to identify and handle outliers by capping extreme values within acceptable range.
- **Null Value Imputation:** Checking for null values in the dataset; no imputation needed as none were found.
- **Categorical Encoding:** Using one-hot encoding for categorical variables with no inherent order and ordinal encoding for those with a clear order.
- **Feature Engineering:** Creating new features that enhance model performance, such as calculating years from present based on outlet year.
- **Permutation Feature Importance:** Measuring the impact of each feature by randomly shuffling values and observing changes in model accuracy.
