# Create a small, ready-to-run helper script that merges your portfolio returns with `mkt_ind.csv`
# and computes CAPM alpha, t-stat, and information ratio. It works with the outputs produced by
# mcgill_monthly_alpha_pipeline.py (portfolio_monthly.csv), but it also accepts any CSV with
# columns [year, month, port_ret].

path = "/mnt/data/alpha_merge_and_capm.py"

import os, sys, json
import numpy as np
import pandas as pd

def main(port_path, mkt_path, out_json_path='./alpha_results.json'):
    # Load portfolio (expects columns: year, month, port_ret)
    port = pd.read_csv(port_path)
    need_port = {'year','month','port_ret'}
    if not need_port.issubset(set(port.columns)):
        raise ValueError(f'Portfolio file must contain columns {need_port}, got {list(port.columns)}')
    # Load market factors (expects columns: year, month, mkt_rf)
    mkt = pd.read_csv(mkt_path)
    need_mkt = {'year','month','mkt_rf'}
    if not need_mkt.issubset(set(mkt.columns)):
        raise ValueError(f'Market file must contain columns {need_mkt}, got {list(mkt.columns)}')
    # Merge
    df = pd.merge(port[['year','month','port_ret']], mkt[['year','month','mkt_rf']],
                  on=['year','month'], how='inner').dropna()
    if df.empty:
        raise RuntimeError('No overlapping months after merge. Check year/month alignment.')
    # OLS with White/HAC fallback
    try:
        import statsmodels.api as sm
        X = sm.add_constant(df['mkt_rf'].values, has_constant='add')
        y = df['port_ret'].values
        model = sm.OLS(y, X).fit(cov_type='HAC', cov_kwds={'maxlags':3})
        alpha_monthly = float(model.params[0])
        alpha_ann = alpha_monthly * 12.0
        t_alpha = float(model.tvalues[0])
        mse = float(model.mse_resid)
        info_ratio_ann = (alpha_monthly / np.sqrt(mse)) * np.sqrt(12.0)
        beta = float(model.params[1])
    except Exception:
        # Basic OLS fallback
        X = np.c_[np.ones(len(df)), df['mkt_rf'].values]
        y = df['port_ret'].values
        beta_hat, *_ = np.linalg.lstsq(X, y, rcond=None)
        resid = y - X @ beta_hat
        s2 = (resid**2).sum() / (len(y) - X.shape[1])
        cov_beta = s2 * np.linalg.inv(X.T @ X)
        se_alpha = np.sqrt(cov_beta[0,0])
        alpha_monthly = float(beta_hat[0])
        alpha_ann = alpha_monthly * 12.0
        t_alpha = float(alpha_monthly / (se_alpha + 1e-12))
        info_ratio_ann = None
        beta = float(beta_hat[1])

    out = dict(
        alpha_monthly=alpha_monthly,
        alpha_ann=alpha_ann,
        alpha_tstat=t_alpha,
        beta=beta,
        info_ratio_ann=info_ratio_ann,
        n_obs=int(len(df))
    )
    with open(out_json_path, 'w') as f:
        json.dump(out, f, indent=2)
    print(json.dumps(out, indent=2))

if __name__ == '__main__':
    # Usage:
    # python alpha_merge_and_capm.py /path/to/portfolio_monthly.csv /path/to/mkt_ind.csv /path/to/alpha_results.json
    port_path = sys.argv[1] if len(sys.argv) > 1 else './pred/portfolio_monthly.csv'
    mkt_path = sys.argv[2] if len(sys.argv) > 2 else './asset/mkt_ind.csv'
    out_json = sys.argv[3] if len(sys.argv) > 3 else './pred/metrics.json'
    main(port_path, mkt_path, out_json)

