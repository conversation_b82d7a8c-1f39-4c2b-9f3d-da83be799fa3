{"title": "CAD: Clustering And Deep Reinforcement Learning Based Multi-Period Portfolio Management Strategy", "source_url": "http://arxiv.org/abs/2310.01319v1", "thesis": "The paper presents a novel trading strategy that integrates reinforcement learning methods with clustering techniques for portfolio management in multi-period trading. The approach clusters stocks based on financial indices and uses reinforcement learning algorithms like Asynchronous Advantage Actor-Critic (A3C) and Deep Deterministic Policy Gradient (DDPG) to determine trading actions and generate portfolio weights, respectively. This method is evaluated through back-tests and demonstrates superior performance compared to traditional strategies.", "universe": "800 stocks from the Shanghai Stock Exchange and National Association of Securities Deal Automated Quotations (NASDAQ)", "rebalancing": "weekly", "signals": [{"name": "Clustering Method", "definition": "Categorizes stocks into various clusters based on their financial indices."}, {"name": "Asynchronous Advantage Actor-Critic (A3C) Algorithm", "definition": "Determines the trading actions for stocks within each cluster by learning optimal policies through reinforcement learning."}, {"name": "Deep Deterministic Policy Gradient (DDPG) Algorithm", "definition": "Generates the portfolio weight vector, deciding the amount of stocks to buy, sell, or hold based on the trading actions from different clusters."}]}