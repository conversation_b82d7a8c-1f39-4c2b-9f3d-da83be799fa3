<!doctype html><html lang="en"><head><title data-rh="true">Decoding <PERSON><PERSON>’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈 | by <PERSON> | Medium</title><meta data-rh="true" charset="utf-8"/><meta data-rh="true" name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1,maximum-scale=1"/><meta data-rh="true" name="theme-color" content="#000000"/><meta data-rh="true" name="twitter:app:name:iphone" content="Medium"/><meta data-rh="true" name="twitter:app:id:iphone" content="828256236"/><meta data-rh="true" property="al:ios:app_name" content="Medium"/><meta data-rh="true" property="al:ios:app_store_id" content="828256236"/><meta data-rh="true" property="al:android:package" content="com.medium.reader"/><meta data-rh="true" property="fb:app_id" content="542599432471018"/><meta data-rh="true" property="og:site_name" content="Medium"/><meta data-rh="true" name="apple-itunes-app" content="app-id=828256236, app-argument=/@vince-lam/decoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d, affiliate-data=pt=698524&amp;ct=smart_app_banner&amp;mt=8"/><meta data-rh="true" property="og:type" content="article"/><meta data-rh="true" property="article:published_time" content="2023-10-30T11:46:16.482Z"/><meta data-rh="true" name="title" content="Decoding Kaggle’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈 | by Vince Lam | Medium"/><meta data-rh="true" property="og:title" content="Decoding Kaggle’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈"/><meta data-rh="true" property="al:android:url" content="medium://p/4ed5bf765d8d"/><meta data-rh="true" property="al:ios:url" content="medium://p/4ed5bf765d8d"/><meta data-rh="true" property="al:android:app_name" content="Medium"/><meta data-rh="true" name="description" content="It’s difficult for us to stay on top of the latest AI advancements with 100s of research papers, articles, and newsletters published daily. Luckily, Kaggle has recently published their [annual AI…"/><meta data-rh="true" property="og:description" content="It’s difficult for us to stay on top of the latest AI advancements with 100s of research papers, articles, and newsletters published daily…"/><meta data-rh="true" property="og:url" content="https://medium.com/@vince-lam/decoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d"/><meta data-rh="true" property="al:web:url" content="https://medium.com/@vince-lam/decoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d"/><meta data-rh="true" property="og:image" content="https://miro.medium.com/v2/resize:fit:1200/1*CO3XhjWwoHmkfBCog-eYfg.png"/><meta data-rh="true" property="article:author" content="https://medium.com/@vince-lam"/><meta data-rh="true" name="author" content="Vince Lam"/><meta data-rh="true" name="robots" content="index,noarchive,follow,max-image-preview:large"/><meta data-rh="true" name="referrer" content="unsafe-url"/><meta data-rh="true" property="twitter:title" content="Decoding Kaggle’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈"/><meta data-rh="true" name="twitter:site" content="@Medium"/><meta data-rh="true" name="twitter:app:url:iphone" content="medium://p/4ed5bf765d8d"/><meta data-rh="true" property="twitter:description" content="It’s difficult for us to stay on top of the latest AI advancements with 100s of research papers, articles, and newsletters published daily…"/><meta data-rh="true" name="twitter:image:src" content="https://miro.medium.com/v2/resize:fit:1200/1*CO3XhjWwoHmkfBCog-eYfg.png"/><meta data-rh="true" name="twitter:card" content="summary_large_image"/><meta data-rh="true" name="twitter:creator" content="@_vincelam"/><meta data-rh="true" name="twitter:label1" content="Reading time"/><meta data-rh="true" name="twitter:data1" content="11 min read"/><link data-rh="true" rel="icon" href="https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19"/><link data-rh="true" rel="search" type="application/opensearchdescription+xml" title="Medium" href="/osd.xml"/><link data-rh="true" rel="apple-touch-icon" sizes="152x152" href="https://miro.medium.com/v2/resize:fill:304:304/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="apple-touch-icon" sizes="120x120" href="https://miro.medium.com/v2/resize:fill:240:240/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="apple-touch-icon" sizes="76x76" href="https://miro.medium.com/v2/resize:fill:152:152/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="apple-touch-icon" sizes="60x60" href="https://miro.medium.com/v2/resize:fill:120:120/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="mask-icon" href="https://miro.medium.com/v2/resize:fill:1000:1000/7*GAOKVe--MXbEJmV9230oOQ.png" color="#171717"/><link data-rh="true" rel="preconnect" href="https://glyph.medium.com" crossOrigin=""/><link data-rh="true" rel="manifest" href="/manifest.json"/><link data-rh="true" rel="preconnect" href="https://www.google.com"/><link data-rh="true" rel="preconnect" href="https://www.gstatic.com" crossOrigin=""/><link data-rh="true" id="glyph_preload_link" rel="preload" as="style" type="text/css" href="https://glyph.medium.com/css/unbound.css"/><link data-rh="true" id="glyph_link" rel="stylesheet" type="text/css" href="https://glyph.medium.com/css/unbound.css"/><link data-rh="true" rel="author" href="https://medium.com/@vince-lam"/><link data-rh="true" rel="canonical" href="https://medium.com/@vince-lam/decoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d"/><link data-rh="true" rel="alternate" href="android-app://com.medium.reader/https/medium.com/p/4ed5bf765d8d"/><script data-rh="true" type="application/ld+json">{"@context":"https://schema.org","@id":"https://medium.com/@vince-lam/decoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d","@type":"SocialMediaPosting","image":["https://miro.medium.com/1*CO3XhjWwoHmkfBCog-eYfg.png"],"url":"https://medium.com/@vince-lam/decoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d","dateCreated":"2023-10-30T11:46:16Z","datePublished":"2023-10-30T11:46:16Z","dateModified":"2023-10-30T11:46:16Z","headline":"Decoding Kaggle’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈","name":"Decoding Kaggle’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈","description":"Decoding Kaggle’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈\nIt’s difficult for us to stay on top of the latest AI advancements with 100s of research papers …","identifier":"4ed5bf765d8d","author":{"@context":"https://schema.org","@id":"https://medium.com/@vince-lam","@type":"Person","identifier":"vince-lam","name":"Vince Lam","url":"https://medium.com/@vince-lam"},"creator":{"@context":"https://schema.org","@id":"https://medium.com/@vince-lam","@type":"Person","identifier":"vince-lam","name":"Vince Lam","url":"https://medium.com/@vince-lam"},"publisher":{"@context":"https://schema.org","@type":"Organization","@id":"https://medium.com","name":"Medium","url":"https://medium.com","logo":{"@type":"ImageObject","width":500,"height":110,"url":"https://miro.medium.com/v2/resize:fit:500/7%2AV1_7XP4snlmqrc_0Njontw.png"}},"mainEntityOfPage":"https://medium.com/@vince-lam/decoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d","isAccessibleForFree":true}</script><script data-rh="true" src="https://www.google.com/recaptcha/enterprise.js?render=6Le-uGgpAAAAAPprRaokM8AKthQ9KNGdoxaGUvVp" async="true"></script><style type="text/css" data-fela-rehydration="529" data-fela-type="STATIC">html{box-sizing:border-box;-webkit-text-size-adjust:100%}*, *:before, *:after{box-sizing:inherit}body{margin:0;padding:0;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;color:rgba(0,0,0,0.8);position:relative;min-height:100vh}h1, h2, h3, h4, h5, h6, dl, dd, ol, ul, menu, figure, blockquote, p, pre, form{margin:0}menu, ol, ul{padding:0;list-style:none;list-style-image:none}main{display:block}a{color:inherit;text-decoration:none}a, button, input{-webkit-tap-highlight-color:transparent}img, svg{vertical-align:middle}button{background:transparent;overflow:visible}button, input, optgroup, select, textarea{margin:0}:root{--reach-tabs:1;--reach-menu-button:1}#speechify-root{font-family:Sohne, sans-serif}div[data-popper-reference-hidden="true"]{visibility:hidden;pointer-events:none}.grecaptcha-badge{visibility:hidden}
/*XCode style (c) Angel Garcia <<EMAIL>>*/.hljs {background: #fff;color: black;
}/* Gray DOCTYPE selectors like WebKit */
.xml .hljs-meta {color: #c0c0c0;
}.hljs-comment,
.hljs-quote {color: #007400;
}.hljs-tag,
.hljs-attribute,
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-name {color: #aa0d91;
}.hljs-variable,
.hljs-template-variable {color: #3F6E74;
}.hljs-code,
.hljs-string,
.hljs-meta .hljs-string {color: #c41a16;
}.hljs-regexp,
.hljs-link {color: #0E0EFF;
}.hljs-title,
.hljs-symbol,
.hljs-bullet,
.hljs-number {color: #1c00cf;
}.hljs-section,
.hljs-meta {color: #643820;
}.hljs-title.class_,
.hljs-class .hljs-title,
.hljs-type,
.hljs-built_in,
.hljs-params {color: #5c2699;
}.hljs-attr {color: #836C28;
}.hljs-subst {color: #000;
}.hljs-formula {background-color: #eee;font-style: italic;
}.hljs-addition {background-color: #baeeba;
}.hljs-deletion {background-color: #ffc8bd;
}.hljs-selector-id,
.hljs-selector-class {color: #9b703f;
}.hljs-doctag,
.hljs-strong {font-weight: bold;
}.hljs-emphasis {font-style: italic;
}
</style><style type="text/css" data-fela-rehydration="529" data-fela-type="KEYFRAME">@-webkit-keyframes k1{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}@-moz-keyframes k1{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}@keyframes k1{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}</style><style type="text/css" data-fela-rehydration="529" data-fela-type="RULE">.a{font-family:medium-content-sans-serif-font, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif}.b{font-weight:400}.c{background-color:rgba(255, 255, 255, 1)}.d{display:none}.m{display:block}.n{position:sticky}.o{top:0}.p{z-index:500}.q{padding:0 24px}.r{align-items:center}.s{border-bottom:solid 1px #F2F2F2}.z{height:41px}.ab{line-height:20px}.ac{display:flex}.ae{height:57px}.af{flex:1 0 auto}.ag{color:inherit}.ah{fill:inherit}.ai{font-size:inherit}.aj{border:none}.ak{font-family:inherit}.al{letter-spacing:inherit}.am{font-weight:inherit}.an{padding:0}.ao{margin:0}.ap{cursor:pointer}.aq:disabled{cursor:not-allowed}.ar:disabled{color:#6B6B6B}.as:disabled{fill:#6B6B6B}.av{width:auto}.aw path{fill:#242424}.ax{height:25px}.ay{margin-left:24px}.az{border-radius:20px}.ba{width:240px}.bb{background:#F9F9F9}.bc path{fill:#6B6B6B}.be{outline:none}.bf{font-family:sohne, "Helvetica Neue", Helvetica, Arial, sans-serif}.bg{font-size:14px}.bh{width:100%}.bi{padding:10px 20px 10px 0}.bj{background-color:transparent}.bk{color:#242424}.bl::placeholder{color:#6B6B6B}.bm{display:inline-block}.bn{margin-left:12px}.bo{margin-right:12px}.bp{border-radius:4px}.bq{height:24px}.bw{background-color:#F9F9F9}.bx{border-radius:50%}.by{height:32px}.bz{width:32px}.ca{flex:1 1 auto}.cb{justify-content:center}.ch{max-width:680px}.ci{min-width:0}.cj{animation:k1 1.2s ease-in-out infinite}.ck{height:100vh}.cl{margin-bottom:16px}.cm{margin-top:48px}.cn{align-items:flex-start}.co{flex-direction:column}.cp{justify-content:space-between}.cq{margin-bottom:24px}.cw{width:80%}.cx{background-color:#F2F2F2}.dd{height:44px}.de{width:44px}.df{margin:auto 0}.dg{margin-bottom:4px}.dh{height:16px}.di{width:120px}.dj{width:80px}.dp{margin-bottom:8px}.dq{width:96%}.dr{width:98%}.ds{width:81%}.dt{margin-left:8px}.du{color:#6B6B6B}.dv{font-size:13px}.dw{height:100%}.ep{color:#FFFFFF}.eq{fill:#FFFFFF}.er{background:#1A8917}.es{border-color:#1A8917}.ew:disabled{cursor:inherit !important}.ex:disabled{opacity:0.3}.ey:disabled:hover{background:#1A8917}.ez:disabled:hover{border-color:#1A8917}.fa{border-radius:99em}.fb{border-width:1px}.fc{border-style:solid}.fd{box-sizing:border-box}.fe{text-decoration:none}.ff{text-align:center}.fg{margin-left:16px}.fh{border:inherit}.fk{margin-right:32px}.fl{position:relative}.fm{fill:#6B6B6B}.fp{background:transparent}.fq svg{margin-left:4px}.fr svg{fill:#6B6B6B}.ft{box-shadow:inset 0 0 0 1px rgba(0, 0, 0, 0.05)}.fu{position:absolute}.gb{margin:0 24px}.gf{background:rgba(255, 255, 255, 1)}.gg{border:1px solid #F2F2F2}.gh{box-shadow:0 1px 4px #F2F2F2}.gi{max-height:100vh}.gj{overflow-y:auto}.gk{left:0}.gl{top:calc(100vh + 100px)}.gm{bottom:calc(100vh + 100px)}.gn{width:10px}.go{pointer-events:none}.gp{word-break:break-word}.gq{word-wrap:break-word}.gr:after{display:block}.gs:after{content:""}.gt:after{clear:both}.gu{line-height:1.23}.gv{letter-spacing:0}.gw{font-style:normal}.gx{font-weight:700}.ih{gap:12px}.ii{align-items:baseline}.ij{width:36px}.ik{height:36px}.il{border:2px solid rgba(255, 255, 255, 1)}.im{z-index:0}.in{box-shadow:none}.io{border:1px solid rgba(0, 0, 0, 0.05)}.ip{margin-bottom:2px}.iq{flex-wrap:nowrap}.is{width:12px}.it{flex-wrap:wrap}.iu{padding-left:8px}.iv{padding-right:8px}.jw> *{flex-shrink:0}.jx{overflow-x:scroll}.jy::-webkit-scrollbar{display:none}.jz{scrollbar-width:none}.ka{-ms-overflow-style:none}.kb{width:74px}.kc{flex-direction:row}.kd{z-index:2}.ke{margin-right:4px}.kh{-webkit-user-select:none}.ki{border:0}.kj{fill:rgba(117, 117, 117, 1)}.km{outline:0}.kn{user-select:none}.ko> svg{pointer-events:none}.kx{cursor:progress}.ky{opacity:1}.kz{padding:4px 0}.lc{margin-top:0px}.ld{width:16px}.lf{display:inline-flex}.ll{max-width:100%}.lm{padding:8px 2px}.ln svg{color:#6B6B6B}.me{line-height:1.58}.mf{letter-spacing:-0.004em}.mg{font-family:source-serif-pro, Georgia, Cambria, "Times New Roman", Times, serif}.nb{margin-bottom:-0.46em}.nc{text-decoration:underline}.nd{box-shadow:inset 3px 0 0 0 #242424}.ne{padding-left:23px}.nf{margin-left:-20px}.ng{font-style:italic}.nh{margin-left:auto}.ni{margin-right:auto}.nj{max-width:1792px}.np{clear:both}.nr{cursor:zoom-in}.ns{z-index:auto}.nu{width:1px}.nv{height:1px}.nw{margin:-1px}.nx{overflow:hidden}.ny{clip:rect(0, 0, 0, 0)}.nz{white-space:nowrap}.oa{border-width:0}.ob{height:auto}.oc{line-height:1.12}.od{letter-spacing:-0.022em}.oe{font-weight:600}.oz{margin-bottom:-0.28em}.pf{line-height:1.18}.pt{margin-bottom:-0.31em}.pu{list-style-type:disc}.pv{margin-left:30px}.pw{padding-left:0px}.qc{max-width:790px}.qd{margin-top:10px}.qe{max-width:728px}.qh{max-width:1600px}.qi{max-width:852px}.qj{margin-bottom:26px}.qk{margin-top:6px}.ql{margin-top:8px}.qm{margin-right:8px}.qn{padding:8px 16px}.qo{border-radius:100px}.qp{transition:background 300ms ease}.qr{border-top:none}.qs{margin-bottom:50px}.qt{height:52px}.qu{max-height:52px}.qv{box-sizing:content-box}.qw{position:static}.qx{z-index:1}.qz{max-width:155px}.rf{margin-right:20px}.rg{flex:0 0 auto}.rh{margin-bottom:64px}.ru{height:48px}.rv{width:48px}.rx{height:64px}.ry{width:64px}.rz{align-self:flex-end}.sf{padding-right:4px}.sg{font-weight:500}.sn{white-space:pre-wrap}.so{margin:0 8px}.sp{margin-top:16px}.sq{margin-bottom:54px}.sr{height:0px}.ss{gap:18px}.st{fill:rgba(61, 61, 61, 1)}.tf{border-bottom:solid 1px #E5E5E5}.tg{margin-top:72px}.th{padding:24px 0}.ti{margin-bottom:0px}.tj{margin-right:16px}.at:hover:not(:disabled){color:rgba(25, 25, 25, 1)}.au:hover:not(:disabled){fill:rgba(25, 25, 25, 1)}.et:hover{background:#156D12}.eu:hover{border-color:#156D12}.ev:hover{cursor:pointer}.fn:hover{color:#242424}.fo:hover{fill:#242424}.fs:hover svg{fill:#242424}.fv:hover{background-color:rgba(0, 0, 0, 0.1)}.ir:hover{text-decoration:underline}.kl:hover{fill:rgba(8, 8, 8, 1)}.la:hover{fill:#000000}.lb:hover p{color:#000000}.le:hover{color:#000000}.lo:hover svg{color:#000000}.qq:hover{background-color:#F2F2F2}.rw:hover{background-color:none}.su:hover{fill:rgba(25, 25, 25, 1)}.bd:focus-within path{fill:#242424}.kk:focus{fill:rgba(8, 8, 8, 1)}.lp:focus svg{color:#000000}.nt:focus{transform:scale(1.01)}.kp:active{border-style:none}</style><style type="text/css" data-fela-rehydration="529" data-fela-type="RULE" media="all and (min-width: 1080px)">.e{display:none}.bv{width:64px}.cg{margin:0 64px}.cv{height:48px}.dc{margin-bottom:52px}.do{margin-bottom:48px}.ef{font-size:14px}.eg{line-height:20px}.em{font-size:13px}.eo{padding:5px 12px}.fj{display:flex}.ga{margin-bottom:50px}.ge{max-width:680px}.hs{font-size:42px}.ht{margin-top:1.19em}.hu{margin-bottom:32px}.hv{line-height:52px}.hw{letter-spacing:-0.011em}.if{align-items:center}.ig{flex-direction:row}.ji{border-top:solid 1px #F2F2F2}.jj{border-bottom:solid 1px #F2F2F2}.jk{margin:32px 0 0}.jl{padding:3px 8px}.ju> *{margin-right:24px}.jv> :last-child{margin-right:0}.kw{margin-top:0px}.lk{margin:0}.mx{font-size:20px}.my{margin-top:2.14em}.mz{line-height:32px}.na{letter-spacing:-0.003em}.no{margin-top:56px}.ov{font-size:24px}.ow{margin-top:1.95em}.ox{line-height:30px}.oy{letter-spacing:-0.016em}.pe{margin-top:0.94em}.pq{margin-top:1.72em}.pr{line-height:24px}.ps{letter-spacing:0}.qb{margin-top:1.14em}.re{display:inline-block}.rk{margin-bottom:0}.rl{margin-right:20px}.sa{max-width:500px}.sz{margin:40px 0 0}.te{padding-top:72px}</style><style type="text/css" data-fela-rehydration="529" data-fela-type="RULE" media="all and (max-width: 1079.98px)">.f{display:none}.kv{margin-top:0px}.qf{margin-left:auto}.qg{text-align:center}.rd{display:inline-block}</style><style type="text/css" data-fela-rehydration="529" data-fela-type="RULE" media="all and (max-width: 903.98px)">.g{display:none}.ku{margin-top:0px}.rc{display:inline-block}</style><style type="text/css" data-fela-rehydration="529" data-fela-type="RULE" media="all and (max-width: 727.98px)">.h{display:none}.ks{margin-top:0px}.kt{margin-right:0px}.rb{display:inline-block}</style><style type="text/css" data-fela-rehydration="529" data-fela-type="RULE" media="all and (max-width: 551.98px)">.i{display:none}.t{display:flex}.u{justify-content:space-between}.br{width:24px}.cc{margin:0 24px}.cr{height:40px}.cy{margin-bottom:44px}.dk{margin-bottom:32px}.dx{font-size:13px}.dy{line-height:20px}.eh{padding:0px 8px 1px}.fw{margin-bottom:2px}.gy{font-size:32px}.gz{margin-top:1.01em}.ha{margin-bottom:24px}.hb{line-height:38px}.hc{letter-spacing:-0.014em}.hx{align-items:flex-start}.hy{flex-direction:column-reverse}.iw{margin:24px -24px 0}.ix{padding:0}.jm> *{margin-right:8px}.jn> :last-child{margin-right:24px}.kf{margin-left:0px}.kq{margin-top:0px}.kr{margin-right:0px}.lg{margin:0}.lq{border:1px solid #F2F2F2}.lr{border-radius:99em}.ls{padding:0px 16px 0px 12px}.lt{height:38px}.lu{align-items:center}.lw svg{margin-right:8px}.mh{font-size:18px}.mi{margin-top:1.56em}.mj{line-height:28px}.mk{letter-spacing:-0.003em}.nk{margin-top:40px}.of{font-size:20px}.og{margin-top:1.2em}.oh{line-height:24px}.oi{letter-spacing:0}.pa{margin-top:0.67em}.pg{font-size:16px}.ph{margin-top:1.23em}.px{margin-top:1.34em}.ra{display:inline-block}.rj{flex-direction:column}.rs{margin-bottom:20px}.rt{margin-right:0}.se{max-width:100%}.sh{font-size:24px}.si{line-height:30px}.sj{letter-spacing:-0.016em}.sv{margin:32px 0 0}.ta{padding-top:48px}.lv:hover{border-color:#E5E5E5}</style><style type="text/css" data-fela-rehydration="529" data-fela-type="RULE" media="all and (min-width: 904px) and (max-width: 1079.98px)">.j{display:none}.bu{width:64px}.cf{margin:0 64px}.cu{height:48px}.db{margin-bottom:52px}.dn{margin-bottom:48px}.ed{font-size:14px}.ee{line-height:20px}.ek{font-size:13px}.el{padding:5px 12px}.fi{display:flex}.fz{margin-bottom:50px}.gd{max-width:680px}.hn{font-size:42px}.ho{margin-top:1.19em}.hp{margin-bottom:32px}.hq{line-height:52px}.hr{letter-spacing:-0.011em}.id{align-items:center}.ie{flex-direction:row}.je{border-top:solid 1px #F2F2F2}.jf{border-bottom:solid 1px #F2F2F2}.jg{margin:32px 0 0}.jh{padding:3px 8px}.js> *{margin-right:24px}.jt> :last-child{margin-right:0}.lj{margin:0}.mt{font-size:20px}.mu{margin-top:2.14em}.mv{line-height:32px}.mw{letter-spacing:-0.003em}.nn{margin-top:56px}.or{font-size:24px}.os{margin-top:1.95em}.ot{line-height:30px}.ou{letter-spacing:-0.016em}.pd{margin-top:0.94em}.pn{margin-top:1.72em}.po{line-height:24px}.pp{letter-spacing:0}.qa{margin-top:1.14em}.rm{margin-bottom:0}.rn{margin-right:20px}.sb{max-width:500px}.sy{margin:40px 0 0}.td{padding-top:72px}</style><style type="text/css" data-fela-rehydration="529" data-fela-type="RULE" media="all and (min-width: 728px) and (max-width: 903.98px)">.k{display:none}.x{display:flex}.y{justify-content:space-between}.bt{width:64px}.ce{margin:0 48px}.ct{height:48px}.da{margin-bottom:52px}.dm{margin-bottom:48px}.eb{font-size:13px}.ec{line-height:20px}.ej{padding:0px 8px 1px}.fy{margin-bottom:50px}.gc{max-width:680px}.hi{font-size:42px}.hj{margin-top:1.19em}.hk{margin-bottom:32px}.hl{line-height:52px}.hm{letter-spacing:-0.011em}.ib{align-items:center}.ic{flex-direction:row}.ja{border-top:solid 1px #F2F2F2}.jb{border-bottom:solid 1px #F2F2F2}.jc{margin:32px 0 0}.jd{padding:3px 8px}.jq> *{margin-right:24px}.jr> :last-child{margin-right:0}.li{margin:0}.mp{font-size:20px}.mq{margin-top:2.14em}.mr{line-height:32px}.ms{letter-spacing:-0.003em}.nm{margin-top:56px}.on{font-size:24px}.oo{margin-top:1.95em}.op{line-height:30px}.oq{letter-spacing:-0.016em}.pc{margin-top:0.94em}.pk{margin-top:1.72em}.pl{line-height:24px}.pm{letter-spacing:0}.pz{margin-top:1.14em}.ro{margin-bottom:0}.rp{margin-right:20px}.sc{max-width:500px}.sx{margin:40px 0 0}.tc{padding-top:72px}</style><style type="text/css" data-fela-rehydration="529" data-fela-type="RULE" media="all and (min-width: 552px) and (max-width: 727.98px)">.l{display:none}.v{display:flex}.w{justify-content:space-between}.bs{width:24px}.cd{margin:0 24px}.cs{height:40px}.cz{margin-bottom:44px}.dl{margin-bottom:32px}.dz{font-size:13px}.ea{line-height:20px}.ei{padding:0px 8px 1px}.fx{margin-bottom:2px}.hd{font-size:32px}.he{margin-top:1.01em}.hf{margin-bottom:24px}.hg{line-height:38px}.hh{letter-spacing:-0.014em}.hz{align-items:flex-start}.ia{flex-direction:column-reverse}.iy{margin:24px 0 0}.iz{padding:0}.jo> *{margin-right:8px}.jp> :last-child{margin-right:8px}.kg{margin-left:0px}.lh{margin:0}.lx{border:1px solid #F2F2F2}.ly{border-radius:99em}.lz{padding:0px 16px 0px 12px}.ma{height:38px}.mb{align-items:center}.md svg{margin-right:8px}.ml{font-size:18px}.mm{margin-top:1.56em}.mn{line-height:28px}.mo{letter-spacing:-0.003em}.nl{margin-top:40px}.oj{font-size:20px}.ok{margin-top:1.2em}.ol{line-height:24px}.om{letter-spacing:0}.pb{margin-top:0.67em}.pi{font-size:16px}.pj{margin-top:1.23em}.py{margin-top:1.34em}.ri{flex-direction:column}.rq{margin-bottom:20px}.rr{margin-right:0}.sd{max-width:100%}.sk{font-size:24px}.sl{line-height:30px}.sm{letter-spacing:-0.016em}.sw{margin:32px 0 0}.tb{padding-top:48px}.mc:hover{border-color:#E5E5E5}</style><style type="text/css" data-fela-rehydration="529" data-fela-type="RULE" media="print">.qy{display:none}</style><style type="text/css" data-fela-rehydration="529" data-fela-type="RULE" media="(prefers-reduced-motion: no-preference)">.nq{transition:transform 300ms cubic-bezier(0.2, 0, 0.2, 1)}</style></head><body><div id="root"><div class="a b c"><a href="/sitemap/sitemap.xml" class="d">Sitemap</a><div class="e f g h i j k l"></div><script>document.domain = document.domain;</script><div class="m c"><div class="m n o p c"><div class="q r s t u v w x y j e z ab"><a class="du ah dv bf al b an ao ap aq ar as at au t v x j e r dw ab" href="https://rsci.app.link/?%24canonical_url=https%3A%2F%2Fmedium.com%2Fp%2F4ed5bf765d8d&amp;%7Efeature=LoOpenInAppButton&amp;%7Echannel=ShowPostUnderUser&amp;%7Estage=mobileNavBar&amp;source=post_page---top_nav_layout_nav-----------------------------------------" rel="noopener follow">Open in app<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="none" viewBox="0 0 10 10" class="dt"><path fill="currentColor" d="M.985 8.485a.375.375 0 1 0 .53.53zM8.75 1.25h.375A.375.375 0 0 0 8.75.875zM8.375 6.5a.375.375 0 1 0 .75 0zM3.5.875a.375.375 0 1 0 0 .75zm-1.985 8.14 7.5-7.5-.53-.53-7.5 7.5zm6.86-7.765V6.5h.75V1.25zM3.5 1.625h5.25v-.75H3.5z"></path></svg></a><div class="ac r"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><button class="bf b dx dy eh dz ea ei eb ec ej ek ee el em eg eo ep eq er es et eu ev ew ex ey ez fa fb fc fd bm fe ff" data-testid="headerSignUpButton">Sign up</button></span></p><div class="fg m"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerSignInButton" rel="noopener follow" href="/m/signin?operation=login&amp;redirect=https%3A%2F%2Fmedium.com%2F%40vince-lam%2Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d&amp;source=post_page---top_nav_layout_nav-----------------------global_nav------------------" data-discover="true">Sign in</a></span></p></div></div></div><div class="q r s ac ae"><div class="ac r af"><a class="ag ah ai aj ak al am an ao ap aq ar as at au ac" aria-label="Homepage" data-testid="headerMediumLogo" rel="noopener follow" href="/?source=post_page---top_nav_layout_nav-----------------------------------------" data-discover="true"><svg xmlns="http://www.w3.org/2000/svg" width="719" height="160" fill="none" aria-labelledby="wordmark-medium-desc" viewBox="0 0 719 160" class="av aw ax"><desc id="wordmark-medium-desc">Medium Logo</desc><path fill="#242424" d="m174.104 9.734.215-.047V8.02H130.39L89.6 103.89 48.81 8.021H1.472v1.666l.212.047c8.018 1.81 12.09 4.509 12.09 14.242V137.93c0 9.734-4.087 12.433-12.106 14.243l-.212.047v1.671h32.118v-1.665l-.213-.048c-8.018-1.809-12.089-4.509-12.089-14.242V30.586l52.399 123.305h2.972l53.925-126.743V140.75c-.687 7.688-4.721 10.062-11.982 11.701l-.215.05v1.652h55.948v-1.652l-.215-.05c-7.269-1.639-11.4-4.013-12.087-11.701l-.037-116.774h.037c0-9.733 4.071-12.432 12.087-14.242m25.555 75.488c.915-20.474 8.268-35.252 20.606-35.507 3.806.063 6.998 1.312 9.479 3.714 5.272 5.118 7.751 15.812 7.368 31.793zm-.553 5.77h65.573v-.275c-.186-15.656-4.721-27.834-13.466-36.196-7.559-7.227-18.751-11.203-30.507-11.203h-.263c-6.101 0-13.584 1.48-18.909 4.16-6.061 2.807-11.407 7.003-15.855 12.511-7.161 8.874-11.499 20.866-12.554 34.343q-.05.606-.092 1.212a50 50 0 0 0-.065 1.151 85.807 85.807 0 0 0-.094 5.689c.71 30.524 17.198 54.917 46.483 54.917 25.705 0 40.675-18.791 44.407-44.013l-1.886-.664c-6.557 13.556-18.334 21.771-31.738 20.769-18.297-1.369-32.314-19.922-31.042-42.395m139.722 41.359c-2.151 5.101-6.639 7.908-12.653 7.908s-11.513-4.129-15.418-11.63c-4.197-8.053-6.405-19.436-6.405-32.92 0-28.067 8.729-46.22 22.24-46.22 5.657 0 10.111 2.807 12.236 7.704zm43.499 20.008c-8.019-1.897-12.089-4.722-12.089-14.951V1.309l-48.716 14.353v1.757l.299-.024c6.72-.543 11.278.386 13.925 2.83 2.072 1.915 3.082 4.853 3.082 8.987v18.66c-4.803-3.067-10.516-4.56-17.448-4.56-14.059 0-26.909 5.92-36.176 16.672-9.66 11.205-14.767 26.518-14.767 44.278-.003 31.72 15.612 53.039 38.851 53.039 13.595 0 24.533-7.449 29.54-20.013v16.865h43.711v-1.746zM424.1 19.819c0-9.904-7.468-17.374-17.375-17.374-9.859 0-17.573 7.632-17.573 17.374s7.721 17.374 17.573 17.374c9.907 0 17.375-7.47 17.375-17.374m11.499 132.546c-8.019-1.897-12.089-4.722-12.089-14.951h-.035V43.635l-43.714 12.551v1.705l.263.024c9.458.842 12.047 4.1 12.047 15.152v81.086h43.751v-1.746zm112.013 0c-8.018-1.897-12.089-4.722-12.089-14.951V43.635l-41.621 12.137v1.71l.246.026c7.733.813 9.967 4.257 9.967 15.36v59.279c-2.578 5.102-7.415 8.131-13.274 8.336-9.503 0-14.736-6.419-14.736-18.073V43.638l-43.714 12.55v1.703l.262.024c9.459.84 12.05 4.097 12.05 15.152v50.17a56.3 56.3 0 0 0 .91 10.444l.787 3.423c3.701 13.262 13.398 20.197 28.59 20.197 12.868 0 24.147-7.966 29.115-20.43v17.311h43.714v-1.747zm169.818 1.788v-1.749l-.213-.05c-8.7-2.006-12.089-5.789-12.089-13.49v-63.79c0-19.89-11.171-31.761-29.883-31.761-13.64 0-25.141 7.882-29.569 20.16-3.517-13.01-13.639-20.16-28.606-20.16-13.146 0-23.449 6.938-27.869 18.657V43.643L545.487 55.68v1.715l.263.024c9.345.829 12.047 4.181 12.047 14.95v81.784h40.787v-1.746l-.215-.053c-6.941-1.631-9.181-4.606-9.181-12.239V66.998c1.836-4.289 5.537-9.37 12.853-9.37 9.086 0 13.692 6.296 13.692 18.697v77.828h40.797v-1.746l-.215-.053c-6.94-1.631-9.18-4.606-9.18-12.239V75.066a42 42 0 0 0-.578-7.26c1.947-4.661 5.86-10.177 13.475-10.177 9.214 0 13.691 6.114 13.691 18.696v77.828z"></path></svg></a><div class="ay i"><div class="ac aj az ba bb r bc bd"><div class="bm" aria-hidden="false" aria-describedby="searchResults" aria-labelledby="searchResults" aria-haspopup="listbox" role="listbox"></div><div class="bn bo ac"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M4.092 11.06a6.95 6.95 0 1 1 13.9 0 6.95 6.95 0 0 1-13.9 0m6.95-8.05a8.05 8.05 0 1 0 5.13 14.26l3.75 3.75a.56.56 0 1 0 .79-.79l-3.73-3.73A8.05 8.05 0 0 0 11.042 3z" clip-rule="evenodd"></path></svg></div><input role="combobox" aria-controls="searchResults" aria-expanded="false" aria-label="search" data-testid="headerSearchInput" tabindex="0" class="aj be bf bg ab bh bi bj bk bl" placeholder="Search" value=""/></div></div></div><div class="i l x fi fj"><div class="fk ac"><span data-dd-action-name="Susi presentation tracker new_post_topnav"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerWriteButton" rel="noopener follow" href="/m/signin?operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fnew-story&amp;source=---top_nav_layout_nav-----------------------new_post_topnav------------------" data-discover="true"><div class="bf b bg ab du fl fm ac r fn fo"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" aria-label="Write"><path fill="currentColor" d="M14 4a.5.5 0 0 0 0-1zm7 6a.5.5 0 0 0-1 0zm-7-7H4v1h10zM3 4v16h1V4zm1 17h16v-1H4zm17-1V10h-1v10zm-1 1a1 1 0 0 0 1-1h-1zM3 20a1 1 0 0 0 1 1v-1zM4 3a1 1 0 0 0-1 1h1z"></path><path stroke="currentColor" d="m17.5 4.5-8.458 8.458a.25.25 0 0 0-.06.098l-.824 2.47a.25.25 0 0 0 .316.316l2.47-.823a.25.25 0 0 0 .098-.06L19.5 6.5m-2-2 2.323-2.323a.25.25 0 0 1 .354 0l1.646 1.646a.25.25 0 0 1 0 .354L19.5 6.5m-2-2 2 2"></path></svg><div class="dt m">Write</div></div></a></span></div></div><div class="l k j e"><div class="fk ac"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerSearchButton" rel="noopener follow" href="/search?source=post_page---top_nav_layout_nav-----------------------------------------" data-discover="true"><div class="bf b bg ab du fl fm ac r fn fo"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" aria-label="Search"><path fill="currentColor" fill-rule="evenodd" d="M4.092 11.06a6.95 6.95 0 1 1 13.9 0 6.95 6.95 0 0 1-13.9 0m6.95-8.05a8.05 8.05 0 1 0 5.13 14.26l3.75 3.75a.56.56 0 1 0 .79-.79l-3.73-3.73A8.05 8.05 0 0 0 11.042 3z" clip-rule="evenodd"></path></svg></div></a></div></div><div class="fk i l k"><div class="ac r"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><button class="bf b dx dy eh dz ea ei eb ec ej ek ee el em eg eo ep eq er es et eu ev ew ex ey ez fa fb fc fd bm fe ff" data-testid="headerSignUpButton">Sign up</button></span></p><div class="fg m"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerSignInButton" rel="noopener follow" href="/m/signin?operation=login&amp;redirect=https%3A%2F%2Fmedium.com%2F%40vince-lam%2Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d&amp;source=post_page---top_nav_layout_nav-----------------------global_nav------------------" data-discover="true">Sign in</a></span></p></div></div></div><div class="m" aria-hidden="false"><button class="aj fp an ac r ap fl fq fr fs" aria-label="user options menu" data-testid="headerUserIcon"><div class="m fl"><img alt="" class="m fd bx by bz cx" src="https://miro.medium.com/v2/resize:fill:64:64/1*dmbNkD5D-u45r44go_cf0g.png" width="32" height="32" loading="lazy" role="presentation"/><div class="ft bx m by bz fu o aj fv"></div></div></button></div></div></div><div class="ac"><div class="ca bh"><div class="m"><div class="fw fx fy fz ga m"><div class="ac cb"><div class="ci bh gb gc gd ge"></div></div><article><div class="m"><div class="m"><span class="m"></span><section><div><div class="fu gk gl gm gn go"></div><div class="gp gq gr gs gt"><div class="ac cb"><div class="ci bh gb gc gd ge"><div><h1 id="d012" class="pw-post-title gu gv gw bf gx gy gz ha hb hc hd he hf hg hh hi hj hk hl hm hn ho hp hq hr hs ht hu hv hw bk" data-testid="storyTitle">Decoding Kaggle’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈</h1><div><div class="speechify-ignore ac cp"><div class="speechify-ignore bh m"><div class="ac hx hy hz ia ib ic id ie if ig ih"><div class="ac r ih"><div class="ac ii"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><a rel="noopener follow" href="/@vince-lam?source=post_page---byline--4ed5bf765d8d---------------------------------------" data-discover="true"><div class="m ij ik bx il im"><div class="m fl"><img alt="Vince Lam" class="m fd bx by bz cx" src="https://miro.medium.com/v2/resize:fill:64:64/1*FdY_N_dCL4n7tdWO_FJrng.png" width="32" height="32" loading="lazy" data-testid="authorPhoto"/><div class="in bx m by bz fu o io fv"></div></div></div></a></div></div></div></div><span class="bf b bg ab bk"><div class="ip ac r"><div class="ac r iq"><div class="ac r"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><span class="bf b bg ab bk"><a class="ag ah ai fh ak al am an ao ap aq ar as ir" data-testid="authorName" rel="noopener follow" href="/@vince-lam?source=post_page---byline--4ed5bf765d8d---------------------------------------" data-discover="true">Vince Lam</a></span></div></div></div></div><div class="is bm"></div></div></div></span></div><div class="ac r it"><span class="bf b bg ab du"><div class="ac af"><span data-testid="storyReadTime">11 min read</span><div class="iu iv m" aria-hidden="true"><span class="m" aria-hidden="true"><span class="bf b bg ab du">·</span></span></div><span data-testid="storyPublishDate">Oct 30, 2023</span></div></span></div></div><div class="ac cp iw ix iy iz ja jb jc jd je jf jg jh ji jj jk jl"><div class="i l x fi fj r"><div class="kb m"><div class="ac r kc kd"><div class="pw-multi-vote-icon fl ke kf kg kh"><span data-dd-action-name="Susi presentation tracker clap_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerClapButton" rel="noopener follow" href="/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fp%2F4ed5bf765d8d&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2F%40vince-lam%2Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d&amp;user=Vince+Lam&amp;userId=ce24b5d6cbb&amp;source=---header_actions--4ed5bf765d8d---------------------clap_footer------------------" data-discover="true"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><div class="ki ap kj kk kl km an kn ko kp kh" role="presentation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="clap"><path fill-rule="evenodd" d="M11.37.828 12 3.282l.63-2.454zM13.916 3.953l1.523-2.112-1.184-.39zM8.589 1.84l1.522 2.112-.337-2.501zM18.523 18.92c-.86.86-1.75 1.246-2.62 1.33a6 6 0 0 0 .407-.372c2.388-2.389 2.86-4.951 1.399-7.623l-.912-1.603-.79-1.672c-.26-.56-.194-.98.203-1.288a.7.7 0 0 1 .546-.132c.283.046.546.231.728.5l2.363 4.157c.976 1.624 1.141 4.237-1.324 6.702m-10.999-.438L3.37 14.328a.828.828 0 0 1 .585-1.408.83.83 0 0 1 .585.242l2.158 2.157a.365.365 0 0 0 .516-.516l-2.157-2.158-1.449-1.449a.826.826 0 0 1 1.167-1.17l3.438 3.44a.363.363 0 0 0 .516 0 .364.364 0 0 0 0-.516L5.293 9.513l-.97-.97a.826.826 0 0 1 0-*********** 0 0 1 1.167 0l.97.968 3.437 3.436a.36.36 0 0 0 .517 0 .366.366 0 0 0 0-.516L6.977 7.83a.82.82 0 0 1-.241-.584.82.82 0 0 1 .824-.826c.219 0 .43.087.584.242l5.787 5.787a.366.366 0 0 0 .587-.415l-1.117-2.363c-.26-.56-.194-.98.204-1.289a.7.7 0 0 1 .546-.132c.283.046.545.232.727.501l2.193 3.86c1.302 2.38.883 4.59-1.277 6.75-1.156 1.156-2.602 1.627-4.19 1.367-1.418-.236-2.866-1.033-4.079-2.246M10.75 5.971l2.12 2.12c-.41.502-.465 1.17-.128 1.89l.22.465-3.523-3.523a.8.8 0 0 1-.097-.368c0-.22.086-.428.241-.584a.847.847 0 0 1 1.167 0m7.355 1.705c-.31-.461-.746-.758-1.23-.837a1.44 1.44 0 0 0-1.11.275c-.312.24-.505.543-.59.881a1.74 1.74 0 0 0-.906-.465 1.47 1.47 0 0 0-.82.106l-2.182-2.182a1.56 1.56 0 0 0-2.2 0 1.54 1.54 0 0 0-.396.701 1.56 1.56 0 0 0-2.21-.01 1.55 1.55 0 0 0-.416.753c-.624-.624-1.649-.624-2.237-.037a1.557 1.557 0 0 0 0 2.2c-.239.1-.501.238-.715.453a1.56 1.56 0 0 0 0 2.2l.516.515a1.556 1.556 0 0 0-.753 2.615L7.01 19c1.32 1.319 2.909 2.189 4.475 2.449q.482.08.971.08c.85 0 1.653-.198 2.393-.579.**************.686.054 1.266 0 2.457-.52 3.505-1.567 2.763-2.763 2.552-5.734 1.439-7.586z" clip-rule="evenodd"></path></svg></div></div></div></div></a></span></div><div class="pw-multi-vote-count m kq kr ks kt ku kv kw"><p class="bf b dv ab du"><span class="kx">--</span></p></div></div></div><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button class="ap ki ky kz ac r fm la lb" aria-label="responses"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="lc"><path d="M18.006 16.803c1.533-1.456 2.234-3.325 2.234-5.321C20.24 7.357 16.709 4 12.191 4S4 7.357 4 11.482c0 4.126 3.674 7.482 8.191 7.482.817 0 1.622-.111 2.393-.327.231.2.48.391.744.559 1.06.693 2.203 1.044 3.399 1.044.224-.008.4-.112.486-.287a.49.49 0 0 0-.042-.518c-.495-.67-.845-1.364-1.04-2.057a4 4 0 0 1-.125-.598zm-3.122 1.055-.067-.223-.315.096a8 8 0 0 1-2.311.338c-4.023 0-7.292-2.955-7.292-6.587 0-3.633 3.269-6.588 7.292-6.588 4.014 0 7.112 2.958 7.112 6.593 0 1.794-.608 3.469-2.027 4.72l-.195.168v.255c0 .056 0 .151.016.295.025.231.081.478.154.733.154.558.398 1.117.722 1.659a5.3 5.3 0 0 1-2.165-.845c-.276-.176-.714-.383-.941-.59z"></path></svg></button></div></div></div></div><div class="ac r jm jn jo jp jq jr js jt ju jv jw jx jy jz ka"><div class="ld l k j e"></div><div class="i l"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><span data-dd-action-name="Susi presentation tracker bookmark_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerBookmarkButton" rel="noopener follow" href="/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2F4ed5bf765d8d&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2F%40vince-lam%2Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d&amp;source=---header_actions--4ed5bf765d8d---------------------bookmark_footer------------------" data-discover="true"><svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="none" viewBox="0 0 25 25" class="du le" aria-label="Add to list bookmark button"><path fill="currentColor" d="M18 2.5a.5.5 0 0 1 1 0V5h2.5a.5.5 0 0 1 0 1H19v2.5a.5.5 0 1 1-1 0V6h-2.5a.5.5 0 0 1 0-1H18zM7 7a1 1 0 0 1 1-1h3.5a.5.5 0 0 0 0-1H8a2 2 0 0 0-2 2v14a.5.5 0 0 0 .805.396L12.5 17l5.695 4.396A.5.5 0 0 0 19 21v-8.5a.5.5 0 0 0-1 0v7.485l-5.195-4.012a.5.5 0 0 0-.61 0L7 19.985z"></path></svg></a></span></div></div></div></div><div class="fd lf cn"><div class="m af"><div class="ac cb"><div class="lg lh li lj lk ll ci bh"><div class="ac"><div class="bm" aria-hidden="false" role="tooltip"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button aria-label="Listen" data-testid="audioPlayButton" class="ag fm ai fh ak al am lm ao ap aq ex ln lo lb lp lq lr ls lt t lu lv lw lx ly lz ma v mb mc md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a9 9 0 1 1 18 0 9 9 0 0 1-18 0m9-10C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m3.376 10.416-4.599 3.066a.5.5 0 0 1-.777-.416V8.934a.5.5 0 0 1 .777-.416l4.599 3.066a.5.5 0 0 1 0 .832" clip-rule="evenodd"></path></svg><div class="k j e"><p class="bf b bg ab du">Listen</p></div></button></div></div></div></div></div></div></div></div></div><div class="bm" aria-hidden="false" aria-describedby="postFooterSocialMenu" aria-labelledby="postFooterSocialMenu"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button aria-controls="postFooterSocialMenu" aria-expanded="false" aria-label="Share Post" data-testid="headerSocialShareButton" class="ag fm ai fh ak al am lm ao ap aq ex ln lo lb lp lq lr ls lt t lu lv lw lx ly lz ma v mb mc md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M15.218 4.931a.4.4 0 0 1-.118.132l.012.006a.45.45 0 0 1-.292.074.5.5 0 0 1-.3-.13l-2.02-2.02v7.07c0 .28-.23.5-.5.5s-.5-.22-.5-.5v-7.04l-2 2a.45.45 0 0 1-.57.04h-.02a.4.4 0 0 1-.16-.3.4.4 0 0 1 .1-.32l2.8-2.8a.5.5 0 0 1 .7 0l2.8 2.79a.42.42 0 0 1 .068.498m-.106.138.008.004v-.01zM16 7.063h1.5a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-11c-1.1 0-2-.9-2-2v-10a2 2 0 0 1 2-2H8a.5.5 0 0 1 .********* 0 0 1 .********* 0 0 1-.********* 0 0 1-.35.15H6.4c-.5 0-.9.4-.9.9v10.2a.9.9 0 0 0 .9.9h11.2c.5 0 .9-.4.9-.9v-10.2c0-.5-.4-.9-.9-.9H16a.5.5 0 0 1 0-1" clip-rule="evenodd"></path></svg><div class="k j e"><p class="bf b bg ab du">Share</p></div></button></div></div></div></div></div></div></div></div></div></div><p id="3335" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">It’s difficult for us to stay on top of the latest AI advancements with 100s of research papers, articles, and newsletters published daily. Luckily, Kaggle has recently published their [annual AI report](https://www.kaggle.com/AI-Report-2023) earlier this month, which distills and summarises the latest advancements this past action-packed year.</p><p id="83b1" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">The <a class="ag nc" href="https://www.kaggle.com/AI-Report-2023" rel="noopener ugc nofollow" target="_blank">Kaggle AI Report 2023</a> collates the best 3 essays on 7 important AI topics: (1) <strong class="mg gx">Generative AI</strong>, (2) <strong class="mg gx">text data</strong>, (3) <strong class="mg gx">image &amp; video data</strong>, (4) <strong class="mg gx">tabular / time-series data</strong>, (5) <strong class="mg gx">Kaggle competitions</strong>, (6) <strong class="mg gx">AI Ethics</strong>, and (7) <strong class="mg gx">Other </strong>(for topics that do not fall neatly under the previous 6 areas). All essays are fascinating reads, jam-packed with information. Some essays provide a comprehensive overview of a topic, other essays dive deep into the technicalities, and other essays leave you with thought-provoking ideas on what the future may entail.</p><p id="4065" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">For this blog post, I’ll focus on <strong class="mg gx">tabular and time-series data</strong> (in the context of machine learning (ML) competitions) since this is the most common data type that data scientists face and has the highest applicability/utility. As Bojan Tunguz said:</p><blockquote class="nd ne nf"><p id="c276" class="me mf ng mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">“It is estimated that between 50% and 90% of practicing data scientists use tabular data as their primary type of data in their primary setting.”</p></blockquote><p id="d186" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">To supplement my takeaways on best practice for tabular data, I’ll also draw on the findings from ML Contest’s <a class="ag nc" href="https://mlcontests.com/state-of-competitive-machine-learning-2022" rel="noopener ugc nofollow" target="_blank">State of Competitive Machine Learning Report</a> published earlier this year, which broadly aligns with Kaggle report. I’ll write up on the latest advancements on the other AI topics in future blog posts.</p><figure class="nk nl nm nn no np nh ni paragraph-image"><div role="button" tabindex="0" class="nq nr fl ns bh nt"><span class="fu nu nv an nw nx ny nz oa speechify-ignore">Press enter or click to view image in full size</span><div class="nh ni nj"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*CO3XhjWwoHmkfBCog-eYfg.png 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*CO3XhjWwoHmkfBCog-eYfg.png 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*CO3XhjWwoHmkfBCog-eYfg.png 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*CO3XhjWwoHmkfBCog-eYfg.png 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*CO3XhjWwoHmkfBCog-eYfg.png 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*CO3XhjWwoHmkfBCog-eYfg.png 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*CO3XhjWwoHmkfBCog-eYfg.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*CO3XhjWwoHmkfBCog-eYfg.png 640w, https://miro.medium.com/v2/resize:fit:720/1*CO3XhjWwoHmkfBCog-eYfg.png 720w, https://miro.medium.com/v2/resize:fit:750/1*CO3XhjWwoHmkfBCog-eYfg.png 750w, https://miro.medium.com/v2/resize:fit:786/1*CO3XhjWwoHmkfBCog-eYfg.png 786w, https://miro.medium.com/v2/resize:fit:828/1*CO3XhjWwoHmkfBCog-eYfg.png 828w, https://miro.medium.com/v2/resize:fit:1100/1*CO3XhjWwoHmkfBCog-eYfg.png 1100w, https://miro.medium.com/v2/resize:fit:1400/1*CO3XhjWwoHmkfBCog-eYfg.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="" class="bh ll ob c" width="700" height="400" loading="eager" role="presentation"/></picture></div></div></figure><h2 id="e548" class="oc od gw bf oe of og oh oi oj ok ol om on oo op oq or os ot ou ov ow ox oy oz bk">Why learn from ML competitions winners?</h2><p id="215b" class="pw-post-body-paragraph me mf gw mg b mh pa mj mk ml pb mn mo mp pc mr ms mt pd mv mw mx pe mz na nb gp bk">There is great value in analysing winning solutions of ML competitions. Competitions act as a battleground for participants to test out the latest research models and architectures. Or as <a class="ag nc" href="https://mlcontests.com/state-of-competitive-machine-learning-2022" rel="noopener ugc nofollow" target="_blank">ML Contests</a> puts it:</p><blockquote class="nd ne nf"><p id="fc86" class="me mf ng mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">“One way to think of competitive machine learning is as a sandbox for evaluating predictive methods.”</p></blockquote><p id="abfd" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Competitions become more useful over time as we can see trends on which ideas work, as teams incorporate techniques from winning solutions from previous competitions into their own — forming new baselines. Two great resources for reading competition write-ups by winners are <a class="ag nc" href="https://drivendata.co/blog.html" rel="noopener ugc nofollow" target="_blank">DrivenData’s blog</a> and <a class="ag nc" href="https://www.kaggle.com/discussions?sort=votes&amp;category=competitionWriteUps" rel="noopener ugc nofollow" target="_blank">Kaggle discussions</a>. Following these discussions is a useful proxy of staying on top of the latest practical advancements.</p><h3 id="3a61" class="pf od gw bf oe pg ph dy oi pi pj ea om mp pk pl pm mt pn po pp mx pq pr ps pt bk">However, do take some of the learnings with a pinch of salt</h3><p id="14ca" class="pw-post-body-paragraph me mf gw mg b mh pa mj mk ml pb mn mo mp pc mr ms mt pd mv mw mx pe mz na nb gp bk">Learning how to improve a model’s performance by a few decimal points may have a positive impact to a company’s bottom line, especially if it serving millions of customers. However, there does come a point of diminishing returns when trying to eek out that extra .0001 of performance, depending on the business context. Because of the iterative nature of ML, it can be difficult to decide when “good” is “good enough”.</p><p id="73a2" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Whilst machine learning competitions do push advancements in AI, the focus does not fully translate to the real-world professional setting. With the growing importance of <strong class="mg gx">AI ethics</strong>, many are concerned with the integrity and fairness of machine learning-based systems, services, and products.</p><p id="f14b" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Since models are judged by their performance in competitions, a metric that is easily quantified, understood by competitors, and the determinant of the ranking for prizes and accolades — it becomes the main focus. This means a result-first approach rewards black-box approaches which do not consider <strong class="mg gx">explainability</strong> and <strong class="mg gx">interpretability</strong>. This is particularly relevant for <strong class="mg gx">ensembling</strong>, more on that later.</p><p id="8baf" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">However, I still believe using the consensus of the ML community acts as a useful starting point. As it is very easy to get bogged down by analysis paralysis when there are thousands of models, tools, and packages to choose from.</p><h2 id="8bfb" class="oc od gw bf oe of og oh oi oj ok ol om on oo op oq or os ot ou ov ow ox oy oz bk">Common themes from the winners of tabular / time-series data competitions</h2><p id="b54e" class="pw-post-body-paragraph me mf gw mg b mh pa mj mk ml pb mn mo mp pc mr ms mt pd mv mw mx pe mz na nb gp bk">There are a number of common themes that regularly appear amongst winning solutions of ML competitions, across various domains such as finance to healthcare. These themes agree with findings of academic literature too which have studied these competitions. These themes include the importance of:</p><ul class=""><li id="5407" class="me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb pu pv pw bk">Feature engineering</li><li id="3173" class="me mf gw mg b mh px mj mk ml py mn mo mp pz mr ms mt qa mv mw mx qb mz na nb pu pv pw bk">Cross-validation</li><li id="cd22" class="me mf gw mg b mh px mj mk ml py mn mo mp pz mr ms mt qa mv mw mx qb mz na nb pu pv pw bk">Gradient Boosted Decision Trees</li><li id="0ba0" class="me mf gw mg b mh px mj mk ml py mn mo mp pz mr ms mt qa mv mw mx qb mz na nb pu pv pw bk">Ensembling</li></ul><h2 id="a448" class="oc od gw bf oe of og oh oi oj ok ol om on oo op oq or os ot ou ov ow ox oy oz bk">Feature engineering is still a crucial part of the ML modelling pipeline</h2><p id="4864" class="pw-post-body-paragraph me mf gw mg b mh pa mj mk ml pb mn mo mp pc mr ms mt pd mv mw mx pe mz na nb gp bk"><strong class="mg gx">Feature engineering</strong> is the <strong class="mg gx">process of creating, selecting, and transforming variables to maximise their predictive power</strong> in models. It’s a dynamic, iterative, and highly time-consuming process. Feature engineering is <a class="ag nc" href="https://www.kaggle.com/discussions/getting-started/44997" rel="noopener ugc nofollow" target="_blank">well recognised</a> as being one of the most important, if not the <a class="ag nc" href="https://www.kaggle.com/competitions/home-credit-default-risk/discussion/64821)" rel="noopener ugc nofollow" target="_blank">most important</a>, part of a tabular ML modelling pipeline, for both competitions and industry. Feature engineering is especially important for tabular data compared to deep learning techniques for computer vision tasks, where data augmentation is more focused on.</p><p id="81d5" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">In Rhys Cook’s <a class="ag nc" href="https://www.kaggle.com/code/rhysie/learnings-from-the-typical-tabular-pipeline" rel="noopener ugc nofollow" target="_blank">winning essay</a> they provided two great competition examples to illustrate how varied feature engineering approaches can be:</p><p id="c04c" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">1. <a class="ag nc" href="https://www.kaggle.com/competitions/amex-default-prediction/overview" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">Amex Credit Fault Prediction</strong></a> (the most popular ML competition of 2022 with 5,000 teams entered, likely due to the growing scarcity of tabular Kaggle competitions and the $100k prize pool)</p><p id="2aaf" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">2. <a class="ag nc" href="https://www.kaggle.com/competitions/amp-parkinsons-disease-progression-prediction/overview" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">AMP Parkinson’s Disease Progression Prediction</strong></a></p><p id="c8b3" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">In <strong class="mg gx">Amex Credit Fraud Prediction competition</strong>, the <a class="ag nc" href="https://www.kaggle.com/competitions/amex-default-prediction/discussion/347637" rel="noopener ugc nofollow" target="_blank">2nd place solution team</a> used a brute force feature engineering approach. Where they created thousands of features, such as aggregations, differences, ratio, lags, and normalisation, based on the original 190 features. Then the team applied extensive feature selection techniques: (1) removal of <strong class="mg gx">zero importance</strong> features, (2) <strong class="mg gx">stepped hierarchical permutation importance</strong>, (3) <strong class="mg gx">stepped permutation importance</strong>, (4) <strong class="mg gx">forward feature selection</strong>, and (5) <strong class="mg gx">time-series cross-validation</strong>, to obtain the final subset of 2,500 features.</p><p id="5fe1" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">For the <strong class="mg gx">Parkinson’s Disease Progression Prediction</strong> competition, the <a class="ag nc" href="https://www.kaggle.com/competitions/amp-parkinsons-disease-progression-prediction/discussion/411398" rel="noopener ugc nofollow" target="_blank">4th place solution team</a> realised that the <strong class="mg gx">curse of dimensionality</strong> applied to this dataset — as there were 1,195 features for proteins and peptides for only 248 patients in the training set, across 17 patient visit dates. This dataset had sufficient train data to train only 25 features. As a result, they focused on creating features based on the patient visit dates, rather than the protein/peptide features which were the main focus of the competition. The top 18 teams noticed that features based on patient visit dates had the best predictive ability.</p><p id="657a" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">However, <a class="ag nc" href="https://www.kaggle.com/code/rhysie/learnings-from-the-typical-tabular-pipeline" rel="noopener ugc nofollow" target="_blank">Cook</a> found that there is not much academic literature on the topic of feature engineering. This the lack of research is likely due to the fact that f<strong class="mg gx">eature engineering is very specific to the problem and dataset</strong>, and requires <strong class="mg gx">creativity and flexibility</strong>, and therefore is difficult to generalise.</p><p id="5729" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Despite this, there are resources which provide useful guidance for many domain applications, such as the <a class="ag nc" href="http://www.feat.engineering" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">Feature Engineering and Selection book</strong></a>. There are automated feature engineering solutions available such as <a class="ag nc" href="https://featuretools.alteryx.com/en/stable/" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">Featuretools</strong></a> and <a class="ag nc" href="https://neptune.ai/blog/feature-engineering-tools" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">others</strong></a>. However, most competitors tend to do it manually.</p><h2 id="955b" class="oc od gw bf oe of og oh oi oj ok ol om on oo op oq or os ot ou ov ow ox oy oz bk">Robust cross-validation to trust the model results</h2><p id="8ae6" class="pw-post-body-paragraph me mf gw mg b mh pa mj mk ml pb mn mo mp pc mr ms mt pd mv mw mx pe mz na nb gp bk"><a class="ag nc" href="https://neptune.ai/blog/cross-validation-in-machine-learning-how-to-do-it-right" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">Cross-validation (CV)</strong></a><strong class="mg gx"> </strong>is the process of evaluating a ML model and testing its performance. By training on only a subset of the training data, and measuring performance on the remaing datam we can understand how well a model generalises. It allows data scientists to know if the model is reliably improving model performance rather than overfitting to the test dataset (or public leaderboard (LB) for ML competitors).</p><p id="e4a3" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">In fact, it is common for competitors to select a final model that underperforms according to the LB but has more robust CV, in the hopes of generalising better against the private test data. However, good performance is not guaranteed on the holdout / test set, as it may be taken from a different distribution.</p><p id="3b2b" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">There are two general approaches to cross-validation:</p><p id="7a3f" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">1. Simple <strong class="mg gx">train/test split</strong> (or <strong class="mg gx">Leave-one-out CV (LOOCV)</strong>): Where you take split your shuffled training data into two splits, for example 80% for training and 20% for testing.</p><p id="38b3" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">2. <a class="ag nc" href="https://machinelearningmastery.com/k-fold-cross-validation/" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">k-Fold Cross-Validation</strong></a>: Where you split the shuffled data into k-equal groups, or folds. The model is trained k times, where one of the folds is excluded as a test set and the remaining folds are trained on, using the excluded test set to evaluate the model performance. After each evaluation, the evaluation score is retained and the model is discarded. After the training, the skill of the model is summarised using the sample of model evaluation scores.</p><p id="947b" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk"><strong class="mg gx">k-fold cross-validation</strong> is the typical go-to CV scheme of choice for ML competitors, despite being more computationally intensive than the first method. There are many different types of k-fold CV strategies available, depending on the skew of the target variable (<a class="ag nc" href="https://scikit-learn.org/stable/modules/generated/sklearn.model_selection.KFold.html#sklearn.model_selection.KFold" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">standard</strong></a>, <a class="ag nc" href="https://scikit-learn.org/stable/modules/generated/sklearn.model_selection.StratifiedKFold.html#sklearn.model_selection.StratifiedKFold" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">stratified</strong></a>, <a class="ag nc" href="https://scikit-learn.org/stable/modules/generated/sklearn.model_selection.StratifiedGroupKFold.html#sklearn.model_selection.StratifiedGroupKFold" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">stratified in groups</strong></a>, and <a class="ag nc" href="https://github.com/trent-b/iterative-stratification#multilabelstratifiedkfold" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">multi-label stratified</strong></a>) and if the data is time series which will require <strong class="mg gx">time-series cross-validation</strong>, such as <strong class="mg gx">rolling CV</strong> or <strong class="mg gx">blocked CV</strong>.</p><figure class="nk nl nm nn no np nh ni paragraph-image"><div role="button" tabindex="0" class="nq nr fl ns bh nt"><span class="fu nu nv an nw nx ny nz oa speechify-ignore">Press enter or click to view image in full size</span><div class="nh ni qc"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*RadsKK642BgQfJ1L5uCOCA.png 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*RadsKK642BgQfJ1L5uCOCA.png 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*RadsKK642BgQfJ1L5uCOCA.png 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*RadsKK642BgQfJ1L5uCOCA.png 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*RadsKK642BgQfJ1L5uCOCA.png 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*RadsKK642BgQfJ1L5uCOCA.png 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*RadsKK642BgQfJ1L5uCOCA.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*RadsKK642BgQfJ1L5uCOCA.png 640w, https://miro.medium.com/v2/resize:fit:720/1*RadsKK642BgQfJ1L5uCOCA.png 720w, https://miro.medium.com/v2/resize:fit:750/1*RadsKK642BgQfJ1L5uCOCA.png 750w, https://miro.medium.com/v2/resize:fit:786/1*RadsKK642BgQfJ1L5uCOCA.png 786w, https://miro.medium.com/v2/resize:fit:828/1*RadsKK642BgQfJ1L5uCOCA.png 828w, https://miro.medium.com/v2/resize:fit:1100/1*RadsKK642BgQfJ1L5uCOCA.png 1100w, https://miro.medium.com/v2/resize:fit:1400/1*RadsKK642BgQfJ1L5uCOCA.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="Types of cross-validation techniques — Source" class="bh ll ob c" width="700" height="436" loading="lazy"/></picture></div></div><figcaption class="qd ff qe nh ni qf qg bf b bg ab du">Types of cross-validation techniques — <a class="ag nc" href="https://www.linkedin.com/posts/danleedata_wait-you-are-telling-me-your-models-performance-activity-7057341218493493248-Xa2l" rel="noopener ugc nofollow" target="_blank">Source</a></figcaption></figure><h2 id="33d4" class="oc od gw bf oe of og oh oi oj ok ol om on oo op oq or os ot ou ov ow ox oy oz bk">Gradient Boosted Decision Trees are still beating Deep Learning models for tabular data</h2><p id="969d" class="pw-post-body-paragraph me mf gw mg b mh pa mj mk ml pb mn mo mp pc mr ms mt pd mv mw mx pe mz na nb gp bk"><strong class="mg gx">Gradient Boosted Decision Trees (GBDT)</strong>, or also known as <strong class="mg gx">Gradient Boosted Trees (GBT)</strong> or <strong class="mg gx">Gradient Boosted Machines (GBM)</strong>, are the go-to family of traditional ML models for tabular data. GBDTs even outperform <strong class="mg gx">deep learning (DL)</strong> models which were <a class="ag nc" href="https://paperswithcode.com/methods/category/deep-tabular-learning" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">purpose built for tabular data</strong></a>, especially when performance is averaged across multiple datasets.</p><h3 id="8cbf" class="pf od gw bf oe pg ph dy oi pi pj ea om mp pk pl pm mt pn po pp mx pq pr ps pt bk">Quick Recap on Gradient Boosted Decision Trees</h3><p id="f9a4" class="pw-post-body-paragraph me mf gw mg b mh pa mj mk ml pb mn mo mp pc mr ms mt pd mv mw mx pe mz na nb gp bk">GBDTs are a ML ensemble method that <strong class="mg gx">constructs a strong predictive model by combining weak learners</strong>, decision trees. The algorithm operates iteratively where each new tree corrects the errors made by the existing ensemble of trees. They are used for both classification and regression tasks.</p><p id="5a18" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">The three main GBDT models are:</p><p id="d18f" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">1. <a class="ag nc" href="https://xgboost.readthedocs.io/en/stable/" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">XGBoost</strong></a><strong class="mg gx"> (eXtreme Gradient Boosting)</strong>: Introduced in 2014 by Tianqi Chen and has been a staple of ML competition winning solutions the past 9 years (this <a class="ag nc" href="https://github.com/dmlc/xgboost/tree/master/demo#machine-learning-challenge-winning-solutions" rel="noopener ugc nofollow" target="_blank">repo</a> shows some of the many winning XGBoost solutions). Unlike standard Gradient Boosting, which starts by growing a full tree before pruning, XGBoost prunes the trees depth-first, which is more efficient. XGBoost also has inbuilt mechanisms for:</p><ul class=""><li id="846a" class="me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb pu pv pw bk"><strong class="mg gx">L1 (Lasso) and L2 (Ridge) regularization </strong>to reduce model complexity and overfitting</li><li id="d50d" class="me mf gw mg b mh px mj mk ml py mn mo mp pz mr ms mt qa mv mw mx qb mz na nb pu pv pw bk">Handling missing values</li><li id="60ad" class="me mf gw mg b mh px mj mk ml py mn mo mp pz mr ms mt qa mv mw mx qb mz na nb pu pv pw bk">Efficient k-fold cross-validation</li></ul><p id="e958" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">2. <a class="ag nc" href="https://lightgbm.readthedocs.io/" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">LightGBM</strong></a><strong class="mg gx"> (Light Gradient Boosting Machine):</strong> Introduced by Microsoft in 2016, LightGBM offers some benefits over XGBoost — having <strong class="mg gx">leaf-wise tree growth</strong>, instead of tree-level growth. This means that algorithm will choose the leaf that will reduce the most loss, rather than making all leaves in the tree level grow — resulting in more compact trees.</p><p id="8a6f" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">3. <a class="ag nc" href="https://catboost.ai/" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">CatBoost</strong></a><strong class="mg gx"> (Categorical Boosting):</strong> Introduced by Yandex in 2017, CatBoost can handle categorical features without pre-processing like <strong class="mg gx">one-hot encoding</strong>.</p><p id="a0bc" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">According to <a class="ag nc" href="https://mlcontests.com/winning-toolkit/" rel="noopener ugc nofollow" target="_blank">ML Contests</a>, <strong class="mg gx">LightGBM was the the most used GBDT library in 2022 ML competitions</strong> — mentioned in 25% of write ups or in their questionnaire, which is the same amount as CatBoost (second most used GBDT library) and XGBoost combined. Although this may change with the newly updated XGBoost 2.</p><figure class="nk nl nm nn no np nh ni paragraph-image"><div role="button" tabindex="0" class="nq nr fl ns bh nt"><span class="fu nu nv an nw nx ny nz oa speechify-ignore">Press enter or click to view image in full size</span><div class="nh ni qh"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*eXHHwwV-GNqakew0TAzTKg.png 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*eXHHwwV-GNqakew0TAzTKg.png 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*eXHHwwV-GNqakew0TAzTKg.png 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*eXHHwwV-GNqakew0TAzTKg.png 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*eXHHwwV-GNqakew0TAzTKg.png 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*eXHHwwV-GNqakew0TAzTKg.png 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*eXHHwwV-GNqakew0TAzTKg.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*eXHHwwV-GNqakew0TAzTKg.png 640w, https://miro.medium.com/v2/resize:fit:720/1*eXHHwwV-GNqakew0TAzTKg.png 720w, https://miro.medium.com/v2/resize:fit:750/1*eXHHwwV-GNqakew0TAzTKg.png 750w, https://miro.medium.com/v2/resize:fit:786/1*eXHHwwV-GNqakew0TAzTKg.png 786w, https://miro.medium.com/v2/resize:fit:828/1*eXHHwwV-GNqakew0TAzTKg.png 828w, https://miro.medium.com/v2/resize:fit:1100/1*eXHHwwV-GNqakew0TAzTKg.png 1100w, https://miro.medium.com/v2/resize:fit:1400/1*eXHHwwV-GNqakew0TAzTKg.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="Top GDBT packages. Image from ML Contests" class="bh ll ob c" width="700" height="394" loading="lazy"/></picture></div></div><figcaption class="qd ff qe nh ni qf qg bf b bg ab du">Top GDBT packages. Image from <a class="ag nc" href="https://mlcontests.com/winning-toolkit/" rel="noopener ugc nofollow" target="_blank">ML Contests</a></figcaption></figure><h3 id="498d" class="pf od gw bf oe pg ph dy oi pi pj ea om mp pk pl pm mt pn po pp mx pq pr ps pt bk">What does the research literature have to say on the debate?</h3><p id="c667" class="pw-post-body-paragraph me mf gw mg b mh pa mj mk ml pb mn mo mp pc mr ms mt pd mv mw mx pe mz na nb gp bk">There have been many papers that have compared these two broad groups of models and have come to the similar conclusions. In <a class="ag nc" href="https://arxiv.org/abs/2106.03253" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">Tabular Data: Deep Learning is Not ALl You Need (2021)</strong></a>, Shwartz-Ziv and Armon compared XGBoost and DL models for tabular data and found that <strong class="mg gx">XGBoost performed better than most DL models across all datasets</strong>, but typically a <strong class="mg gx">specific DL model would perform better at a specific dataset</strong>. They highlighted that <strong class="mg gx">XGBoost requires significantly less hyperparameter tuning</strong> to achieve good performance, which is important in the real-world setting.</p><p id="2e70" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">From the paper <a class="ag nc" href="https://arxiv.org/abs/2207.08815" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">Why do tree-based models still outperform deep learning on tabular data (2022)</strong></a>, Grinsztajn, Oyallon, and Varoquaux found that this phenomenon is especially true for medium-sized datasets (around 10k training examples) but the gap between tree-based models and deep learning lessons as dataset size increases (around 50k training examples). Also, <strong class="mg gx">tree-based models are more robust against uninformative features</strong>, as they process features independently, whereas <strong class="mg gx">deep learning methods are more prone to overfitting to noise</strong>.</p><figure class="nk nl nm nn no np nh ni paragraph-image"><div role="button" tabindex="0" class="nq nr fl ns bh nt"><span class="fu nu nv an nw nx ny nz oa speechify-ignore">Press enter or click to view image in full size</span><div class="nh ni qi"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*RMjjBhG0y0kWxA85xUOoIw.png 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*RMjjBhG0y0kWxA85xUOoIw.png 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*RMjjBhG0y0kWxA85xUOoIw.png 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*RMjjBhG0y0kWxA85xUOoIw.png 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*RMjjBhG0y0kWxA85xUOoIw.png 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*RMjjBhG0y0kWxA85xUOoIw.png 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*RMjjBhG0y0kWxA85xUOoIw.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*RMjjBhG0y0kWxA85xUOoIw.png 640w, https://miro.medium.com/v2/resize:fit:720/1*RMjjBhG0y0kWxA85xUOoIw.png 720w, https://miro.medium.com/v2/resize:fit:750/1*RMjjBhG0y0kWxA85xUOoIw.png 750w, https://miro.medium.com/v2/resize:fit:786/1*RMjjBhG0y0kWxA85xUOoIw.png 786w, https://miro.medium.com/v2/resize:fit:828/1*RMjjBhG0y0kWxA85xUOoIw.png 828w, https://miro.medium.com/v2/resize:fit:1100/1*RMjjBhG0y0kWxA85xUOoIw.png 1100w, https://miro.medium.com/v2/resize:fit:1400/1*RMjjBhG0y0kWxA85xUOoIw.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="Benchmark performance comparisons between GBDTs and deep neural networks, taken from Why do tree-based models still outperform deep learning on tabular data (2022)" class="bh ll ob c" width="700" height="314" loading="lazy"/></picture></div></div><figcaption class="qd ff qe nh ni qf qg bf b bg ab du">Benchmark performance comparisons between GBDTs and deep neural networks, taken from <a class="ag nc" href="https://arxiv.org/abs/2207.08815" rel="noopener ugc nofollow" target="_blank">Why do tree-based models still outperform deep learning on tabular data (2022)</a></figcaption></figure><p id="6459" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">All that being said, neural networks can provide benefits if the tabular data has some <strong class="mg gx">underlying structure</strong> well suited for neural network architectures, such as <strong class="mg gx">sequenced or hierarchical data</strong>. For example, the famous <strong class="mg gx">time-series M-competitions</strong> have recently been dominated by deep learning models. This <a class="ag nc" href="https://sebastianraschka.com/blog/2022/deep-learning-for-tabular-data.html" rel="noopener ugc nofollow" target="_blank">blog post</a> and <a class="ag nc" href="https://arxiv.org/pdf/2004.10240.pdf" rel="noopener ugc nofollow" target="_blank">paper by Amazon Research</a> are great starting points to get a comprehensive overview on the pivotal advancements in DL for tabular data and time-series, respectively.</p><h2 id="a4fe" class="oc od gw bf oe of og oh oi oj ok ol om on oo op oq or os ot ou ov ow ox oy oz bk">Ensembling for performance gains</h2><p id="e09d" class="pw-post-body-paragraph me mf gw mg b mh pa mj mk ml pb mn mo mp pc mr ms mt pd mv mw mx pe mz na nb gp bk">Although deep learning has not replaced GBDTs for tabular data, they do provide value when ensembled with boosting models. <a class="ag nc" href="https://neptune.ai/blog/ensemble-learning-guide" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">Ensembling</strong></a> is when multiple different machine learning models are combined to improve the overall prediction performance. This <strong class="mg gx">works best when diverse models are combined</strong> as collectively they can <strong class="mg gx">offset each other when individual models make uncorrelated errors</strong>.</p><p id="cf0c" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Almost all competition winning solutions involve a large ensemble of models, rather than a single model (although it does happen rarely). Kaggle is known as an ensembling playground, where participants will ensemble many different models to improve their score. However, this comes at a <strong class="mg gx">cost of computational resources, complexity, explainability, and inference time</strong> — which are important considerations in a real-world applications.</p><p id="e781" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Model outputs are typically ensembled / aggregated by:</p><p id="6350" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">1. <strong class="mg gx">Averaging</strong> (or <strong class="mg gx">blending</strong>): This is the simplest method of ensembling, options include averaging, <strong class="mg gx">weighted average</strong> (most common method), and rank averaging.</p><p id="4588" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">2. <a class="ag nc" href="https://scikit-learn.org/stable/modules/ensemble.html#voting-classifier" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">Voting</strong></a>: For classifications tasks, models can vote on the prediction. The most common option is majority voting, where class is predicted by the selection of the majority of models.</p><p id="03d6" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">3. <strong class="mg gx">Bagging, boosting, and stacking</strong>:</p><ul class=""><li id="d986" class="me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb pu pv pw bk"><a class="ag nc" href="https://scikit-learn.org/stable/modules/ensemble.html#bagging-meta-estimator)** (short for **bootstrap aggregating" rel="noopener ugc nofollow" target="_blank">Bagging</a> (short for <strong class="mg gx">bootstrap aggregating</strong>) is when you sample with replacement to create different datasets called bootstraps and then a different model on each of those bootstraps, and then aggregate the results — by averaging for regression or voting for classification.</li><li id="2f6b" class="me mf gw mg b mh px mj mk ml py mn mo mp pz mr ms mt qa mv mw mx qb mz na nb pu pv pw bk"><strong class="mg gx">Boosting</strong> is a family of iterative optimum algorithms that converts weak learners to strong learners. Each learner in the ensemble is trained on the same set of samples but samples are weighted based on how well the ensemble classifies them.</li><li id="85a8" class="me mf gw mg b mh px mj mk ml py mn mo mp pz mr ms mt qa mv mw mx qb mz na nb pu pv pw bk"><a class="ag nc" href="https://scikit-learn.org/stable/modules/ensemble.html#stacked-generalization" rel="noopener ugc nofollow" target="_blank"><strong class="mg gx">Stacking</strong></a> is the process of training base learners from the training data and then create a meta learner that combines the output of the base learners to output final predictions.</li></ul><h2 id="1f79" class="oc od gw bf oe of og oh oi oj ok ol om on oo op oq or os ot ou ov ow ox oy oz bk">Conclusion</h2><p id="2825" class="pw-post-body-paragraph me mf gw mg b mh pa mj mk ml pb mn mo mp pc mr ms mt pd mv mw mx pe mz na nb gp bk">It’s difficult staying on top of the latest advancements in AI and ML but the Kaggle AI Report 2023 and the State of Competitive Machine Learning Report provide invaluable insights, especially for tabular and time-series data. This blog post highlighted the continued importance of feature engineering and robust cross-validation, the unassailable effectiveness of GBDTs over DL models for tabular data, and the power of ensembling to improve performance.</p><p id="b19a" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">However, it is important to recognise that competition settings has nuances and different priorities that does not directly translate to real-world applications, especially with the growing importance of explainable AI. In reality, you most of your time will likely be on data cleaning or converting your notebook code into production with MLOps (a topic I’ll cover in future).</p><p id="9909" class="pw-post-body-paragraph me mf gw mg b mh mi mj mk ml mm mn mo mp mq mr ms mt mu mv mw mx my mz na nb gp bk">Thanks for reading if you’ve made it this far, I hope you’ve found it useful!</p></div></div></div></div></section></div></div></article></div><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="qj qk ac it"><div class="ql ac"><a class="qm aj an ap" rel="noopener follow" href="/tag/ai?source=post_page-----4ed5bf765d8d---------------------------------------" data-discover="true"><div class="qn fl cx qo gg qp qq bf b bg ab bk nz">AI</div></a></div><div class="ql ac"><a class="qm aj an ap" rel="noopener follow" href="/tag/machine-learning?source=post_page-----4ed5bf765d8d---------------------------------------" data-discover="true"><div class="qn fl cx qo gg qp qq bf b bg ab bk nz">Machine Learning</div></a></div><div class="ql ac"><a class="qm aj an ap" rel="noopener follow" href="/tag/data-science?source=post_page-----4ed5bf765d8d---------------------------------------" data-discover="true"><div class="qn fl cx qo gg qp qq bf b bg ab bk nz">Data Science</div></a></div><div class="ql ac"><a class="qm aj an ap" rel="noopener follow" href="/tag/data?source=post_page-----4ed5bf765d8d---------------------------------------" data-discover="true"><div class="qn fl cx qo gg qp qq bf b bg ab bk nz">Data</div></a></div><div class="ql ac"><a class="qm aj an ap" rel="noopener follow" href="/tag/artificial-intelligence?source=post_page-----4ed5bf765d8d---------------------------------------" data-discover="true"><div class="qn fl cx qo gg qp qq bf b bg ab bk nz">Artificial Intelligence</div></a></div></div></div></div><div class="m"></div><footer class="qr qs qt qu qv ac r qw qx c"><div class="m af"><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="ac cp qy"><div class="ac r kc"><div class="qz m"><span class="m ra rb rc f e"><div class="ac r kc kd"><div class="pw-multi-vote-icon fl ke kf kg kh"><span data-dd-action-name="Susi presentation tracker clap_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="footerClapButton" rel="noopener follow" href="/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fp%2F4ed5bf765d8d&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2F%40vince-lam%2Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d&amp;user=Vince+Lam&amp;userId=ce24b5d6cbb&amp;source=---footer_actions--4ed5bf765d8d---------------------clap_footer------------------" data-discover="true"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><div class="ki ap kj kk kl km an kn ko kp kh" role="presentation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="clap"><path fill-rule="evenodd" d="M11.37.828 12 3.282l.63-2.454zM13.916 3.953l1.523-2.112-1.184-.39zM8.589 1.84l1.522 2.112-.337-2.501zM18.523 18.92c-.86.86-1.75 1.246-2.62 1.33a6 6 0 0 0 .407-.372c2.388-2.389 2.86-4.951 1.399-7.623l-.912-1.603-.79-1.672c-.26-.56-.194-.98.203-1.288a.7.7 0 0 1 .546-.132c.283.046.546.231.728.5l2.363 4.157c.976 1.624 1.141 4.237-1.324 6.702m-10.999-.438L3.37 14.328a.828.828 0 0 1 .585-1.408.83.83 0 0 1 .585.242l2.158 2.157a.365.365 0 0 0 .516-.516l-2.157-2.158-1.449-1.449a.826.826 0 0 1 1.167-1.17l3.438 3.44a.363.363 0 0 0 .516 0 .364.364 0 0 0 0-.516L5.293 9.513l-.97-.97a.826.826 0 0 1 0-*********** 0 0 1 1.167 0l.97.968 3.437 3.436a.36.36 0 0 0 .517 0 .366.366 0 0 0 0-.516L6.977 7.83a.82.82 0 0 1-.241-.584.82.82 0 0 1 .824-.826c.219 0 .43.087.584.242l5.787 5.787a.366.366 0 0 0 .587-.415l-1.117-2.363c-.26-.56-.194-.98.204-1.289a.7.7 0 0 1 .546-.132c.283.046.545.232.727.501l2.193 3.86c1.302 2.38.883 4.59-1.277 6.75-1.156 1.156-2.602 1.627-4.19 1.367-1.418-.236-2.866-1.033-4.079-2.246M10.75 5.971l2.12 2.12c-.41.502-.465 1.17-.128 1.89l.22.465-3.523-3.523a.8.8 0 0 1-.097-.368c0-.22.086-.428.241-.584a.847.847 0 0 1 1.167 0m7.355 1.705c-.31-.461-.746-.758-1.23-.837a1.44 1.44 0 0 0-1.11.275c-.312.24-.505.543-.59.881a1.74 1.74 0 0 0-.906-.465 1.47 1.47 0 0 0-.82.106l-2.182-2.182a1.56 1.56 0 0 0-2.2 0 1.54 1.54 0 0 0-.396.701 1.56 1.56 0 0 0-2.21-.01 1.55 1.55 0 0 0-.416.753c-.624-.624-1.649-.624-2.237-.037a1.557 1.557 0 0 0 0 2.2c-.239.1-.501.238-.715.453a1.56 1.56 0 0 0 0 2.2l.516.515a1.556 1.556 0 0 0-.753 2.615L7.01 19c1.32 1.319 2.909 2.189 4.475 2.449q.482.08.971.08c.85 0 1.653-.198 2.393-.579.**************.686.054 1.266 0 2.457-.52 3.505-1.567 2.763-2.763 2.552-5.734 1.439-7.586z" clip-rule="evenodd"></path></svg></div></div></div></div></a></span></div><div class="pw-multi-vote-count m kq kr ks kt ku kv kw"><p class="bf b dv ab du"><span class="kx">--</span></p></div></div></span><span class="m i h g rd re"><div class="ac r kc kd"><div class="pw-multi-vote-icon fl ke kf kg kh"><span data-dd-action-name="Susi presentation tracker clap_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="footerClapButton" rel="noopener follow" href="/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fp%2F4ed5bf765d8d&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2F%40vince-lam%2Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d&amp;user=Vince+Lam&amp;userId=ce24b5d6cbb&amp;source=---footer_actions--4ed5bf765d8d---------------------clap_footer------------------" data-discover="true"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><div class="ki ap kj kk kl km an kn ko kp kh" role="presentation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="clap"><path fill-rule="evenodd" d="M11.37.828 12 3.282l.63-2.454zM13.916 3.953l1.523-2.112-1.184-.39zM8.589 1.84l1.522 2.112-.337-2.501zM18.523 18.92c-.86.86-1.75 1.246-2.62 1.33a6 6 0 0 0 .407-.372c2.388-2.389 2.86-4.951 1.399-7.623l-.912-1.603-.79-1.672c-.26-.56-.194-.98.203-1.288a.7.7 0 0 1 .546-.132c.283.046.546.231.728.5l2.363 4.157c.976 1.624 1.141 4.237-1.324 6.702m-10.999-.438L3.37 14.328a.828.828 0 0 1 .585-1.408.83.83 0 0 1 .585.242l2.158 2.157a.365.365 0 0 0 .516-.516l-2.157-2.158-1.449-1.449a.826.826 0 0 1 1.167-1.17l3.438 3.44a.363.363 0 0 0 .516 0 .364.364 0 0 0 0-.516L5.293 9.513l-.97-.97a.826.826 0 0 1 0-*********** 0 0 1 1.167 0l.97.968 3.437 3.436a.36.36 0 0 0 .517 0 .366.366 0 0 0 0-.516L6.977 7.83a.82.82 0 0 1-.241-.584.82.82 0 0 1 .824-.826c.219 0 .43.087.584.242l5.787 5.787a.366.366 0 0 0 .587-.415l-1.117-2.363c-.26-.56-.194-.98.204-1.289a.7.7 0 0 1 .546-.132c.283.046.545.232.727.501l2.193 3.86c1.302 2.38.883 4.59-1.277 6.75-1.156 1.156-2.602 1.627-4.19 1.367-1.418-.236-2.866-1.033-4.079-2.246M10.75 5.971l2.12 2.12c-.41.502-.465 1.17-.128 1.89l.22.465-3.523-3.523a.8.8 0 0 1-.097-.368c0-.22.086-.428.241-.584a.847.847 0 0 1 1.167 0m7.355 1.705c-.31-.461-.746-.758-1.23-.837a1.44 1.44 0 0 0-1.11.275c-.312.24-.505.543-.59.881a1.74 1.74 0 0 0-.906-.465 1.47 1.47 0 0 0-.82.106l-2.182-2.182a1.56 1.56 0 0 0-2.2 0 1.54 1.54 0 0 0-.396.701 1.56 1.56 0 0 0-2.21-.01 1.55 1.55 0 0 0-.416.753c-.624-.624-1.649-.624-2.237-.037a1.557 1.557 0 0 0 0 2.2c-.239.1-.501.238-.715.453a1.56 1.56 0 0 0 0 2.2l.516.515a1.556 1.556 0 0 0-.753 2.615L7.01 19c1.32 1.319 2.909 2.189 4.475 2.449q.482.08.971.08c.85 0 1.653-.198 2.393-.579.**************.686.054 1.266 0 2.457-.52 3.505-1.567 2.763-2.763 2.552-5.734 1.439-7.586z" clip-rule="evenodd"></path></svg></div></div></div></div></a></span></div><div class="pw-multi-vote-count m kq kr ks kt ku kv kw"><p class="bf b dv ab du"><span class="kx">--</span></p></div></div></span></div><div class="ay ac"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button class="ap ki ky kz ac r fm la lb" aria-label="responses"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="lc"><path d="M18.006 16.803c1.533-1.456 2.234-3.325 2.234-5.321C20.24 7.357 16.709 4 12.191 4S4 7.357 4 11.482c0 4.126 3.674 7.482 8.191 7.482.817 0 1.622-.111 2.393-.327.231.2.48.391.744.559 1.06.693 2.203 1.044 3.399 1.044.224-.008.4-.112.486-.287a.49.49 0 0 0-.042-.518c-.495-.67-.845-1.364-1.04-2.057a4 4 0 0 1-.125-.598zm-3.122 1.055-.067-.223-.315.096a8 8 0 0 1-2.311.338c-4.023 0-7.292-2.955-7.292-6.587 0-3.633 3.269-6.588 7.292-6.588 4.014 0 7.112 2.958 7.112 6.593 0 1.794-.608 3.469-2.027 4.72l-.195.168v.255c0 .056 0 .151.016.295.025.231.081.478.154.733.154.558.398 1.117.722 1.659a5.3 5.3 0 0 1-2.165-.845c-.276-.176-.714-.383-.941-.59z"></path></svg></button></div></div></div></div></div><div class="ac r"><div class="rf m rg"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><span data-dd-action-name="Susi presentation tracker bookmark_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="footerBookmarkButton" rel="noopener follow" href="/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2F4ed5bf765d8d&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2F%40vince-lam%2Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d&amp;source=---footer_actions--4ed5bf765d8d---------------------bookmark_footer------------------" data-discover="true"><svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="none" viewBox="0 0 25 25" class="du le" aria-label="Add to list bookmark button"><path fill="currentColor" d="M18 2.5a.5.5 0 0 1 1 0V5h2.5a.5.5 0 0 1 0 1H19v2.5a.5.5 0 1 1-1 0V6h-2.5a.5.5 0 0 1 0-1H18zM7 7a1 1 0 0 1 1-1h3.5a.5.5 0 0 0 0-1H8a2 2 0 0 0-2 2v14a.5.5 0 0 0 .805.396L12.5 17l5.695 4.396A.5.5 0 0 0 19 21v-8.5a.5.5 0 0 0-1 0v7.485l-5.195-4.012a.5.5 0 0 0-.61 0L7 19.985z"></path></svg></a></span></div></div></div></div><div class="rf m rg"><div class="bm" aria-hidden="false" aria-describedby="postFooterSocialMenu" aria-labelledby="postFooterSocialMenu"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button aria-controls="postFooterSocialMenu" aria-expanded="false" aria-label="Share Post" data-testid="footerSocialShareButton" class="ag fm ai fh ak al am lm ao ap aq ex ln lo lb lp"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M15.218 4.931a.4.4 0 0 1-.118.132l.012.006a.45.45 0 0 1-.292.074.5.5 0 0 1-.3-.13l-2.02-2.02v7.07c0 .28-.23.5-.5.5s-.5-.22-.5-.5v-7.04l-2 2a.45.45 0 0 1-.57.04h-.02a.4.4 0 0 1-.16-.3.4.4 0 0 1 .1-.32l2.8-2.8a.5.5 0 0 1 .7 0l2.8 2.79a.42.42 0 0 1 .068.498m-.106.138.008.004v-.01zM16 7.063h1.5a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-11c-1.1 0-2-.9-2-2v-10a2 2 0 0 1 2-2H8a.5.5 0 0 1 .********* 0 0 1 .********* 0 0 1-.********* 0 0 1-.35.15H6.4c-.5 0-.9.4-.9.9v10.2a.9.9 0 0 0 .9.9h11.2c.5 0 .9-.4.9-.9v-10.2c0-.5-.4-.9-.9-.9H16a.5.5 0 0 1 0-1" clip-rule="evenodd"></path></svg></button></div></div></div></div></div></div></div></div></div></div></footer><div class="rh m"><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="ac ig ie ic ri rj"><div class="rk rl rm rn ro rp rq rr rs rt ac cp"><div class="i l"><a tabindex="0" rel="noopener follow" href="/@vince-lam?source=post_page---post_author_info--4ed5bf765d8d---------------------------------------" data-discover="true"><div class="m fl"><img alt="Vince Lam" class="m fd bx ru rv cx" src="https://miro.medium.com/v2/resize:fill:96:96/1*FdY_N_dCL4n7tdWO_FJrng.png" width="48" height="48" loading="lazy"/><div class="ft bx m ru rv fu o aj rw"></div></div></a></div><div class="k j e"><a tabindex="0" rel="noopener follow" href="/@vince-lam?source=post_page---post_author_info--4ed5bf765d8d---------------------------------------" data-discover="true"><div class="m fl"><img alt="Vince Lam" class="m fd bx rx ry cx" src="https://miro.medium.com/v2/resize:fill:128:128/1*FdY_N_dCL4n7tdWO_FJrng.png" width="64" height="64" loading="lazy"/><div class="ft bx m rx ry fu o aj rw"></div></div></a></div><div class="k j e rz rg"><div class="ac"></div></div></div><div class="ac co ca"><div class="sa sb sc sd se m"><a class="ag ah ai ak al am an ao ap aq ar as at au ac r" rel="noopener follow" href="/@vince-lam?source=post_page---post_author_info--4ed5bf765d8d---------------------------------------" data-discover="true"><h2 class="pw-author-name bf sg sh si sj sk sl sm mp pl pm mt po pp mx pr ps bk"><span class="gp sf">Written by <!-- -->Vince Lam</span></h2></a><div class="ql ac ii"><div class="m rg"><span class="pw-follower-count bf b bg ab du"><a class="ag ah ai fh ak al am an ao ap aq ar as ir" rel="noopener follow" href="/@vince-lam/followers?source=post_page---post_author_info--4ed5bf765d8d---------------------------------------" data-discover="true">366 followers</a></span></div><div class="bf b bg ab du ac sn"><span class="so m" aria-hidden="true"><span class="bf b bg ab du">·</span></span><a class="ag ah ai fh ak al am an ao ap aq ar as ir" rel="noopener follow" href="/@vince-lam/following?source=post_page---post_author_info--4ed5bf765d8d---------------------------------------" data-discover="true">51 following</a></div></div><div class="sp m"><p class="bf b bg ab bk">Data Scientist living abroad in Singapore 🇸🇬 Sharing tips &amp; insights on AI &amp; data science to boost our professional and personal lives. <a class="ag ah ai fh ak al am an ao ap aq ar as nc gq gp" href="https://vinlam.com" rel="noopener  ugc nofollow">https://vinlam.com</a></p></div></div></div><div class="i l"><div class="ac"></div></div></div></div></div></div><div class="sq m"><div class="sr bh s rh"></div><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="ac r cp"><h2 class="bf sg of oh oi oj ol om on op oq or ot ou ov ox oy bk">No responses yet</h2><div class="ac ss"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><a class="st su" href="https://policy.medium.com/medium-rules-30e5502c4eb4?source=post_page---post_responses--4ed5bf765d8d---------------------------------------" rel="noopener follow" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" aria-label="Shield with a checkmark" viewBox="0 0 25 25"><path fill-rule="evenodd" d="M11.987 5.036a.754.754 0 0 1 .914-.01c.972.721 1.767 1.218 2.6 1.543.828.322 1.719.485 2.887.505a.755.755 0 0 1 .741.757c-.018 3.623-.43 6.256-1.449 8.21-1.034 1.984-2.662 3.209-4.966 4.083a.75.75 0 0 1-.537-.003c-2.243-.874-3.858-2.095-4.897-4.074-1.024-1.951-1.457-4.583-1.476-8.216a.755.755 0 0 1 .741-.757c1.195-.02 2.1-.182 2.923-.503.827-.322 1.6-.815 2.519-1.535m.468.903c-.897.69-1.717 1.21-2.623 1.564-.898.35-1.856.527-3.026.565.037 3.45.469 5.817 1.36 7.515.884 1.684 2.25 2.762 4.284 3.571 2.092-.81 3.465-1.89 4.344-3.575.886-1.698 1.299-4.065 1.334-7.512-1.149-.039-2.091-.217-2.99-.567-.906-.353-1.745-.873-2.683-1.561m-.009 9.155a2.672 2.672 0 1 0 0-5.344 2.672 2.672 0 0 0 0 5.344m0 1a3.672 3.672 0 1 0 0-7.344 3.672 3.672 0 0 0 0 7.344m-1.813-3.777.525-.526.916.917 1.623-1.625.526.526-2.149 2.152z" clip-rule="evenodd"></path></svg></a></div></div></div></div></div><div class="sv sw sx sy sz m"></div></div></div></div><div class="ta tb tc td te m bw"><div class="i l k"><div class="sr bh tf tg"></div><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="th ac kc it"><div class="ti tj m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://help.medium.com/hc/en-us?source=post_page-----4ed5bf765d8d---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Help</p></a></div><div class="ti tj m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://medium.statuspage.io/?source=post_page-----4ed5bf765d8d---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Status</p></a></div><div class="ti tj m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" rel="noopener follow" href="/about?autoplay=1&amp;source=post_page-----4ed5bf765d8d---------------------------------------" data-discover="true"><p class="bf b dv ab du">About</p></a></div><div class="ti tj m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" rel="noopener follow" href="/jobs-at-medium/work-at-medium-959d1a85284e?source=post_page-----4ed5bf765d8d---------------------------------------" data-discover="true"><p class="bf b dv ab du">Careers</p></a></div><div class="ti tj m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="mailto:<EMAIL>" rel="noopener follow"><p class="bf b dv ab du">Press</p></a></div><div class="ti tj m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://blog.medium.com/?source=post_page-----4ed5bf765d8d---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Blog</p></a></div><div class="ti tj m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://policy.medium.com/medium-privacy-policy-f03bf92035c9?source=post_page-----4ed5bf765d8d---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Privacy</p></a></div><div class="ti tj m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://policy.medium.com/medium-rules-30e5502c4eb4?source=post_page-----4ed5bf765d8d---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Rules</p></a></div><div class="ti tj m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://policy.medium.com/medium-terms-of-service-9db0094a1e0f?source=post_page-----4ed5bf765d8d---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Terms</p></a></div><div class="ti m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://speechify.com/medium?source=post_page-----4ed5bf765d8d---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Text to speech</p></a></div></div></div></div></div></div></div></div></div></div></div></div><script>window.__BUILD_ID__="main-********-185947-c929e6f698"</script><script>window.__GRAPHQL_URI__ = "https://medium.com/_/graphql"</script><script>window.__PRELOADED_STATE__ = {"algolia":{"queries":{}},"cache":{"experimentGroupSet":true,"reason":"","group":"enabled","tags":["group-edgeCachePosts","post-4ed5bf765d8d","user-ce24b5d6cbb","group-newLoNonMocUpsellExperimentGroup-group2"],"serverVariantState":"44136fa355b3678a1146ad16f7e8649e94fb4fc21fe77e8310c060f61caaff8a","middlewareEnabled":true,"cacheStatus":"DYNAMIC","shouldUseCache":true,"vary":[],"pubFeaturingPostPageLabelEnabled":false,"shouldFollowPostQueryEnabled":false,"newLoNonMocUpsellExperimentGroup":"group2"},"client":{"hydrated":false,"isUs":false,"isNativeMedium":false,"isSafariMobile":false,"isSafari":false,"isFirefox":false,"routingEntity":{"type":"DEFAULT","explicit":false},"viewerIsBot":false},"debug":{"requestId":"8ed38d9d-d49a-421a-ab7a-e0c1a1a7e410","requestTag":"","hybridDevServices":[],"originalSpanCarrier":{"traceparent":"00-95970864c2f1a5dde8730610ec914aa7-04f8978b0ca1069d-01"}},"multiVote":{"clapsPerPost":{}},"navigation":{"branch":{"show":null,"hasRendered":null,"blockedByCTA":false},"hideGoogleOneTap":false,"hasRenderedAlternateUserBanner":null,"currentLocation":"https:\u002F\u002Fmedium.com\u002F@vince-lam\u002Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d","host":"medium.com","hostname":"medium.com","referrer":"","hasSetReferrer":false,"susiModal":{"step":null,"operation":"register"},"postRead":false,"partnerProgram":{"selectedCountryCode":null},"staticRouterContext":{"route":{"name":"ShowPostUnderUser"},"statusCode":200},"toastQueue":[],"currentToast":null,"queryString":"","currentHash":""},"config":{"nodeEnv":"production","version":"main-********-185947-c929e6f698","target":"production","productName":"Medium","publicUrl":"https:\u002F\u002Fcdn-client.medium.com\u002Flite","authDomain":"medium.com","authGoogleClientId":"216296035834-k1k6qe060s2tp2a2jam4ljdcms00sttg.apps.googleusercontent.com","favicon":"production","iosAppId":"828256236","glyphUrl":"https:\u002F\u002Fglyph.medium.com","branchKey":"key_live_ofxXr2qTrrU9NqURK8ZwEhknBxiI6KBm","algolia":{"appId":"MQ57UUUQZ2","apiKeySearch":"********************************","indexPrefix":"medium_","host":"-dsn.algolia.net"},"recaptchaKey":"6Lfc37IUAAAAAKGGtC6rLS13R1Hrw_BqADfS1LRk","recaptcha3Key":"6Lf8R9wUAAAAABMI_85Wb8melS7Zj6ziuf99Yot5","recaptchaEnterpriseKeyId":"6Le-uGgpAAAAAPprRaokM8AKthQ9KNGdoxaGUvVp","datadog":{"applicationId":"6702d87d-a7e0-42fe-bbcb-95b469547ea0","clientToken":"pub853ea8d17ad6821d9f8f11861d23dfed","rumToken":"pubf9cc52896502b9413b68ba36fc0c7162","context":{"deployment":{"target":"production","tag":"main-********-185947-c929e6f698","commit":"c929e6f698a22d0c3be06d191c5c594e2e313e6d"}},"datacenter":"us"},"googleAdsCode":"AW-17106321204","googleAnalyticsCode":"G-7JY7T788PK","googlePay":{"apiVersion":"2","apiVersionMinor":"0","merchantId":"BCR2DN6TV7EMTGBM","merchantName":"Medium","instanceMerchantId":"13685562959212738550"},"applePay":{"version":3},"signInWallCustomDomainCollectionIds":["3a8144eabfe3","336d898217ee","61061eb0c96b","138adf9c44c","819cc2aaeee0"],"mediumMastodonDomainName":"me.dm","mediumOwnedAndOperatedCollectionIds":["8a9336e5bb4","b7e45b22fec3","193b68bd4fba","8d6b8a439e32","54c98c43354d","3f6ecf56618","d944778ce714","92d2092dc598","ae2a65f35510","1285ba81cada","544c7006046e","fc8964313712","40187e704f1c","88d9857e584e","7b6769f2748b","bcc38c8f6edf","cef6983b292","cb8577c9149e","444d13b52878","713d7dbc99b0","ef8e90590e66","191186aaafa0","55760f21cdc5","9dc80918cc93","bdc4052bbdba","8ccfed20cbb2"],"tierOneDomains":["medium.com","thebolditalic.com","arcdigital.media","towardsdatascience.com","uxdesign.cc","codeburst.io","psiloveyou.xyz","writingcooperative.com","entrepreneurshandbook.co","prototypr.io","betterhumans.coach.me","theascent.pub"],"topicsToFollow":["d61cf867d93f","8a146bc21b28","1eca0103fff3","4d562ee63426","aef1078a3ef5","e15e46793f8d","6158eb913466","55f1c20aba7a","3d18b94f6858","4861fee224fd","63c6f1f93ee","1d98b3a9a871","decb52b64abf","ae5d4995e225","830cded25262"],"topicToTagMappings":{"accessibility":"accessibility","addiction":"addiction","android-development":"android-development","art":"art","artificial-intelligence":"artificial-intelligence","astrology":"astrology","basic-income":"basic-income","beauty":"beauty","biotech":"biotech","blockchain":"blockchain","books":"books","business":"business","cannabis":"cannabis","cities":"cities","climate-change":"climate-change","comics":"comics","coronavirus":"coronavirus","creativity":"creativity","cryptocurrency":"cryptocurrency","culture":"culture","cybersecurity":"cybersecurity","data-science":"data-science","design":"design","digital-life":"digital-life","disability":"disability","economy":"economy","education":"education","equality":"equality","family":"family","feminism":"feminism","fiction":"fiction","film":"film","fitness":"fitness","food":"food","freelancing":"freelancing","future":"future","gadgets":"gadgets","gaming":"gaming","gun-control":"gun-control","health":"health","history":"history","humor":"humor","immigration":"immigration","ios-development":"ios-development","javascript":"javascript","justice":"justice","language":"language","leadership":"leadership","lgbtqia":"lgbtqia","lifestyle":"lifestyle","machine-learning":"machine-learning","makers":"makers","marketing":"marketing","math":"math","media":"media","mental-health":"mental-health","mindfulness":"mindfulness","money":"money","music":"music","neuroscience":"neuroscience","nonfiction":"nonfiction","outdoors":"outdoors","parenting":"parenting","pets":"pets","philosophy":"philosophy","photography":"photography","podcasts":"podcast","poetry":"poetry","politics":"politics","privacy":"privacy","product-management":"product-management","productivity":"productivity","programming":"programming","psychedelics":"psychedelics","psychology":"psychology","race":"race","relationships":"relationships","religion":"religion","remote-work":"remote-work","san-francisco":"san-francisco","science":"science","self":"self","self-driving-cars":"self-driving-cars","sexuality":"sexuality","social-media":"social-media","society":"society","software-engineering":"software-engineering","space":"space","spirituality":"spirituality","sports":"sports","startups":"startup","style":"style","technology":"technology","transportation":"transportation","travel":"travel","true-crime":"true-crime","tv":"tv","ux":"ux","venture-capital":"venture-capital","visual-design":"visual-design","work":"work","world":"world","writing":"writing"},"defaultImages":{"avatar":{"imageId":"1*dmbNkD5D-u45r44go_cf0g.png","height":150,"width":150},"orgLogo":{"imageId":"7*V1_7XP4snlmqrc_0Njontw.png","height":110,"width":500},"postLogo":{"imageId":"167cff2a3d17ac1e64d0762539978f2d54c0058886e8b3c8a03a725a83012ec0","height":630,"width":1200},"postPreviewImage":{"imageId":"bc1f8416df0cad099e43cda2872716e5864f18a73bda2a7547ea082aca9b5632","height":630,"width":1200}},"embeddedPostIds":{"coronavirus":"cd3010f9d81f"},"sharedCdcMessaging":{"COVID_APPLICABLE_TAG_SLUGS":[],"COVID_APPLICABLE_TOPIC_NAMES":[],"COVID_APPLICABLE_TOPIC_NAMES_FOR_TOPIC_PAGE":[],"COVID_MESSAGES":{"tierA":{"text":"For more information on the novel coronavirus and Covid-19, visit cdc.gov.","markups":[{"start":66,"end":73,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]},"tierB":{"text":"Anyone can publish on Medium per our Policies, but we don’t fact-check every story. For more info about the coronavirus, see cdc.gov.","markups":[{"start":37,"end":45,"href":"https:\u002F\u002Fhelp.medium.com\u002Fhc\u002Fen-us\u002Fcategories\u002F201931128-Policies-Safety"},{"start":125,"end":132,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]},"paywall":{"text":"This article has been made free for everyone, thanks to Medium Members. For more information on the novel coronavirus and Covid-19, visit cdc.gov.","markups":[{"start":56,"end":70,"href":"https:\u002F\u002Fmedium.com\u002Fmembership"},{"start":138,"end":145,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]},"unbound":{"text":"This article is free for everyone, thanks to Medium Members. For more information on the novel coronavirus and Covid-19, visit cdc.gov.","markups":[{"start":45,"end":59,"href":"https:\u002F\u002Fmedium.com\u002Fmembership"},{"start":127,"end":134,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]}},"COVID_BANNER_POST_ID_OVERRIDE_WHITELIST":["3b31a67bff4a"]},"sharedVoteMessaging":{"TAGS":["politics","election-2020","government","us-politics","election","2020-presidential-race","trump","donald-trump","democrats","republicans","congress","republican-party","democratic-party","biden","joe-biden","maga"],"TOPICS":["politics","election"],"MESSAGE":{"text":"Find out more about the U.S. election results here.","markups":[{"start":46,"end":50,"href":"https:\u002F\u002Fcookpolitical.com\u002F2020-national-popular-vote-tracker"}]},"EXCLUDE_POSTS":["397ef29e3ca5"]},"embedPostRules":[],"recircOptions":{"v1":{"limit":3},"v2":{"limit":8}},"braintreeClientKey":"production_zjkj96jm_m56f8fqpf7ngnrd4","braintree":{"enabled":true,"merchantId":"m56f8fqpf7ngnrd4","merchantAccountId":{"usd":"AMediumCorporation_instant","eur":"amediumcorporation_EUR","cad":"amediumcorporation_CAD"},"publicKey":"ds2nn34bg2z7j5gd","braintreeEnvironment":"production","dashboardUrl":"https:\u002F\u002Fwww.braintreegateway.com\u002Fmerchants","gracePeriodDurationInDays":14,"mediumMembershipPlanId":{"monthly":"ce105f8c57a3","monthlyV2":"e8a5e126-792b-4ee6-8fba-d574c1b02fc5","monthlyWithTrial":"d5ee3dbe3db8","monthlyPremium":"fa741a9b47a2","yearly":"a40ad4a43185","yearlyV2":"3815d7d6-b8ca-4224-9b8c-182f9047866e","yearlyStaff":"d74fb811198a","yearlyWithTrial":"b3bc7350e5c7","yearlyPremium":"e21bd2c12166","monthlyOneYearFree":"e6c0637a-2bad-4171-ab4f-3c268633d83c","monthly25PercentOffFirstYear":"235ecc62-0cdb-49ae-9378-726cd21c504b","monthly20PercentOffFirstYear":"ba518864-9c13-4a99-91ca-411bf0cac756","monthly15PercentOffFirstYear":"594c029b-9f89-43d5-88f8-8173af4e070e","monthly10PercentOffFirstYear":"c6c7bc9a-40f2-4b51-8126-e28511d5bdb0","monthlyForStudents":"629ebe51-da7d-41fd-8293-34cd2f2030a8","yearlyOneYearFree":"78ba7be9-0d9f-4ece-aa3e-b54b826f2bf1","yearly25PercentOffFirstYear":"2dbb010d-bb8f-4eeb-ad5c-a08509f42d34","yearly20PercentOffFirstYear":"47565488-435b-47f8-bf93-40d5fbe0ebc8","yearly15PercentOffFirstYear":"8259809b-0881-47d9-acf7-6c001c7f720f","yearly10PercentOffFirstYear":"9dd694fb-96e1-472c-8d9e-3c868d5c1506","yearlyForStudents":"e29345ef-ab1c-4234-95c5-70e50fe6bc23","monthlyCad":"p52orjkaceei","yearlyCad":"h4q9g2up9ktt"},"braintreeDiscountId":{"oneMonthFree":"MONTHS_FREE_01","threeMonthsFree":"MONTHS_FREE_03","sixMonthsFree":"MONTHS_FREE_06","fiftyPercentOffOneYear":"FIFTY_PERCENT_OFF_ONE_YEAR"},"3DSecureVersion":"2","defaultCurrency":"usd","providerPlanIdCurrency":{"4ycw":"usd","rz3b":"usd","3kqm":"usd","jzw6":"usd","c2q2":"usd","nnsw":"usd","q8qw":"usd","d9y6":"usd","fx7w":"cad","nwf2":"cad"}},"paypalClientId":"AXj1G4fotC2GE8KzWX9mSxCH1wmPE3nJglf4Z2ig_amnhvlMVX87otaq58niAg9iuLktVNF_1WCMnN7v","paypal":{"host":"https:\u002F\u002Fapi.paypal.com:443","clientMode":"production","serverMode":"live","webhookId":"4G466076A0294510S","monthlyPlan":{"planId":"P-9WR0658853113943TMU5FDQA","name":"Medium Membership (Monthly) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"yearlyPlan":{"planId":"P-7N8963881P8875835MU5JOPQ","name":"Medium Membership (Annual) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"oneYearGift":{"name":"Medium Membership (1 Year, Digital Gift Code)","description":"Unlimited access to the best and brightest stories on Medium. Gift codes can be redeemed at medium.com\u002Fredeem.","price":"50.00","currency":"USD","sku":"membership-gift-1-yr"},"oldMonthlyPlan":{"planId":"P-96U02458LM656772MJZUVH2Y","name":"Medium Membership (Monthly)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"oldYearlyPlan":{"planId":"P-59P80963JF186412JJZU3SMI","name":"Medium Membership (Annual)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"monthlyPlanWithTrial":{"planId":"P-66C21969LR178604GJPVKUKY","name":"Medium Membership (Monthly) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"yearlyPlanWithTrial":{"planId":"P-6XW32684EX226940VKCT2MFA","name":"Medium Membership (Annual) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"oldMonthlyPlanNoSetupFee":{"planId":"P-4N046520HR188054PCJC7LJI","name":"Medium Membership (Monthly)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"oldYearlyPlanNoSetupFee":{"planId":"P-7A4913502Y5181304CJEJMXQ","name":"Medium Membership (Annual)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"sdkUrl":"https:\u002F\u002Fwww.paypal.com\u002Fsdk\u002Fjs"},"stripePublishableKey":"pk_live_7FReX44VnNIInZwrIIx6ghjl","log":{"json":true,"level":"info"},"imageUploadMaxSizeMb":25,"staffPicks":{"title":"Staff Picks","catalogId":"c7bc6e1ee00f"}},"session":{"xsrf":""}}</script><script>window.__APOLLO_STATE__ = {"ROOT_QUERY":{"__typename":"Query","collectionByDomainOrSlug({\"domainOrSlug\":\"medium.com\"})":null,"viewer":null,"postResult({\"id\":\"4ed5bf765d8d\"})":{"__ref":"Post:4ed5bf765d8d"}},"LinkedAccounts:ce24b5d6cbb":{"__typename":"LinkedAccounts","mastodon":null,"id":"ce24b5d6cbb"},"NewsletterV3:1d5752638725":{"__typename":"NewsletterV3","id":"1d5752638725","type":"NEWSLETTER_TYPE_AUTHOR","slug":"ce24b5d6cbb","name":"ce24b5d6cbb","collection":null,"user":{"__ref":"User:ce24b5d6cbb"}},"User:ce24b5d6cbb":{"__typename":"User","id":"ce24b5d6cbb","name":"Vince Lam","username":"vince-lam","newsletterV3":{"__ref":"NewsletterV3:1d5752638725"},"linkedAccounts":{"__ref":"LinkedAccounts:ce24b5d6cbb"},"isSuspended":false,"imageId":"1*FdY_N_dCL4n7tdWO_FJrng.png","customDomainState":{"__typename":"CustomDomainState","live":null},"hasSubdomain":false,"verifications":{"__typename":"VerifiedInfo","isBookAuthor":false},"socialStats":{"__typename":"SocialStats","followerCount":366,"followingCount":44,"collectionFollowingCount":7},"bio":"Data Scientist living abroad in Singapore 🇸🇬 Sharing tips & insights on AI & data science to boost our professional and personal lives. https:\u002F\u002Fvinlam.com","membership":{"__ref":"Membership:99a5cce65132"},"allowNotes":true,"viewerEdge":{"__ref":"UserViewerEdge:userId:ce24b5d6cbb-viewerId:lo_40f07550e3be"},"twitterScreenName":"_vincelam"},"Membership:99a5cce65132":{"__typename":"Membership","tier":"MEMBER","id":"99a5cce65132"},"Paragraph:ba993cd688b2_0":{"__typename":"Paragraph","id":"ba993cd688b2_0","name":"d012","type":"H3","href":null,"layout":null,"metadata":null,"text":"Decoding Kaggle’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_1":{"__typename":"Paragraph","id":"ba993cd688b2_1","name":"3335","type":"P","href":null,"layout":null,"metadata":null,"text":"It’s difficult for us to stay on top of the latest AI advancements with 100s of research papers, articles, and newsletters published daily. Luckily, Kaggle has recently published their [annual AI report](https:\u002F\u002Fwww.kaggle.com\u002FAI-Report-2023) earlier this month, which distills and summarises the latest advancements this past action-packed year.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_2":{"__typename":"Paragraph","id":"ba993cd688b2_2","name":"83b1","type":"P","href":null,"layout":null,"metadata":null,"text":"The Kaggle AI Report 2023 collates the best 3 essays on 7 important AI topics: (1) Generative AI, (2) text data, (3) image & video data, (4) tabular \u002F time-series data, (5) Kaggle competitions, (6) AI Ethics, and (7) Other (for topics that do not fall neatly under the previous 6 areas). All essays are fascinating reads, jam-packed with information. Some essays provide a comprehensive overview of a topic, other essays dive deep into the technicalities, and other essays leave you with thought-provoking ideas on what the future may entail.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":4,"end":25,"href":"https:\u002F\u002Fwww.kaggle.com\u002FAI-Report-2023","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":83,"end":96,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":102,"end":111,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":117,"end":135,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":141,"end":167,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":173,"end":192,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":198,"end":207,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":217,"end":223,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_3":{"__typename":"Paragraph","id":"ba993cd688b2_3","name":"4065","type":"P","href":null,"layout":null,"metadata":null,"text":"For this blog post, I’ll focus on tabular and time-series data (in the context of machine learning (ML) competitions) since this is the most common data type that data scientists face and has the highest applicability\u002Futility. As Bojan Tunguz said:","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":34,"end":62,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_4":{"__typename":"Paragraph","id":"ba993cd688b2_4","name":"c276","type":"BQ","href":null,"layout":null,"metadata":null,"text":"“It is estimated that between 50% and 90% of practicing data scientists use tabular data as their primary type of data in their primary setting.”","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_5":{"__typename":"Paragraph","id":"ba993cd688b2_5","name":"d186","type":"P","href":null,"layout":null,"metadata":null,"text":"To supplement my takeaways on best practice for tabular data, I’ll also draw on the findings from ML Contest’s State of Competitive Machine Learning Report published earlier this year, which broadly aligns with Kaggle report. I’ll write up on the latest advancements on the other AI topics in future blog posts.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":111,"end":155,"href":"https:\u002F\u002Fmlcontests.com\u002Fstate-of-competitive-machine-learning-2022","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*CO3XhjWwoHmkfBCog-eYfg.png":{"__typename":"ImageMetadata","id":"1*CO3XhjWwoHmkfBCog-eYfg.png","originalHeight":1024,"originalWidth":1792,"focusPercentX":null,"focusPercentY":null,"alt":null},"Paragraph:ba993cd688b2_6":{"__typename":"Paragraph","id":"ba993cd688b2_6","name":"d386","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*CO3XhjWwoHmkfBCog-eYfg.png"},"text":"","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_7":{"__typename":"Paragraph","id":"ba993cd688b2_7","name":"e548","type":"H3","href":null,"layout":null,"metadata":null,"text":"Why learn from ML competitions winners?","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_8":{"__typename":"Paragraph","id":"ba993cd688b2_8","name":"215b","type":"P","href":null,"layout":null,"metadata":null,"text":"There is great value in analysing winning solutions of ML competitions. Competitions act as a battleground for participants to test out the latest research models and architectures. Or as ML Contests puts it:","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":188,"end":199,"href":"https:\u002F\u002Fmlcontests.com\u002Fstate-of-competitive-machine-learning-2022","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_9":{"__typename":"Paragraph","id":"ba993cd688b2_9","name":"fc86","type":"BQ","href":null,"layout":null,"metadata":null,"text":"“One way to think of competitive machine learning is as a sandbox for evaluating predictive methods.”","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_10":{"__typename":"Paragraph","id":"ba993cd688b2_10","name":"abfd","type":"P","href":null,"layout":null,"metadata":null,"text":"Competitions become more useful over time as we can see trends on which ideas work, as teams incorporate techniques from winning solutions from previous competitions into their own — forming new baselines. Two great resources for reading competition write-ups by winners are DrivenData’s blog and Kaggle discussions. Following these discussions is a useful proxy of staying on top of the latest practical advancements.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":275,"end":292,"href":"https:\u002F\u002Fdrivendata.co\u002Fblog.html","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":297,"end":315,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fdiscussions?sort=votes&category=competitionWriteUps","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_11":{"__typename":"Paragraph","id":"ba993cd688b2_11","name":"3a61","type":"H4","href":null,"layout":null,"metadata":null,"text":"However, do take some of the learnings with a pinch of salt","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_12":{"__typename":"Paragraph","id":"ba993cd688b2_12","name":"14ca","type":"P","href":null,"layout":null,"metadata":null,"text":"Learning how to improve a model’s performance by a few decimal points may have a positive impact to a company’s bottom line, especially if it serving millions of customers. However, there does come a point of diminishing returns when trying to eek out that extra .0001 of performance, depending on the business context. Because of the iterative nature of ML, it can be difficult to decide when “good” is “good enough”.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_13":{"__typename":"Paragraph","id":"ba993cd688b2_13","name":"73a2","type":"P","href":null,"layout":null,"metadata":null,"text":"Whilst machine learning competitions do push advancements in AI, the focus does not fully translate to the real-world professional setting. With the growing importance of AI ethics, many are concerned with the integrity and fairness of machine learning-based systems, services, and products.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":171,"end":180,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_14":{"__typename":"Paragraph","id":"ba993cd688b2_14","name":"f14b","type":"P","href":null,"layout":null,"metadata":null,"text":"Since models are judged by their performance in competitions, a metric that is easily quantified, understood by competitors, and the determinant of the ranking for prizes and accolades — it becomes the main focus. This means a result-first approach rewards black-box approaches which do not consider explainability and interpretability. This is particularly relevant for ensembling, more on that later.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":300,"end":314,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":319,"end":335,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":371,"end":381,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_15":{"__typename":"Paragraph","id":"ba993cd688b2_15","name":"8baf","type":"P","href":null,"layout":null,"metadata":null,"text":"However, I still believe using the consensus of the ML community acts as a useful starting point. As it is very easy to get bogged down by analysis paralysis when there are thousands of models, tools, and packages to choose from.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_16":{"__typename":"Paragraph","id":"ba993cd688b2_16","name":"8bfb","type":"H3","href":null,"layout":null,"metadata":null,"text":"Common themes from the winners of tabular \u002F time-series data competitions","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_17":{"__typename":"Paragraph","id":"ba993cd688b2_17","name":"b54e","type":"P","href":null,"layout":null,"metadata":null,"text":"There are a number of common themes that regularly appear amongst winning solutions of ML competitions, across various domains such as finance to healthcare. These themes agree with findings of academic literature too which have studied these competitions. These themes include the importance of:","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_18":{"__typename":"Paragraph","id":"ba993cd688b2_18","name":"5407","type":"ULI","href":null,"layout":null,"metadata":null,"text":"Feature engineering","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_19":{"__typename":"Paragraph","id":"ba993cd688b2_19","name":"3173","type":"ULI","href":null,"layout":null,"metadata":null,"text":"Cross-validation","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_20":{"__typename":"Paragraph","id":"ba993cd688b2_20","name":"cd22","type":"ULI","href":null,"layout":null,"metadata":null,"text":"Gradient Boosted Decision Trees","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_21":{"__typename":"Paragraph","id":"ba993cd688b2_21","name":"0ba0","type":"ULI","href":null,"layout":null,"metadata":null,"text":"Ensembling","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_22":{"__typename":"Paragraph","id":"ba993cd688b2_22","name":"a448","type":"H3","href":null,"layout":null,"metadata":null,"text":"Feature engineering is still a crucial part of the ML modelling pipeline","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_23":{"__typename":"Paragraph","id":"ba993cd688b2_23","name":"4864","type":"P","href":null,"layout":null,"metadata":null,"text":"Feature engineering is the process of creating, selecting, and transforming variables to maximise their predictive power in models. It’s a dynamic, iterative, and highly time-consuming process. Feature engineering is well recognised as being one of the most important, if not the most important, part of a tabular ML modelling pipeline, for both competitions and industry. Feature engineering is especially important for tabular data compared to deep learning techniques for computer vision tasks, where data augmentation is more focused on.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":217,"end":232,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fdiscussions\u002Fgetting-started\u002F44997","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":280,"end":294,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fcompetitions\u002Fhome-credit-default-risk\u002Fdiscussion\u002F64821)","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":0,"end":19,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":27,"end":120,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_24":{"__typename":"Paragraph","id":"ba993cd688b2_24","name":"81d5","type":"P","href":null,"layout":null,"metadata":null,"text":"In Rhys Cook’s winning essay they provided two great competition examples to illustrate how varied feature engineering approaches can be:","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":15,"end":28,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fcode\u002Frhysie\u002Flearnings-from-the-typical-tabular-pipeline","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_25":{"__typename":"Paragraph","id":"ba993cd688b2_25","name":"c04c","type":"P","href":null,"layout":null,"metadata":null,"text":"1. Amex Credit Fault Prediction (the most popular ML competition of 2022 with 5,000 teams entered, likely due to the growing scarcity of tabular Kaggle competitions and the $100k prize pool)","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":3,"end":31,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fcompetitions\u002Famex-default-prediction\u002Foverview","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":3,"end":31,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_26":{"__typename":"Paragraph","id":"ba993cd688b2_26","name":"2aaf","type":"P","href":null,"layout":null,"metadata":null,"text":"2. AMP Parkinson’s Disease Progression Prediction","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":3,"end":49,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fcompetitions\u002Famp-parkinsons-disease-progression-prediction\u002Foverview","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":3,"end":49,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_27":{"__typename":"Paragraph","id":"ba993cd688b2_27","name":"c8b3","type":"P","href":null,"layout":null,"metadata":null,"text":"In Amex Credit Fraud Prediction competition, the 2nd place solution team used a brute force feature engineering approach. Where they created thousands of features, such as aggregations, differences, ratio, lags, and normalisation, based on the original 190 features. Then the team applied extensive feature selection techniques: (1) removal of zero importance features, (2) stepped hierarchical permutation importance, (3) stepped permutation importance, (4) forward feature selection, and (5) time-series cross-validation, to obtain the final subset of 2,500 features.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":49,"end":72,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fcompetitions\u002Famex-default-prediction\u002Fdiscussion\u002F347637","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":3,"end":43,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":344,"end":359,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":374,"end":417,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":423,"end":453,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":459,"end":484,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":494,"end":522,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_28":{"__typename":"Paragraph","id":"ba993cd688b2_28","name":"5fe1","type":"P","href":null,"layout":null,"metadata":null,"text":"For the Parkinson’s Disease Progression Prediction competition, the 4th place solution team realised that the curse of dimensionality applied to this dataset — as there were 1,195 features for proteins and peptides for only 248 patients in the training set, across 17 patient visit dates. This dataset had sufficient train data to train only 25 features. As a result, they focused on creating features based on the patient visit dates, rather than the protein\u002Fpeptide features which were the main focus of the competition. The top 18 teams noticed that features based on patient visit dates had the best predictive ability.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":68,"end":91,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fcompetitions\u002Famp-parkinsons-disease-progression-prediction\u002Fdiscussion\u002F411398","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":8,"end":50,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":110,"end":133,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_29":{"__typename":"Paragraph","id":"ba993cd688b2_29","name":"657a","type":"P","href":null,"layout":null,"metadata":null,"text":"However, Cook found that there is not much academic literature on the topic of feature engineering. This the lack of research is likely due to the fact that feature engineering is very specific to the problem and dataset, and requires creativity and flexibility, and therefore is difficult to generalise.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":9,"end":13,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fcode\u002Frhysie\u002Flearnings-from-the-typical-tabular-pipeline","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":158,"end":220,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":235,"end":261,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_30":{"__typename":"Paragraph","id":"ba993cd688b2_30","name":"5729","type":"P","href":null,"layout":null,"metadata":null,"text":"Despite this, there are resources which provide useful guidance for many domain applications, such as the Feature Engineering and Selection book. There are automated feature engineering solutions available such as Featuretools and others. However, most competitors tend to do it manually.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":106,"end":144,"href":"http:\u002F\u002Fwww.feat.engineering","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":214,"end":226,"href":"https:\u002F\u002Ffeaturetools.alteryx.com\u002Fen\u002Fstable\u002F","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":231,"end":237,"href":"https:\u002F\u002Fneptune.ai\u002Fblog\u002Ffeature-engineering-tools","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":106,"end":144,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":214,"end":226,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":231,"end":237,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_31":{"__typename":"Paragraph","id":"ba993cd688b2_31","name":"955b","type":"H3","href":null,"layout":null,"metadata":null,"text":"Robust cross-validation to trust the model results","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_32":{"__typename":"Paragraph","id":"ba993cd688b2_32","name":"8ae6","type":"P","href":null,"layout":null,"metadata":null,"text":"Cross-validation (CV) is the process of evaluating a ML model and testing its performance. By training on only a subset of the training data, and measuring performance on the remaing datam we can understand how well a model generalises. It allows data scientists to know if the model is reliably improving model performance rather than overfitting to the test dataset (or public leaderboard (LB) for ML competitors).","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":0,"end":21,"href":"https:\u002F\u002Fneptune.ai\u002Fblog\u002Fcross-validation-in-machine-learning-how-to-do-it-right","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":0,"end":22,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_33":{"__typename":"Paragraph","id":"ba993cd688b2_33","name":"e4a3","type":"P","href":null,"layout":null,"metadata":null,"text":"In fact, it is common for competitors to select a final model that underperforms according to the LB but has more robust CV, in the hopes of generalising better against the private test data. However, good performance is not guaranteed on the holdout \u002F test set, as it may be taken from a different distribution.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_34":{"__typename":"Paragraph","id":"ba993cd688b2_34","name":"3b2b","type":"P","href":null,"layout":null,"metadata":null,"text":"There are two general approaches to cross-validation:","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_35":{"__typename":"Paragraph","id":"ba993cd688b2_35","name":"7a3f","type":"P","href":null,"layout":null,"metadata":null,"text":"1. Simple train\u002Ftest split (or Leave-one-out CV (LOOCV)): Where you take split your shuffled training data into two splits, for example 80% for training and 20% for testing.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":10,"end":26,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":31,"end":55,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_36":{"__typename":"Paragraph","id":"ba993cd688b2_36","name":"38b3","type":"P","href":null,"layout":null,"metadata":null,"text":"2. k-Fold Cross-Validation: Where you split the shuffled data into k-equal groups, or folds. The model is trained k times, where one of the folds is excluded as a test set and the remaining folds are trained on, using the excluded test set to evaluate the model performance. After each evaluation, the evaluation score is retained and the model is discarded. After the training, the skill of the model is summarised using the sample of model evaluation scores.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":3,"end":26,"href":"https:\u002F\u002Fmachinelearningmastery.com\u002Fk-fold-cross-validation\u002F","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":3,"end":26,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_37":{"__typename":"Paragraph","id":"ba993cd688b2_37","name":"947b","type":"P","href":null,"layout":null,"metadata":null,"text":"k-fold cross-validation is the typical go-to CV scheme of choice for ML competitors, despite being more computationally intensive than the first method. There are many different types of k-fold CV strategies available, depending on the skew of the target variable (standard, stratified, stratified in groups, and multi-label stratified) and if the data is time series which will require time-series cross-validation, such as rolling CV or blocked CV.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":265,"end":273,"href":"https:\u002F\u002Fscikit-learn.org\u002Fstable\u002Fmodules\u002Fgenerated\u002Fsklearn.model_selection.KFold.html#sklearn.model_selection.KFold","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":275,"end":285,"href":"https:\u002F\u002Fscikit-learn.org\u002Fstable\u002Fmodules\u002Fgenerated\u002Fsklearn.model_selection.StratifiedKFold.html#sklearn.model_selection.StratifiedKFold","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":287,"end":307,"href":"https:\u002F\u002Fscikit-learn.org\u002Fstable\u002Fmodules\u002Fgenerated\u002Fsklearn.model_selection.StratifiedGroupKFold.html#sklearn.model_selection.StratifiedGroupKFold","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":313,"end":335,"href":"https:\u002F\u002Fgithub.com\u002Ftrent-b\u002Fiterative-stratification#multilabelstratifiedkfold","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":0,"end":23,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":265,"end":273,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":275,"end":285,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":287,"end":307,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":313,"end":335,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":387,"end":415,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":425,"end":435,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":439,"end":449,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*RadsKK642BgQfJ1L5uCOCA.png":{"__typename":"ImageMetadata","id":"1*RadsKK642BgQfJ1L5uCOCA.png","originalHeight":491,"originalWidth":790,"focusPercentX":null,"focusPercentY":null,"alt":"Types of cross-validation techniques — Source"},"Paragraph:ba993cd688b2_38":{"__typename":"Paragraph","id":"ba993cd688b2_38","name":"517f","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*RadsKK642BgQfJ1L5uCOCA.png"},"text":"Types of cross-validation techniques — Source","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":39,"end":45,"href":"https:\u002F\u002Fwww.linkedin.com\u002Fposts\u002Fdanleedata_wait-you-are-telling-me-your-models-performance-activity-7057341218493493248-Xa2l","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_39":{"__typename":"Paragraph","id":"ba993cd688b2_39","name":"33d4","type":"H3","href":null,"layout":null,"metadata":null,"text":"Gradient Boosted Decision Trees are still beating Deep Learning models for tabular data","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_40":{"__typename":"Paragraph","id":"ba993cd688b2_40","name":"969d","type":"P","href":null,"layout":null,"metadata":null,"text":"Gradient Boosted Decision Trees (GBDT), or also known as Gradient Boosted Trees (GBT) or Gradient Boosted Machines (GBM), are the go-to family of traditional ML models for tabular data. GBDTs even outperform deep learning (DL) models which were purpose built for tabular data, especially when performance is averaged across multiple datasets.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":245,"end":275,"href":"https:\u002F\u002Fpaperswithcode.com\u002Fmethods\u002Fcategory\u002Fdeep-tabular-learning","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":0,"end":38,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":57,"end":85,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":89,"end":120,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":208,"end":226,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":245,"end":275,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_41":{"__typename":"Paragraph","id":"ba993cd688b2_41","name":"8cbf","type":"H4","href":null,"layout":null,"metadata":null,"text":"Quick Recap on Gradient Boosted Decision Trees","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_42":{"__typename":"Paragraph","id":"ba993cd688b2_42","name":"f9a4","type":"P","href":null,"layout":null,"metadata":null,"text":"GBDTs are a ML ensemble method that constructs a strong predictive model by combining weak learners, decision trees. The algorithm operates iteratively where each new tree corrects the errors made by the existing ensemble of trees. They are used for both classification and regression tasks.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":36,"end":99,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_43":{"__typename":"Paragraph","id":"ba993cd688b2_43","name":"5a18","type":"P","href":null,"layout":null,"metadata":null,"text":"The three main GBDT models are:","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_44":{"__typename":"Paragraph","id":"ba993cd688b2_44","name":"d18f","type":"P","href":null,"layout":null,"metadata":null,"text":"1. XGBoost (eXtreme Gradient Boosting): Introduced in 2014 by Tianqi Chen and has been a staple of ML competition winning solutions the past 9 years (this repo shows some of the many winning XGBoost solutions). Unlike standard Gradient Boosting, which starts by growing a full tree before pruning, XGBoost prunes the trees depth-first, which is more efficient. XGBoost also has inbuilt mechanisms for:","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":3,"end":10,"href":"https:\u002F\u002Fxgboost.readthedocs.io\u002Fen\u002Fstable\u002F","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":155,"end":159,"href":"https:\u002F\u002Fgithub.com\u002Fdmlc\u002Fxgboost\u002Ftree\u002Fmaster\u002Fdemo#machine-learning-challenge-winning-solutions","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":3,"end":38,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_45":{"__typename":"Paragraph","id":"ba993cd688b2_45","name":"846a","type":"ULI","href":null,"layout":null,"metadata":null,"text":"L1 (Lasso) and L2 (Ridge) regularization to reduce model complexity and overfitting","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":41,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_46":{"__typename":"Paragraph","id":"ba993cd688b2_46","name":"d50d","type":"ULI","href":null,"layout":null,"metadata":null,"text":"Handling missing values","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_47":{"__typename":"Paragraph","id":"ba993cd688b2_47","name":"60ad","type":"ULI","href":null,"layout":null,"metadata":null,"text":"Efficient k-fold cross-validation","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_48":{"__typename":"Paragraph","id":"ba993cd688b2_48","name":"e958","type":"P","href":null,"layout":null,"metadata":null,"text":"2. LightGBM (Light Gradient Boosting Machine): Introduced by Microsoft in 2016, LightGBM offers some benefits over XGBoost — having leaf-wise tree growth, instead of tree-level growth. This means that algorithm will choose the leaf that will reduce the most loss, rather than making all leaves in the tree level grow — resulting in more compact trees.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":3,"end":11,"href":"https:\u002F\u002Flightgbm.readthedocs.io\u002F","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":3,"end":46,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":132,"end":153,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_49":{"__typename":"Paragraph","id":"ba993cd688b2_49","name":"8a6f","type":"P","href":null,"layout":null,"metadata":null,"text":"3. CatBoost (Categorical Boosting): Introduced by Yandex in 2017, CatBoost can handle categorical features without pre-processing like one-hot encoding.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":3,"end":11,"href":"https:\u002F\u002Fcatboost.ai\u002F","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":3,"end":35,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":135,"end":151,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_50":{"__typename":"Paragraph","id":"ba993cd688b2_50","name":"a0bc","type":"P","href":null,"layout":null,"metadata":null,"text":"According to ML Contests, LightGBM was the the most used GBDT library in 2022 ML competitions — mentioned in 25% of write ups or in their questionnaire, which is the same amount as CatBoost (second most used GBDT library) and XGBoost combined. Although this may change with the newly updated XGBoost 2.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":13,"end":24,"href":"https:\u002F\u002Fmlcontests.com\u002Fwinning-toolkit\u002F","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":26,"end":93,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*eXHHwwV-GNqakew0TAzTKg.png":{"__typename":"ImageMetadata","id":"1*eXHHwwV-GNqakew0TAzTKg.png","originalHeight":900,"originalWidth":1600,"focusPercentX":null,"focusPercentY":null,"alt":"Top GDBT packages. Image from ML Contests"},"Paragraph:ba993cd688b2_51":{"__typename":"Paragraph","id":"ba993cd688b2_51","name":"3b11","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*eXHHwwV-GNqakew0TAzTKg.png"},"text":"Top GDBT packages. Image from ML Contests","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":30,"end":41,"href":"https:\u002F\u002Fmlcontests.com\u002Fwinning-toolkit\u002F","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_52":{"__typename":"Paragraph","id":"ba993cd688b2_52","name":"498d","type":"H4","href":null,"layout":null,"metadata":null,"text":"What does the research literature have to say on the debate?","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_53":{"__typename":"Paragraph","id":"ba993cd688b2_53","name":"c667","type":"P","href":null,"layout":null,"metadata":null,"text":"There have been many papers that have compared these two broad groups of models and have come to the similar conclusions. In Tabular Data: Deep Learning is Not ALl You Need (2021), Shwartz-Ziv and Armon compared XGBoost and DL models for tabular data and found that XGBoost performed better than most DL models across all datasets, but typically a specific DL model would perform better at a specific dataset. They highlighted that XGBoost requires significantly less hyperparameter tuning to achieve good performance, which is important in the real-world setting.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":125,"end":179,"href":"https:\u002F\u002Farxiv.org\u002Fabs\u002F2106.03253","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":125,"end":179,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":266,"end":330,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":348,"end":408,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":432,"end":489,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_54":{"__typename":"Paragraph","id":"ba993cd688b2_54","name":"2e70","type":"P","href":null,"layout":null,"metadata":null,"text":"From the paper Why do tree-based models still outperform deep learning on tabular data (2022), Grinsztajn, Oyallon, and Varoquaux found that this phenomenon is especially true for medium-sized datasets (around 10k training examples) but the gap between tree-based models and deep learning lessons as dataset size increases (around 50k training examples). Also, tree-based models are more robust against uninformative features, as they process features independently, whereas deep learning methods are more prone to overfitting to noise.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":15,"end":93,"href":"https:\u002F\u002Farxiv.org\u002Fabs\u002F2207.08815","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":15,"end":93,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":361,"end":425,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":475,"end":535,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*RMjjBhG0y0kWxA85xUOoIw.png":{"__typename":"ImageMetadata","id":"1*RMjjBhG0y0kWxA85xUOoIw.png","originalHeight":381,"originalWidth":852,"focusPercentX":null,"focusPercentY":null,"alt":"Benchmark performance comparisons between GBDTs and deep neural networks, taken from Why do tree-based models still outperform deep learning on tabular data (2022)"},"Paragraph:ba993cd688b2_55":{"__typename":"Paragraph","id":"ba993cd688b2_55","name":"52ed","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*RMjjBhG0y0kWxA85xUOoIw.png"},"text":"Benchmark performance comparisons between GBDTs and deep neural networks, taken from Why do tree-based models still outperform deep learning on tabular data (2022)","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":85,"end":163,"href":"https:\u002F\u002Farxiv.org\u002Fabs\u002F2207.08815","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_56":{"__typename":"Paragraph","id":"ba993cd688b2_56","name":"6459","type":"P","href":null,"layout":null,"metadata":null,"text":"All that being said, neural networks can provide benefits if the tabular data has some underlying structure well suited for neural network architectures, such as sequenced or hierarchical data. For example, the famous time-series M-competitions have recently been dominated by deep learning models. This blog post and paper by Amazon Research are great starting points to get a comprehensive overview on the pivotal advancements in DL for tabular data and time-series, respectively.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":304,"end":313,"href":"https:\u002F\u002Fsebastianraschka.com\u002Fblog\u002F2022\u002Fdeep-learning-for-tabular-data.html","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":318,"end":342,"href":"https:\u002F\u002Farxiv.org\u002Fpdf\u002F2004.10240.pdf","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":87,"end":107,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":162,"end":192,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":218,"end":244,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_57":{"__typename":"Paragraph","id":"ba993cd688b2_57","name":"a4fe","type":"H3","href":null,"layout":null,"metadata":null,"text":"Ensembling for performance gains","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_58":{"__typename":"Paragraph","id":"ba993cd688b2_58","name":"e09d","type":"P","href":null,"layout":null,"metadata":null,"text":"Although deep learning has not replaced GBDTs for tabular data, they do provide value when ensembled with boosting models. Ensembling is when multiple different machine learning models are combined to improve the overall prediction performance. This works best when diverse models are combined as collectively they can offset each other when individual models make uncorrelated errors.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":123,"end":133,"href":"https:\u002F\u002Fneptune.ai\u002Fblog\u002Fensemble-learning-guide","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":123,"end":133,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":250,"end":293,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":319,"end":384,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_59":{"__typename":"Paragraph","id":"ba993cd688b2_59","name":"cf0c","type":"P","href":null,"layout":null,"metadata":null,"text":"Almost all competition winning solutions involve a large ensemble of models, rather than a single model (although it does happen rarely). Kaggle is known as an ensembling playground, where participants will ensemble many different models to improve their score. However, this comes at a cost of computational resources, complexity, explainability, and inference time — which are important considerations in a real-world applications.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":287,"end":366,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_60":{"__typename":"Paragraph","id":"ba993cd688b2_60","name":"e781","type":"P","href":null,"layout":null,"metadata":null,"text":"Model outputs are typically ensembled \u002F aggregated by:","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_61":{"__typename":"Paragraph","id":"ba993cd688b2_61","name":"6350","type":"P","href":null,"layout":null,"metadata":null,"text":"1. Averaging (or blending): This is the simplest method of ensembling, options include averaging, weighted average (most common method), and rank averaging.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":3,"end":12,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":17,"end":25,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":98,"end":114,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_62":{"__typename":"Paragraph","id":"ba993cd688b2_62","name":"4588","type":"P","href":null,"layout":null,"metadata":null,"text":"2. Voting: For classifications tasks, models can vote on the prediction. The most common option is majority voting, where class is predicted by the selection of the majority of models.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":3,"end":9,"href":"https:\u002F\u002Fscikit-learn.org\u002Fstable\u002Fmodules\u002Fensemble.html#voting-classifier","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":3,"end":9,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_63":{"__typename":"Paragraph","id":"ba993cd688b2_63","name":"03d6","type":"P","href":null,"layout":null,"metadata":null,"text":"3. Bagging, boosting, and stacking:","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":3,"end":34,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_64":{"__typename":"Paragraph","id":"ba993cd688b2_64","name":"d986","type":"ULI","href":null,"layout":null,"metadata":null,"text":"Bagging (short for bootstrap aggregating) is when you sample with replacement to create different datasets called bootstraps and then a different model on each of those bootstraps, and then aggregate the results — by averaging for regression or voting for classification.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":0,"end":7,"href":"https:\u002F\u002Fscikit-learn.org\u002Fstable\u002Fmodules\u002Fensemble.html#bagging-meta-estimator)** (short for **bootstrap aggregating","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":19,"end":40,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_65":{"__typename":"Paragraph","id":"ba993cd688b2_65","name":"2f6b","type":"ULI","href":null,"layout":null,"metadata":null,"text":"Boosting is a family of iterative optimum algorithms that converts weak learners to strong learners. Each learner in the ensemble is trained on the same set of samples but samples are weighted based on how well the ensemble classifies them.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":8,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_66":{"__typename":"Paragraph","id":"ba993cd688b2_66","name":"85a8","type":"ULI","href":null,"layout":null,"metadata":null,"text":"Stacking is the process of training base learners from the training data and then create a meta learner that combines the output of the base learners to output final predictions.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":0,"end":8,"href":"https:\u002F\u002Fscikit-learn.org\u002Fstable\u002Fmodules\u002Fensemble.html#stacked-generalization","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":0,"end":8,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_67":{"__typename":"Paragraph","id":"ba993cd688b2_67","name":"1f79","type":"H3","href":null,"layout":null,"metadata":null,"text":"Conclusion","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_68":{"__typename":"Paragraph","id":"ba993cd688b2_68","name":"2825","type":"P","href":null,"layout":null,"metadata":null,"text":"It’s difficult staying on top of the latest advancements in AI and ML but the Kaggle AI Report 2023 and the State of Competitive Machine Learning Report provide invaluable insights, especially for tabular and time-series data. This blog post highlighted the continued importance of feature engineering and robust cross-validation, the unassailable effectiveness of GBDTs over DL models for tabular data, and the power of ensembling to improve performance.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_69":{"__typename":"Paragraph","id":"ba993cd688b2_69","name":"b19a","type":"P","href":null,"layout":null,"metadata":null,"text":"However, it is important to recognise that competition settings has nuances and different priorities that does not directly translate to real-world applications, especially with the growing importance of explainable AI. In reality, you most of your time will likely be on data cleaning or converting your notebook code into production with MLOps (a topic I’ll cover in future).","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:ba993cd688b2_70":{"__typename":"Paragraph","id":"ba993cd688b2_70","name":"9909","type":"P","href":null,"layout":null,"metadata":null,"text":"Thanks for reading if you’ve made it this far, I hope you’ve found it useful!","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"UserViewerEdge:userId:ce24b5d6cbb-viewerId:lo_40f07550e3be":{"__typename":"UserViewerEdge","id":"userId:ce24b5d6cbb-viewerId:lo_40f07550e3be","isMuting":false},"PostViewerEdge:postId:4ed5bf765d8d-viewerId:lo_40f07550e3be":{"__typename":"PostViewerEdge","shouldIndexPostForExternalSearch":true,"id":"postId:4ed5bf765d8d-viewerId:lo_40f07550e3be"},"Tag:ai":{"__typename":"Tag","id":"ai","displayTitle":"AI","normalizedTagSlug":"ai"},"Tag:machine-learning":{"__typename":"Tag","id":"machine-learning","displayTitle":"Machine Learning","normalizedTagSlug":"machine-learning"},"Tag:data-science":{"__typename":"Tag","id":"data-science","displayTitle":"Data Science","normalizedTagSlug":"data-science"},"Tag:data":{"__typename":"Tag","id":"data","displayTitle":"Data","normalizedTagSlug":"data"},"Tag:artificial-intelligence":{"__typename":"Tag","id":"artificial-intelligence","displayTitle":"Artificial Intelligence","normalizedTagSlug":"artificial-intelligence"},"Post:4ed5bf765d8d":{"__typename":"Post","id":"4ed5bf765d8d","collection":null,"content({\"postMeteringOptions\":{\"referrer\":\"\"}})":{"__typename":"PostContent","isLockedPreviewOnly":false,"bodyModel":{"__typename":"RichText","sections":[{"__typename":"Section","name":"745c","startIndex":0,"textLayout":null,"imageLayout":null,"backgroundImage":null,"videoLayout":null,"backgroundVideo":null}],"paragraphs":[{"__ref":"Paragraph:ba993cd688b2_0"},{"__ref":"Paragraph:ba993cd688b2_1"},{"__ref":"Paragraph:ba993cd688b2_2"},{"__ref":"Paragraph:ba993cd688b2_3"},{"__ref":"Paragraph:ba993cd688b2_4"},{"__ref":"Paragraph:ba993cd688b2_5"},{"__ref":"Paragraph:ba993cd688b2_6"},{"__ref":"Paragraph:ba993cd688b2_7"},{"__ref":"Paragraph:ba993cd688b2_8"},{"__ref":"Paragraph:ba993cd688b2_9"},{"__ref":"Paragraph:ba993cd688b2_10"},{"__ref":"Paragraph:ba993cd688b2_11"},{"__ref":"Paragraph:ba993cd688b2_12"},{"__ref":"Paragraph:ba993cd688b2_13"},{"__ref":"Paragraph:ba993cd688b2_14"},{"__ref":"Paragraph:ba993cd688b2_15"},{"__ref":"Paragraph:ba993cd688b2_16"},{"__ref":"Paragraph:ba993cd688b2_17"},{"__ref":"Paragraph:ba993cd688b2_18"},{"__ref":"Paragraph:ba993cd688b2_19"},{"__ref":"Paragraph:ba993cd688b2_20"},{"__ref":"Paragraph:ba993cd688b2_21"},{"__ref":"Paragraph:ba993cd688b2_22"},{"__ref":"Paragraph:ba993cd688b2_23"},{"__ref":"Paragraph:ba993cd688b2_24"},{"__ref":"Paragraph:ba993cd688b2_25"},{"__ref":"Paragraph:ba993cd688b2_26"},{"__ref":"Paragraph:ba993cd688b2_27"},{"__ref":"Paragraph:ba993cd688b2_28"},{"__ref":"Paragraph:ba993cd688b2_29"},{"__ref":"Paragraph:ba993cd688b2_30"},{"__ref":"Paragraph:ba993cd688b2_31"},{"__ref":"Paragraph:ba993cd688b2_32"},{"__ref":"Paragraph:ba993cd688b2_33"},{"__ref":"Paragraph:ba993cd688b2_34"},{"__ref":"Paragraph:ba993cd688b2_35"},{"__ref":"Paragraph:ba993cd688b2_36"},{"__ref":"Paragraph:ba993cd688b2_37"},{"__ref":"Paragraph:ba993cd688b2_38"},{"__ref":"Paragraph:ba993cd688b2_39"},{"__ref":"Paragraph:ba993cd688b2_40"},{"__ref":"Paragraph:ba993cd688b2_41"},{"__ref":"Paragraph:ba993cd688b2_42"},{"__ref":"Paragraph:ba993cd688b2_43"},{"__ref":"Paragraph:ba993cd688b2_44"},{"__ref":"Paragraph:ba993cd688b2_45"},{"__ref":"Paragraph:ba993cd688b2_46"},{"__ref":"Paragraph:ba993cd688b2_47"},{"__ref":"Paragraph:ba993cd688b2_48"},{"__ref":"Paragraph:ba993cd688b2_49"},{"__ref":"Paragraph:ba993cd688b2_50"},{"__ref":"Paragraph:ba993cd688b2_51"},{"__ref":"Paragraph:ba993cd688b2_52"},{"__ref":"Paragraph:ba993cd688b2_53"},{"__ref":"Paragraph:ba993cd688b2_54"},{"__ref":"Paragraph:ba993cd688b2_55"},{"__ref":"Paragraph:ba993cd688b2_56"},{"__ref":"Paragraph:ba993cd688b2_57"},{"__ref":"Paragraph:ba993cd688b2_58"},{"__ref":"Paragraph:ba993cd688b2_59"},{"__ref":"Paragraph:ba993cd688b2_60"},{"__ref":"Paragraph:ba993cd688b2_61"},{"__ref":"Paragraph:ba993cd688b2_62"},{"__ref":"Paragraph:ba993cd688b2_63"},{"__ref":"Paragraph:ba993cd688b2_64"},{"__ref":"Paragraph:ba993cd688b2_65"},{"__ref":"Paragraph:ba993cd688b2_66"},{"__ref":"Paragraph:ba993cd688b2_67"},{"__ref":"Paragraph:ba993cd688b2_68"},{"__ref":"Paragraph:ba993cd688b2_69"},{"__ref":"Paragraph:ba993cd688b2_70"}]},"validatedShareKey":"","shareKeyCreator":null},"creator":{"__ref":"User:ce24b5d6cbb"},"inResponseToEntityType":null,"isLocked":false,"isMarkedPaywallOnly":false,"lockedSource":"LOCKED_POST_SOURCE_NONE","mediumUrl":"https:\u002F\u002Fmedium.com\u002F@vince-lam\u002Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d","primaryTopic":null,"topics":[{"__typename":"Topic","slug":"artificial-intelligence"},{"__typename":"Topic","slug":"machine-learning"},{"__typename":"Topic","slug":"data-science"}],"isLimitedState":false,"isPublished":true,"allowResponses":true,"responsesLocked":false,"visibility":"PUBLIC","latestPublishedVersion":"ba993cd688b2","postResponses":{"__typename":"PostResponses","count":0},"responseDistribution":"NOT_DISTRIBUTED","clapCount":31,"title":"Decoding Kaggle’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈","isSeries":false,"sequence":null,"uniqueSlug":"decoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d","socialTitle":"","socialDek":"","canonicalUrl":"https:\u002F\u002Fmedium.com\u002F@vince-lam\u002Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d","metaDescription":"","latestPublishedAt":1698666376482,"readingTime":10.00943396226415,"previewContent":{"__typename":"PreviewContent","subtitle":"It’s difficult for us to stay on top of the latest AI advancements with 100s of research papers, articles, and newsletters published daily…"},"previewImage":{"__ref":"ImageMetadata:1*CO3XhjWwoHmkfBCog-eYfg.png"},"isShortform":false,"seoMetaTags":{"__typename":"SEOMetaTags","jsonLd":"{\"@context\":\"https:\u002F\u002Fschema.org\",\"@id\":\"https:\u002F\u002Fmedium.com\u002F@vince-lam\u002Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d\",\"@type\":\"SocialMediaPosting\",\"image\":[\"https:\u002F\u002Fmiro.medium.com\u002F1*CO3XhjWwoHmkfBCog-eYfg.png\"],\"url\":\"https:\u002F\u002Fmedium.com\u002F@vince-lam\u002Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d\",\"dateCreated\":\"2023-10-30T11:46:16Z\",\"datePublished\":\"2023-10-30T11:46:16Z\",\"dateModified\":\"2023-10-30T11:46:16Z\",\"headline\":\"Decoding Kaggle’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈\",\"name\":\"Decoding Kaggle’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈\",\"description\":\"Decoding Kaggle’s 2023 AI Report: Essential Tips for Machine Learning with Tabular Data 🔍📈\\nIt’s difficult for us to stay on top of the latest AI advancements with 100s of research papers …\",\"identifier\":\"4ed5bf765d8d\",\"author\":{\"@context\":\"https:\u002F\u002Fschema.org\",\"@id\":\"https:\u002F\u002Fmedium.com\u002F@vince-lam\",\"@type\":\"Person\",\"identifier\":\"vince-lam\",\"name\":\"Vince Lam\",\"url\":\"https:\u002F\u002Fmedium.com\u002F@vince-lam\"},\"creator\":{\"@context\":\"https:\u002F\u002Fschema.org\",\"@id\":\"https:\u002F\u002Fmedium.com\u002F@vince-lam\",\"@type\":\"Person\",\"identifier\":\"vince-lam\",\"name\":\"Vince Lam\",\"url\":\"https:\u002F\u002Fmedium.com\u002F@vince-lam\"},\"publisher\":{\"@context\":\"https:\u002F\u002Fschema.org\",\"@type\":\"Organization\",\"@id\":\"https:\u002F\u002Fmedium.com\",\"name\":\"Medium\",\"url\":\"https:\u002F\u002Fmedium.com\",\"logo\":{\"@type\":\"ImageObject\",\"width\":500,\"height\":110,\"url\":\"https:\u002F\u002Fmiro.medium.com\u002Fv2\u002Fresize:fit:500\u002F7%2AV1_7XP4snlmqrc_0Njontw.png\"}},\"mainEntityOfPage\":\"https:\u002F\u002Fmedium.com\u002F@vince-lam\u002Fdecoding-kaggles-2023-ai-report-essential-tips-for-machine-learning-with-tabular-data-4ed5bf765d8d\",\"isAccessibleForFree\":true}"},"seoDescription":"","shortformType":"SHORTFORM_TYPE_LINK","firstPublishedAt":1698666376482,"viewerEdge":{"__ref":"PostViewerEdge:postId:4ed5bf765d8d-viewerId:lo_40f07550e3be"},"seoTitle":"","isSuspended":false,"license":"ALL_RIGHTS_RESERVED","tags":[{"__ref":"Tag:ai"},{"__ref":"Tag:machine-learning"},{"__ref":"Tag:data-science"},{"__ref":"Tag:data"},{"__ref":"Tag:artificial-intelligence"}],"isFeaturedInPublishedPublication":false,"isNewsletter":false,"statusForCollection":null,"pendingCollection":null,"detectedLanguage":"en","wordCount":2467,"layerCake":0}}</script><script>window.__MIDDLEWARE_STATE__={"session":{"xsrf":""},"cache":{"cacheStatus":"MISS"}}</script><script src="https://cdn-client.medium.com/lite/static/js/manifest.91087f21.js"></script><script src="https://cdn-client.medium.com/lite/static/js/723.093de8f1.js"></script><script src="https://cdn-client.medium.com/lite/static/js/main.668f262a.js"></script><script src="https://cdn-client.medium.com/lite/static/js/instrumentation.47ae8b31.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/reporting.851fdaca.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/5052.eb638269.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/683.abfef39e.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/6618.4aea0357.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/2050.3c25fb60.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3326.9712e10d.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7566.fa51707d.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7908.908acb8a.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3927.2f9f3eed.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/8640.0d3bced2.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9967.f31ca2af.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9214.a792bbcc.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/1214.9ae8faaf.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/556.d95c90dd.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7381.c53435ab.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9768.a85f5560.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/5522.3fb24bd9.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/792.f17e92fb.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3507.8b27b9e8.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7561.fc7962fc.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/4929.75144692.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/6834.6c66e3cc.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/1887.f9daf0b6.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7979.35c5b2af.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7975.3f8d607c.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3877.96683729.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9256.629cdc7e.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/8768.62c3639c.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/144.f38d4759.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3666.6579eeda.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/1069.6236ad3b.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/PostPage.MainContent.e31fff2e.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/2698.9eecb474.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3974.ee0dd7bf.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/2527.358dc2fb.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/PostResponsesContent.3c0c12ee.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/responses.editor.d61aa7c4.chunk.js"></script>
<script id="__LOADABLE_REQUIRED_CHUNKS__" type="application/json">[]</script>
<script id="__LOADABLE_REQUIRED_CHUNKS___ext" type="application/json">{"namedChunks":[]}</script><script>window.main();</script><script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'977757362baaa2c2',t:'MTc1NjU4ODkxNy4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body></html>