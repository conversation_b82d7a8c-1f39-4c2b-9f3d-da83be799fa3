My project gathers the 12-month returns of assets using Yahoo Finance tickers and uses the returns to decide which assets to go long or go short on.
- Python 3.13+
- pandas
- numpy
- matplotlib
- yfinance
- jupyter
- Activate the virtual environment with the required packages and run the script in the main.py file:
- Activate the virtual environment.
source .venv/bin/activate
- Install dependencies.
pip install -r requirements.txt
- Run the full backtest.
python main.py
- Plot of cumulative return at each date, with drawdowns filled in when the highest return seen at the date is higher than the current cumulative return.
- Performance metrics
- Cumulative return
- Sharpe ratio
- Max drawdown
- t-value