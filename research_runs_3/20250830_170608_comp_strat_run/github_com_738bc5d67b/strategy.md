# Alpha Strategy Backtest System

**Source:** https://github.com/yuba316/Alpha_Strategy_BackTest_System


## Thesis

Develop a daily return prediction model for 100 U.S. equities and develop a trading strategy based on alpha factors, including beta, momentum, volatility, price-volume correlation, reverse drawdown, and volume spike. The thesis involves using machine learning algorithms to achieve nonlinear regression of tomorrow's return on today's factor scores and transforming the rank into position weights for each stock.

## Universe & Rebalancing

**Universe:** 100 U.S. equities
**Rebalancing:** daily


## Signals

- **Beta:** The slope of the rolling linear regression between the stock return and the market return with a 1-year window and a 1-quarter half-life.
- **Momentum:** The exponential moving average of the stock’s log return with a 1-year window and a 1-month lag.
- **Volatility:** The exponential moving standard deviation of the stock return with a 1-year window and a 2-month half-life.
- **Price-Volume Correlation:** The rolling covariance of the cross-section rank of the stock’s adjusted price and volume with a 1-month window.
- **Reverse Drawdown:** The rolling max percentage drawdown of the stock’s adjusted price with a 1-month window.
- **Volume Spike:** Daily volume divided by its 5-day moving average.
