# Community Detection Network Analysis for Portfolio Construction

**Source:** https://akiboy96.medium.com/community-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9


## Thesis

The thesis is to construct a portfolio that outperforms the S&P 500 by leveraging community detection and centrality measures in network analysis. The approach involves calculating four types of centralities (degree, betweenness, closeness, and eigenvector) for stocks within the S&P 500 based on their correlation networks. These centralities are combined to form a single metric that identifies influential stocks. By filtering these stocks by sector and centrality value, we aim to create a diversified portfolio with higher expected returns.

## Universe & Rebalancing

**Universe:** S&P 500
**Rebalancing:** monthly


## Signals

- **Degree Centrality:** The number of links held by each node in the network.
- **Betweenness Centrality:** The number of times a node lies on the shortest path between other nodes.
- **Closeness Centrality:** Scores each node based on their 'closeness' to all other nodes in the network.
- **Eigen Centrality:** Measures a node’s influence based on the number of links it has to other nodes in the network.
