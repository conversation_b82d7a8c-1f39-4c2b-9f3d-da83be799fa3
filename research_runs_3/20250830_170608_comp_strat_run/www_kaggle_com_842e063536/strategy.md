# Stock Market Analysis & Prediction using LSTM

**Source:** https://www.kaggle.com/code/faressayah/stock-market-analysis-prediction-using-lstm/notebook


## Thesis

The thesis of this project is to develop a predictive model for stock market analysis and prediction using Long Short-Term Memory (LSTM) networks. The goal is to leverage the power of LSTM to capture long-term dependencies in time-series data, which can be used to forecast future prices or returns of stocks. This approach aims to provide insights into potential trading opportunities by predicting price movements based on historical data.

## Universe & Rebalancing

**Universe:** The universe consists of a specific set of stocks from the S&P 500 index for demonstration purposes. The exact list of tickers is not provided in the source text, but it implies using a subset of highly traded and liquid stocks within this major market index.
**Rebalancing:** Monthly


## Signals

- **LSTM Predicted Return Signal:** The signal is generated by training an LSTM model on historical stock price data. The model predicts future returns for each stock in the universe, and these predictions are used to determine whether a stock should be bought (positive predicted return), sold (negative predicted return), or held (neutral prediction).
