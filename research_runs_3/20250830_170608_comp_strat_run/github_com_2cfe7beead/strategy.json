{"title": "Backtesting Algos", "source_url": "https://github.com/PhilipWaddilove/Backtesting-Algos", "thesis": "The analysis evaluates common algo trading signals and their effectiveness in delivering profitable signals, comparing risks and returns of each strategy. It also explores the efficacy of combining these strategies using machine learning techniques such as deep learning models like LSTM to optimize signal performance.", "universe": "ETFs (all subsets of Standard & Poor's 'SPY' index) or Cryptocurrency", "rebalancing": "annual", "signals": [{"name": "SMA", "definition": "Simple Moving Average with a lookback window"}, {"name": "EMA", "definition": "Exponentially Weighted Moving Average with a lookback window"}, {"name": "BBD", "definition": "Bollinger Bands, calculated over a given lookback period"}, {"name": "RSI", "definition": "Relative Strength Index, used to measure the speed and change of price movements"}]}