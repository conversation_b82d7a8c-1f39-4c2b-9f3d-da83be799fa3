<!doctype html><html lang="en"><head><title data-rh="true">Data Science Hackathons: Winner’s Guide (Part 1) | by <PERSON><PERSON> | Medium</title><meta data-rh="true" charset="utf-8"/><meta data-rh="true" name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1,maximum-scale=1"/><meta data-rh="true" name="theme-color" content="#000000"/><meta data-rh="true" name="twitter:app:name:iphone" content="Medium"/><meta data-rh="true" name="twitter:app:id:iphone" content="828256236"/><meta data-rh="true" property="al:ios:app_name" content="Medium"/><meta data-rh="true" property="al:ios:app_store_id" content="828256236"/><meta data-rh="true" property="al:android:package" content="com.medium.reader"/><meta data-rh="true" property="fb:app_id" content="542599432471018"/><meta data-rh="true" property="og:site_name" content="Medium"/><meta data-rh="true" name="apple-itunes-app" content="app-id=828256236, app-argument=/data-science-hackathons-winners-guide-part-1-fecbf3b2c023, affiliate-data=pt=698524&amp;ct=smart_app_banner&amp;mt=8"/><meta data-rh="true" property="og:type" content="article"/><meta data-rh="true" property="article:published_time" content="2023-11-28T13:49:17.306Z"/><meta data-rh="true" name="title" content="Data Science Hackathons: Winner’s Guide (Part 1) | by Yash Khandelwal | Medium"/><meta data-rh="true" property="og:title" content="Data Science Hackathons: Winner’s Guide (Part 1)"/><meta data-rh="true" property="al:android:url" content="medium://p/fecbf3b2c023"/><meta data-rh="true" property="al:ios:url" content="medium://p/fecbf3b2c023"/><meta data-rh="true" property="al:android:app_name" content="Medium"/><meta data-rh="true" name="description" content="Get ready to up your game in data science hackathons! In this blog, I’ll be sharing the winning solution approaches &amp; best practices in Data Science &amp; Machine Learning. It’s a practical guide to help…"/><meta data-rh="true" property="og:description" content="Tactics for Mastering Data Science Hackathons"/><meta data-rh="true" property="og:url" content="https://yashkhandelwal07.medium.com/data-science-hackathons-winners-guide-part-1-fecbf3b2c023"/><meta data-rh="true" property="al:web:url" content="https://yashkhandelwal07.medium.com/data-science-hackathons-winners-guide-part-1-fecbf3b2c023"/><meta data-rh="true" property="og:image" content="https://miro.medium.com/v2/resize:fit:1200/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg"/><meta data-rh="true" property="article:author" content="https://yashkhandelwal07.medium.com"/><meta data-rh="true" name="author" content="Yash Khandelwal"/><meta data-rh="true" name="robots" content="index,noarchive,follow,max-image-preview:large"/><meta data-rh="true" name="referrer" content="unsafe-url"/><meta data-rh="true" property="twitter:title" content="Data Science Hackathons: Winner’s Guide (Part 1)"/><meta data-rh="true" name="twitter:site" content="@Medium"/><meta data-rh="true" name="twitter:app:url:iphone" content="medium://p/fecbf3b2c023"/><meta data-rh="true" property="twitter:description" content="Tactics for Mastering Data Science Hackathons"/><meta data-rh="true" name="twitter:image:src" content="https://miro.medium.com/v2/resize:fit:1200/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg"/><meta data-rh="true" name="twitter:card" content="summary_large_image"/><meta data-rh="true" name="twitter:label1" content="Reading time"/><meta data-rh="true" name="twitter:data1" content="7 min read"/><link data-rh="true" rel="icon" href="https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19"/><link data-rh="true" rel="search" type="application/opensearchdescription+xml" title="Medium" href="/osd.xml"/><link data-rh="true" rel="apple-touch-icon" sizes="152x152" href="https://miro.medium.com/v2/resize:fill:304:304/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="apple-touch-icon" sizes="120x120" href="https://miro.medium.com/v2/resize:fill:240:240/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="apple-touch-icon" sizes="76x76" href="https://miro.medium.com/v2/resize:fill:152:152/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="apple-touch-icon" sizes="60x60" href="https://miro.medium.com/v2/resize:fill:120:120/10fd5c419ac61637245384e7099e131627900034828f4f386bdaa47a74eae156"/><link data-rh="true" rel="mask-icon" href="https://miro.medium.com/v2/resize:fill:1000:1000/7*GAOKVe--MXbEJmV9230oOQ.png" color="#171717"/><link data-rh="true" rel="preconnect" href="https://glyph.medium.com" crossOrigin=""/><link data-rh="true" rel="manifest" href="/manifest.json"/><link data-rh="true" rel="preconnect" href="https://www.google.com"/><link data-rh="true" rel="preconnect" href="https://www.gstatic.com" crossOrigin=""/><link data-rh="true" id="glyph_preload_link" rel="preload" as="style" type="text/css" href="https://glyph.medium.com/css/unbound.css"/><link data-rh="true" id="glyph_link" rel="stylesheet" type="text/css" href="https://glyph.medium.com/css/unbound.css"/><link data-rh="true" rel="author" href="https://yashkhandelwal07.medium.com"/><link data-rh="true" rel="canonical" href="https://yashkhandelwal07.medium.com/data-science-hackathons-winners-guide-part-1-fecbf3b2c023"/><link data-rh="true" rel="alternate" href="android-app://com.medium.reader/https/medium.com/p/fecbf3b2c023"/><script data-rh="true" type="application/ld+json">{"@context":"https://schema.org","@id":"https://yashkhandelwal07.medium.com/data-science-hackathons-winners-guide-part-1-fecbf3b2c023","@type":"SocialMediaPosting","image":["https://miro.medium.com/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg"],"url":"https://yashkhandelwal07.medium.com/data-science-hackathons-winners-guide-part-1-fecbf3b2c023","dateCreated":"2023-11-28T13:46:13Z","datePublished":"2023-11-28T13:46:13Z","dateModified":"2023-11-28T13:49:17Z","headline":"Get ready to up your game in data science hackathons!","name":"Get ready to up your game in data science hackathons!","description":"Get ready to up your game in data science hackathons! In this blog, I’ll be sharing the winning solution approaches \u0026 best practices in Data Science \u0026 Machine Learning. It’s a practical guide to …","identifier":"fecbf3b2c023","author":{"@context":"https://schema.org","@id":"https://medium.com/@yashkhandelwal07","@type":"Person","identifier":"yashkhandelwal07","name":"Yash Khandelwal","url":"https://medium.com/@yashkhandelwal07"},"creator":{"@context":"https://schema.org","@id":"https://medium.com/@yashkhandelwal07","@type":"Person","identifier":"yashkhandelwal07","name":"Yash Khandelwal","url":"https://medium.com/@yashkhandelwal07"},"publisher":{"@context":"https://schema.org","@type":"Organization","@id":"https://medium.com","name":"Medium","url":"https://medium.com","logo":{"@type":"ImageObject","width":500,"height":110,"url":"https://miro.medium.com/v2/resize:fit:500/7%2AV1_7XP4snlmqrc_0Njontw.png"}},"mainEntityOfPage":"https://yashkhandelwal07.medium.com/data-science-hackathons-winners-guide-part-1-fecbf3b2c023","isAccessibleForFree":true}</script><script data-rh="true" src="https://www.google.com/recaptcha/enterprise.js?render=6Le-uGgpAAAAAPprRaokM8AKthQ9KNGdoxaGUvVp" async="true"></script><style type="text/css" data-fela-rehydration="500" data-fela-type="STATIC">html{box-sizing:border-box;-webkit-text-size-adjust:100%}*, *:before, *:after{box-sizing:inherit}body{margin:0;padding:0;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;color:rgba(0,0,0,0.8);position:relative;min-height:100vh}h1, h2, h3, h4, h5, h6, dl, dd, ol, ul, menu, figure, blockquote, p, pre, form{margin:0}menu, ol, ul{padding:0;list-style:none;list-style-image:none}main{display:block}a{color:inherit;text-decoration:none}a, button, input{-webkit-tap-highlight-color:transparent}img, svg{vertical-align:middle}button{background:transparent;overflow:visible}button, input, optgroup, select, textarea{margin:0}:root{--reach-tabs:1;--reach-menu-button:1}#speechify-root{font-family:Sohne, sans-serif}div[data-popper-reference-hidden="true"]{visibility:hidden;pointer-events:none}.grecaptcha-badge{visibility:hidden}
/*XCode style (c) Angel Garcia <<EMAIL>>*/.hljs {background: #fff;color: black;
}/* Gray DOCTYPE selectors like WebKit */
.xml .hljs-meta {color: #c0c0c0;
}.hljs-comment,
.hljs-quote {color: #007400;
}.hljs-tag,
.hljs-attribute,
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-name {color: #aa0d91;
}.hljs-variable,
.hljs-template-variable {color: #3F6E74;
}.hljs-code,
.hljs-string,
.hljs-meta .hljs-string {color: #c41a16;
}.hljs-regexp,
.hljs-link {color: #0E0EFF;
}.hljs-title,
.hljs-symbol,
.hljs-bullet,
.hljs-number {color: #1c00cf;
}.hljs-section,
.hljs-meta {color: #643820;
}.hljs-title.class_,
.hljs-class .hljs-title,
.hljs-type,
.hljs-built_in,
.hljs-params {color: #5c2699;
}.hljs-attr {color: #836C28;
}.hljs-subst {color: #000;
}.hljs-formula {background-color: #eee;font-style: italic;
}.hljs-addition {background-color: #baeeba;
}.hljs-deletion {background-color: #ffc8bd;
}.hljs-selector-id,
.hljs-selector-class {color: #9b703f;
}.hljs-doctag,
.hljs-strong {font-weight: bold;
}.hljs-emphasis {font-style: italic;
}
</style><style type="text/css" data-fela-rehydration="500" data-fela-type="KEYFRAME">@-webkit-keyframes k1{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}@-moz-keyframes k1{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}@keyframes k1{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}</style><style type="text/css" data-fela-rehydration="500" data-fela-type="RULE">.a{font-family:medium-content-sans-serif-font, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif}.b{font-weight:400}.c{background-color:rgba(255, 255, 255, 1)}.d{display:none}.m{display:block}.n{position:sticky}.o{top:0}.p{z-index:500}.q{padding:0 24px}.r{align-items:center}.s{border-bottom:solid 1px #F2F2F2}.z{height:41px}.ab{line-height:20px}.ac{display:flex}.ae{height:57px}.af{flex:1 0 auto}.ag{color:inherit}.ah{fill:inherit}.ai{font-size:inherit}.aj{border:none}.ak{font-family:inherit}.al{letter-spacing:inherit}.am{font-weight:inherit}.an{padding:0}.ao{margin:0}.ap{cursor:pointer}.aq:disabled{cursor:not-allowed}.ar:disabled{color:#6B6B6B}.as:disabled{fill:#6B6B6B}.av{width:auto}.aw path{fill:#242424}.ax{height:25px}.ay{margin-left:24px}.az{border-radius:20px}.ba{width:240px}.bb{background:#F9F9F9}.bc path{fill:#6B6B6B}.be{outline:none}.bf{font-family:sohne, "Helvetica Neue", Helvetica, Arial, sans-serif}.bg{font-size:14px}.bh{width:100%}.bi{padding:10px 20px 10px 0}.bj{background-color:transparent}.bk{color:#242424}.bl::placeholder{color:#6B6B6B}.bm{display:inline-block}.bn{margin-left:12px}.bo{margin-right:12px}.bp{border-radius:4px}.bq{height:24px}.bw{background-color:#F9F9F9}.bx{border-radius:50%}.by{height:32px}.bz{width:32px}.ca{flex:1 1 auto}.cb{justify-content:center}.ch{max-width:680px}.ci{min-width:0}.cj{animation:k1 1.2s ease-in-out infinite}.ck{height:100vh}.cl{margin-bottom:16px}.cm{margin-top:48px}.cn{align-items:flex-start}.co{flex-direction:column}.cp{justify-content:space-between}.cq{margin-bottom:24px}.cw{width:80%}.cx{background-color:#F2F2F2}.dd{height:44px}.de{width:44px}.df{margin:auto 0}.dg{margin-bottom:4px}.dh{height:16px}.di{width:120px}.dj{width:80px}.dp{margin-bottom:8px}.dq{width:96%}.dr{width:98%}.ds{width:81%}.dt{margin-left:8px}.du{color:#6B6B6B}.dv{font-size:13px}.dw{height:100%}.ep{color:#FFFFFF}.eq{fill:#FFFFFF}.er{background:#1A8917}.es{border-color:#1A8917}.ew:disabled{cursor:inherit !important}.ex:disabled{opacity:0.3}.ey:disabled:hover{background:#1A8917}.ez:disabled:hover{border-color:#1A8917}.fa{border-radius:99em}.fb{border-width:1px}.fc{border-style:solid}.fd{box-sizing:border-box}.fe{text-decoration:none}.ff{text-align:center}.fg{margin-left:16px}.fh{border:inherit}.fk{margin-right:32px}.fl{position:relative}.fm{fill:#6B6B6B}.fp{background:transparent}.fq svg{margin-left:4px}.fr svg{fill:#6B6B6B}.ft{box-shadow:inset 0 0 0 1px rgba(0, 0, 0, 0.05)}.fu{position:absolute}.gb{margin:0 24px}.gf{background:rgba(255, 255, 255, 1)}.gg{border:1px solid #F2F2F2}.gh{box-shadow:0 1px 4px #F2F2F2}.gi{max-height:100vh}.gj{overflow-y:auto}.gk{left:0}.gl{top:calc(100vh + 100px)}.gm{bottom:calc(100vh + 100px)}.gn{width:10px}.go{pointer-events:none}.gp{margin-top:32px}.ha{gap:12px}.hb{align-items:baseline}.hc{width:36px}.hd{height:36px}.he{border:2px solid rgba(255, 255, 255, 1)}.hf{z-index:0}.hg{box-shadow:none}.hh{border:1px solid rgba(0, 0, 0, 0.05)}.hi{margin-bottom:2px}.hj{flex-wrap:nowrap}.hl{width:12px}.hm{flex-wrap:wrap}.hn{padding-left:8px}.ho{padding-right:8px}.ip> *{flex-shrink:0}.iq{overflow-x:scroll}.ir::-webkit-scrollbar{display:none}.is{scrollbar-width:none}.it{-ms-overflow-style:none}.iu{width:74px}.iv{flex-direction:row}.iw{z-index:2}.ix{margin-right:4px}.ja{-webkit-user-select:none}.jb{border:0}.jc{fill:rgba(117, 117, 117, 1)}.jf{outline:0}.jg{user-select:none}.jh> svg{pointer-events:none}.jq{cursor:progress}.jr{opacity:1}.js{padding:4px 0}.jv{margin-top:0px}.jw{width:16px}.jy{display:inline-flex}.ke{max-width:100%}.kf{padding:8px 2px}.kg svg{color:#6B6B6B}.kx{word-break:break-word}.ky{word-wrap:break-word}.kz:after{display:block}.la:after{content:""}.lb:after{clear:both}.lc{margin-left:auto}.ld{margin-right:auto}.le{max-width:3200px}.lf{clear:both}.lh{cursor:zoom-in}.li{z-index:auto}.lk{width:1px}.ll{height:1px}.lm{margin:-1px}.ln{overflow:hidden}.lo{clip:rect(0, 0, 0, 0)}.lp{white-space:nowrap}.lq{border-width:0}.lr{height:auto}.ls{margin-top:10px}.lt{max-width:728px}.lw{line-height:1.58}.lx{letter-spacing:-0.004em}.ly{font-style:normal}.lz{font-family:source-serif-pro, Georgia, Cambria, "Times New Roman", Times, serif}.mu{margin-bottom:-0.46em}.mv{line-height:1.12}.mw{letter-spacing:-0.022em}.mx{font-weight:600}.ns{margin-bottom:-0.28em}.nt{max-width:4714px}.nz{text-decoration:underline}.oa{max-width:7952px}.ob{font-weight:700}.oh{max-width:850px}.oi{font-style:italic}.oj{overflow-x:auto}.ok{font-family:source-code-pro, Menlo, Monaco, "Courier New", Courier, monospace}.ol{padding:32px}.om{border:1px solid #E5E5E5}.on{line-height:1.4}.oo{margin-top:-0.2em}.op{margin-bottom:-0.2em}.oq{white-space:pre}.or{min-width:fit-content}.os{margin-top:16px}.ot{list-style-type:decimal}.ou{margin-left:30px}.ov{padding-left:0px}.pb{margin-bottom:26px}.pc{margin-top:6px}.pd{margin-top:8px}.pe{margin-right:8px}.pf{padding:8px 16px}.pg{border-radius:100px}.ph{transition:background 300ms ease}.pj{border-top:none}.pk{margin-bottom:50px}.pl{height:52px}.pm{max-height:52px}.pn{box-sizing:content-box}.po{position:static}.pp{z-index:1}.pr{max-width:155px}.px{margin-right:20px}.py{flex:0 0 auto}.pz{margin-bottom:64px}.qm{height:48px}.qn{width:48px}.qp{height:64px}.qq{width:64px}.qr{align-self:flex-end}.qx{padding-right:4px}.qy{font-weight:500}.rl{white-space:pre-wrap}.rm{margin:0 8px}.rn{margin-bottom:54px}.ro{height:0px}.rp{gap:18px}.rq{fill:rgba(61, 61, 61, 1)}.sc{border-bottom:solid 1px #E5E5E5}.sd{margin-top:72px}.se{padding:24px 0}.sf{margin-bottom:0px}.sg{margin-right:16px}.at:hover:not(:disabled){color:rgba(25, 25, 25, 1)}.au:hover:not(:disabled){fill:rgba(25, 25, 25, 1)}.et:hover{background:#156D12}.eu:hover{border-color:#156D12}.ev:hover{cursor:pointer}.fn:hover{color:#242424}.fo:hover{fill:#242424}.fs:hover svg{fill:#242424}.fv:hover{background-color:rgba(0, 0, 0, 0.1)}.hk:hover{text-decoration:underline}.je:hover{fill:rgba(8, 8, 8, 1)}.jt:hover{fill:#000000}.ju:hover p{color:#000000}.jx:hover{color:#000000}.kh:hover svg{color:#000000}.pi:hover{background-color:#F2F2F2}.qo:hover{background-color:none}.rr:hover{fill:rgba(25, 25, 25, 1)}.bd:focus-within path{fill:#242424}.jd:focus{fill:rgba(8, 8, 8, 1)}.ki:focus svg{color:#000000}.lj:focus{transform:scale(1.01)}.ji:active{border-style:none}</style><style type="text/css" data-fela-rehydration="500" data-fela-type="RULE" media="all and (min-width: 1080px)">.e{display:none}.bv{width:64px}.cg{margin:0 64px}.cv{height:48px}.dc{margin-bottom:52px}.do{margin-bottom:48px}.ef{font-size:14px}.eg{line-height:20px}.em{font-size:13px}.eo{padding:5px 12px}.fj{display:flex}.ga{margin-bottom:50px}.ge{max-width:680px}.gy{align-items:center}.gz{flex-direction:row}.ib{border-top:solid 1px #F2F2F2}.ic{border-bottom:solid 1px #F2F2F2}.id{margin:32px 0 0}.ie{padding:3px 8px}.in> *{margin-right:24px}.io> :last-child{margin-right:0}.jp{margin-top:0px}.kd{margin:0}.mq{font-size:20px}.mr{margin-top:2.14em}.ms{line-height:32px}.mt{letter-spacing:-0.003em}.no{font-size:24px}.np{margin-top:1.95em}.nq{line-height:30px}.nr{letter-spacing:-0.016em}.ny{margin-top:56px}.og{margin-top:0.94em}.pa{margin-top:1.14em}.pw{display:inline-block}.qc{margin-bottom:0}.qd{margin-right:20px}.qs{max-width:500px}.rj{line-height:24px}.rk{letter-spacing:0}.rw{margin:40px 0 0}.sb{padding-top:72px}</style><style type="text/css" data-fela-rehydration="500" data-fela-type="RULE" media="all and (max-width: 1079.98px)">.f{display:none}.jo{margin-top:0px}.lu{margin-left:auto}.lv{text-align:center}.pv{display:inline-block}</style><style type="text/css" data-fela-rehydration="500" data-fela-type="RULE" media="all and (max-width: 903.98px)">.g{display:none}.jn{margin-top:0px}.pu{display:inline-block}</style><style type="text/css" data-fela-rehydration="500" data-fela-type="RULE" media="all and (max-width: 727.98px)">.h{display:none}.jl{margin-top:0px}.jm{margin-right:0px}.pt{display:inline-block}</style><style type="text/css" data-fela-rehydration="500" data-fela-type="RULE" media="all and (max-width: 551.98px)">.i{display:none}.t{display:flex}.u{justify-content:space-between}.br{width:24px}.cc{margin:0 24px}.cr{height:40px}.cy{margin-bottom:44px}.dk{margin-bottom:32px}.dx{font-size:13px}.dy{line-height:20px}.eh{padding:0px 8px 1px}.fw{margin-bottom:2px}.gq{align-items:flex-start}.gr{flex-direction:column-reverse}.hp{margin:24px -24px 0}.hq{padding:0}.if> *{margin-right:8px}.ig> :last-child{margin-right:24px}.iy{margin-left:0px}.jj{margin-top:0px}.jk{margin-right:0px}.jz{margin:0}.kj{border:1px solid #F2F2F2}.kk{border-radius:99em}.kl{padding:0px 16px 0px 12px}.km{height:38px}.kn{align-items:center}.kp svg{margin-right:8px}.ma{font-size:18px}.mb{margin-top:1.56em}.mc{line-height:28px}.md{letter-spacing:-0.003em}.my{font-size:20px}.mz{margin-top:1.2em}.na{line-height:24px}.nb{letter-spacing:0}.nu{margin-top:40px}.oc{margin-top:0.67em}.ow{margin-top:1.34em}.ps{display:inline-block}.qb{flex-direction:column}.qk{margin-bottom:20px}.ql{margin-right:0}.qw{max-width:100%}.qz{font-size:24px}.ra{line-height:30px}.rb{letter-spacing:-0.016em}.rs{margin:32px 0 0}.rx{padding-top:48px}.ko:hover{border-color:#E5E5E5}</style><style type="text/css" data-fela-rehydration="500" data-fela-type="RULE" media="all and (min-width: 904px) and (max-width: 1079.98px)">.j{display:none}.bu{width:64px}.cf{margin:0 64px}.cu{height:48px}.db{margin-bottom:52px}.dn{margin-bottom:48px}.ed{font-size:14px}.ee{line-height:20px}.ek{font-size:13px}.el{padding:5px 12px}.fi{display:flex}.fz{margin-bottom:50px}.gd{max-width:680px}.gw{align-items:center}.gx{flex-direction:row}.hx{border-top:solid 1px #F2F2F2}.hy{border-bottom:solid 1px #F2F2F2}.hz{margin:32px 0 0}.ia{padding:3px 8px}.il> *{margin-right:24px}.im> :last-child{margin-right:0}.kc{margin:0}.mm{font-size:20px}.mn{margin-top:2.14em}.mo{line-height:32px}.mp{letter-spacing:-0.003em}.nk{font-size:24px}.nl{margin-top:1.95em}.nm{line-height:30px}.nn{letter-spacing:-0.016em}.nx{margin-top:56px}.of{margin-top:0.94em}.oz{margin-top:1.14em}.qe{margin-bottom:0}.qf{margin-right:20px}.qt{max-width:500px}.rh{line-height:24px}.ri{letter-spacing:0}.rv{margin:40px 0 0}.sa{padding-top:72px}</style><style type="text/css" data-fela-rehydration="500" data-fela-type="RULE" media="all and (min-width: 728px) and (max-width: 903.98px)">.k{display:none}.x{display:flex}.y{justify-content:space-between}.bt{width:64px}.ce{margin:0 48px}.ct{height:48px}.da{margin-bottom:52px}.dm{margin-bottom:48px}.eb{font-size:13px}.ec{line-height:20px}.ej{padding:0px 8px 1px}.fy{margin-bottom:50px}.gc{max-width:680px}.gu{align-items:center}.gv{flex-direction:row}.ht{border-top:solid 1px #F2F2F2}.hu{border-bottom:solid 1px #F2F2F2}.hv{margin:32px 0 0}.hw{padding:3px 8px}.ij> *{margin-right:24px}.ik> :last-child{margin-right:0}.kb{margin:0}.mi{font-size:20px}.mj{margin-top:2.14em}.mk{line-height:32px}.ml{letter-spacing:-0.003em}.ng{font-size:24px}.nh{margin-top:1.95em}.ni{line-height:30px}.nj{letter-spacing:-0.016em}.nw{margin-top:56px}.oe{margin-top:0.94em}.oy{margin-top:1.14em}.qg{margin-bottom:0}.qh{margin-right:20px}.qu{max-width:500px}.rf{line-height:24px}.rg{letter-spacing:0}.ru{margin:40px 0 0}.rz{padding-top:72px}</style><style type="text/css" data-fela-rehydration="500" data-fela-type="RULE" media="all and (min-width: 552px) and (max-width: 727.98px)">.l{display:none}.v{display:flex}.w{justify-content:space-between}.bs{width:24px}.cd{margin:0 24px}.cs{height:40px}.cz{margin-bottom:44px}.dl{margin-bottom:32px}.dz{font-size:13px}.ea{line-height:20px}.ei{padding:0px 8px 1px}.fx{margin-bottom:2px}.gs{align-items:flex-start}.gt{flex-direction:column-reverse}.hr{margin:24px 0 0}.hs{padding:0}.ih> *{margin-right:8px}.ii> :last-child{margin-right:8px}.iz{margin-left:0px}.ka{margin:0}.kq{border:1px solid #F2F2F2}.kr{border-radius:99em}.ks{padding:0px 16px 0px 12px}.kt{height:38px}.ku{align-items:center}.kw svg{margin-right:8px}.me{font-size:18px}.mf{margin-top:1.56em}.mg{line-height:28px}.mh{letter-spacing:-0.003em}.nc{font-size:20px}.nd{margin-top:1.2em}.ne{line-height:24px}.nf{letter-spacing:0}.nv{margin-top:40px}.od{margin-top:0.67em}.ox{margin-top:1.34em}.qa{flex-direction:column}.qi{margin-bottom:20px}.qj{margin-right:0}.qv{max-width:100%}.rc{font-size:24px}.rd{line-height:30px}.re{letter-spacing:-0.016em}.rt{margin:32px 0 0}.ry{padding-top:48px}.kv:hover{border-color:#E5E5E5}</style><style type="text/css" data-fela-rehydration="500" data-fela-type="RULE" media="print">.pq{display:none}</style><style type="text/css" data-fela-rehydration="500" data-fela-type="RULE" media="(prefers-reduced-motion: no-preference)">.lg{transition:transform 300ms cubic-bezier(0.2, 0, 0.2, 1)}</style></head><body><div id="root"><div class="a b c"><a href="/sitemap/sitemap.xml" class="d">Sitemap</a><div class="e f g h i j k l"></div><script>document.domain = document.domain;</script><div class="m c"><div class="m n o p c"><div class="q r s t u v w x y j e z ab"><a class="du ah dv bf al b an ao ap aq ar as at au t v x j e r dw ab" href="https://rsci.app.link/?%24canonical_url=https%3A%2F%2Fmedium.com%2Fp%2Ffecbf3b2c023&amp;%7Efeature=LoOpenInAppButton&amp;%7Echannel=ShowPostUnderUser&amp;%7Estage=mobileNavBar&amp;source=post_page---top_nav_layout_nav-----------------------------------------" rel="noopener follow">Open in app<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="none" viewBox="0 0 10 10" class="dt"><path fill="currentColor" d="M.985 8.485a.375.375 0 1 0 .53.53zM8.75 1.25h.375A.375.375 0 0 0 8.75.875zM8.375 6.5a.375.375 0 1 0 .75 0zM3.5.875a.375.375 0 1 0 0 .75zm-1.985 8.14 7.5-7.5-.53-.53-7.5 7.5zm6.86-7.765V6.5h.75V1.25zM3.5 1.625h5.25v-.75H3.5z"></path></svg></a><div class="ac r"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><button class="bf b dx dy eh dz ea ei eb ec ej ek ee el em eg eo ep eq er es et eu ev ew ex ey ez fa fb fc fd bm fe ff" data-testid="headerSignUpButton">Sign up</button></span></p><div class="fg m"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerSignInButton" href="https://medium.com/m/signin?operation=login&amp;redirect=https%3A%2F%2Fyashkhandelwal07.medium.com%2Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023&amp;source=post_page---top_nav_layout_nav-----------------------global_nav------------------" rel="noopener follow">Sign in</a></span></p></div></div></div><div class="q r s ac ae"><div class="ac r af"><a class="ag ah ai aj ak al am an ao ap aq ar as at au ac" aria-label="Homepage" data-testid="headerMediumLogo" href="https://medium.com/?source=post_page---top_nav_layout_nav-----------------------------------------" rel="noopener follow"><svg xmlns="http://www.w3.org/2000/svg" width="719" height="160" fill="none" aria-labelledby="wordmark-medium-desc" viewBox="0 0 719 160" class="av aw ax"><desc id="wordmark-medium-desc">Medium Logo</desc><path fill="#242424" d="m174.104 9.734.215-.047V8.02H130.39L89.6 103.89 48.81 8.021H1.472v1.666l.212.047c8.018 1.81 12.09 4.509 12.09 14.242V137.93c0 9.734-4.087 12.433-12.106 14.243l-.212.047v1.671h32.118v-1.665l-.213-.048c-8.018-1.809-12.089-4.509-12.089-14.242V30.586l52.399 123.305h2.972l53.925-126.743V140.75c-.687 7.688-4.721 10.062-11.982 11.701l-.215.05v1.652h55.948v-1.652l-.215-.05c-7.269-1.639-11.4-4.013-12.087-11.701l-.037-116.774h.037c0-9.733 4.071-12.432 12.087-14.242m25.555 75.488c.915-20.474 8.268-35.252 20.606-35.507 3.806.063 6.998 1.312 9.479 3.714 5.272 5.118 7.751 15.812 7.368 31.793zm-.553 5.77h65.573v-.275c-.186-15.656-4.721-27.834-13.466-36.196-7.559-7.227-18.751-11.203-30.507-11.203h-.263c-6.101 0-13.584 1.48-18.909 4.16-6.061 2.807-11.407 7.003-15.855 12.511-7.161 8.874-11.499 20.866-12.554 34.343q-.05.606-.092 1.212a50 50 0 0 0-.065 1.151 85.807 85.807 0 0 0-.094 5.689c.71 30.524 17.198 54.917 46.483 54.917 25.705 0 40.675-18.791 44.407-44.013l-1.886-.664c-6.557 13.556-18.334 21.771-31.738 20.769-18.297-1.369-32.314-19.922-31.042-42.395m139.722 41.359c-2.151 5.101-6.639 7.908-12.653 7.908s-11.513-4.129-15.418-11.63c-4.197-8.053-6.405-19.436-6.405-32.92 0-28.067 8.729-46.22 22.24-46.22 5.657 0 10.111 2.807 12.236 7.704zm43.499 20.008c-8.019-1.897-12.089-4.722-12.089-14.951V1.309l-48.716 14.353v1.757l.299-.024c6.72-.543 11.278.386 13.925 2.83 2.072 1.915 3.082 4.853 3.082 8.987v18.66c-4.803-3.067-10.516-4.56-17.448-4.56-14.059 0-26.909 5.92-36.176 16.672-9.66 11.205-14.767 26.518-14.767 44.278-.003 31.72 15.612 53.039 38.851 53.039 13.595 0 24.533-7.449 29.54-20.013v16.865h43.711v-1.746zM424.1 19.819c0-9.904-7.468-17.374-17.375-17.374-9.859 0-17.573 7.632-17.573 17.374s7.721 17.374 17.573 17.374c9.907 0 17.375-7.47 17.375-17.374m11.499 132.546c-8.019-1.897-12.089-4.722-12.089-14.951h-.035V43.635l-43.714 12.551v1.705l.263.024c9.458.842 12.047 4.1 12.047 15.152v81.086h43.751v-1.746zm112.013 0c-8.018-1.897-12.089-4.722-12.089-14.951V43.635l-41.621 12.137v1.71l.246.026c7.733.813 9.967 4.257 9.967 15.36v59.279c-2.578 5.102-7.415 8.131-13.274 8.336-9.503 0-14.736-6.419-14.736-18.073V43.638l-43.714 12.55v1.703l.262.024c9.459.84 12.05 4.097 12.05 15.152v50.17a56.3 56.3 0 0 0 .91 10.444l.787 3.423c3.701 13.262 13.398 20.197 28.59 20.197 12.868 0 24.147-7.966 29.115-20.43v17.311h43.714v-1.747zm169.818 1.788v-1.749l-.213-.05c-8.7-2.006-12.089-5.789-12.089-13.49v-63.79c0-19.89-11.171-31.761-29.883-31.761-13.64 0-25.141 7.882-29.569 20.16-3.517-13.01-13.639-20.16-28.606-20.16-13.146 0-23.449 6.938-27.869 18.657V43.643L545.487 55.68v1.715l.263.024c9.345.829 12.047 4.181 12.047 14.95v81.784h40.787v-1.746l-.215-.053c-6.941-1.631-9.181-4.606-9.181-12.239V66.998c1.836-4.289 5.537-9.37 12.853-9.37 9.086 0 13.692 6.296 13.692 18.697v77.828h40.797v-1.746l-.215-.053c-6.94-1.631-9.18-4.606-9.18-12.239V75.066a42 42 0 0 0-.578-7.26c1.947-4.661 5.86-10.177 13.475-10.177 9.214 0 13.691 6.114 13.691 18.696v77.828z"></path></svg></a><div class="ay i"><div class="ac aj az ba bb r bc bd"><div class="bm" aria-hidden="false" aria-describedby="searchResults" aria-labelledby="searchResults" aria-haspopup="listbox" role="listbox"></div><div class="bn bo ac"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M4.092 11.06a6.95 6.95 0 1 1 13.9 0 6.95 6.95 0 0 1-13.9 0m6.95-8.05a8.05 8.05 0 1 0 5.13 14.26l3.75 3.75a.56.56 0 1 0 .79-.79l-3.73-3.73A8.05 8.05 0 0 0 11.042 3z" clip-rule="evenodd"></path></svg></div><input role="combobox" aria-controls="searchResults" aria-expanded="false" aria-label="search" data-testid="headerSearchInput" tabindex="0" class="aj be bf bg ab bh bi bj bk bl" placeholder="Search" value=""/></div></div></div><div class="i l x fi fj"><div class="fk ac"><span data-dd-action-name="Susi presentation tracker new_post_topnav"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerWriteButton" href="https://medium.com/m/signin?operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fnew-story&amp;source=---top_nav_layout_nav-----------------------new_post_topnav------------------" rel="noopener follow"><div class="bf b bg ab du fl fm ac r fn fo"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" aria-label="Write"><path fill="currentColor" d="M14 4a.5.5 0 0 0 0-1zm7 6a.5.5 0 0 0-1 0zm-7-7H4v1h10zM3 4v16h1V4zm1 17h16v-1H4zm17-1V10h-1v10zm-1 1a1 1 0 0 0 1-1h-1zM3 20a1 1 0 0 0 1 1v-1zM4 3a1 1 0 0 0-1 1h1z"></path><path stroke="currentColor" d="m17.5 4.5-8.458 8.458a.25.25 0 0 0-.06.098l-.824 2.47a.25.25 0 0 0 .316.316l2.47-.823a.25.25 0 0 0 .098-.06L19.5 6.5m-2-2 2.323-2.323a.25.25 0 0 1 .354 0l1.646 1.646a.25.25 0 0 1 0 .354L19.5 6.5m-2-2 2 2"></path></svg><div class="dt m">Write</div></div></a></span></div></div><div class="l k j e"><div class="fk ac"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerSearchButton" href="https://medium.com/search?source=post_page---top_nav_layout_nav-----------------------------------------" rel="noopener follow"><div class="bf b bg ab du fl fm ac r fn fo"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" aria-label="Search"><path fill="currentColor" fill-rule="evenodd" d="M4.092 11.06a6.95 6.95 0 1 1 13.9 0 6.95 6.95 0 0 1-13.9 0m6.95-8.05a8.05 8.05 0 1 0 5.13 14.26l3.75 3.75a.56.56 0 1 0 .79-.79l-3.73-3.73A8.05 8.05 0 0 0 11.042 3z" clip-rule="evenodd"></path></svg></div></a></div></div><div class="fk i l k"><div class="ac r"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><button class="bf b dx dy eh dz ea ei eb ec ej ek ee el em eg eo ep eq er es et eu ev ew ex ey ez fa fb fc fd bm fe ff" data-testid="headerSignUpButton">Sign up</button></span></p><div class="fg m"><p class="bf b dx dy dz ea eb ec ed ee ef eg du"><span data-dd-action-name="Susi presentation tracker global_nav"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerSignInButton" href="https://medium.com/m/signin?operation=login&amp;redirect=https%3A%2F%2Fyashkhandelwal07.medium.com%2Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023&amp;source=post_page---top_nav_layout_nav-----------------------global_nav------------------" rel="noopener follow">Sign in</a></span></p></div></div></div><div class="m" aria-hidden="false"><button class="aj fp an ac r ap fl fq fr fs" aria-label="user options menu" data-testid="headerUserIcon"><div class="m fl"><img alt="" class="m fd bx by bz cx" src="https://miro.medium.com/v2/resize:fill:64:64/1*dmbNkD5D-u45r44go_cf0g.png" width="32" height="32" loading="lazy" role="presentation"/><div class="ft bx m by bz fu o aj fv"></div></div></button></div></div></div><div class="ac"><div class="ca bh"><div class="m"><div class="fw fx fy fz ga m"><div class="ac cb"><div class="ci bh gb gc gd ge"></div></div><article><div class="m"><div class="m"><span class="m"></span><section><div><div class="fu gk gl gm gn go"></div><div><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="speechify-ignore gp m"><div><div class="speechify-ignore ac cp"><div class="speechify-ignore bh m"><div class="ac gq gr gs gt gu gv gw gx gy gz ha"><div class="ac r ha"><div class="ac hb"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><a rel="noopener follow" href="/?source=post_page---byline--fecbf3b2c023---------------------------------------" data-discover="true"><div class="m hc hd bx he hf"><div class="m fl"><img alt="Yash Khandelwal" class="m fd bx by bz cx" src="https://miro.medium.com/v2/resize:fill:64:64/1*EiDslhOwxjP4PmZdC4houw.jpeg" width="32" height="32" loading="lazy" data-testid="authorPhoto"/><div class="hg bx m by bz fu o hh fv"></div></div></div></a></div></div></div></div><span class="bf b bg ab bk"><div class="hi ac r"><div class="ac r hj"><div class="ac r"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><span class="bf b bg ab bk"><a class="ag ah ai fh ak al am an ao ap aq ar as hk" data-testid="authorName" rel="noopener follow" href="/?source=post_page---byline--fecbf3b2c023---------------------------------------" data-discover="true">Yash Khandelwal</a></span></div></div></div></div><div class="hl bm"></div></div></div></span></div><div class="ac r hm"><span class="bf b bg ab du"><div class="ac af"><span data-testid="storyReadTime">7 min read</span><div class="hn ho m" aria-hidden="true"><span class="m" aria-hidden="true"><span class="bf b bg ab du">·</span></span></div><span data-testid="storyPublishDate">Nov 28, 2023</span></div></span></div></div><div class="ac cp hp hq hr hs ht hu hv hw hx hy hz ia ib ic id ie"><div class="i l x fi fj r"><div class="iu m"><div class="ac r iv iw"><div class="pw-multi-vote-icon fl ix iy iz ja"><span data-dd-action-name="Susi presentation tracker clap_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerClapButton" href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fp%2Ffecbf3b2c023&amp;operation=register&amp;redirect=https%3A%2F%2Fyashkhandelwal07.medium.com%2Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023&amp;user=Yash+Khandelwal&amp;userId=abe69fbfb436&amp;source=---header_actions--fecbf3b2c023---------------------clap_footer------------------" rel="noopener follow"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><div class="jb ap jc jd je jf an jg jh ji ja" role="presentation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="clap"><path fill-rule="evenodd" d="M11.37.828 12 3.282l.63-2.454zM13.916 3.953l1.523-2.112-1.184-.39zM8.589 1.84l1.522 2.112-.337-2.501zM18.523 18.92c-.86.86-1.75 1.246-2.62 1.33a6 6 0 0 0 .407-.372c2.388-2.389 2.86-4.951 1.399-7.623l-.912-1.603-.79-1.672c-.26-.56-.194-.98.203-1.288a.7.7 0 0 1 .546-.132c.283.046.546.231.728.5l2.363 4.157c.976 1.624 1.141 4.237-1.324 6.702m-10.999-.438L3.37 14.328a.828.828 0 0 1 .585-1.408.83.83 0 0 1 .585.242l2.158 2.157a.365.365 0 0 0 .516-.516l-2.157-2.158-1.449-1.449a.826.826 0 0 1 1.167-1.17l3.438 3.44a.363.363 0 0 0 .516 0 .364.364 0 0 0 0-.516L5.293 9.513l-.97-.97a.826.826 0 0 1 0-*********** 0 0 1 1.167 0l.97.968 3.437 3.436a.36.36 0 0 0 .517 0 .366.366 0 0 0 0-.516L6.977 7.83a.82.82 0 0 1-.241-.584.82.82 0 0 1 .824-.826c.219 0 .43.087.584.242l5.787 5.787a.366.366 0 0 0 .587-.415l-1.117-2.363c-.26-.56-.194-.98.204-1.289a.7.7 0 0 1 .546-.132c.283.046.545.232.727.501l2.193 3.86c1.302 2.38.883 4.59-1.277 6.75-1.156 1.156-2.602 1.627-4.19 1.367-1.418-.236-2.866-1.033-4.079-2.246M10.75 5.971l2.12 2.12c-.41.502-.465 1.17-.128 1.89l.22.465-3.523-3.523a.8.8 0 0 1-.097-.368c0-.22.086-.428.241-.584a.847.847 0 0 1 1.167 0m7.355 1.705c-.31-.461-.746-.758-1.23-.837a1.44 1.44 0 0 0-1.11.275c-.312.24-.505.543-.59.881a1.74 1.74 0 0 0-.906-.465 1.47 1.47 0 0 0-.82.106l-2.182-2.182a1.56 1.56 0 0 0-2.2 0 1.54 1.54 0 0 0-.396.701 1.56 1.56 0 0 0-2.21-.01 1.55 1.55 0 0 0-.416.753c-.624-.624-1.649-.624-2.237-.037a1.557 1.557 0 0 0 0 2.2c-.239.1-.501.238-.715.453a1.56 1.56 0 0 0 0 2.2l.516.515a1.556 1.556 0 0 0-.753 2.615L7.01 19c1.32 1.319 2.909 2.189 4.475 2.449q.482.08.971.08c.85 0 1.653-.198 2.393-.579.**************.686.054 1.266 0 2.457-.52 3.505-1.567 2.763-2.763 2.552-5.734 1.439-7.586z" clip-rule="evenodd"></path></svg></div></div></div></div></a></span></div><div class="pw-multi-vote-count m jj jk jl jm jn jo jp"><p class="bf b dv ab du"><span class="jq">--</span></p></div></div></div><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button class="ap jb jr js ac r fm jt ju" aria-label="responses"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="jv"><path d="M18.006 16.803c1.533-1.456 2.234-3.325 2.234-5.321C20.24 7.357 16.709 4 12.191 4S4 7.357 4 11.482c0 4.126 3.674 7.482 8.191 7.482.817 0 1.622-.111 2.393-.327.231.2.48.391.744.559 1.06.693 2.203 1.044 3.399 1.044.224-.008.4-.112.486-.287a.49.49 0 0 0-.042-.518c-.495-.67-.845-1.364-1.04-2.057a4 4 0 0 1-.125-.598zm-3.122 1.055-.067-.223-.315.096a8 8 0 0 1-2.311.338c-4.023 0-7.292-2.955-7.292-6.587 0-3.633 3.269-6.588 7.292-6.588 4.014 0 7.112 2.958 7.112 6.593 0 1.794-.608 3.469-2.027 4.72l-.195.168v.255c0 .056 0 .151.016.295.025.231.081.478.154.733.154.558.398 1.117.722 1.659a5.3 5.3 0 0 1-2.165-.845c-.276-.176-.714-.383-.941-.59z"></path></svg></button></div></div></div></div><div class="ac r if ig ih ii ij ik il im in io ip iq ir is it"><div class="jw l k j e"></div><div class="i l"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><span data-dd-action-name="Susi presentation tracker bookmark_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="headerBookmarkButton" href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2Ffecbf3b2c023&amp;operation=register&amp;redirect=https%3A%2F%2Fyashkhandelwal07.medium.com%2Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023&amp;source=---header_actions--fecbf3b2c023---------------------bookmark_footer------------------" rel="noopener follow"><svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="none" viewBox="0 0 25 25" class="du jx" aria-label="Add to list bookmark button"><path fill="currentColor" d="M18 2.5a.5.5 0 0 1 1 0V5h2.5a.5.5 0 0 1 0 1H19v2.5a.5.5 0 1 1-1 0V6h-2.5a.5.5 0 0 1 0-1H18zM7 7a1 1 0 0 1 1-1h3.5a.5.5 0 0 0 0-1H8a2 2 0 0 0-2 2v14a.5.5 0 0 0 .805.396L12.5 17l5.695 4.396A.5.5 0 0 0 19 21v-8.5a.5.5 0 0 0-1 0v7.485l-5.195-4.012a.5.5 0 0 0-.61 0L7 19.985z"></path></svg></a></span></div></div></div></div><div class="fd jy cn"><div class="m af"><div class="ac cb"><div class="jz ka kb kc kd ke ci bh"><div class="ac"><div class="bm" aria-hidden="false" role="tooltip"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button aria-label="Listen" data-testid="audioPlayButton" class="ag fm ai fh ak al am kf ao ap aq ex kg kh ju ki kj kk kl km t kn ko kp kq kr ks kt v ku kv kw"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a9 9 0 1 1 18 0 9 9 0 0 1-18 0m9-10C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m3.376 10.416-4.599 3.066a.5.5 0 0 1-.777-.416V8.934a.5.5 0 0 1 .777-.416l4.599 3.066a.5.5 0 0 1 0 .832" clip-rule="evenodd"></path></svg><div class="k j e"><p class="bf b bg ab du">Listen</p></div></button></div></div></div></div></div></div></div></div></div><div class="bm" aria-hidden="false" aria-describedby="postFooterSocialMenu" aria-labelledby="postFooterSocialMenu"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button aria-controls="postFooterSocialMenu" aria-expanded="false" aria-label="Share Post" data-testid="headerSocialShareButton" class="ag fm ai fh ak al am kf ao ap aq ex kg kh ju ki kj kk kl km t kn ko kp kq kr ks kt v ku kv kw"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M15.218 4.931a.4.4 0 0 1-.118.132l.012.006a.45.45 0 0 1-.292.074.5.5 0 0 1-.3-.13l-2.02-2.02v7.07c0 .28-.23.5-.5.5s-.5-.22-.5-.5v-7.04l-2 2a.45.45 0 0 1-.57.04h-.02a.4.4 0 0 1-.16-.3.4.4 0 0 1 .1-.32l2.8-2.8a.5.5 0 0 1 .7 0l2.8 2.79a.42.42 0 0 1 .068.498m-.106.138.008.004v-.01zM16 7.063h1.5a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-11c-1.1 0-2-.9-2-2v-10a2 2 0 0 1 2-2H8a.5.5 0 0 1 .********* 0 0 1 .********* 0 0 1-.********* 0 0 1-.35.15H6.4c-.5 0-.9.4-.9.9v10.2a.9.9 0 0 0 .9.9h11.2c.5 0 .9-.4.9-.9v-10.2c0-.5-.4-.9-.9-.9H16a.5.5 0 0 1 0-1" clip-rule="evenodd"></path></svg><div class="k j e"><p class="bf b bg ab du">Share</p></div></button></div></div></div></div></div></div></div></div></div></div></div></div></div><div class="kx ky kz la lb"><div class="ac cb"><div class="ci bh gb gc gd ge"><figure class="gp lf lc ld paragraph-image"><div role="button" tabindex="0" class="lg lh fl li bh lj"><span class="fu lk ll an lm ln lo lp lq speechify-ignore">Press enter or click to view image in full size</span><div class="lc ld le"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 640w, https://miro.medium.com/v2/resize:fit:720/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 720w, https://miro.medium.com/v2/resize:fit:750/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 750w, https://miro.medium.com/v2/resize:fit:786/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 786w, https://miro.medium.com/v2/resize:fit:828/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 828w, https://miro.medium.com/v2/resize:fit:1100/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 1100w, https://miro.medium.com/v2/resize:fit:1400/1*XB0OpXrvtI-61pRUOAhdGQ.jpeg 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="" class="bh ke lr c" width="700" height="700" loading="eager" role="presentation"/></picture></div></div><figcaption class="ls ff lt lc ld lu lv bf b bg ab du">Hackathons can be rewarding!!!</figcaption></figure><p id="a2db" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Get ready to up your game in data science hackathons! In this blog, I’ll be sharing the winning solution approaches &amp; best practices in Data Science &amp; Machine Learning. It’s a practical guide to help you navigate and triumph in data hackathons, distilled from the lessons learned through my own National victories in past hackathons. Let’s dive into the essentials that can boost your chances of emerging victorious in any data science challenge.</p><h2 id="aeb1" class="mv mw ly bf mx my mz na nb nc nd ne nf ng nh ni nj nk nl nm nn no np nq nr ns bk">Where to participate?</h2><figure class="nu nv nw nx ny lf lc ld paragraph-image"><div role="button" tabindex="0" class="lg lh fl li bh lj"><span class="fu lk ll an lm ln lo lp lq speechify-ignore">Press enter or click to view image in full size</span><div class="lc ld nt"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 640w, https://miro.medium.com/v2/resize:fit:720/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 720w, https://miro.medium.com/v2/resize:fit:750/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 750w, https://miro.medium.com/v2/resize:fit:786/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 786w, https://miro.medium.com/v2/resize:fit:828/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 828w, https://miro.medium.com/v2/resize:fit:1100/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 1100w, https://miro.medium.com/v2/resize:fit:1400/1*8sV0YYB2GHuNOnT4BqAGxw.jpeg 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="" class="bh ke lr c" width="700" height="394" loading="lazy" role="presentation"/></picture></div></div><figcaption class="ls ff lt lc ld lu lv bf b bg ab du">Source: Unsplash</figcaption></figure><p id="2714" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">The platforms, such as <a class="ag nz" href="https://www.kaggle.com/competitions" rel="noopener ugc nofollow" target="_blank">Kaggle</a>, <a class="ag nz" href="https://datahack.analyticsvidhya.com/contest/all/" rel="noopener ugc nofollow" target="_blank">DataHack</a>, <a class="ag nz" href="https://machinehack.com/hackathons" rel="noopener ugc nofollow" target="_blank">MachineHack</a>, <a class="ag nz" href="https://unstop.com/hackathons" rel="noopener ugc nofollow" target="_blank">Unstop</a>, etc. offer a chance to apply your skills in Python, Data Analytics, Machine Learning, etc in real-world challenges. Participating in these platforms is not merely about clinching the top spot; it’s a journey of hands-on learning and community engagement. These hackathons provide an invaluable chance to test your capabilities, learn from the diverse approaches of fellow participants, and stay attuned to the ever-evolving landscape of data science. Consider it your ticket to continuous improvement, community collaboration, and a noteworthy addition to your professional portfolio!</p><h2 id="2951" class="mv mw ly bf mx my mz na nb nc nd ne nf ng nh ni nj nk nl nm nn no np nq nr ns bk">Problem Statements</h2><figure class="nu nv nw nx ny lf lc ld paragraph-image"><div role="button" tabindex="0" class="lg lh fl li bh lj"><span class="fu lk ll an lm ln lo lp lq speechify-ignore">Press enter or click to view image in full size</span><div class="lc ld oa"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 640w, https://miro.medium.com/v2/resize:fit:720/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 720w, https://miro.medium.com/v2/resize:fit:750/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 750w, https://miro.medium.com/v2/resize:fit:786/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 786w, https://miro.medium.com/v2/resize:fit:828/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 828w, https://miro.medium.com/v2/resize:fit:1100/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 1100w, https://miro.medium.com/v2/resize:fit:1400/1*rw5LDHRkBY2aajU84p2Dfg.jpeg 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="" class="bh ke lr c" width="700" height="394" loading="lazy" role="presentation"/></picture></div></div><figcaption class="ls ff lt lc ld lu lv bf b bg ab du">Source: Unsplash</figcaption></figure><p id="a56c" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Let’s now see a glimpse of intriguing problem statements you’ll encounter -</p><p id="eed4" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">1. Supervised/ Unsupervised Machine Learning (ML):</strong></p><p id="ae39" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Example: Predicting the sales of a mega mart and making recommendations for sustainable sales.</p><p id="2efc" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">2. Time Series Forecasting:</strong></p><p id="1c2c" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Example: Forecasting PM2.5 concentrations for the first 3 days of 2023 for 34 Indian Cities.</p><p id="f222" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">3. Deep Learning:</strong></p><p id="4c1f" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Example: Estimating the intensity of cyclones from satellite imagery using CNN.</p><p id="d06b" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">How the Game Works: Behind the Scenes</strong></p><p id="c9fe" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">In hackathons, participants submit the predictions from their model for evaluation. A private leaderboard is used to track participants’ performance, but the scores are hidden until the end. Winners are determined based on the predictive accuracy of their models through various metrics depending on the type of predicting task. The target is to develop a ML model that is perfect fit on the training data and it doesn’t overfit, giving the best metric value on the leaderboard.</p><p id="8a8d" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Here’s how it works: you build a model to predict things, like sales or pollution. You submit your predictions, but you don’t know how well you did until the end. There’s a special scoreboard that keeps track, but it keeps the scores a secret until it’s over. The winners are the ones whose predictions were super close to what really happened.</p><p id="b2c8" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">Lets now look at the state-of-the-art techniques and best ML practices to top the Private leaderboard!</strong></p><h2 id="27c7" class="mv mw ly bf mx my mz na nb nc nd ne nf ng nh ni nj nk nl nm nn no np nq nr ns bk">Supervised/ Unsupervised Machine Learning (ML)</h2><p id="bb28" class="pw-post-body-paragraph lw lx ly lz b ma oc mc md me od mg mh mi oe mk ml mm of mo mp mq og ms mt mu kx bk">Let’s take the problem statement mentioned before into account — “Predicting the sales of a mega mart and making recommendations for sustainable sales” and see how my winning solution approach &amp; techniques were.</p><p id="a2e9" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">1. Understanding the Data (Link to the dataset, </strong><a class="ag nz" href="https://machinehack.com/hackathons/data_analytics_championship/overview" rel="noopener ugc nofollow" target="_blank">Hackathon Link</a><strong class="lz ob">) -</strong></p><p id="1831" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">The challenge revolves around predicting the sales of a mega mart. In this regression task, the training data comprises 87,864 observations with 12 features, including 7 categorical and 5 continuous ones. The test data consists of 37,656 observations, and the target feature is the Sales of items in the mega mart.</p><p id="3c81" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">2. Data Preparation</strong></p><p id="f23f" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">The journey to a successful model kicks off with meticulous data preparation. This involves transforming raw data into an informative dataset, laying the foundation for a machine learning model that not only comprehends the intricacies of the training data but also excels in making accurate predictions on real-world data.</p><p id="3f14" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Let’s, look at the findings in the data and the preparation methods that worked best on it.</p><figure class="nu nv nw nx ny lf lc ld paragraph-image"><div role="button" tabindex="0" class="lg lh fl li bh lj"><span class="fu lk ll an lm ln lo lp lq speechify-ignore">Press enter or click to view image in full size</span><div class="lc ld oh"><picture><source srcSet="https://miro.medium.com/v2/resize:fit:640/format:webp/1*0hLwpxEFSIT6oBZ9lIbx_A.png 640w, https://miro.medium.com/v2/resize:fit:720/format:webp/1*0hLwpxEFSIT6oBZ9lIbx_A.png 720w, https://miro.medium.com/v2/resize:fit:750/format:webp/1*0hLwpxEFSIT6oBZ9lIbx_A.png 750w, https://miro.medium.com/v2/resize:fit:786/format:webp/1*0hLwpxEFSIT6oBZ9lIbx_A.png 786w, https://miro.medium.com/v2/resize:fit:828/format:webp/1*0hLwpxEFSIT6oBZ9lIbx_A.png 828w, https://miro.medium.com/v2/resize:fit:1100/format:webp/1*0hLwpxEFSIT6oBZ9lIbx_A.png 1100w, https://miro.medium.com/v2/resize:fit:1400/format:webp/1*0hLwpxEFSIT6oBZ9lIbx_A.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px" type="image/webp"/><source data-testid="og" srcSet="https://miro.medium.com/v2/resize:fit:640/1*0hLwpxEFSIT6oBZ9lIbx_A.png 640w, https://miro.medium.com/v2/resize:fit:720/1*0hLwpxEFSIT6oBZ9lIbx_A.png 720w, https://miro.medium.com/v2/resize:fit:750/1*0hLwpxEFSIT6oBZ9lIbx_A.png 750w, https://miro.medium.com/v2/resize:fit:786/1*0hLwpxEFSIT6oBZ9lIbx_A.png 786w, https://miro.medium.com/v2/resize:fit:828/1*0hLwpxEFSIT6oBZ9lIbx_A.png 828w, https://miro.medium.com/v2/resize:fit:1100/1*0hLwpxEFSIT6oBZ9lIbx_A.png 1100w, https://miro.medium.com/v2/resize:fit:1400/1*0hLwpxEFSIT6oBZ9lIbx_A.png 1400w" sizes="(min-resolution: 4dppx) and (max-width: 700px) 50vw, (-webkit-min-device-pixel-ratio: 4) and (max-width: 700px) 50vw, (min-resolution: 3dppx) and (max-width: 700px) 67vw, (-webkit-min-device-pixel-ratio: 3) and (max-width: 700px) 65vw, (min-resolution: 2.5dppx) and (max-width: 700px) 80vw, (-webkit-min-device-pixel-ratio: 2.5) and (max-width: 700px) 80vw, (min-resolution: 2dppx) and (max-width: 700px) 100vw, (-webkit-min-device-pixel-ratio: 2) and (max-width: 700px) 100vw, 700px"/><img alt="" class="bh ke lr c" width="700" height="496" loading="lazy" role="presentation"/></picture></div></div><figcaption class="ls ff lt lc ld lu lv bf b bg ab du">The dataset</figcaption></figure><p id="06c9" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob"><em class="oi">Outlier Detection:</em></strong><em class="oi"> Inter Quartile Range Method</em></p><p id="1626" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Applying the Interquartile Range (IQR) Method to identify and handle outliers ensures a cleaner dataset. The code snippet demonstrates how to cap extreme values within the acceptable range.</p><pre class="nu nv nw nx ny oj ok ol bp om bb bk"><span id="f74d" class="on mw ly ok b bg oo op m oq or"># Assuming df is the DataFrame <br/>Q1 = df[‘Sales’].quantile(0.25) <br/>Q3 = df[‘Sales’].quantile(0.75) <br/>IQR = Q3 — Q1 <br/>lower_bound = Q1–1.5 * IQR <br/>upper_bound = Q3 + 1.5 * IQR <br/>df[‘Sales’] = df[‘Sales’].apply(lambda x: upper_bound if x &gt; upper_bound else (lower_bound if x &lt; lower_bound else x))</span></pre><p id="e14c" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob"><em class="oi">Null Value Imputation: </em></strong><em class="oi">No null values found!</em></p><p id="ca8f" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">A check for null values is conducted, and in this case, none are found, simplifying the data preparation process.</p><p id="ae00" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob"><em class="oi">Categorical Encoding: </em></strong><em class="oi">One Hot Encoding (</em>When categorical variables have no inherent order or ranking), <em class="oi">Ordinal Encoding </em>(When categorical variables have a clear order or rank).</p><p id="dd45" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">For example, in this dataset — Outlet_ID, Item_Type, Outlet_Year, Outlet_Type have no order while Outlet_Size, Outlet_location_type, Item_FC have a visible order.</p><pre class="nu nv nw nx ny oj ok ol bp om bb bk"><span id="d39f" class="on mw ly ok b bg oo op m oq or"># One Hot Encoding <br/>df_encoded = pd.get_dummies(df, columns=[‘categorical_feature’], drop_first=True) # Ordinal Encoding <br/>ordinal_mapping = {‘low’: 0, ‘medium’: 1, ‘high’: 2} <br/>df[‘ordinal_feature’] = df[‘ordinal_feature’].map(ordinal_mapping)</span></pre><p id="9775" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">Feature Engineering: </strong>Creating<strong class="lz ob"> </strong>meaningful features that enhance a machine learning model’s performance. For example in this data — <em class="oi">Years from present</em> (2023 — Outlet_year)</p><pre class="nu nv nw nx ny oj ok ol bp om bb bk"><span id="6095" class="on mw ly ok b bg oo op m oq or"># Assuming df is your DataFrame<br/>df[‘new_feature’] = df[‘feature1’] * df[‘feature2’]</span></pre><p id="7fe6" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob"><em class="oi">Feature Selection:</em></strong></p><p id="57ab" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><em class="oi">Permutation Feature Importance:</em> is a technique that measures the impact of each feature on a model’s performance by randomly shuffling the values of a single feature and observing the effect on model accuracy.</p><pre class="nu nv nw nx ny oj ok ol bp om bb bk"><span id="f47c" class="on mw ly ok b bg oo op m oq or">from sklearn.inspection import permutation_importance <br/># Assuming model is your trained machine learning model <br/>perm_importance = permutation_importance(model, X_test, y_test, n_repeats=10, random_state=42) <br/>feature_importance = perm_importance.importances_mean</span></pre><p id="9691" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">XGBoost Feature Importance: XGBoost, a powerful gradient boosting algorithm, provides a built-in method for feature importance. It calculates how each feature contributes to reducing the impurity during tree construction.</p><p id="57d0" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">import xgboost as xgb # Assuming X_train, y_train are your training data model = xgb.XGBClassifier() model.fit(X_train, y_train) # Accessing feature importance scores feature_importance = model.feature_importances_#import csv12345678makefile</p><p id="580d" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Both techniques offer insights into feature importance, helping you identify key variables and refine your feature set for improved model performance.</p><p id="89fc" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">3. Machine Learning — Modelling</strong></p><p id="ff2d" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">The modelling begins with splitting the training data into training and validation. There are major two techniques for the same — 1.<em class="oi"> K Fold Cross Validation </em>2. <em class="oi">Train-test-split</em></p><p id="0b2f" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">For this dataset, train-test-split is itself efficient because of abundant data, prioritising simplicity and faster computation.</p><pre class="nu nv nw nx ny oj ok ol bp om bb bk"><span id="9f92" class="on mw ly ok b bg oo op m oq or">from sklearn.model_selection import train_test_split <br/># Assuming X is your feature matrix and y is your target variable <br/>X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42) #import csv12345python</span></pre><p id="c756" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">Modelling</strong></p><p id="bd2f" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Efficient regression model development begins with a baseline model, often a simple linear regression, serving as a performance benchmark. Evaluate its results using appropriate metrics. Explore feature relationships and engineer informative features. Even try to alter the data preparation with suitable methods. Experiment with various models, fine-tune hyperparameters, and consider ensemble methods for improved robustness.</p><p id="555b" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">This is the order in which I begin my modelling experimentation.</p><p id="3350" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">1. Linear Regression</strong></p><p id="6fc3" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">2. Lasso &amp; Ridge Regression</strong> (Regularization, will prevent over fitting)</p><p id="1bd7" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">3. Decision Tree</strong> (Will give better metric values on training data through high predictive performance)</p><p id="09fb" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">4. Random Forest</strong> (Will solve the overfitting issue of decision trees)</p><p id="17cf" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">5. XGBoost, AdaBoost, LightGBM, CatBoost</strong> (Boosting models sequentially build a robust learner by focusing on correcting errors made by weak models, effectively reducing bias, lowering variance, and excelling in capturing intricate patterns)</p><p id="7467" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Expect the best performance from the boosting models. But where does the crux of winning solution lies then?</p><p id="72b2" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">There are few more, less common techniques — <strong class="lz ob">Stacking Ensemble</strong> &amp; <strong class="lz ob">Weighted Average Ensemble. </strong>Stacking, with its meta-model learning from varied base model predictions, often outperforms individual models. Average ensemble methods, on the other hand, leverage collective wisdom to create a more balanced and robust prediction by mitigating biases or errors present in individual models. Both approaches contribute to enhanced model performance through effective combination and adaptation to different aspects of the underlying data.</p><p id="7707" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Both of these models had the best performance on this data. These models topped the leaderboard!</p><pre class="nu nv nw nx ny oj ok ol bp om bb bk"><span id="9891" class="on mw ly ok b bg oo op m oq or">#Code for Stacking Ensembling<br/>from sklearn.ensemble import StackingRegressor <br/>from sklearn.linear_model import LinearRegression <br/>from sklearn.ensemble import RandomForestRegressor<br/>from sklearn.tree import DecisionTreeRegressor <br/># Assuming base models are your individual regression models <br/>base_models = [(‘linear’, LinearRegression()), (‘rf’, RandomForestRegressor()), (‘tree’, DecisionTreeRegressor())] <br/>stacking_model = StackingRegressor(estimators=base_models, final_estimator=LinearRegression()) <br/>stacking_model.fit(X_train, y_train)</span></pre><pre class="os oj ok ol bp om bb bk"><span id="c83a" class="on mw ly ok b bg oo op m oq or">#Code for Weighted Average Ensemble<br/># Assuming model1, model2, model3 are your regression models<br/>weights = [0.4, 0.3, 0.3] <br/># Adjust weights based on model performance<br/>predictions_model1 = model1.predict(X_test) <br/>predictions_model2 = model2.predict(X_test) <br/>predictions_model3 = model3.predict(X_test) <br/>weighted_average_predictions = (weights[0] * predictions_model1 + weights[1] * predictions_model2 + weights[2] * predictions_model3) / sum(weights) </span></pre><p id="badd" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk"><strong class="lz ob">End Note</strong></p><p id="a373" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Creating an efficient machine learning (ML) model involves foundational steps. Starting with a comprehensive understanding of the data, addressing missing values, and outliers with patience. Thoughtful feature engineering enhances the model’s pattern recognition. Systematic hyperparameter tuning fine-tunes performance, and careful model selection aligns with specific tasks. Ensemble methods, cross-validation, and regularization contribute to robustness. Incorporate machine learning explainability for transparency in model decisions, crucial for business understanding. <strong class="lz ob">Lastly, continuous iteration based on evaluation insights ensures ongoing enhancement of predictive capabilities, aligning the model with both technical and business objectives of problem statement of the hackathon.</strong></p><p id="dbcb" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Remember, to win a hackathon, an efficient ML model &amp; your leaderboard standing may not be enough. You might have to also deliver an outstanding presentation on your ML solution in order to win 🏆🏅</p><p id="9517" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Here is the link to my winning solution for this problem statement which was a part of <a class="ag nz" href="https://analyticsindiamag.com/analytics-olympiad-organised-by-shiv-nadar-university-aim-ends-on-a-high-note/" rel="noopener ugc nofollow" target="_blank">Analytics Olympiad 2021</a> : <a class="ag nz" href="https://github.com/YashK07/Analytics-Olympiad-2021-My-Winning-Solution" rel="noopener ugc nofollow" target="_blank"><strong class="lz ob">Github</strong></a><strong class="lz ob"> Link</strong>(also, have a look on how a presentation for your solution should look like)</p><p id="0274" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Here are a few helpful resource links that might be useful along this blog:</p><ol class=""><li id="0a21" class="lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu ot ou ov bk"><strong class="lz ob">Stacking Ensemble in Depth with code -</strong><a class="ag nz" href="https://www.analyticsvidhya.com/blog/2021/08/ensemble-stacking-for-machine-learning-and-deep-learning/" rel="noopener ugc nofollow" target="_blank">https://www.analyticsvidhya.com/blog/2021/08/ensemble-stacking-for-machine-learning-and-deep-learning/</a></li><li id="fb16" class="lw lx ly lz b ma ow mc md me ox mg mh mi oy mk ml mm oz mo mp mq pa ms mt mu ot ou ov bk"><strong class="lz ob">Lifecycle of a Data Science Project -</strong><a class="ag nz" href="https://www.analyticsvidhya.com/blog/2021/10/introduction-to-the-lifecycle-of-data-science-project/#h2_9" rel="noopener ugc nofollow" target="_blank">https://www.analyticsvidhya.com/blog/2021/10/introduction-to-the-lifecycle-of-data-science-project/#h2_9</a></li></ol><h2 id="231c" class="mv mw ly bf mx my mz na nb nc nd ne nf ng nh ni nj nk nl nm nn no np nq nr ns bk">Time Series Forecasting</h2><p id="d7fb" class="pw-post-body-paragraph lw lx ly lz b ma oc mc md me od mg mh mi oe mk ml mm of mo mp mq og ms mt mu kx bk">This is another challenging problem statement in Data Science hackthons.</p><p id="2880" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Let’s continue with Time Series Forecasting in the next blog titled — “Data Science Hackathons: A Practical Guide (Part 2)”. Happy Reading! 😊💻</p><h2 id="19d7" class="mv mw ly bf mx my mz na nb nc nd ne nf ng nh ni nj nk nl nm nn no np nq nr ns bk">About the Author</h2><p id="dd37" class="pw-post-body-paragraph lw lx ly lz b ma oc mc md me od mg mh mi oe mk ml mm of mo mp mq og ms mt mu kx bk">Yash Khandelwal is a final year student pursuing Mathematics &amp; Computing at Birla Institute of Technology, Mesra. He was a National Winner of EXL EQ 2023, National Finalist of Smart India Hackathon 2022 and a National Runner up of Analytics Olympiad 2021.</p><p id="5d95" class="pw-post-body-paragraph lw lx ly lz b ma mb mc md me mf mg mh mi mj mk ml mm mn mo mp mq mr ms mt mu kx bk">Connect with him on LinkedIn — <a class="ag nz" href="https://www.linkedin.com/in/yash-khandelwal-a40484bb/" rel="noopener ugc nofollow" target="_blank">https://www.linkedin.com/in/yash-khandelwal-a40484bb/</a></p></div></div></div></div></section></div></div></article></div><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="pb pc ac hm"><div class="pd ac"><a class="pe aj an ap" href="https://medium.com/tag/data-science?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><div class="pf fl cx pg gg ph pi bf b bg ab bk lp">Data Science</div></a></div><div class="pd ac"><a class="pe aj an ap" href="https://medium.com/tag/hackathons?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><div class="pf fl cx pg gg ph pi bf b bg ab bk lp">Hackathons</div></a></div><div class="pd ac"><a class="pe aj an ap" href="https://medium.com/tag/machine-learning?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><div class="pf fl cx pg gg ph pi bf b bg ab bk lp">Machine Learning</div></a></div><div class="pd ac"><a class="pe aj an ap" href="https://medium.com/tag/code?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><div class="pf fl cx pg gg ph pi bf b bg ab bk lp">Code</div></a></div><div class="pd ac"><a class="pe aj an ap" href="https://medium.com/tag/data-analysis?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><div class="pf fl cx pg gg ph pi bf b bg ab bk lp">Data Analysis</div></a></div></div></div></div><div class="m"></div><footer class="pj pk pl pm pn ac r po pp c"><div class="m af"><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="ac cp pq"><div class="ac r iv"><div class="pr m"><span class="m ps pt pu f e"><div class="ac r iv iw"><div class="pw-multi-vote-icon fl ix iy iz ja"><span data-dd-action-name="Susi presentation tracker clap_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="footerClapButton" href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fp%2Ffecbf3b2c023&amp;operation=register&amp;redirect=https%3A%2F%2Fyashkhandelwal07.medium.com%2Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023&amp;user=Yash+Khandelwal&amp;userId=abe69fbfb436&amp;source=---footer_actions--fecbf3b2c023---------------------clap_footer------------------" rel="noopener follow"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><div class="jb ap jc jd je jf an jg jh ji ja" role="presentation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="clap"><path fill-rule="evenodd" d="M11.37.828 12 3.282l.63-2.454zM13.916 3.953l1.523-2.112-1.184-.39zM8.589 1.84l1.522 2.112-.337-2.501zM18.523 18.92c-.86.86-1.75 1.246-2.62 1.33a6 6 0 0 0 .407-.372c2.388-2.389 2.86-4.951 1.399-7.623l-.912-1.603-.79-1.672c-.26-.56-.194-.98.203-1.288a.7.7 0 0 1 .546-.132c.283.046.546.231.728.5l2.363 4.157c.976 1.624 1.141 4.237-1.324 6.702m-10.999-.438L3.37 14.328a.828.828 0 0 1 .585-1.408.83.83 0 0 1 .585.242l2.158 2.157a.365.365 0 0 0 .516-.516l-2.157-2.158-1.449-1.449a.826.826 0 0 1 1.167-1.17l3.438 3.44a.363.363 0 0 0 .516 0 .364.364 0 0 0 0-.516L5.293 9.513l-.97-.97a.826.826 0 0 1 0-*********** 0 0 1 1.167 0l.97.968 3.437 3.436a.36.36 0 0 0 .517 0 .366.366 0 0 0 0-.516L6.977 7.83a.82.82 0 0 1-.241-.584.82.82 0 0 1 .824-.826c.219 0 .43.087.584.242l5.787 5.787a.366.366 0 0 0 .587-.415l-1.117-2.363c-.26-.56-.194-.98.204-1.289a.7.7 0 0 1 .546-.132c.283.046.545.232.727.501l2.193 3.86c1.302 2.38.883 4.59-1.277 6.75-1.156 1.156-2.602 1.627-4.19 1.367-1.418-.236-2.866-1.033-4.079-2.246M10.75 5.971l2.12 2.12c-.41.502-.465 1.17-.128 1.89l.22.465-3.523-3.523a.8.8 0 0 1-.097-.368c0-.22.086-.428.241-.584a.847.847 0 0 1 1.167 0m7.355 1.705c-.31-.461-.746-.758-1.23-.837a1.44 1.44 0 0 0-1.11.275c-.312.24-.505.543-.59.881a1.74 1.74 0 0 0-.906-.465 1.47 1.47 0 0 0-.82.106l-2.182-2.182a1.56 1.56 0 0 0-2.2 0 1.54 1.54 0 0 0-.396.701 1.56 1.56 0 0 0-2.21-.01 1.55 1.55 0 0 0-.416.753c-.624-.624-1.649-.624-2.237-.037a1.557 1.557 0 0 0 0 2.2c-.239.1-.501.238-.715.453a1.56 1.56 0 0 0 0 2.2l.516.515a1.556 1.556 0 0 0-.753 2.615L7.01 19c1.32 1.319 2.909 2.189 4.475 2.449q.482.08.971.08c.85 0 1.653-.198 2.393-.579.**************.686.054 1.266 0 2.457-.52 3.505-1.567 2.763-2.763 2.552-5.734 1.439-7.586z" clip-rule="evenodd"></path></svg></div></div></div></div></a></span></div><div class="pw-multi-vote-count m jj jk jl jm jn jo jp"><p class="bf b dv ab du"><span class="jq">--</span></p></div></div></span><span class="m i h g pv pw"><div class="ac r iv iw"><div class="pw-multi-vote-icon fl ix iy iz ja"><span data-dd-action-name="Susi presentation tracker clap_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="footerClapButton" href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fp%2Ffecbf3b2c023&amp;operation=register&amp;redirect=https%3A%2F%2Fyashkhandelwal07.medium.com%2Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023&amp;user=Yash+Khandelwal&amp;userId=abe69fbfb436&amp;source=---footer_actions--fecbf3b2c023---------------------clap_footer------------------" rel="noopener follow"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><div class="jb ap jc jd je jf an jg jh ji ja" role="presentation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="clap"><path fill-rule="evenodd" d="M11.37.828 12 3.282l.63-2.454zM13.916 3.953l1.523-2.112-1.184-.39zM8.589 1.84l1.522 2.112-.337-2.501zM18.523 18.92c-.86.86-1.75 1.246-2.62 1.33a6 6 0 0 0 .407-.372c2.388-2.389 2.86-4.951 1.399-7.623l-.912-1.603-.79-1.672c-.26-.56-.194-.98.203-1.288a.7.7 0 0 1 .546-.132c.283.046.546.231.728.5l2.363 4.157c.976 1.624 1.141 4.237-1.324 6.702m-10.999-.438L3.37 14.328a.828.828 0 0 1 .585-1.408.83.83 0 0 1 .585.242l2.158 2.157a.365.365 0 0 0 .516-.516l-2.157-2.158-1.449-1.449a.826.826 0 0 1 1.167-1.17l3.438 3.44a.363.363 0 0 0 .516 0 .364.364 0 0 0 0-.516L5.293 9.513l-.97-.97a.826.826 0 0 1 0-*********** 0 0 1 1.167 0l.97.968 3.437 3.436a.36.36 0 0 0 .517 0 .366.366 0 0 0 0-.516L6.977 7.83a.82.82 0 0 1-.241-.584.82.82 0 0 1 .824-.826c.219 0 .43.087.584.242l5.787 5.787a.366.366 0 0 0 .587-.415l-1.117-2.363c-.26-.56-.194-.98.204-1.289a.7.7 0 0 1 .546-.132c.283.046.545.232.727.501l2.193 3.86c1.302 2.38.883 4.59-1.277 6.75-1.156 1.156-2.602 1.627-4.19 1.367-1.418-.236-2.866-1.033-4.079-2.246M10.75 5.971l2.12 2.12c-.41.502-.465 1.17-.128 1.89l.22.465-3.523-3.523a.8.8 0 0 1-.097-.368c0-.22.086-.428.241-.584a.847.847 0 0 1 1.167 0m7.355 1.705c-.31-.461-.746-.758-1.23-.837a1.44 1.44 0 0 0-1.11.275c-.312.24-.505.543-.59.881a1.74 1.74 0 0 0-.906-.465 1.47 1.47 0 0 0-.82.106l-2.182-2.182a1.56 1.56 0 0 0-2.2 0 1.54 1.54 0 0 0-.396.701 1.56 1.56 0 0 0-2.21-.01 1.55 1.55 0 0 0-.416.753c-.624-.624-1.649-.624-2.237-.037a1.557 1.557 0 0 0 0 2.2c-.239.1-.501.238-.715.453a1.56 1.56 0 0 0 0 2.2l.516.515a1.556 1.556 0 0 0-.753 2.615L7.01 19c1.32 1.319 2.909 2.189 4.475 2.449q.482.08.971.08c.85 0 1.653-.198 2.393-.579.**************.686.054 1.266 0 2.457-.52 3.505-1.567 2.763-2.763 2.552-5.734 1.439-7.586z" clip-rule="evenodd"></path></svg></div></div></div></div></a></span></div><div class="pw-multi-vote-count m jj jk jl jm jn jo jp"><p class="bf b dv ab du"><span class="jq">--</span></p></div></div></span></div><div class="ay ac"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button class="ap jb jr js ac r fm jt ju" aria-label="responses"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="jv"><path d="M18.006 16.803c1.533-1.456 2.234-3.325 2.234-5.321C20.24 7.357 16.709 4 12.191 4S4 7.357 4 11.482c0 4.126 3.674 7.482 8.191 7.482.817 0 1.622-.111 2.393-.327.231.2.48.391.744.559 1.06.693 2.203 1.044 3.399 1.044.224-.008.4-.112.486-.287a.49.49 0 0 0-.042-.518c-.495-.67-.845-1.364-1.04-2.057a4 4 0 0 1-.125-.598zm-3.122 1.055-.067-.223-.315.096a8 8 0 0 1-2.311.338c-4.023 0-7.292-2.955-7.292-6.587 0-3.633 3.269-6.588 7.292-6.588 4.014 0 7.112 2.958 7.112 6.593 0 1.794-.608 3.469-2.027 4.72l-.195.168v.255c0 .056 0 .151.016.295.025.231.081.478.154.733.154.558.398 1.117.722 1.659a5.3 5.3 0 0 1-2.165-.845c-.276-.176-.714-.383-.941-.59z"></path></svg></button></div></div></div></div></div><div class="ac r"><div class="px m py"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><span data-dd-action-name="Susi presentation tracker bookmark_footer"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" data-testid="footerBookmarkButton" href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2Ffecbf3b2c023&amp;operation=register&amp;redirect=https%3A%2F%2Fyashkhandelwal07.medium.com%2Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023&amp;source=---footer_actions--fecbf3b2c023---------------------bookmark_footer------------------" rel="noopener follow"><svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="none" viewBox="0 0 25 25" class="du jx" aria-label="Add to list bookmark button"><path fill="currentColor" d="M18 2.5a.5.5 0 0 1 1 0V5h2.5a.5.5 0 0 1 0 1H19v2.5a.5.5 0 1 1-1 0V6h-2.5a.5.5 0 0 1 0-1H18zM7 7a1 1 0 0 1 1-1h3.5a.5.5 0 0 0 0-1H8a2 2 0 0 0-2 2v14a.5.5 0 0 0 .805.396L12.5 17l5.695 4.396A.5.5 0 0 0 19 21v-8.5a.5.5 0 0 0-1 0v7.485l-5.195-4.012a.5.5 0 0 0-.61 0L7 19.985z"></path></svg></a></span></div></div></div></div><div class="px m py"><div class="bm" aria-hidden="false" aria-describedby="postFooterSocialMenu" aria-labelledby="postFooterSocialMenu"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><button aria-controls="postFooterSocialMenu" aria-expanded="false" aria-label="Share Post" data-testid="footerSocialShareButton" class="ag fm ai fh ak al am kf ao ap aq ex kg kh ju ki"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M15.218 4.931a.4.4 0 0 1-.118.132l.012.006a.45.45 0 0 1-.292.074.5.5 0 0 1-.3-.13l-2.02-2.02v7.07c0 .28-.23.5-.5.5s-.5-.22-.5-.5v-7.04l-2 2a.45.45 0 0 1-.57.04h-.02a.4.4 0 0 1-.16-.3.4.4 0 0 1 .1-.32l2.8-2.8a.5.5 0 0 1 .7 0l2.8 2.79a.42.42 0 0 1 .068.498m-.106.138.008.004v-.01zM16 7.063h1.5a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-11c-1.1 0-2-.9-2-2v-10a2 2 0 0 1 2-2H8a.5.5 0 0 1 .********* 0 0 1 .********* 0 0 1-.********* 0 0 1-.35.15H6.4c-.5 0-.9.4-.9.9v10.2a.9.9 0 0 0 .9.9h11.2c.5 0 .9-.4.9-.9v-10.2c0-.5-.4-.9-.9-.9H16a.5.5 0 0 1 0-1" clip-rule="evenodd"></path></svg></button></div></div></div></div></div></div></div></div></div></div></footer><div class="pz m"><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="ac gz gx gv qa qb"><div class="qc qd qe qf qg qh qi qj qk ql ac cp"><div class="i l"><a tabindex="0" rel="noopener follow" href="/?source=post_page---post_author_info--fecbf3b2c023---------------------------------------" data-discover="true"><div class="m fl"><img alt="Yash Khandelwal" class="m fd bx qm qn cx" src="https://miro.medium.com/v2/resize:fill:96:96/1*EiDslhOwxjP4PmZdC4houw.jpeg" width="48" height="48" loading="lazy"/><div class="ft bx m qm qn fu o aj qo"></div></div></a></div><div class="k j e"><a tabindex="0" rel="noopener follow" href="/?source=post_page---post_author_info--fecbf3b2c023---------------------------------------" data-discover="true"><div class="m fl"><img alt="Yash Khandelwal" class="m fd bx qp qq cx" src="https://miro.medium.com/v2/resize:fill:128:128/1*EiDslhOwxjP4PmZdC4houw.jpeg" width="64" height="64" loading="lazy"/><div class="ft bx m qp qq fu o aj qo"></div></div></a></div><div class="k j e qr py"><div class="ac"></div></div></div><div class="ac co ca"><div class="qs qt qu qv qw m"><a class="ag ah ai ak al am an ao ap aq ar as at au ac r" rel="noopener follow" href="/?source=post_page---post_author_info--fecbf3b2c023---------------------------------------" data-discover="true"><h2 class="pw-author-name bf qy qz ra rb rc rd re mi rf rg mm rh ri mq rj rk bk"><span class="kx qx">Written by <!-- -->Yash Khandelwal</span></h2></a><div class="pd ac hb"><div class="m py"><span class="pw-follower-count bf b bg ab du"><a class="ag ah ai fh ak al am an ao ap aq ar as hk" rel="noopener follow" href="/followers?source=post_page---post_author_info--fecbf3b2c023---------------------------------------" data-discover="true">20 followers</a></span></div><div class="bf b bg ab du ac rl"><span class="rm m" aria-hidden="true"><span class="bf b bg ab du">·</span></span><a class="ag ah ai fh ak al am an ao ap aq ar as hk" rel="noopener follow" href="/following?source=post_page---post_author_info--fecbf3b2c023---------------------------------------" data-discover="true">11 following</a></div></div><div class="os m"><p class="bf b bg ab bk"><span class="kx">Data Science &amp; ML enthusiast | Sports Fanatic | Passionate Learner</span></p></div></div></div><div class="i l"><div class="ac"></div></div></div></div></div></div><div class="rn m"><div class="ro bh s pz"></div><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="ac r cp"><h2 class="bf qy my na nb nc ne nf ng ni nj nk nm nn no nq nr bk">No responses yet</h2><div class="ac rp"><div><div class="bm" aria-hidden="false" role="tooltip"><div tabindex="-1" class="be"><a class="rq rr" href="https://policy.medium.com/medium-rules-30e5502c4eb4?source=post_page---post_responses--fecbf3b2c023---------------------------------------" rel="noopener follow" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" aria-label="Shield with a checkmark" viewBox="0 0 25 25"><path fill-rule="evenodd" d="M11.987 5.036a.754.754 0 0 1 .914-.01c.972.721 1.767 1.218 2.6 1.543.828.322 1.719.485 2.887.505a.755.755 0 0 1 .741.757c-.018 3.623-.43 6.256-1.449 8.21-1.034 1.984-2.662 3.209-4.966 4.083a.75.75 0 0 1-.537-.003c-2.243-.874-3.858-2.095-4.897-4.074-1.024-1.951-1.457-4.583-1.476-8.216a.755.755 0 0 1 .741-.757c1.195-.02 2.1-.182 2.923-.503.827-.322 1.6-.815 2.519-1.535m.468.903c-.897.69-1.717 1.21-2.623 1.564-.898.35-1.856.527-3.026.565.037 3.45.469 5.817 1.36 7.515.884 1.684 2.25 2.762 4.284 3.571 2.092-.81 3.465-1.89 4.344-3.575.886-1.698 1.299-4.065 1.334-7.512-1.149-.039-2.091-.217-2.99-.567-.906-.353-1.745-.873-2.683-1.561m-.009 9.155a2.672 2.672 0 1 0 0-5.344 2.672 2.672 0 0 0 0 5.344m0 1a3.672 3.672 0 1 0 0-7.344 3.672 3.672 0 0 0 0 7.344m-1.813-3.777.525-.526.916.917 1.623-1.625.526.526-2.149 2.152z" clip-rule="evenodd"></path></svg></a></div></div></div></div></div><div class="rs rt ru rv rw m"></div></div></div></div><div class="rx ry rz sa sb m bw"><div class="i l k"><div class="ro bh sc sd"></div><div class="ac cb"><div class="ci bh gb gc gd ge"><div class="se ac iv hm"><div class="sf sg m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://help.medium.com/hc/en-us?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Help</p></a></div><div class="sf sg m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://medium.statuspage.io/?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Status</p></a></div><div class="sf sg m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://medium.com/about?autoplay=1&amp;source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">About</p></a></div><div class="sf sg m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://medium.com/jobs-at-medium/work-at-medium-959d1a85284e?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Careers</p></a></div><div class="sf sg m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="mailto:<EMAIL>" rel="noopener follow"><p class="bf b dv ab du">Press</p></a></div><div class="sf sg m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://blog.medium.com/?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Blog</p></a></div><div class="sf sg m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://policy.medium.com/medium-privacy-policy-f03bf92035c9?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Privacy</p></a></div><div class="sf sg m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://policy.medium.com/medium-rules-30e5502c4eb4?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Rules</p></a></div><div class="sf sg m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://policy.medium.com/medium-terms-of-service-9db0094a1e0f?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Terms</p></a></div><div class="sf m"><a class="ag ah ai fh ak al am an ao ap aq ar as at au" href="https://speechify.com/medium?source=post_page-----fecbf3b2c023---------------------------------------" rel="noopener follow"><p class="bf b dv ab du">Text to speech</p></a></div></div></div></div></div></div></div></div></div></div></div></div><script>window.__BUILD_ID__="main-20250829-185947-c929e6f698"</script><script>window.__GRAPHQL_URI__ = "https://yashkhandelwal07.medium.com/_/graphql"</script><script>window.__PRELOADED_STATE__ = {"algolia":{"queries":{}},"cache":{"experimentGroupSet":true,"reason":"","group":"enabled","tags":["group-edgeCachePosts","post-fecbf3b2c023","user-abe69fbfb436","group-newLoNonMocUpsellExperimentGroup-group2"],"serverVariantState":"44136fa355b3678a1146ad16f7e8649e94fb4fc21fe77e8310c060f61caaff8a","middlewareEnabled":true,"cacheStatus":"DYNAMIC","shouldUseCache":true,"vary":[],"pubFeaturingPostPageLabelEnabled":false,"shouldFollowPostQueryEnabled":false,"newLoNonMocUpsellExperimentGroup":"group2"},"client":{"hydrated":false,"isUs":false,"isNativeMedium":false,"isSafariMobile":false,"isSafari":false,"isFirefox":false,"routingEntity":{"type":"USER","id":"abe69fbfb436","explicit":true},"viewerIsBot":false},"debug":{"requestId":"38e9b2ac-076d-4400-a5e0-************","requestTag":"","hybridDevServices":[],"originalSpanCarrier":{"traceparent":"00-70106e6fbb5ca0411692e4213f70334e-e7fa2ef66ce48958-01"}},"multiVote":{"clapsPerPost":{}},"navigation":{"branch":{"show":null,"hasRendered":null,"blockedByCTA":false},"hideGoogleOneTap":false,"hasRenderedAlternateUserBanner":null,"currentLocation":"https:\u002F\u002Fyashkhandelwal07.medium.com\u002Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023","host":"yashkhandelwal07.medium.com","hostname":"yashkhandelwal07.medium.com","referrer":"","hasSetReferrer":false,"susiModal":{"step":null,"operation":"register"},"postRead":false,"partnerProgram":{"selectedCountryCode":null},"staticRouterContext":{"route":{"name":"ShowPostUnderUser"},"statusCode":200},"toastQueue":[],"currentToast":null,"queryString":"","currentHash":""},"config":{"nodeEnv":"production","version":"main-20250829-185947-c929e6f698","target":"production","productName":"Medium","publicUrl":"https:\u002F\u002Fcdn-client.medium.com\u002Flite","authDomain":"medium.com","authGoogleClientId":"************-k1k6qe060s2tp2a2jam4ljdcms00sttg.apps.googleusercontent.com","favicon":"production","iosAppId":"828256236","glyphUrl":"https:\u002F\u002Fglyph.medium.com","branchKey":"key_live_ofxXr2qTrrU9NqURK8ZwEhknBxiI6KBm","algolia":{"appId":"MQ57UUUQZ2","apiKeySearch":"********************************","indexPrefix":"medium_","host":"-dsn.algolia.net"},"recaptchaKey":"6Lfc37IUAAAAAKGGtC6rLS13R1Hrw_BqADfS1LRk","recaptcha3Key":"6Lf8R9wUAAAAABMI_85Wb8melS7Zj6ziuf99Yot5","recaptchaEnterpriseKeyId":"6Le-uGgpAAAAAPprRaokM8AKthQ9KNGdoxaGUvVp","datadog":{"applicationId":"6702d87d-a7e0-42fe-bbcb-95b469547ea0","clientToken":"pub853ea8d17ad6821d9f8f11861d23dfed","rumToken":"pubf9cc52896502b9413b68ba36fc0c7162","context":{"deployment":{"target":"production","tag":"main-20250829-185947-c929e6f698","commit":"c929e6f698a22d0c3be06d191c5c594e2e313e6d"}},"datacenter":"us"},"googleAdsCode":"AW-17106321204","googleAnalyticsCode":"G-7JY7T788PK","googlePay":{"apiVersion":"2","apiVersionMinor":"0","merchantId":"BCR2DN6TV7EMTGBM","merchantName":"Medium","instanceMerchantId":"13685562959212738550"},"applePay":{"version":3},"signInWallCustomDomainCollectionIds":["3a8144eabfe3","336d898217ee","61061eb0c96b","138adf9c44c","819cc2aaeee0"],"mediumMastodonDomainName":"me.dm","mediumOwnedAndOperatedCollectionIds":["8a9336e5bb4","b7e45b22fec3","193b68bd4fba","8d6b8a439e32","54c98c43354d","3f6ecf56618","d944778ce714","92d2092dc598","ae2a65f35510","1285ba81cada","544c7006046e","fc8964313712","40187e704f1c","88d9857e584e","7b6769f2748b","bcc38c8f6edf","cef6983b292","cb8577c9149e","444d13b52878","713d7dbc99b0","ef8e90590e66","191186aaafa0","55760f21cdc5","9dc80918cc93","bdc4052bbdba","8ccfed20cbb2"],"tierOneDomains":["medium.com","thebolditalic.com","arcdigital.media","towardsdatascience.com","uxdesign.cc","codeburst.io","psiloveyou.xyz","writingcooperative.com","entrepreneurshandbook.co","prototypr.io","betterhumans.coach.me","theascent.pub"],"topicsToFollow":["d61cf867d93f","8a146bc21b28","1eca0103fff3","4d562ee63426","aef1078a3ef5","e15e46793f8d","6158eb913466","55f1c20aba7a","3d18b94f6858","4861fee224fd","63c6f1f93ee","1d98b3a9a871","decb52b64abf","ae5d4995e225","830cded25262"],"topicToTagMappings":{"accessibility":"accessibility","addiction":"addiction","android-development":"android-development","art":"art","artificial-intelligence":"artificial-intelligence","astrology":"astrology","basic-income":"basic-income","beauty":"beauty","biotech":"biotech","blockchain":"blockchain","books":"books","business":"business","cannabis":"cannabis","cities":"cities","climate-change":"climate-change","comics":"comics","coronavirus":"coronavirus","creativity":"creativity","cryptocurrency":"cryptocurrency","culture":"culture","cybersecurity":"cybersecurity","data-science":"data-science","design":"design","digital-life":"digital-life","disability":"disability","economy":"economy","education":"education","equality":"equality","family":"family","feminism":"feminism","fiction":"fiction","film":"film","fitness":"fitness","food":"food","freelancing":"freelancing","future":"future","gadgets":"gadgets","gaming":"gaming","gun-control":"gun-control","health":"health","history":"history","humor":"humor","immigration":"immigration","ios-development":"ios-development","javascript":"javascript","justice":"justice","language":"language","leadership":"leadership","lgbtqia":"lgbtqia","lifestyle":"lifestyle","machine-learning":"machine-learning","makers":"makers","marketing":"marketing","math":"math","media":"media","mental-health":"mental-health","mindfulness":"mindfulness","money":"money","music":"music","neuroscience":"neuroscience","nonfiction":"nonfiction","outdoors":"outdoors","parenting":"parenting","pets":"pets","philosophy":"philosophy","photography":"photography","podcasts":"podcast","poetry":"poetry","politics":"politics","privacy":"privacy","product-management":"product-management","productivity":"productivity","programming":"programming","psychedelics":"psychedelics","psychology":"psychology","race":"race","relationships":"relationships","religion":"religion","remote-work":"remote-work","san-francisco":"san-francisco","science":"science","self":"self","self-driving-cars":"self-driving-cars","sexuality":"sexuality","social-media":"social-media","society":"society","software-engineering":"software-engineering","space":"space","spirituality":"spirituality","sports":"sports","startups":"startup","style":"style","technology":"technology","transportation":"transportation","travel":"travel","true-crime":"true-crime","tv":"tv","ux":"ux","venture-capital":"venture-capital","visual-design":"visual-design","work":"work","world":"world","writing":"writing"},"defaultImages":{"avatar":{"imageId":"1*dmbNkD5D-u45r44go_cf0g.png","height":150,"width":150},"orgLogo":{"imageId":"7*V1_7XP4snlmqrc_0Njontw.png","height":110,"width":500},"postLogo":{"imageId":"167cff2a3d17ac1e64d0762539978f2d54c0058886e8b3c8a03a725a83012ec0","height":630,"width":1200},"postPreviewImage":{"imageId":"bc1f8416df0cad099e43cda2872716e5864f18a73bda2a7547ea082aca9b5632","height":630,"width":1200}},"embeddedPostIds":{"coronavirus":"cd3010f9d81f"},"sharedCdcMessaging":{"COVID_APPLICABLE_TAG_SLUGS":[],"COVID_APPLICABLE_TOPIC_NAMES":[],"COVID_APPLICABLE_TOPIC_NAMES_FOR_TOPIC_PAGE":[],"COVID_MESSAGES":{"tierA":{"text":"For more information on the novel coronavirus and Covid-19, visit cdc.gov.","markups":[{"start":66,"end":73,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]},"tierB":{"text":"Anyone can publish on Medium per our Policies, but we don’t fact-check every story. For more info about the coronavirus, see cdc.gov.","markups":[{"start":37,"end":45,"href":"https:\u002F\u002Fhelp.medium.com\u002Fhc\u002Fen-us\u002Fcategories\u002F201931128-Policies-Safety"},{"start":125,"end":132,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]},"paywall":{"text":"This article has been made free for everyone, thanks to Medium Members. For more information on the novel coronavirus and Covid-19, visit cdc.gov.","markups":[{"start":56,"end":70,"href":"https:\u002F\u002Fmedium.com\u002Fmembership"},{"start":138,"end":145,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]},"unbound":{"text":"This article is free for everyone, thanks to Medium Members. For more information on the novel coronavirus and Covid-19, visit cdc.gov.","markups":[{"start":45,"end":59,"href":"https:\u002F\u002Fmedium.com\u002Fmembership"},{"start":127,"end":134,"href":"https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV"}]}},"COVID_BANNER_POST_ID_OVERRIDE_WHITELIST":["3b31a67bff4a"]},"sharedVoteMessaging":{"TAGS":["politics","election-2020","government","us-politics","election","2020-presidential-race","trump","donald-trump","democrats","republicans","congress","republican-party","democratic-party","biden","joe-biden","maga"],"TOPICS":["politics","election"],"MESSAGE":{"text":"Find out more about the U.S. election results here.","markups":[{"start":46,"end":50,"href":"https:\u002F\u002Fcookpolitical.com\u002F2020-national-popular-vote-tracker"}]},"EXCLUDE_POSTS":["397ef29e3ca5"]},"embedPostRules":[],"recircOptions":{"v1":{"limit":3},"v2":{"limit":8}},"braintreeClientKey":"production_zjkj96jm_m56f8fqpf7ngnrd4","braintree":{"enabled":true,"merchantId":"m56f8fqpf7ngnrd4","merchantAccountId":{"usd":"AMediumCorporation_instant","eur":"amediumcorporation_EUR","cad":"amediumcorporation_CAD"},"publicKey":"ds2nn34bg2z7j5gd","braintreeEnvironment":"production","dashboardUrl":"https:\u002F\u002Fwww.braintreegateway.com\u002Fmerchants","gracePeriodDurationInDays":14,"mediumMembershipPlanId":{"monthly":"ce105f8c57a3","monthlyV2":"e8a5e126-792b-4ee6-8fba-d574c1b02fc5","monthlyWithTrial":"d5ee3dbe3db8","monthlyPremium":"fa741a9b47a2","yearly":"a40ad4a43185","yearlyV2":"3815d7d6-b8ca-4224-9b8c-182f9047866e","yearlyStaff":"d74fb811198a","yearlyWithTrial":"b3bc7350e5c7","yearlyPremium":"e21bd2c12166","monthlyOneYearFree":"e6c0637a-2bad-4171-ab4f-3c268633d83c","monthly25PercentOffFirstYear":"235ecc62-0cdb-49ae-9378-726cd21c504b","monthly20PercentOffFirstYear":"ba518864-9c13-4a99-91ca-411bf0cac756","monthly15PercentOffFirstYear":"594c029b-9f89-43d5-88f8-8173af4e070e","monthly10PercentOffFirstYear":"c6c7bc9a-40f2-4b51-8126-e28511d5bdb0","monthlyForStudents":"629ebe51-da7d-41fd-8293-34cd2f2030a8","yearlyOneYearFree":"78ba7be9-0d9f-4ece-aa3e-b54b826f2bf1","yearly25PercentOffFirstYear":"2dbb010d-bb8f-4eeb-ad5c-a08509f42d34","yearly20PercentOffFirstYear":"47565488-435b-47f8-bf93-40d5fbe0ebc8","yearly15PercentOffFirstYear":"8259809b-0881-47d9-acf7-6c001c7f720f","yearly10PercentOffFirstYear":"9dd694fb-96e1-472c-8d9e-3c868d5c1506","yearlyForStudents":"e29345ef-ab1c-4234-95c5-70e50fe6bc23","monthlyCad":"p52orjkaceei","yearlyCad":"h4q9g2up9ktt"},"braintreeDiscountId":{"oneMonthFree":"MONTHS_FREE_01","threeMonthsFree":"MONTHS_FREE_03","sixMonthsFree":"MONTHS_FREE_06","fiftyPercentOffOneYear":"FIFTY_PERCENT_OFF_ONE_YEAR"},"3DSecureVersion":"2","defaultCurrency":"usd","providerPlanIdCurrency":{"4ycw":"usd","rz3b":"usd","3kqm":"usd","jzw6":"usd","c2q2":"usd","nnsw":"usd","q8qw":"usd","d9y6":"usd","fx7w":"cad","nwf2":"cad"}},"paypalClientId":"AXj1G4fotC2GE8KzWX9mSxCH1wmPE3nJglf4Z2ig_amnhvlMVX87otaq58niAg9iuLktVNF_1WCMnN7v","paypal":{"host":"https:\u002F\u002Fapi.paypal.com:443","clientMode":"production","serverMode":"live","webhookId":"4G466076A0294510S","monthlyPlan":{"planId":"P-9WR0658853113943TMU5FDQA","name":"Medium Membership (Monthly) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"yearlyPlan":{"planId":"P-7N8963881P8875835MU5JOPQ","name":"Medium Membership (Annual) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"oneYearGift":{"name":"Medium Membership (1 Year, Digital Gift Code)","description":"Unlimited access to the best and brightest stories on Medium. Gift codes can be redeemed at medium.com\u002Fredeem.","price":"50.00","currency":"USD","sku":"membership-gift-1-yr"},"oldMonthlyPlan":{"planId":"P-96U02458LM656772MJZUVH2Y","name":"Medium Membership (Monthly)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"oldYearlyPlan":{"planId":"P-59P80963JF186412JJZU3SMI","name":"Medium Membership (Annual)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"monthlyPlanWithTrial":{"planId":"P-66C21969LR178604GJPVKUKY","name":"Medium Membership (Monthly) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"yearlyPlanWithTrial":{"planId":"P-6XW32684EX226940VKCT2MFA","name":"Medium Membership (Annual) with setup fee","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"oldMonthlyPlanNoSetupFee":{"planId":"P-4N046520HR188054PCJC7LJI","name":"Medium Membership (Monthly)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed monthly."},"oldYearlyPlanNoSetupFee":{"planId":"P-7A4913502Y5181304CJEJMXQ","name":"Medium Membership (Annual)","description":"Unlimited access to the best and brightest stories on Medium. Membership billed annually."},"sdkUrl":"https:\u002F\u002Fwww.paypal.com\u002Fsdk\u002Fjs"},"stripePublishableKey":"pk_live_7FReX44VnNIInZwrIIx6ghjl","log":{"json":true,"level":"info"},"imageUploadMaxSizeMb":25,"staffPicks":{"title":"Staff Picks","catalogId":"c7bc6e1ee00f"}},"session":{"xsrf":""}}</script><script>window.__APOLLO_STATE__ = {"ROOT_QUERY":{"__typename":"Query","viewer":null,"collectionByDomainOrSlug({\"domainOrSlug\":\"yashkhandelwal07.medium.com\"})":null,"postResult({\"id\":\"fecbf3b2c023\"})":{"__ref":"Post:fecbf3b2c023"}},"LinkedAccounts:abe69fbfb436":{"__typename":"LinkedAccounts","mastodon":null,"id":"abe69fbfb436"},"NewsletterV3:5b85c0a10070":{"__typename":"NewsletterV3","id":"5b85c0a10070","type":"NEWSLETTER_TYPE_AUTHOR","slug":"abe69fbfb436","name":"abe69fbfb436","collection":null,"user":{"__ref":"User:abe69fbfb436"}},"User:abe69fbfb436":{"__typename":"User","id":"abe69fbfb436","name":"Yash Khandelwal","username":"yashkhandelwal07","newsletterV3":{"__ref":"NewsletterV3:5b85c0a10070"},"linkedAccounts":{"__ref":"LinkedAccounts:abe69fbfb436"},"isSuspended":false,"imageId":"1*EiDslhOwxjP4PmZdC4houw.jpeg","customDomainState":{"__typename":"CustomDomainState","live":{"__typename":"CustomDomain","domain":"yashkhandelwal07.medium.com"}},"hasSubdomain":true,"verifications":{"__typename":"VerifiedInfo","isBookAuthor":false},"socialStats":{"__typename":"SocialStats","followerCount":20,"followingCount":9,"collectionFollowingCount":2},"bio":"Data Science & ML enthusiast | Sports Fanatic | Passionate Learner","membership":null,"allowNotes":true,"viewerEdge":{"__ref":"UserViewerEdge:userId:abe69fbfb436-viewerId:lo_40f07550e3be"},"twitterScreenName":""},"ImageMetadata:1*XB0OpXrvtI-61pRUOAhdGQ.jpeg":{"__typename":"ImageMetadata","id":"1*XB0OpXrvtI-61pRUOAhdGQ.jpeg","originalHeight":3200,"originalWidth":3200,"focusPercentX":null,"focusPercentY":null,"alt":null},"Paragraph:0546f7824995_0":{"__typename":"Paragraph","id":"0546f7824995_0","name":"cb08","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*XB0OpXrvtI-61pRUOAhdGQ.jpeg"},"text":"Hackathons can be rewarding!!!","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_1":{"__typename":"Paragraph","id":"0546f7824995_1","name":"a2db","type":"P","href":null,"layout":null,"metadata":null,"text":"Get ready to up your game in data science hackathons! In this blog, I’ll be sharing the winning solution approaches & best practices in Data Science & Machine Learning. It’s a practical guide to help you navigate and triumph in data hackathons, distilled from the lessons learned through my own National victories in past hackathons. Let’s dive into the essentials that can boost your chances of emerging victorious in any data science challenge.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_2":{"__typename":"Paragraph","id":"0546f7824995_2","name":"aeb1","type":"H3","href":null,"layout":null,"metadata":null,"text":"Where to participate?","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*8sV0YYB2GHuNOnT4BqAGxw.jpeg":{"__typename":"ImageMetadata","id":"1*8sV0YYB2GHuNOnT4BqAGxw.jpeg","originalHeight":2652,"originalWidth":4714,"focusPercentX":null,"focusPercentY":null,"alt":null},"Paragraph:0546f7824995_3":{"__typename":"Paragraph","id":"0546f7824995_3","name":"6edd","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*8sV0YYB2GHuNOnT4BqAGxw.jpeg"},"text":"Source: Unsplash","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_4":{"__typename":"Paragraph","id":"0546f7824995_4","name":"2714","type":"P","href":null,"layout":null,"metadata":null,"text":"The platforms, such as Kaggle, DataHack, MachineHack, Unstop, etc. offer a chance to apply your skills in Python, Data Analytics, Machine Learning, etc in real-world challenges. Participating in these platforms is not merely about clinching the top spot; it’s a journey of hands-on learning and community engagement. These hackathons provide an invaluable chance to test your capabilities, learn from the diverse approaches of fellow participants, and stay attuned to the ever-evolving landscape of data science. Consider it your ticket to continuous improvement, community collaboration, and a noteworthy addition to your professional portfolio!","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":23,"end":29,"href":"https:\u002F\u002Fwww.kaggle.com\u002Fcompetitions","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":31,"end":39,"href":"https:\u002F\u002Fdatahack.analyticsvidhya.com\u002Fcontest\u002Fall\u002F","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":41,"end":52,"href":"https:\u002F\u002Fmachinehack.com\u002Fhackathons","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":54,"end":60,"href":"https:\u002F\u002Funstop.com\u002Fhackathons","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_5":{"__typename":"Paragraph","id":"0546f7824995_5","name":"2951","type":"H3","href":null,"layout":null,"metadata":null,"text":"Problem Statements","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*rw5LDHRkBY2aajU84p2Dfg.jpeg":{"__typename":"ImageMetadata","id":"1*rw5LDHRkBY2aajU84p2Dfg.jpeg","originalHeight":4472,"originalWidth":7952,"focusPercentX":null,"focusPercentY":null,"alt":null},"Paragraph:0546f7824995_6":{"__typename":"Paragraph","id":"0546f7824995_6","name":"2cfa","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*rw5LDHRkBY2aajU84p2Dfg.jpeg"},"text":"Source: Unsplash","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_7":{"__typename":"Paragraph","id":"0546f7824995_7","name":"a56c","type":"P","href":null,"layout":null,"metadata":null,"text":"Let’s now see a glimpse of intriguing problem statements you’ll encounter -","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_8":{"__typename":"Paragraph","id":"0546f7824995_8","name":"eed4","type":"P","href":null,"layout":null,"metadata":null,"text":"1. Supervised\u002F Unsupervised Machine Learning (ML):","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":50,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_9":{"__typename":"Paragraph","id":"0546f7824995_9","name":"ae39","type":"P","href":null,"layout":null,"metadata":null,"text":"Example: Predicting the sales of a mega mart and making recommendations for sustainable sales.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_10":{"__typename":"Paragraph","id":"0546f7824995_10","name":"2efc","type":"P","href":null,"layout":null,"metadata":null,"text":"2. Time Series Forecasting:","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":27,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_11":{"__typename":"Paragraph","id":"0546f7824995_11","name":"1c2c","type":"P","href":null,"layout":null,"metadata":null,"text":"Example: Forecasting PM2.5 concentrations for the first 3 days of 2023 for 34 Indian Cities.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_12":{"__typename":"Paragraph","id":"0546f7824995_12","name":"f222","type":"P","href":null,"layout":null,"metadata":null,"text":"3. Deep Learning:","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":17,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_13":{"__typename":"Paragraph","id":"0546f7824995_13","name":"4c1f","type":"P","href":null,"layout":null,"metadata":null,"text":"Example: Estimating the intensity of cyclones from satellite imagery using CNN.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_14":{"__typename":"Paragraph","id":"0546f7824995_14","name":"d06b","type":"P","href":null,"layout":null,"metadata":null,"text":"How the Game Works: Behind the Scenes","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":37,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_15":{"__typename":"Paragraph","id":"0546f7824995_15","name":"c9fe","type":"P","href":null,"layout":null,"metadata":null,"text":"In hackathons, participants submit the predictions from their model for evaluation. A private leaderboard is used to track participants’ performance, but the scores are hidden until the end. Winners are determined based on the predictive accuracy of their models through various metrics depending on the type of predicting task. The target is to develop a ML model that is perfect fit on the training data and it doesn’t overfit, giving the best metric value on the leaderboard.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_16":{"__typename":"Paragraph","id":"0546f7824995_16","name":"8a8d","type":"P","href":null,"layout":null,"metadata":null,"text":"Here’s how it works: you build a model to predict things, like sales or pollution. You submit your predictions, but you don’t know how well you did until the end. There’s a special scoreboard that keeps track, but it keeps the scores a secret until it’s over. The winners are the ones whose predictions were super close to what really happened.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_17":{"__typename":"Paragraph","id":"0546f7824995_17","name":"b2c8","type":"P","href":null,"layout":null,"metadata":null,"text":"Lets now look at the state-of-the-art techniques and best ML practices to top the Private leaderboard!","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":102,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_18":{"__typename":"Paragraph","id":"0546f7824995_18","name":"27c7","type":"H3","href":null,"layout":null,"metadata":null,"text":"Supervised\u002F Unsupervised Machine Learning (ML)","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_19":{"__typename":"Paragraph","id":"0546f7824995_19","name":"bb28","type":"P","href":null,"layout":null,"metadata":null,"text":"Let’s take the problem statement mentioned before into account — “Predicting the sales of a mega mart and making recommendations for sustainable sales” and see how my winning solution approach & techniques were.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_20":{"__typename":"Paragraph","id":"0546f7824995_20","name":"a2e9","type":"P","href":null,"layout":null,"metadata":null,"text":"1. Understanding the Data (Link to the dataset, Hackathon Link) -","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":48,"end":62,"href":"https:\u002F\u002Fmachinehack.com\u002Fhackathons\u002Fdata_analytics_championship\u002Foverview","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":0,"end":48,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":62,"end":65,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_21":{"__typename":"Paragraph","id":"0546f7824995_21","name":"1831","type":"P","href":null,"layout":null,"metadata":null,"text":"The challenge revolves around predicting the sales of a mega mart. In this regression task, the training data comprises 87,864 observations with 12 features, including 7 categorical and 5 continuous ones. The test data consists of 37,656 observations, and the target feature is the Sales of items in the mega mart.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_22":{"__typename":"Paragraph","id":"0546f7824995_22","name":"3c81","type":"P","href":null,"layout":null,"metadata":null,"text":"2. Data Preparation","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":19,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_23":{"__typename":"Paragraph","id":"0546f7824995_23","name":"f23f","type":"P","href":null,"layout":null,"metadata":null,"text":"The journey to a successful model kicks off with meticulous data preparation. This involves transforming raw data into an informative dataset, laying the foundation for a machine learning model that not only comprehends the intricacies of the training data but also excels in making accurate predictions on real-world data.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_24":{"__typename":"Paragraph","id":"0546f7824995_24","name":"3f14","type":"P","href":null,"layout":null,"metadata":null,"text":"Let’s, look at the findings in the data and the preparation methods that worked best on it.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"ImageMetadata:1*0hLwpxEFSIT6oBZ9lIbx_A.png":{"__typename":"ImageMetadata","id":"1*0hLwpxEFSIT6oBZ9lIbx_A.png","originalHeight":602,"originalWidth":850,"focusPercentX":null,"focusPercentY":null,"alt":null},"Paragraph:0546f7824995_25":{"__typename":"Paragraph","id":"0546f7824995_25","name":"cf55","type":"IMG","href":null,"layout":"INSET_CENTER","metadata":{"__ref":"ImageMetadata:1*0hLwpxEFSIT6oBZ9lIbx_A.png"},"text":"The dataset","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_26":{"__typename":"Paragraph","id":"0546f7824995_26","name":"06c9","type":"P","href":null,"layout":null,"metadata":null,"text":"Outlier Detection: Inter Quartile Range Method","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":18,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":0,"end":46,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_27":{"__typename":"Paragraph","id":"0546f7824995_27","name":"1626","type":"P","href":null,"layout":null,"metadata":null,"text":"Applying the Interquartile Range (IQR) Method to identify and handle outliers ensures a cleaner dataset. The code snippet demonstrates how to cap extreme values within the acceptable range.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_28":{"__typename":"Paragraph","id":"0546f7824995_28","name":"f74d","type":"PRE","href":null,"layout":null,"metadata":null,"text":"# Assuming df is the DataFrame \nQ1 = df[‘Sales’].quantile(0.25) \nQ3 = df[‘Sales’].quantile(0.75) \nIQR = Q3 — Q1 \nlower_bound = Q1–1.5 * IQR \nupper_bound = Q3 + 1.5 * IQR \ndf[‘Sales’] = df[‘Sales’].apply(lambda x: upper_bound if x \u003E upper_bound else (lower_bound if x \u003C lower_bound else x))","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":{"__typename":"CodeBlockMetadata","mode":"AUTO","lang":"makefile"},"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_29":{"__typename":"Paragraph","id":"0546f7824995_29","name":"e14c","type":"P","href":null,"layout":null,"metadata":null,"text":"Null Value Imputation: No null values found!","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":23,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":0,"end":44,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_30":{"__typename":"Paragraph","id":"0546f7824995_30","name":"ca8f","type":"P","href":null,"layout":null,"metadata":null,"text":"A check for null values is conducted, and in this case, none are found, simplifying the data preparation process.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_31":{"__typename":"Paragraph","id":"0546f7824995_31","name":"ae00","type":"P","href":null,"layout":null,"metadata":null,"text":"Categorical Encoding: One Hot Encoding (When categorical variables have no inherent order or ranking), Ordinal Encoding (When categorical variables have a clear order or rank).","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":22,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":0,"end":40,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":103,"end":120,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_32":{"__typename":"Paragraph","id":"0546f7824995_32","name":"dd45","type":"P","href":null,"layout":null,"metadata":null,"text":"For example, in this dataset — Outlet_ID, Item_Type, Outlet_Year, Outlet_Type have no order while Outlet_Size, Outlet_location_type, Item_FC have a visible order.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_33":{"__typename":"Paragraph","id":"0546f7824995_33","name":"d39f","type":"PRE","href":null,"layout":null,"metadata":null,"text":"# One Hot Encoding \ndf_encoded = pd.get_dummies(df, columns=[‘categorical_feature’], drop_first=True) # Ordinal Encoding \nordinal_mapping = {‘low’: 0, ‘medium’: 1, ‘high’: 2} \ndf[‘ordinal_feature’] = df[‘ordinal_feature’].map(ordinal_mapping)","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":{"__typename":"CodeBlockMetadata","mode":"AUTO","lang":"bash"},"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_34":{"__typename":"Paragraph","id":"0546f7824995_34","name":"9775","type":"P","href":null,"layout":null,"metadata":null,"text":"Feature Engineering: Creating meaningful features that enhance a machine learning model’s performance. For example in this data — Years from present (2023 — Outlet_year)","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":21,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":29,"end":30,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":130,"end":148,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_35":{"__typename":"Paragraph","id":"0546f7824995_35","name":"6095","type":"PRE","href":null,"layout":null,"metadata":null,"text":"# Assuming df is your DataFrame\ndf[‘new_feature’] = df[‘feature1’] * df[‘feature2’]","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":{"__typename":"CodeBlockMetadata","mode":"AUTO","lang":"bash"},"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_36":{"__typename":"Paragraph","id":"0546f7824995_36","name":"7fe6","type":"P","href":null,"layout":null,"metadata":null,"text":"Feature Selection:","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":18,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":0,"end":18,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_37":{"__typename":"Paragraph","id":"0546f7824995_37","name":"57ab","type":"P","href":null,"layout":null,"metadata":null,"text":"Permutation Feature Importance: is a technique that measures the impact of each feature on a model’s performance by randomly shuffling the values of a single feature and observing the effect on model accuracy.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":0,"end":31,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_38":{"__typename":"Paragraph","id":"0546f7824995_38","name":"f47c","type":"PRE","href":null,"layout":null,"metadata":null,"text":"from sklearn.inspection import permutation_importance \n# Assuming model is your trained machine learning model \nperm_importance = permutation_importance(model, X_test, y_test, n_repeats=10, random_state=42) \nfeature_importance = perm_importance.importances_mean","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":{"__typename":"CodeBlockMetadata","mode":"AUTO","lang":"makefile"},"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_39":{"__typename":"Paragraph","id":"0546f7824995_39","name":"9691","type":"P","href":null,"layout":null,"metadata":null,"text":"XGBoost Feature Importance: XGBoost, a powerful gradient boosting algorithm, provides a built-in method for feature importance. It calculates how each feature contributes to reducing the impurity during tree construction.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_40":{"__typename":"Paragraph","id":"0546f7824995_40","name":"57d0","type":"P","href":null,"layout":null,"metadata":null,"text":"import xgboost as xgb # Assuming X_train, y_train are your training data model = xgb.XGBClassifier() model.fit(X_train, y_train) # Accessing feature importance scores feature_importance = model.feature_importances_#import csv12345678makefile","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_41":{"__typename":"Paragraph","id":"0546f7824995_41","name":"580d","type":"P","href":null,"layout":null,"metadata":null,"text":"Both techniques offer insights into feature importance, helping you identify key variables and refine your feature set for improved model performance.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_42":{"__typename":"Paragraph","id":"0546f7824995_42","name":"89fc","type":"P","href":null,"layout":null,"metadata":null,"text":"3. Machine Learning — Modelling","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":31,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_43":{"__typename":"Paragraph","id":"0546f7824995_43","name":"ff2d","type":"P","href":null,"layout":null,"metadata":null,"text":"The modelling begins with splitting the training data into training and validation. There are major two techniques for the same — 1. K Fold Cross Validation 2. Train-test-split","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"EM","start":132,"end":157,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"EM","start":160,"end":176,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_44":{"__typename":"Paragraph","id":"0546f7824995_44","name":"0b2f","type":"P","href":null,"layout":null,"metadata":null,"text":"For this dataset, train-test-split is itself efficient because of abundant data, prioritising simplicity and faster computation.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_45":{"__typename":"Paragraph","id":"0546f7824995_45","name":"9f92","type":"PRE","href":null,"layout":null,"metadata":null,"text":"from sklearn.model_selection import train_test_split \n# Assuming X is your feature matrix and y is your target variable \nX_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42) #import csv12345python","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":{"__typename":"CodeBlockMetadata","mode":"AUTO","lang":"python"},"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_46":{"__typename":"Paragraph","id":"0546f7824995_46","name":"c756","type":"P","href":null,"layout":null,"metadata":null,"text":"Modelling","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":9,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_47":{"__typename":"Paragraph","id":"0546f7824995_47","name":"bd2f","type":"P","href":null,"layout":null,"metadata":null,"text":"Efficient regression model development begins with a baseline model, often a simple linear regression, serving as a performance benchmark. Evaluate its results using appropriate metrics. Explore feature relationships and engineer informative features. Even try to alter the data preparation with suitable methods. Experiment with various models, fine-tune hyperparameters, and consider ensemble methods for improved robustness.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_48":{"__typename":"Paragraph","id":"0546f7824995_48","name":"555b","type":"P","href":null,"layout":null,"metadata":null,"text":"This is the order in which I begin my modelling experimentation.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_49":{"__typename":"Paragraph","id":"0546f7824995_49","name":"3350","type":"P","href":null,"layout":null,"metadata":null,"text":"1. Linear Regression","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":20,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_50":{"__typename":"Paragraph","id":"0546f7824995_50","name":"6fc3","type":"P","href":null,"layout":null,"metadata":null,"text":"2. Lasso & Ridge Regression (Regularization, will prevent over fitting)","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":27,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_51":{"__typename":"Paragraph","id":"0546f7824995_51","name":"1bd7","type":"P","href":null,"layout":null,"metadata":null,"text":"3. Decision Tree (Will give better metric values on training data through high predictive performance)","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":16,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_52":{"__typename":"Paragraph","id":"0546f7824995_52","name":"09fb","type":"P","href":null,"layout":null,"metadata":null,"text":"4. Random Forest (Will solve the overfitting issue of decision trees)","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":16,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_53":{"__typename":"Paragraph","id":"0546f7824995_53","name":"17cf","type":"P","href":null,"layout":null,"metadata":null,"text":"5. XGBoost, AdaBoost, LightGBM, CatBoost (Boosting models sequentially build a robust learner by focusing on correcting errors made by weak models, effectively reducing bias, lowering variance, and excelling in capturing intricate patterns)","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":40,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_54":{"__typename":"Paragraph","id":"0546f7824995_54","name":"7467","type":"P","href":null,"layout":null,"metadata":null,"text":"Expect the best performance from the boosting models. But where does the crux of winning solution lies then?","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_55":{"__typename":"Paragraph","id":"0546f7824995_55","name":"72b2","type":"P","href":null,"layout":null,"metadata":null,"text":"There are few more, less common techniques — Stacking Ensemble & Weighted Average Ensemble. Stacking, with its meta-model learning from varied base model predictions, often outperforms individual models. Average ensemble methods, on the other hand, leverage collective wisdom to create a more balanced and robust prediction by mitigating biases or errors present in individual models. Both approaches contribute to enhanced model performance through effective combination and adaptation to different aspects of the underlying data.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":45,"end":62,"href":null,"anchorType":null,"userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":65,"end":92,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_56":{"__typename":"Paragraph","id":"0546f7824995_56","name":"7707","type":"P","href":null,"layout":null,"metadata":null,"text":"Both of these models had the best performance on this data. These models topped the leaderboard!","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_57":{"__typename":"Paragraph","id":"0546f7824995_57","name":"9891","type":"PRE","href":null,"layout":null,"metadata":null,"text":"#Code for Stacking Ensembling\nfrom sklearn.ensemble import StackingRegressor \nfrom sklearn.linear_model import LinearRegression \nfrom sklearn.ensemble import RandomForestRegressor\nfrom sklearn.tree import DecisionTreeRegressor \n# Assuming base models are your individual regression models \nbase_models = [(‘linear’, LinearRegression()), (‘rf’, RandomForestRegressor()), (‘tree’, DecisionTreeRegressor())] \nstacking_model = StackingRegressor(estimators=base_models, final_estimator=LinearRegression()) \nstacking_model.fit(X_train, y_train)","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":{"__typename":"CodeBlockMetadata","mode":"AUTO","lang":"python"},"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_58":{"__typename":"Paragraph","id":"0546f7824995_58","name":"c83a","type":"PRE","href":null,"layout":null,"metadata":null,"text":"#Code for Weighted Average Ensemble\n# Assuming model1, model2, model3 are your regression models\nweights = [0.4, 0.3, 0.3] \n# Adjust weights based on model performance\npredictions_model1 = model1.predict(X_test) \npredictions_model2 = model2.predict(X_test) \npredictions_model3 = model3.predict(X_test) \nweighted_average_predictions = (weights[0] * predictions_model1 + weights[1] * predictions_model2 + weights[2] * predictions_model3) \u002F sum(weights) ","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":{"__typename":"CodeBlockMetadata","mode":"AUTO","lang":"ini"},"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_59":{"__typename":"Paragraph","id":"0546f7824995_59","name":"badd","type":"P","href":null,"layout":null,"metadata":null,"text":"End Note","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":0,"end":8,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_60":{"__typename":"Paragraph","id":"0546f7824995_60","name":"a373","type":"P","href":null,"layout":null,"metadata":null,"text":"Creating an efficient machine learning (ML) model involves foundational steps. Starting with a comprehensive understanding of the data, addressing missing values, and outliers with patience. Thoughtful feature engineering enhances the model’s pattern recognition. Systematic hyperparameter tuning fine-tunes performance, and careful model selection aligns with specific tasks. Ensemble methods, cross-validation, and regularization contribute to robustness. Incorporate machine learning explainability for transparency in model decisions, crucial for business understanding. Lastly, continuous iteration based on evaluation insights ensures ongoing enhancement of predictive capabilities, aligning the model with both technical and business objectives of problem statement of the hackathon.","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"STRONG","start":575,"end":790,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_61":{"__typename":"Paragraph","id":"0546f7824995_61","name":"dbcb","type":"P","href":null,"layout":null,"metadata":null,"text":"Remember, to win a hackathon, an efficient ML model & your leaderboard standing may not be enough. You might have to also deliver an outstanding presentation on your ML solution in order to win 🏆🏅","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_62":{"__typename":"Paragraph","id":"0546f7824995_62","name":"9517","type":"P","href":null,"layout":null,"metadata":null,"text":"Here is the link to my winning solution for this problem statement which was a part of Analytics Olympiad 2021 : Github Link(also, have a look on how a presentation for your solution should look like)","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":87,"end":110,"href":"https:\u002F\u002Fanalyticsindiamag.com\u002Fanalytics-olympiad-organised-by-shiv-nadar-university-aim-ends-on-a-high-note\u002F","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"A","start":113,"end":119,"href":"https:\u002F\u002Fgithub.com\u002FYashK07\u002FAnalytics-Olympiad-2021-My-Winning-Solution","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":113,"end":124,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_63":{"__typename":"Paragraph","id":"0546f7824995_63","name":"0274","type":"P","href":null,"layout":null,"metadata":null,"text":"Here are a few helpful resource links that might be useful along this blog:","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_64":{"__typename":"Paragraph","id":"0546f7824995_64","name":"0a21","type":"OLI","href":null,"layout":null,"metadata":null,"text":"Stacking Ensemble in Depth with code -https:\u002F\u002Fwww.analyticsvidhya.com\u002Fblog\u002F2021\u002F08\u002Fensemble-stacking-for-machine-learning-and-deep-learning\u002F","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":38,"end":140,"href":"https:\u002F\u002Fwww.analyticsvidhya.com\u002Fblog\u002F2021\u002F08\u002Fensemble-stacking-for-machine-learning-and-deep-learning\u002F","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":0,"end":38,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_65":{"__typename":"Paragraph","id":"0546f7824995_65","name":"fb16","type":"OLI","href":null,"layout":null,"metadata":null,"text":"Lifecycle of a Data Science Project -https:\u002F\u002Fwww.analyticsvidhya.com\u002Fblog\u002F2021\u002F10\u002Fintroduction-to-the-lifecycle-of-data-science-project\u002F#h2_9","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":37,"end":141,"href":"https:\u002F\u002Fwww.analyticsvidhya.com\u002Fblog\u002F2021\u002F10\u002Fintroduction-to-the-lifecycle-of-data-science-project\u002F#h2_9","anchorType":"LINK","userId":null,"linkMetadata":null},{"__typename":"Markup","type":"STRONG","start":0,"end":37,"href":null,"anchorType":null,"userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_66":{"__typename":"Paragraph","id":"0546f7824995_66","name":"231c","type":"H3","href":null,"layout":null,"metadata":null,"text":"Time Series Forecasting","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_67":{"__typename":"Paragraph","id":"0546f7824995_67","name":"d7fb","type":"P","href":null,"layout":null,"metadata":null,"text":"This is another challenging problem statement in Data Science hackthons.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_68":{"__typename":"Paragraph","id":"0546f7824995_68","name":"2880","type":"P","href":null,"layout":null,"metadata":null,"text":"Let’s continue with Time Series Forecasting in the next blog titled — “Data Science Hackathons: A Practical Guide (Part 2)”. Happy Reading! 😊💻","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_69":{"__typename":"Paragraph","id":"0546f7824995_69","name":"19d7","type":"H3","href":null,"layout":null,"metadata":null,"text":"About the Author","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_70":{"__typename":"Paragraph","id":"0546f7824995_70","name":"dd37","type":"P","href":null,"layout":null,"metadata":null,"text":"Yash Khandelwal is a final year student pursuing Mathematics & Computing at Birla Institute of Technology, Mesra. He was a National Winner of EXL EQ 2023, National Finalist of Smart India Hackathon 2022 and a National Runner up of Analytics Olympiad 2021.","hasDropCap":null,"dropCapImage":null,"markups":[],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"Paragraph:0546f7824995_71":{"__typename":"Paragraph","id":"0546f7824995_71","name":"5d95","type":"P","href":null,"layout":null,"metadata":null,"text":"Connect with him on LinkedIn — https:\u002F\u002Fwww.linkedin.com\u002Fin\u002Fyash-khandelwal-a40484bb\u002F","hasDropCap":null,"dropCapImage":null,"markups":[{"__typename":"Markup","type":"A","start":31,"end":84,"href":"https:\u002F\u002Fwww.linkedin.com\u002Fin\u002Fyash-khandelwal-a40484bb\u002F","anchorType":"LINK","userId":null,"linkMetadata":null}],"codeBlockMetadata":null,"iframe":null,"mixtapeMetadata":null},"UserViewerEdge:userId:abe69fbfb436-viewerId:lo_40f07550e3be":{"__typename":"UserViewerEdge","id":"userId:abe69fbfb436-viewerId:lo_40f07550e3be","isMuting":false},"PostViewerEdge:postId:fecbf3b2c023-viewerId:lo_40f07550e3be":{"__typename":"PostViewerEdge","shouldIndexPostForExternalSearch":true,"id":"postId:fecbf3b2c023-viewerId:lo_40f07550e3be"},"Tag:data-science":{"__typename":"Tag","id":"data-science","displayTitle":"Data Science","normalizedTagSlug":"data-science"},"Tag:hackathons":{"__typename":"Tag","id":"hackathons","displayTitle":"Hackathons","normalizedTagSlug":"hackathons"},"Tag:machine-learning":{"__typename":"Tag","id":"machine-learning","displayTitle":"Machine Learning","normalizedTagSlug":"machine-learning"},"Tag:code":{"__typename":"Tag","id":"code","displayTitle":"Code","normalizedTagSlug":"code"},"Tag:data-analysis":{"__typename":"Tag","id":"data-analysis","displayTitle":"Data Analysis","normalizedTagSlug":"data-analysis"},"Post:fecbf3b2c023":{"__typename":"Post","id":"fecbf3b2c023","collection":null,"content({\"postMeteringOptions\":{\"referrer\":\"\"}})":{"__typename":"PostContent","isLockedPreviewOnly":false,"bodyModel":{"__typename":"RichText","sections":[{"__typename":"Section","name":"7e7b","startIndex":0,"textLayout":null,"imageLayout":null,"backgroundImage":null,"videoLayout":null,"backgroundVideo":null}],"paragraphs":[{"__ref":"Paragraph:0546f7824995_0"},{"__ref":"Paragraph:0546f7824995_1"},{"__ref":"Paragraph:0546f7824995_2"},{"__ref":"Paragraph:0546f7824995_3"},{"__ref":"Paragraph:0546f7824995_4"},{"__ref":"Paragraph:0546f7824995_5"},{"__ref":"Paragraph:0546f7824995_6"},{"__ref":"Paragraph:0546f7824995_7"},{"__ref":"Paragraph:0546f7824995_8"},{"__ref":"Paragraph:0546f7824995_9"},{"__ref":"Paragraph:0546f7824995_10"},{"__ref":"Paragraph:0546f7824995_11"},{"__ref":"Paragraph:0546f7824995_12"},{"__ref":"Paragraph:0546f7824995_13"},{"__ref":"Paragraph:0546f7824995_14"},{"__ref":"Paragraph:0546f7824995_15"},{"__ref":"Paragraph:0546f7824995_16"},{"__ref":"Paragraph:0546f7824995_17"},{"__ref":"Paragraph:0546f7824995_18"},{"__ref":"Paragraph:0546f7824995_19"},{"__ref":"Paragraph:0546f7824995_20"},{"__ref":"Paragraph:0546f7824995_21"},{"__ref":"Paragraph:0546f7824995_22"},{"__ref":"Paragraph:0546f7824995_23"},{"__ref":"Paragraph:0546f7824995_24"},{"__ref":"Paragraph:0546f7824995_25"},{"__ref":"Paragraph:0546f7824995_26"},{"__ref":"Paragraph:0546f7824995_27"},{"__ref":"Paragraph:0546f7824995_28"},{"__ref":"Paragraph:0546f7824995_29"},{"__ref":"Paragraph:0546f7824995_30"},{"__ref":"Paragraph:0546f7824995_31"},{"__ref":"Paragraph:0546f7824995_32"},{"__ref":"Paragraph:0546f7824995_33"},{"__ref":"Paragraph:0546f7824995_34"},{"__ref":"Paragraph:0546f7824995_35"},{"__ref":"Paragraph:0546f7824995_36"},{"__ref":"Paragraph:0546f7824995_37"},{"__ref":"Paragraph:0546f7824995_38"},{"__ref":"Paragraph:0546f7824995_39"},{"__ref":"Paragraph:0546f7824995_40"},{"__ref":"Paragraph:0546f7824995_41"},{"__ref":"Paragraph:0546f7824995_42"},{"__ref":"Paragraph:0546f7824995_43"},{"__ref":"Paragraph:0546f7824995_44"},{"__ref":"Paragraph:0546f7824995_45"},{"__ref":"Paragraph:0546f7824995_46"},{"__ref":"Paragraph:0546f7824995_47"},{"__ref":"Paragraph:0546f7824995_48"},{"__ref":"Paragraph:0546f7824995_49"},{"__ref":"Paragraph:0546f7824995_50"},{"__ref":"Paragraph:0546f7824995_51"},{"__ref":"Paragraph:0546f7824995_52"},{"__ref":"Paragraph:0546f7824995_53"},{"__ref":"Paragraph:0546f7824995_54"},{"__ref":"Paragraph:0546f7824995_55"},{"__ref":"Paragraph:0546f7824995_56"},{"__ref":"Paragraph:0546f7824995_57"},{"__ref":"Paragraph:0546f7824995_58"},{"__ref":"Paragraph:0546f7824995_59"},{"__ref":"Paragraph:0546f7824995_60"},{"__ref":"Paragraph:0546f7824995_61"},{"__ref":"Paragraph:0546f7824995_62"},{"__ref":"Paragraph:0546f7824995_63"},{"__ref":"Paragraph:0546f7824995_64"},{"__ref":"Paragraph:0546f7824995_65"},{"__ref":"Paragraph:0546f7824995_66"},{"__ref":"Paragraph:0546f7824995_67"},{"__ref":"Paragraph:0546f7824995_68"},{"__ref":"Paragraph:0546f7824995_69"},{"__ref":"Paragraph:0546f7824995_70"},{"__ref":"Paragraph:0546f7824995_71"}]},"validatedShareKey":"","shareKeyCreator":null},"creator":{"__ref":"User:abe69fbfb436"},"inResponseToEntityType":null,"isLocked":false,"isMarkedPaywallOnly":false,"lockedSource":"LOCKED_POST_SOURCE_NONE","mediumUrl":"https:\u002F\u002Fyashkhandelwal07.medium.com\u002Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023","primaryTopic":null,"topics":[{"__typename":"Topic","slug":"data-science"}],"isLimitedState":false,"isPublished":true,"allowResponses":true,"responsesLocked":false,"visibility":"PUBLIC","latestPublishedVersion":"0546f7824995","postResponses":{"__typename":"PostResponses","count":0},"responseDistribution":"NOT_DISTRIBUTED","clapCount":1,"title":"Data Science Hackathons: Winner’s Guide (Part 1)","isSeries":false,"sequence":null,"uniqueSlug":"data-science-hackathons-winners-guide-part-1-fecbf3b2c023","socialTitle":"","socialDek":"","canonicalUrl":"https:\u002F\u002Fyashkhandelwal07.medium.com\u002Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023","metaDescription":"","latestPublishedAt":1701179357306,"readingTime":6.884905660377359,"previewContent":{"__typename":"PreviewContent","subtitle":"Tactics for Mastering Data Science Hackathons"},"previewImage":{"__ref":"ImageMetadata:1*XB0OpXrvtI-61pRUOAhdGQ.jpeg"},"isShortform":false,"seoMetaTags":{"__typename":"SEOMetaTags","jsonLd":"{\"@context\":\"https:\u002F\u002Fschema.org\",\"@id\":\"https:\u002F\u002Fyashkhandelwal07.medium.com\u002Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023\",\"@type\":\"SocialMediaPosting\",\"image\":[\"https:\u002F\u002Fmiro.medium.com\u002F1*XB0OpXrvtI-61pRUOAhdGQ.jpeg\"],\"url\":\"https:\u002F\u002Fyashkhandelwal07.medium.com\u002Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023\",\"dateCreated\":\"2023-11-28T13:46:13Z\",\"datePublished\":\"2023-11-28T13:46:13Z\",\"dateModified\":\"2023-11-28T13:49:17Z\",\"headline\":\"Get ready to up your game in data science hackathons!\",\"name\":\"Get ready to up your game in data science hackathons!\",\"description\":\"Get ready to up your game in data science hackathons! In this blog, I’ll be sharing the winning solution approaches \\u0026 best practices in Data Science \\u0026 Machine Learning. It’s a practical guide to …\",\"identifier\":\"fecbf3b2c023\",\"author\":{\"@context\":\"https:\u002F\u002Fschema.org\",\"@id\":\"https:\u002F\u002Fmedium.com\u002F@yashkhandelwal07\",\"@type\":\"Person\",\"identifier\":\"yashkhandelwal07\",\"name\":\"Yash Khandelwal\",\"url\":\"https:\u002F\u002Fmedium.com\u002F@yashkhandelwal07\"},\"creator\":{\"@context\":\"https:\u002F\u002Fschema.org\",\"@id\":\"https:\u002F\u002Fmedium.com\u002F@yashkhandelwal07\",\"@type\":\"Person\",\"identifier\":\"yashkhandelwal07\",\"name\":\"Yash Khandelwal\",\"url\":\"https:\u002F\u002Fmedium.com\u002F@yashkhandelwal07\"},\"publisher\":{\"@context\":\"https:\u002F\u002Fschema.org\",\"@type\":\"Organization\",\"@id\":\"https:\u002F\u002Fmedium.com\",\"name\":\"Medium\",\"url\":\"https:\u002F\u002Fmedium.com\",\"logo\":{\"@type\":\"ImageObject\",\"width\":500,\"height\":110,\"url\":\"https:\u002F\u002Fmiro.medium.com\u002Fv2\u002Fresize:fit:500\u002F7%2AV1_7XP4snlmqrc_0Njontw.png\"}},\"mainEntityOfPage\":\"https:\u002F\u002Fyashkhandelwal07.medium.com\u002Fdata-science-hackathons-winners-guide-part-1-fecbf3b2c023\",\"isAccessibleForFree\":true}"},"seoDescription":"","shortformType":"SHORTFORM_TYPE_LINK","firstPublishedAt":1701179173208,"viewerEdge":{"__ref":"PostViewerEdge:postId:fecbf3b2c023-viewerId:lo_40f07550e3be"},"seoTitle":"","isSuspended":false,"license":"ALL_RIGHTS_RESERVED","tags":[{"__ref":"Tag:data-science"},{"__ref":"Tag:hackathons"},{"__ref":"Tag:machine-learning"},{"__ref":"Tag:code"},{"__ref":"Tag:data-analysis"}],"isFeaturedInPublishedPublication":false,"isNewsletter":false,"statusForCollection":null,"pendingCollection":null,"detectedLanguage":"en","wordCount":1639,"layerCake":0}}</script><script>window.__MIDDLEWARE_STATE__={"session":{"xsrf":""},"cache":{"cacheStatus":"MISS"}}</script><script src="https://cdn-client.medium.com/lite/static/js/manifest.91087f21.js"></script><script src="https://cdn-client.medium.com/lite/static/js/723.093de8f1.js"></script><script src="https://cdn-client.medium.com/lite/static/js/main.668f262a.js"></script><script src="https://cdn-client.medium.com/lite/static/js/instrumentation.47ae8b31.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/reporting.851fdaca.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/5052.eb638269.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/683.abfef39e.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/6618.4aea0357.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/2050.3c25fb60.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3326.9712e10d.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7566.fa51707d.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7908.908acb8a.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3927.2f9f3eed.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/8640.0d3bced2.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9967.f31ca2af.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9214.a792bbcc.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/1214.9ae8faaf.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/556.d95c90dd.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7381.c53435ab.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9768.a85f5560.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/5522.3fb24bd9.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/792.f17e92fb.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3507.8b27b9e8.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7561.fc7962fc.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/4929.75144692.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/6834.6c66e3cc.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/1887.f9daf0b6.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7979.35c5b2af.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/7975.3f8d607c.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3877.96683729.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/9256.629cdc7e.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/8768.62c3639c.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/144.f38d4759.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3666.6579eeda.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/1069.6236ad3b.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/PostPage.MainContent.e31fff2e.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/2698.9eecb474.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/3974.ee0dd7bf.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/2527.358dc2fb.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/PostResponsesContent.3c0c12ee.chunk.js"></script>
<script src="https://cdn-client.medium.com/lite/static/js/responses.editor.d61aa7c4.chunk.js"></script>
<script id="__LOADABLE_REQUIRED_CHUNKS__" type="application/json">[]</script>
<script id="__LOADABLE_REQUIRED_CHUNKS___ext" type="application/json">{"namedChunks":[]}</script><script>window.main();</script><script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'977755d2ffe3a279',t:'MTc1NjU4ODg1OS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body></html>