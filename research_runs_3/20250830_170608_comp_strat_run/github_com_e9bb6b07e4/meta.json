{"url": "https://github.com/stefan-jansen/machine-learning-for-trading", "kind": "html", "depth": 0, "bytes": 500786, "saved": {"raw": "/home/<USER>/Desktop/Coding/mcgillfiam/research_runs_3/20250830_170608_comp_strat_run/github_com_e9bb6b07e4/raw.bin", "text": "/home/<USER>/Desktop/Coding/mcgillfiam/research_runs_3/20250830_170608_comp_strat_run/github_com_e9bb6b07e4/extracted.txt"}, "timestamp": "2025-08-30T21:13:56.744001Z", "code_blocks_found": 0}