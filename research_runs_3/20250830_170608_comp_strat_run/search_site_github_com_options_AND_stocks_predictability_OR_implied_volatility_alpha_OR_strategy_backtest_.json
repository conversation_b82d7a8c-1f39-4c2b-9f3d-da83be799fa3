[{"title": "OptionsnPython/Option-strategies-backtesting-in-Python", "url": "https://github.com/OptionsnPython/Option-strategies-backtesting-in-Python", "engines": ["google"]}, {"title": "options-strategies", "url": "https://github.com/topics/options-strategies?l=python&o=desc", "engines": ["google"]}, {"title": "ThetaGang is an IBKR bot for collecting money", "url": "https://github.com/brndnmtthws/thetagang", "engines": ["google"]}, {"title": "kurupjayesh/Dispersion-Trading-using-Options", "url": "https://github.com/kurupjayesh/Dispersion-Trading-using-Options", "engines": ["google"]}, {"title": "alpacahq/alpaca-mcp-server", "url": "https://github.com/alpacahq/alpaca-mcp-server", "engines": ["google"]}, {"title": "options-trading · GitHub Topics", "url": "https://github.com/topics/options-trading", "engines": ["google"]}, {"title": "wilsonfreitas/awesome-quant: A curated list of insanely ...", "url": "https://github.com/wilsonfreitas/awesome-quant", "engines": ["google"]}, {"title": "options-pricing · GitHub Topics", "url": "https://github.com/topics/options-pricing?l=python&o=desc&s=forks", "engines": ["google"]}, {"title": "options-trading · GitHub Topics", "url": "https://github.com/topics/options-trading?o=desc&s=stars", "engines": ["google"]}, {"title": "cinar/indicator", "url": "https://github.com/cinar/indicator", "engines": ["google"]}]