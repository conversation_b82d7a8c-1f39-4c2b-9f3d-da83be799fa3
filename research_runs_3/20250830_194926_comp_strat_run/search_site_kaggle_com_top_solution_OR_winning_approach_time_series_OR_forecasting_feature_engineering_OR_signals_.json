[{"title": "Winning solutions of kaggle competitions", "url": "https://www.kaggle.com/code/<PERSON><PERSON><PERSON><PERSON><PERSON>/winning-solutions-of-kaggle-competitions", "engines": ["google"]}, {"title": "5th place solution", "url": "https://www.kaggle.com/competitions/predict-energy-behavior-of-prosumers/writeups/loh-maa-5th-place-solution", "engines": ["google"]}, {"title": "Winning solutions of kaggle competitions", "url": "https://www.kaggle.com/code/rajiao/winning-solutions-of-kaggle-competitions", "engines": ["google"]}, {"title": "Lessons from Our Winning Approach", "url": "https://www.kaggle.com/competitions/phems-hackathon-early-sepsis-prediction/discussion/561775", "engines": ["google"]}, {"title": "Congrats and Solution Sharing", "url": "https://www.kaggle.com/competitions/outbrain-click-prediction/writeups/freshdesk-ml-congrats-and-solution-sharing", "engines": ["google"]}, {"title": "Winning solutions of kaggle competitions 2021", "url": "https://www.kaggle.com/code/mathurinache/winning-solutions-of-kaggle-competitions-2021", "engines": ["google"]}, {"title": "Winning solutions", "url": "https://www.kaggle.com/code/bangdasun/winning-solutions", "engines": ["google"]}, {"title": "BERTOPICs of Top 20 Competitions With Most Users", "url": "https://www.kaggle.com/code/bwandowando/bertopics-of-top-20-competitions-with-most-users", "engines": ["google"]}, {"title": "Solution 1178 Public / 29 Private", "url": "https://www.kaggle.com/competitions/porto-seguro-safe-driver-prediction/writeups/car-accident-which-car-accident-solution-1178-publ", "engines": ["google"]}, {"title": "4th-Place Solution Overview", "url": "https://www.kaggle.com/competitions/favorita-grocery-sales-forecasting/writeups/spp-4th-place-solution-overview", "engines": ["google"]}]