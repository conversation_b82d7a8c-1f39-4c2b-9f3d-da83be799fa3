# ML Trading Strategies

**Source:** https://github.com/AndreasTheodoulou/ML_Trading_Strategies


## Thesis

This project applies machine learning algorithms to predict stock returns based on fundamental features such as Momentum, Value, Quality, and Size. By applying feature engineering techniques like rate of change calculations over different time horizons (1Month, 3Month, 12Month), the model enhances predictive accuracy. The strategy selects top and bottom x% of predictions for long/short positions and constructs a portfolio using either equal weights or classical Portfolio Optimisation methods. Backtesting from 2000 to 2019 on 200 US stocks shows significant outperformance in Sharpe ratio.

## Universe & Rebalancing

**Universe:** 200 US stocks
**Rebalancing:** annual


## Signals

- **Momentum:** Rate of change over different time horizons (1Month, 3Month, 12Month)
- **Value:** Fundamental feature corresponding to stylized factors of returns
- **Quality:** Fundamental feature corresponding to stylized factors of returns
- **Size:** Fundamental feature corresponding to stylized factors of returns
