[{"title": "Winning solutions of kaggle competitions", "url": "https://www.kaggle.com/code/<PERSON><PERSON><PERSON><PERSON><PERSON>/winning-solutions-of-kaggle-competitions", "engines": ["google", "brave"]}, {"title": "1st Place Winning Solution - <PERSON><PERSON> for Gold", "url": "https://www.kaggle.com/competitions/lish-moa/writeups/hungry-for-gold-1st-place-winning-solution-hungry-", "engines": ["google"]}, {"title": "1st Place Winning Solution | Kaggle", "url": "https://www.kaggle.com/code/carnozhao/1st-place-winning-solution", "engines": ["brave"]}, {"title": "Kaggle Winning Solutions Github", "url": "https://www.kaggle.com/getting-started/114661", "engines": ["google"]}, {"title": "Winning solutions of kaggle competitions 2021", "url": "https://www.kaggle.com/code/mathurinache/winning-solutions-of-kaggle-competitions-2021", "engines": ["brave"]}, {"title": "Exercise: Predicting Stock Prices", "url": "https://www.kaggle.com/crawford/exercise-predicting-stock-prices/execution", "engines": ["google"]}, {"title": "Winning Solution: Happiness Prediction | Kaggle", "url": "https://www.kaggle.com/code/mananj<PERSON><PERSON>/winning-solution-happiness-prediction", "engines": ["brave"]}, {"title": "Winning solutions of kaggle competitions", "url": "https://www.kaggle.com/code/<PERSON><PERSON><PERSON><PERSON><PERSON>/winning-solutions-of-kaggle-competitions/notebook?scriptVersionId=4545492", "engines": ["google"]}, {"title": "Winning solutions | Kaggle", "url": "https://www.kaggle.com/code/bangdasun/winning-solutions", "engines": ["brave"]}, {"title": "📊Stock Market Analysis 📈 + Prediction using LSTM", "url": "https://www.kaggle.com/code/faressayah/stock-market-analysis-prediction-using-lstm/output", "engines": ["google"]}, {"title": "Gold Medals - Race | Kaggle", "url": "https://www.kaggle.com/code/hainescity/gold-medals-race", "engines": ["brave"]}, {"title": "Winning solutions of kaggle competitions", "url": "https://www.kaggle.com/code/<PERSON><PERSON><PERSON><PERSON><PERSON>/winning-solutions-of-kaggle-competitions/notebook?scriptVersionId=21991332", "engines": ["google"]}, {"title": "Exercise: Predicting Stock Prices", "url": "https://www.kaggle.com/code/crawford/exercise-predicting-stock-prices/input?scriptVersionId=7422579", "engines": ["google"]}, {"title": "WordCloud of gold medal winning notebook titles", "url": "https://www.kaggle.com/code/carlmcbrideellis/wordcloud-of-gold-medal-winning-notebook-titles", "engines": ["google"]}, {"title": "Stock Market Data Analysis", "url": "https://www.kaggle.com/code/thesnak/stock-market-data-analysis/output", "engines": ["google"]}]