[{"title": "1st Place Winning Solution - <PERSON><PERSON> for Gold", "url": "https://www.kaggle.com/competitions/lish-moa/writeups/hungry-for-gold-1st-place-winning-solution-hungry-", "engines": ["google"]}, {"title": "Winning solutions of kaggle competitions", "url": "https://www.kaggle.com/code/<PERSON><PERSON><PERSON><PERSON><PERSON>/winning-solutions-of-kaggle-competitions", "engines": ["google"]}, {"title": "1st place solution [UPDATED]", "url": "https://www.kaggle.com/competitions/leash-BELKA/writeups/victor-shl<PERSON><PERSON>-1st-place-solution-updated", "engines": ["google"]}, {"title": "WordCloud of gold medal winning notebook titles", "url": "https://www.kaggle.com/code/carlmcbrideellis/wordcloud-of-gold-medal-winning-notebook-titles", "engines": ["google"]}, {"title": "1st place solution", "url": "https://www.kaggle.com/competitions/sberbank-russian-housing-market/writeups/alijs-evgeny-1st-place-solution", "engines": ["google"]}, {"title": "Kaggle Winning Solutions Github", "url": "https://www.kaggle.com/getting-started/114661", "engines": ["google"]}, {"title": "📊Stock Market Analysis 📈 + Prediction using LSTM", "url": "https://www.kaggle.com/code/faressayah/stock-market-analysis-prediction-using-lstm/notebook", "engines": ["google"]}, {"title": "1st Place Solution - Brief Overview", "url": "https://www.kaggle.com/competitions/ncaaw-march-mania-2021/writeups/bojan-tunguz-1st-place-solution-brief-overview", "engines": ["google"]}, {"title": "1st place solution", "url": "https://www.kaggle.com/competitions/playground-series-s4e11/writeups/mahdi-ravaghi-1st-place-solution", "engines": ["google"]}, {"title": "Stock Market Data Analysis", "url": "https://www.kaggle.com/code/thesnak/stock-market-data-analysis/output", "engines": ["google"]}]