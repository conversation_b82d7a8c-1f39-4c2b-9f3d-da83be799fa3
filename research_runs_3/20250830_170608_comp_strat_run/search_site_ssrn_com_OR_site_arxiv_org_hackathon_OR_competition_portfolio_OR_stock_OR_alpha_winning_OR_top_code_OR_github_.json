[{"title": "FinRL Contests: Benchmarking Data-driven Financial ...", "url": "https://arxiv.org/html/2504.02281v3", "engines": ["google"]}, {"title": "Competition-Level Code Generation with AlphaCode", "url": "https://arxiv.org/abs/2203.07814", "engines": ["google"]}, {"title": "M6 Investment Challenge: The Role of Luck and Strategic ...", "url": "https://arxiv.org/html/2412.04490v1", "engines": ["google"]}, {"title": "FinRL Contests: Benchmarking Data-driven Financial ...", "url": "https://arxiv.org/html/2504.02281v4", "engines": ["google"]}, {"title": "A Note on the M6 Forecasting Competition: Rank ...", "url": "https://papers.ssrn.com/sol3/Delivery.cfm/SSRN_ID4527154_code5001647.pdf?abstractid=4527154", "engines": ["google"]}, {"title": "A portfolio-based analysis method for competition results", "url": "https://arxiv.org/pdf/2205.15414", "engines": ["google"]}, {"title": "AlphaAgent: LLM-Driven Alpha Mining with Regularized ...", "url": "https://arxiv.org/html/2502.16789v2", "engines": ["google"]}, {"title": "GitTaskBench: A Benchmark for Code Agents Solving Real ...", "url": "https://arxiv.org/html/2508.18993v1", "engines": ["google"]}, {"title": "Stockformer: A Price-Volume Factor Stock Selection Model ...", "url": "https://arxiv.org/html/2401.06139v2", "engines": ["google"]}, {"title": "Benchmarking M6 Competitors: An Analysis of Financial ...", "url": "https://arxiv.org/abs/2406.19105", "engines": ["google"]}]