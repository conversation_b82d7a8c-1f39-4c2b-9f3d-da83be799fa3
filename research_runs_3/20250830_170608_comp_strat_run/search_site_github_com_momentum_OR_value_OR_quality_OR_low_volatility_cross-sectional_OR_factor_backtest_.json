[{"title": "Cross Sectional Momentum", "url": "https://gist.github.com/FilippoGuerrieri26/3205842fa54d1709d86c49cb6397c69d", "engines": ["google"]}, {"title": "QuantFinance/factor-investing.md at master", "url": "https://github.com/XinheLIU/QuantFinance/blob/master/factor-investing.md", "engines": ["google"]}, {"title": "replacementAI/A-Backtest-A-Day", "url": "https://github.com/replacementAI/A-Backtest-A-Day", "engines": ["google"]}, {"title": "backtest_tutorial/Vectorized_Backtest_Tutorial.ipynb at main", "url": "https://github.com/hudson-and-thames/backtest_tutorial/blob/main/Vectorized_Backtest_Tutorial.ipynb", "engines": ["google"]}, {"title": "Algorithmic Trading project that examines the Fama-French ...", "url": "https://github.com/fischlerben/Algorithmic-Trading-Project", "engines": ["google"]}, {"title": "udacity/artificial-intelligence-for-trading - Backtesting", "url": "https://github.com/udacity/artificial-intelligence-for-trading/blob/master/project/project_8/project_8_starter.ipynb", "engines": ["google"]}, {"title": "A Python project that simulates a portfolio backtest using ...", "url": "https://github.com/sapk806/cross_sectional_factor_backtest_project", "engines": ["google"]}, {"title": "pysystemtrade/docs/backtesting.md at develop", "url": "https://github.com/robcarver17/pysystemtrade/blob/develop/docs/backtesting.md", "engines": ["google"]}, {"title": "paperswithbacktest/awesome-systematic-trading", "url": "https://github.com/paperswithbacktest/awesome-systematic-trading", "engines": ["google"]}, {"title": "futuresio-webinars/02-backtesting-and-optimization.ipynb ...", "url": "https://github.com/ranaroussi/futuresio-webinars/blob/master/02-backtesting-and-optimization.ipynb", "engines": ["google"]}]