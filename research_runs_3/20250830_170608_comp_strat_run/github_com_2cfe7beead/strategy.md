# Backtesting Algos

**Source:** https://github.com/<PERSON><PERSON>love/Backtesting-Algos


## Thesis

The analysis evaluates common algo trading signals and their effectiveness in delivering profitable signals, comparing risks and returns of each strategy. It also explores the efficacy of combining these strategies using machine learning techniques such as deep learning models like LSTM to optimize signal performance.

## Universe & Rebalancing

**Universe:** ETFs (all subsets of Standard & Poor's 'SPY' index) or Cryptocurrency
**Rebalancing:** annual


## Signals

- **SMA:** Simple Moving Average with a lookback window
- **EMA:** Exponentially Weighted Moving Average with a lookback window
- **BBD:** Bollinger Bands, calculated over a given lookback period
- **RSI:** Relative Strength Index, used to measure the speed and change of price movements
