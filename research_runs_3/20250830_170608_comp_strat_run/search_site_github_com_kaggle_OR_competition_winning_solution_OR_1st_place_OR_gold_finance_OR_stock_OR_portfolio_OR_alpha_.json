[{"title": "hansen7/Kaggle_Competition: Summary of the Kaggle ...", "url": "https://github.com/hansen7/Kaggle_Competition", "engines": ["google"]}, {"title": "the-black-knight-01/Data-Science-Competitions", "url": "https://github.com/the-black-knight-01/Data-Science-Competitions", "engines": ["google"]}, {"title": "<PERSON><PERSON><PERSON>-<PERSON>/My-kaggle-notebooks", "url": "https://github.com/<PERSON>-<PERSON>-<PERSON>/My-kaggle-notebooks", "engines": ["google"]}, {"title": "goto_conversion - Powered over 10 Gold Medals and 100 ...", "url": "https://github.com/gotoConversion/goto_conversion", "engines": ["google"]}, {"title": "<PERSON><PERSON>", "url": "https://github.com/erlemar", "engines": ["google"]}, {"title": "<PERSON><PERSON> LuiDataScience", "url": "https://github.com/LuiDataScience", "engines": ["google"]}, {"title": "dimsariyanto/Gold-Price-Prediction", "url": "https://github.com/dimsariyanto/Gold-Price-Prediction", "engines": ["google"]}, {"title": "scaomath/kaggle-jane-street: Machine learning models to ...", "url": "https://github.com/scaomath/kaggle-jane-street", "engines": ["google"]}, {"title": "kaggle-elo-recommendation/lit_review/README.md at ...", "url": "https://github.com/freestander/kaggle-elo-recommendation/blob/master/lit_review/README.md", "engines": ["google"]}, {"title": "jvckdough/Game-Day-Analytics-Competition-2025-Winning- ...", "url": "https://github.com/jvckdough/Game-Day-Analytics-Competition-2025-Winning-Portfolio", "engines": ["google"]}]