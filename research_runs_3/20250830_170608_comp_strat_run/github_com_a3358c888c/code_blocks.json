["import\n \ntr4der\n\n\n\n# Create instance of Tr4der\n\n\ntrader\n \n=\n \ntr4der\n.\nTr4der\n()\n\n\n# Set the API key\n\n\ntrader\n.\nset_api_key\n(\n\"YOUR_OPENAI_API_KEY\"\n)", "import\n \ntr4der\n\n\n\n# Define the query\n\n\nquery\n \n=\n \n\"I want to use mean reversion with bollinger bands to trade GOOGL for the past 10 years\"\n\n\n\n# Pass the query to the Tr4der class\n\n\ntrader\n.\nquery\n(\nquery\n)\n\n\n# Metric Results\n\n\n==\n>\n \n\nStart\n: \n2013\n-\n10\n-\n01\n \n00\n:\n00\n:\n00\n\n\nEnd\n: \n2023\n-\n09\n-\n29\n \n00\n:\n00\n:\n00\n\n\nDuration\n: \n3650\n \ndays\n \n00\n:\n00\n:\n00\n\n\nExposure\n \nTime\n [\n%\n]: \n98.41\n\n\nEquity\n \nInitial\n [$]: \n10000\n\n\nEquity\n \nFinal\n [$]: \n11969.02\n\n\nEquity\n \nPeak\n [$]: \n15128.67\n\n\nReturn\n [\n%\n]: \n19.69\n\n\nReturn\n (\nAnn\n.) [\n%\n]: \n1.82\n\n\nVolatility\n (\nAnn\n.) [\n%\n]: \n27.76\n\n\nSharpe\n \nRatio\n: \n0.07\n\n\nSortino\n \nRatio\n: \n0.07\n\n\nMax\n. \nDrawdown\n [\n%\n]: \n-\n45.95\n\n\nCalmar\n \nRatio\n: \n0.04\n\n\nAvg\n. \nDrawdown\n [\n%\n]: \n-\n19.45\n\n\nMax\n. \nDrawdown\n \nDuration\n: \n1428\n \ndays\n \n00\n:\n00\n:\n00\n\n\nAvg\n. \nDrawdown\n \nDuration\n: \n196\n \ndays\n \n04\n:\n00\n:\n00\n\n\nTrades\n: \n52\n\n\nBest\n \nDay\n [\n%\n]: \n9.62\n\n\nWorst\n \nDay\n [\n%\n]: \n-\n16.26\n\n\nAvg\n. \nTrade\n [\n%\n]: \n0.02\n\n\nMax\n. \nTrade\n \nDuration\n: \n3650\n\n\nstrategy\n: \nbollinger_bands", "# If you want to use a pre-existing strategy and modify it, save your strategy data\n\n\nstrategy_data\n \n=\n \ntrader\n.\nstrategy_data\n\n\n\nstrategy_data\n.\nhead\n()\n\n\n==\n>\n\n\n\n|\n \nDate\n       \n|\n \nGOOGL\n     \n|\n \nGOOGL_return\n \n|\n \nGOOGL_ma\n   \n|\n \nGOOGL_upper_band\n \n|\n \nGOOGL_lower_band\n \n|\n \nGOOGL_position\n \n|\n\n\n|\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n|\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n|\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n|\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n|\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n|\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n|\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n|\n\n\n|\n \n2013\n-\n12\n-\n05\n \n|\n \n26.459961\n \n|\n \n-\n0.000794\n    \n|\n \n25.954617\n  \n|\n \n26.861214\n        \n|\n \n25.048020\n        \n|\n \n-\n1.0\n           \n|\n\n\n|\n \n2013\n-\n12\n-\n06\n \n|\n \n26.773523\n \n|\n \n0.011850\n     \n|\n \n26.032095\n  \n|\n \n26.940630\n        \n|\n \n25.123559\n        \n|\n \n-\n1.0\n           \n|\n\n\n|\n \n2013\n-\n12\n-\n09\n \n|\n \n26.980480\n \n|\n \n0.007730\n     \n|\n \n26.109810\n  \n|\n \n27.064833\n        \n|\n \n25.154786\n        \n|\n \n-\n1.0\n           \n|\n\n\n\n#Call strategy with modified parameters\n\n\ntrader\n.\nMachineLearningStrategies\n.\nnearest_neighbors_regression\n(\nstrategy_data\n, \ntechnical_indicators\n=\n[\n'SMA_20'\n, \n'SMA_50'\n])"]