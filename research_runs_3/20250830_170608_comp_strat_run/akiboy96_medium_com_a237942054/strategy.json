{"title": "Community Detection Network Analysis for Portfolio Construction", "source_url": "https://akiboy96.medium.com/community-detection-network-analysis-of-the-stock-market-in-python-r-part-3-ca4e0fbb8ca9", "thesis": "The thesis is to construct a portfolio that outperforms the S&P 500 by leveraging community detection and centrality measures in network analysis. The approach involves calculating four types of centralities (degree, betweenness, closeness, and eigenvector) for stocks within the S&P 500 based on their correlation networks. These centralities are combined to form a single metric that identifies influential stocks. By filtering these stocks by sector and centrality value, we aim to create a diversified portfolio with higher expected returns.", "universe": "S&P 500", "rebalancing": "monthly", "signals": [{"name": "Degree Centrality", "definition": "The number of links held by each node in the network."}, {"name": "Betweenness Centrality", "definition": "The number of times a node lies on the shortest path between other nodes."}, {"name": "Closeness Centrality", "definition": "Scores each node based on their 'closeness' to all other nodes in the network."}, {"name": "Eigen Centrality", "definition": "Measures a node’s influence based on the number of links it has to other nodes in the network."}]}