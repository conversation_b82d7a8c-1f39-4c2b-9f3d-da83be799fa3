[{"title": "Neuroevolution Neural Architecture Search for Evolving RNNs in Stock Return Prediction and Portfolio Trading", "url": "http://arxiv.org/abs/2410.17212v1", "engines": ["arxiv"]}, {"title": "Dependency Network-Based Portfolio Design with Forecasting and VaR Constraints", "url": "http://arxiv.org/abs/2507.20039v1", "engines": ["arxiv"]}, {"title": "Optimum Risk Portfolio and Eigen Portfolio: A Comparative Analysis Using Selected Stocks from the Indian Stock Market", "url": "http://arxiv.org/abs/2107.11371v1", "engines": ["arxiv"]}, {"title": "Portfolio Optimization: A Comparative Study", "url": "http://arxiv.org/abs/2307.05048v1", "engines": ["arxiv"]}, {"title": "DSPO: An End-to-End Framework for Direct Sorted Portfolio Construction", "url": "http://arxiv.org/abs/2405.15833v1", "engines": ["arxiv"]}, {"title": "Performance Evaluation of Equal-Weight Portfolio and Optimum Risk Portfolio on Indian Stocks", "url": "http://arxiv.org/abs/2309.13696v1", "engines": ["arxiv"]}, {"title": "Designing an Optimal Portfolio for Iran's Stock Market with Genetic Algorithm using Neural Network Prediction of Risk and Return Stocks", "url": "http://arxiv.org/abs/1903.06632v1", "engines": ["arxiv"]}, {"title": "Investment Portfolio Optimization Based on Modern Portfolio Theory and Deep Learning Models", "url": "http://arxiv.org/abs/2508.14999v1", "engines": ["arxiv"]}, {"title": "Forecasting stock return distributions around the globe with quantile neural networks", "url": "http://arxiv.org/abs/2408.07497v2", "engines": ["arxiv"]}, {"title": "Data-driven stock forecasting models based on neural ...", "url": "https://www.sciencedirect.com/science/article/pii/S1566253524003944", "engines": ["google"]}, {"title": "Stock return prediction with multiple measures using neural ...", "url": "https://jfin-swufe.springeropen.com/articles/10.1186/s40854-023-00608-w", "engines": ["google"]}, {"title": "How Neural Networks Can Enhance Stock Market ...", "url": "https://medium.com/@zhonghong9998/how-neural-networks-can-enhance-stock-market-predictions-10fe42033a80", "engines": ["google"]}, {"title": "Forecasting Stock Returns Using Machine Learning", "url": "https://research.cbs.dk/en/studentProjects/forecasting-stock-returns-using-machine-learning-a-comparative-an", "engines": ["google"]}, {"title": "Portfolio Optimization with Prediction-Based Return Using ...", "url": "https://link.springer.com/article/10.1007/s10614-024-10604-6", "engines": ["google"]}, {"title": "Predictability of stock returns using neural networks", "url": "https://www.sciencedirect.com/science/article/abs/pii/S0957417422022217", "engines": ["google"]}, {"title": "Advanced Stock Market Prediction Using Long Short-Term ...", "url": "https://arxiv.org/html/2505.05325v1", "engines": ["google"]}, {"title": "Neural Network Model for Efficient portfolio Management ...", "url": "https://ieeexplore.ieee.org/document/9121049/", "engines": ["google"]}, {"title": "Full article: Prediction of stock return by LSTM neural network", "url": "https://www.tandfonline.com/doi/full/10.1080/08839514.2022.2151159", "engines": ["google"]}, {"title": "Threshold-Based Portfolio: The Role of the Threshold and Its Applications", "url": "http://arxiv.org/abs/1709.09822v2", "engines": ["arxiv"]}, {"title": "(PDF) Forecasting returns on a stock market using Artificial ...", "url": "https://www.researchgate.net/publication/273291653_Forecasting_returns_on_a_stock_market_using_Artificial_Neural_Networks_and_GARCH_family_models_Evidence_of_stock_market_SP_500", "engines": ["google"]}]