

<!DOCTYPE html>
<html lang="en">

<head>
  <title>Copy of Millennium Hiring Test | Kaggle</title>
  <meta charset="utf-8" />
    <meta name="robots" content="index, follow" />
  <meta name="description" content="Millennium&#x27;s Hiring Test invites candidates to showcase their skills and innovation in quantitative finance" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, minimum-scale=1.0">
  <meta name="theme-color" content="#008ABC" />
  <script nonce="CNj&#x2B;&#x2B;T/vrWXlqWie6R/cEw==" type="text/javascript">
    window["pageRequestStartTime"] = *************;
    window["pageRequestEndTime"] = *************;
    window["initialPageLoadStartTime"] = new Date().getTime();
  </script>
  <script nonce="CNj&#x2B;&#x2B;T/vrWXlqWie6R/cEw==" id="gsi-client" src="https://accounts.google.com/gsi/client" async defer></script>
  <script nonce="CNj&#x2B;&#x2B;T/vrWXlqWie6R/cEw==">window.KAGGLE_JUPYTERLAB_PATH = "/static/assets/jupyterlab-v4/jupyterlab-index-da3e27246649f6384bc4.html";</script>
  <link rel="preconnect" href="https://www.google-analytics.com" crossorigin="anonymous" /><link rel="preconnect" href="https://stats.g.doubleclick.net" /><link rel="preconnect" href="https://storage.googleapis.com" /><link rel="preconnect" href="https://apis.google.com" />
    <link href="/static/images/favicon.ico" rel="shortcut icon" type="image/x-icon" id="dynamic-favicon" />
  <link rel="manifest" href="/static/json/manifest.json" crossorigin="use-credentials">


  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

  <link href="https://fonts.googleapis.com/css?family=Inter:400,400i,500,500i,600,600i,700,700i&display=swap"
    rel="preload" as="style" />
  <link href="https://fonts.googleapis.com/css2?family=Google+Symbols:FILL@0..1&display=block"
    rel="preload" as="style" />
  <link href="https://fonts.googleapis.com/css?family=Inter:400,400i,500,500i,600,600i,700,700i&display=swap"
    rel="stylesheet" media="print" id="async-google-font-1" />
  <link href="https://fonts.googleapis.com/css2?family=Google+Symbols:FILL@0..1&display=block"
    rel="stylesheet" media="print" id="async-google-font-2" />
  <script nonce="CNj&#x2B;&#x2B;T/vrWXlqWie6R/cEw==" type="text/javascript">
    const styleSheetIds = ["async-google-font-1", "async-google-font-2"];
    styleSheetIds.forEach(function (id) {
      document.getElementById(id).addEventListener("load", function() {
        this.media = "all";
      });
    });
  </script>


    <link rel="stylesheet" type="text/css" href="/static/assets/app.css?v=2d4e7ec4eb689d926191" />

  
    
 
      <script nonce="CNj&#x2B;&#x2B;T/vrWXlqWie6R/cEw==">
        try{(function(a,s,y,n,c,h,i,d,e){d=s.createElement("style");
        d.appendChild(s.createTextNode(""));s.head.appendChild(d);d=d.sheet;
        y=y.map(x => d.insertRule(x + "{ opacity: 0 !important }"));
        h.start=1*new Date;h.end=i=function(){y.forEach(x => x<d.cssRules.length ? d.deleteRule(x) : {})};
        (a[n]=a[n]||[]).hide=h;setTimeout(function(){i();h.end=null},c);h.timeout=c;
        })(window,document,['.site-header-react__nav'],'dataLayer',2000,{'GTM-52LNT9S':true});}catch(ex){}
    </script>
    <script nonce="CNj&#x2B;&#x2B;T/vrWXlqWie6R/cEw==">
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', 'G-T7QHS60L4Q', {
            'optimize_id': 'GTM-52LNT9S',
            'displayFeaturesTask': null,
            'send_page_view': false,
            'content_group1': 'Competitions'
        });
    </script>
    <script nonce="CNj&#x2B;&#x2B;T/vrWXlqWie6R/cEw==" async src="https://www.googletagmanager.com/gtag/js?id=G-T7QHS60L4Q"></script>

  
    
    <meta property="og:title" content="Copy of Millennium Hiring Test" />
    <meta property="og:description" content="Millennium&#x27;s Hiring Test invites candidates to showcase their skills and innovation in quantitative finance" />
    <meta property="og:type" content="website"  />
    <meta property="og:url" content="https://kaggle.com/copy-of-millennium-hiring-test" />
    <meta property="og:image" content="https://kaggle.com/competitions/72580/images/header" />

    <meta property="twitter:title" content="Copy of Millennium Hiring Test" />
    <meta property="twitter:card" content="summary" />
    <meta property="twitter:site" content="@kaggle" />
    <meta property="twitter:image" content="https://kaggle.com/competitions/72580/images/header" />


  <meta name="twitter:site" content="@Kaggle" /> 
  
    

  
    

  
    


    <script nonce="CNj&#x2B;&#x2B;T/vrWXlqWie6R/cEw==">window['useKaggleAnalytics'] = true;</script>

  <script id="gapi-target" nonce="CNj&#x2B;&#x2B;T/vrWXlqWie6R/cEw==" src="https://apis.google.com/js/api.js" defer
    async></script>
  <script nonce="CNj++T/vrWXlqWie6R/cEw==" src="/static/assets/runtime.js?v=97eb874934c17ed518f0"></script>
  <script nonce="CNj++T/vrWXlqWie6R/cEw==" src="/static/assets/vendor.js?v=93540707f793d66d045a"></script>
  <script nonce="CNj++T/vrWXlqWie6R/cEw==" src="/static/assets/app.js?v=02cd0871599dce515974"></script>
    <script nonce="CNj&#x2B;&#x2B;T/vrWXlqWie6R/cEw==" type="text/javascript">
      window.kaggleStackdriverConfig = {
        key: 'AIzaSyA4eNqUdRRskJsCZWVz-qL655Xa5JEMreE',
        projectId: 'kaggle-161607',
        service: 'web-fe',
        version: 'ci',
        userId: '0'
      }
    </script>
</head>
<body>
  <div id="root">
    










  </div>
</body>
</html>
