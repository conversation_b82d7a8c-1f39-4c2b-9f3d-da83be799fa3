Tested on Python 3.10 and later
Tr4der is an algorithmic trading library for quantitative strategy ideation. Using natural language, you can easily generate and backtest trading strategies for specific stocks and ETFs. Tr4der will parse the user's input, retrieve the data, execute code to filter the data, then apply the trading strategy that the user specifies. All of the data is persisted on disk and can be accessed and modified at will.
- Install the package
$ pip install tr4der
- Set your OpenAI API key
import tr4der
# Create instance of Tr4der
trader = tr4der.Tr4der()
# Set the API key
trader.set_api_key("YOUR_OPENAI_API_KEY")
Examples of valid prompts:
- "I want to pair trade UBER and LYFT over the past 5 years"
- "Go long on the top 5 PE stocks in the Nasdaq"
- "Short the bottom 3 PE stocks in the S&P 500"
- "I want to use a K-nearest neighbors model to trade LYFT stock since their IPO to now"
- "I want to use mean reversion with bollinger bands to trade GOOGL for the past 10 years"
import tr4der
# Define the query
query = "I want to use mean reversion with bollinger bands to trade GOOGL for the past 10 years"
# Pass the query to the Tr4der class
trader.query(query)
# Metric Results
==>
Start: 2013-10-01 00:00:00
End: 2023-09-29 00:00:00
Duration: 3650 days 00:00:00
Exposure Time [%]: 98.41
Equity Initial [$]: 10000
Equity Final [$]: 11969.02
Equity Peak [$]: 15128.67
Return [%]: 19.69
Return (Ann.) [%]: 1.82
Volatility (Ann.) [%]: 27.76
Sharpe Ratio: 0.07
Sortino Ratio: 0.07
Max. Drawdown [%]: -45.95
Calmar Ratio: 0.04
Avg. Drawdown [%]: -19.45
Max. Drawdown Duration: 1428 days 00:00:00
Avg. Drawdown Duration: 196 days 04:00:00
Trades: 52
Best Day [%]: 9.62
Worst Day [%]: -16.26
Avg. Trade [%]: 0.02
Max. Trade Duration: 3650
strategy: bollinger_bands
A plot of the strategy will appear in your console.