Get ready to up your game in data science hackathons! In this blog, I’ll be sharing the winning solution approaches & best practices in Data Science & Machine Learning. It’s a practical guide to help you navigate and triumph in data hackathons, distilled from the lessons learned through my own National victories in past hackathons. Let’s dive into the essentials that can boost your chances of emerging victorious in any data science challenge.
Where to participate?
The platforms, such as Kaggle, DataHack, MachineHack, Unstop, etc. offer a chance to apply your skills in Python, Data Analytics, Machine Learning, etc in real-world challenges. Participating in these platforms is not merely about clinching the top spot; it’s a journey of hands-on learning and community engagement. These hackathons provide an invaluable chance to test your capabilities, learn from the diverse approaches of fellow participants, and stay attuned to the ever-evolving landscape of data science. Consider it your ticket to continuous improvement, community collaboration, and a noteworthy addition to your professional portfolio!
Problem Statements
Let’s now see a glimpse of intriguing problem statements you’ll encounter -
1. Supervised/ Unsupervised Machine Learning (ML):
Example: Predicting the sales of a mega mart and making recommendations for sustainable sales.
2. Time Series Forecasting:
Example: Forecasting PM2.5 concentrations for the first 3 days of 2023 for 34 Indian Cities.
3. Deep Learning:
Example: Estimating the intensity of cyclones from satellite imagery using CNN.
How the Game Works: Behind the Scenes
In hackathons, participants submit the predictions from their model for evaluation. A private leaderboard is used to track participants’ performance, but the scores are hidden until the end. Winners are determined based on the predictive accuracy of their models through various metrics depending on the type of predicting task. The target is to develop a ML model that is perfect fit on the training data and it doesn’t overfit, giving the best metric value on the leaderboard.
Here’s how it works: you build a model to predict things, like sales or pollution. You submit your predictions, but you don’t know how well you did until the end. There’s a special scoreboard that keeps track, but it keeps the scores a secret until it’s over. The winners are the ones whose predictions were super close to what really happened.
Lets now look at the state-of-the-art techniques and best ML practices to top the Private leaderboard!
Supervised/ Unsupervised Machine Learning (ML)
Let’s take the problem statement mentioned before into account — “Predicting the sales of a mega mart and making recommendations for sustainable sales” and see how my winning solution approach & techniques were.
1. Understanding the Data (Link to the dataset, Hackathon Link) -
The challenge revolves around predicting the sales of a mega mart. In this regression task, the training data comprises 87,864 observations with 12 features, including 7 categorical and 5 continuous ones. The test data consists of 37,656 observations, and the target feature is the Sales of items in the mega mart.
2. Data Preparation
The journey to a successful model kicks off with meticulous data preparation. This involves transforming raw data into an informative dataset, laying the foundation for a machine learning model that not only comprehends the intricacies of the training data but also excels in making accurate predictions on real-world data.
Let’s, look at the findings in the data and the preparation methods that worked best on it.
Outlier Detection: Inter Quartile Range Method
Applying the Interquartile Range (IQR) Method to identify and handle outliers ensures a cleaner dataset. The code snippet demonstrates how to cap extreme values within the acceptable range.
# Assuming df is the DataFrame
Q1 = df[‘Sales’].quantile(0.25)
Q3 = df[‘Sales’].quantile(0.75)
IQR = Q3 — Q1
lower_bound = Q1–1.5 * IQR
upper_bound = Q3 + 1.5 * IQR
df[‘Sales’] = df[‘Sales’].apply(lambda x: upper_bound if x > upper_bound else (lower_bound if x < lower_bound else x))
Null Value Imputation: No null values found!
A check for null values is conducted, and in this case, none are found, simplifying the data preparation process.
Categorical Encoding: One Hot Encoding (When categorical variables have no inherent order or ranking), Ordinal Encoding (When categorical variables have a clear order or rank).
For example, in this dataset — Outlet_ID, Item_Type, Outlet_Year, Outlet_Type have no order while Outlet_Size, Outlet_location_type, Item_FC have a visible order.
# One Hot Encoding
df_encoded = pd.get_dummies(df, columns=[‘categorical_feature’], drop_first=True) # Ordinal Encoding
ordinal_mapping = {‘low’: 0, ‘medium’: 1, ‘high’: 2}
df[‘ordinal_feature’] = df[‘ordinal_feature’].map(ordinal_mapping)
Feature Engineering: Creating meaningful features that enhance a machine learning model’s performance. For example in this data — Years from present (2023 — Outlet_year)
# Assuming df is your DataFrame
df[‘new_feature’] = df[‘feature1’] * df[‘feature2’]
Feature Selection:
Permutation Feature Importance: is a technique that measures the impact of each feature on a model’s performance by randomly shuffling the values of a single feature and observing the effect on model accuracy.
from sklearn.inspection import permutation_importance
# Assuming model is your trained machine learning model
perm_importance = permutation_importance(model, X_test, y_test, n_repeats=10, random_state=42)
feature_importance = perm_importance.importances_mean
XGBoost Feature Importance: XGBoost, a powerful gradient boosting algorithm, provides a built-in method for feature importance. It calculates how each feature contributes to reducing the impurity during tree construction.
import xgboost as xgb # Assuming X_train, y_train are your training data model = xgb.XGBClassifier() model.fit(X_train, y_train) # Accessing feature importance scores feature_importance = model.feature_importances_#import csv12345678makefile
Both techniques offer insights into feature importance, helping you identify key variables and refine your feature set for improved model performance.
3. Machine Learning — Modelling
The modelling begins with splitting the training data into training and validation. There are major two techniques for the same — 1. K Fold Cross Validation 2. Train-test-split
For this dataset, train-test-split is itself efficient because of abundant data, prioritising simplicity and faster computation.
from sklearn.model_selection import train_test_split
# Assuming X is your feature matrix and y is your target variable
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42) #import csv12345python
Modelling
Efficient regression model development begins with a baseline model, often a simple linear regression, serving as a performance benchmark. Evaluate its results using appropriate metrics. Explore feature relationships and engineer informative features. Even try to alter the data preparation with suitable methods. Experiment with various models, fine-tune hyperparameters, and consider ensemble methods for improved robustness.
This is the order in which I begin my modelling experimentation.
1. Linear Regression
2. Lasso & Ridge Regression (Regularization, will prevent over fitting)
3. Decision Tree (Will give better metric values on training data through high predictive performance)
4. Random Forest (Will solve the overfitting issue of decision trees)
5. XGBoost, AdaBoost, LightGBM, CatBoost (Boosting models sequentially build a robust learner by focusing on correcting errors made by weak models, effectively reducing bias, lowering variance, and excelling in capturing intricate patterns)
Expect the best performance from the boosting models. But where does the crux of winning solution lies then?
There are few more, less common techniques — Stacking Ensemble & Weighted Average Ensemble. Stacking, with its meta-model learning from varied base model predictions, often outperforms individual models. Average ensemble methods, on the other hand, leverage collective wisdom to create a more balanced and robust prediction by mitigating biases or errors present in individual models. Both approaches contribute to enhanced model performance through effective combination and adaptation to different aspects of the underlying data.
Both of these models had the best performance on this data. These models topped the leaderboard!
#Code for Stacking Ensembling
from sklearn.ensemble import StackingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.tree import DecisionTreeRegressor
# Assuming base models are your individual regression models
base_models = [(‘linear’, LinearRegression()), (‘rf’, RandomForestRegressor()), (‘tree’, DecisionTreeRegressor())]
stacking_model = StackingRegressor(estimators=base_models, final_estimator=LinearRegression())
stacking_model.fit(X_train, y_train)
#Code for Weighted Average Ensemble
# Assuming model1, model2, model3 are your regression models
weights = [0.4, 0.3, 0.3]
# Adjust weights based on model performance
predictions_model1 = model1.predict(X_test)
predictions_model2 = model2.predict(X_test)
predictions_model3 = model3.predict(X_test)
weighted_average_predictions = (weights[0] * predictions_model1 + weights[1] * predictions_model2 + weights[2] * predictions_model3) / sum(weights)
End Note
Creating an efficient machine learning (ML) model involves foundational steps. Starting with a comprehensive understanding of the data, addressing missing values, and outliers with patience. Thoughtful feature engineering enhances the model’s pattern recognition. Systematic hyperparameter tuning fine-tunes performance, and careful model selection aligns with specific tasks. Ensemble methods, cross-validation, and regularization contribute to robustness. Incorporate machine learning explainability for transparency in model decisions, crucial for business understanding. Lastly, continuous iteration based on evaluation insights ensures ongoing enhancement of predictive capabilities, aligning the model with both technical and business objectives of problem statement of the hackathon.
Remember, to win a hackathon, an efficient ML model & your leaderboard standing may not be enough. You might have to also deliver an outstanding presentation on your ML solution in order to win 🏆🏅
Here is the link to my winning solution for this problem statement which was a part of Analytics Olympiad 2021 : Github Link(also, have a look on how a presentation for your solution should look like)
Here are a few helpful resource links that might be useful along this blog:
- Stacking Ensemble in Depth with code -https://www.analyticsvidhya.com/blog/2021/08/ensemble-stacking-for-machine-learning-and-deep-learning/
- Lifecycle of a Data Science Project -https://www.analyticsvidhya.com/blog/2021/10/introduction-to-the-lifecycle-of-data-science-project/#h2_9
Time Series Forecasting
This is another challenging problem statement in Data Science hackthons.
Let’s continue with Time Series Forecasting in the next blog titled — “Data Science Hackathons: A Practical Guide (Part 2)”. Happy Reading! 😊💻
About the Author
Yash Khandelwal is a final year student pursuing Mathematics & Computing at Birla Institute of Technology, Mesra. He was a National Winner of EXL EQ 2023, National Finalist of Smart India Hackathon 2022 and a National Runner up of Analytics Olympiad 2021.
Connect with him on LinkedIn — https://www.linkedin.com/in/yash-khandelwal-a40484bb/