{"title": "ML Trading Strategies", "source_url": "https://github.com/AndreasTheodoulou/ML_Trading_Strategies", "thesis": "This project applies machine learning algorithms to predict stock returns based on fundamental features such as Momentum, Value, Quality, and Size. By applying feature engineering techniques like rate of change calculations over different time horizons (1Month, 3Month, 12Month), the model enhances predictive accuracy. The strategy selects top and bottom x% of predictions for long/short positions and constructs a portfolio using either equal weights or classical Portfolio Optimisation methods. Backtesting from 2000 to 2019 on 200 US stocks shows significant outperformance in Sharpe ratio.", "universe": "200 US stocks", "rebalancing": "annual", "signals": [{"name": "Momentum", "definition": "Rate of change over different time horizons (1Month, 3Month, 12Month)"}, {"name": "Value", "definition": "Fundamental feature corresponding to stylized factors of returns"}, {"name": "Quality", "definition": "Fundamental feature corresponding to stylized factors of returns"}, {"name": "Size", "definition": "Fundamental feature corresponding to stylized factors of returns"}]}