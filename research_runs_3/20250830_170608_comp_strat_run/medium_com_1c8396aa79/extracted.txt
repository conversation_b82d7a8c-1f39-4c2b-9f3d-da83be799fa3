Rock, Paper, Scissors, and the Stock Market
Recounting my experience in the Rock, Paper, Scissors Kaggle competition, and the resemblance of the competition structure to the stock market.
You would not normally relate rock, paper, scissors (RPS for short) to the stock market. It actually seems absurd to do so. And granted, a single game of RPS, or just a series of a thousand games of RPS has no relation whatsoever to the stock market.
But when RPS is put in a competition format where various algorithms battle against each other for points in the leaderboard, my theory is that there are some notable resemblances to the stock market.
More precisely, <PERSON><PERSON>’s recent competition on RPS that started in November, 2020, and ended in February, 2021 seems to have some characteristics of our usual stock market, and I personally achieved 32nd rank (top 2%) by using portfolio optimization theory to take advantage of this similarity.
Note that much of this article is based on intuition; there is no statistical evidence to back up my arguments. But the article should be informative for those interested in market efficiency, Kaggle competitions, portfolio optimization theory, etc.
Data Science in Rock, Paper, Scissors Context
Kaggle’s RPS Competition
Four months ago, probably near the start of November of 2020, <PERSON><PERSON> launched the RPS competition. Below is an abbreviated excerpt from the competition description page.
Rock, Paper, Scissors (sometimes called roshambo) has been a staple to settle playground disagreements or determine who gets to ride in the front seat on a road trip…
Studies have shown that a Rock, Paper, Scissors AI can consistently beat human opponents. With previous games as input, it studies patterns to understand a player’s tendencies. But what happens when we expand the simple “Best-of-3” game to be “Best-of-1000”? How well can artificial intelligence perform?
In this simulation competition, you will create an AI to play against others in many rounds of this classic game… It’s possible to greatly outperform a random player when the matches involve non-random agents. A strong AI can consistently beat predictable AI…
As explained above, the goal of each participant in the competition is to submit an algorithm that plays 1000 consecutive RPS matches against another bot (submitted by some other participant). An algorithm wins that episode (=match of 1000 games) if it wins by more than 20 points, with 1 point rewarded for a win, 0 for a draw, and -1 for a loss. And for each victory in an episode, the algorithm is rewarded learderboard points according to the competition’s evaluation metric.
At first sight, this seems rather pointless. The intuitive rational move in an RPS game is to play random, no debate. If we were to play a single episode against any bot, we should by all means play random.
But the point of the competition lies in this part of the description:
It’s possible to greatly outperform a random player when the matches involve non-random agents.
If there are even a few dozens of non-random bots in the competition, then another bot that can exploit this non-randomness will have a higher chance of outperforming all other random bots as long as there is a sufficient number of matches. This is because a superior non-random algorithm will likely tie against other random bots, but win against inferior non-random algorithms, gaining extra points in the leaderboard that random bots cannot gain.
So while the Nash equilibrium dictates that one should play random under the assumption that every other bot acts rationally, the very fact that not all bots are rational allows for non-random agents to outperform in the leaderboard.
Already we see a resemblance to the stock market. If all market participants acted rationally with given information, then the stock market would be extremely efficient (=unpredictable). It is the existing irrationality of market participants that gives birth to exploitable market inefficiencies for excess return (although this part could be debatable after various risk-adjustment).
Previous RPS Competitions
Believe it or not there had been several RPS competitions before Kaggle. The most recent one is the RPSContest website. Although there were no prizes given out to the monthly winners, people actively participated in the contest, which led to the birth of some truly remarkable algorithms.
In Kaggle discussions, there were other frequently mentioned algorithms, such as Greenberg and Iocaine Powder. These algorithms performed in competitions hosted even before RPSContest, in the year 2001 and before. The best detailed descriptions of these algorithms can be found here, but to briefly put the logic in context, these highly successful algorithms utilized memory-searching algorithms with multiple layers of meta strategy that decided on whether to make the move that beats the predicted move from past history, or the move that beats that move, or the move that beats the move that beats that move, etc.
To put the strength of these algorithms in context, Iocaine Powder and Greenberg can easily beat machine learning based algorithms such as decision tree and multi-layer perceptrons (feed-forward neural network). In fact, using machine learning algorithms requires extra care to avoid over-fitting, as other memory-searching algorithms and statistical models react much quicker to change in opponent’s strategy, which is essential in securing a +20 points victory.
Market Efficiency and Data Science in the Stock Market Context
Efficient Market?
Okay, so now that we’ve talked about the place of RPS in the traditional data science community, let’s examine what it means to have an efficient market.
A fully efficient market incorporates all relevant information into its market price instantly. This means that, as the market price already reflects the asset’s true value, there are no opportunities to find “under-valued” or “over-valued” assets. By this definition, it is impossible to consistently outperform the market — all those with long track records of superior performance are just fortunate.
There are different degrees of proposed market efficiency. The weak form suggests that past price movements are inconsequential in predicting future returns. In other words, trend-based trading or technical analysis based on price movements alone are not sufficient to consistently outperform the market. This has been one of the focal points of researches on cryptocurrency market efficiency in relatively recent academics, which could be interesting to read papers on.
The semi-strong market efficiency states that the market price quickly responds to all publicly available information. In this perspective, even fundamental analysis is deemed obsolete. The only way to beat the market in this way will be to gain non-public information, which hangs dangerously on the line of illegal insider trading.
The strong form argues that the market reflects all information — both public and undisclosed — instantly, meaning that outperforming the market becomes a nigh impossibility.
There however had been certain classes of stocks that seem to outperform the market. To incorporate these anomalies in the efficient market context, we can draw on Fama’s thee factor model, which shows that when adjusted to various risks based on certain factors, the seemingly superior stock returns could be explained (Fama later expands this model to incorporate two more factors, but these factors alone explain the majority of anomalies in expected returns).
It seems that with the exception of academia, it is generally agreed upon that while the market is generally efficient, it still displays inefficiencies, especially in short time-periods — hence the adoption of Efficiently Inefficient as the title of Lasse Heje Pedersen’s famous introductory book on hedge funds and active investing.
Then What is the Place of Data Science in Beating the Market?
As only a new student in this field, I probably do not qualify to answer the question above, but I’ll take a shot at it anyways.
The tricky part of using machine learning algorithms to predict the stock market is that it is very easy to overfit. The evidence is practically everywhere on Medium. More than half of relatively old articles on “predicting the stock market” fall into this trap. The easiest ones to spot are those that use LSTMs on recent price data to predict the next price, which just ends up being a time lag prediction (basically, every day’s price forecast is the previous day’s price; interestingly this is consistent with the Martingale Property, but you do not need LSTMs to make use of this property).
Granted, the top quant hedge funds are probably using ML algorithms for full benefit in beating the market by taking extra care to avoid overfitting, but for a regular retail investor, the cost of taking such extra caution probably outweighs the benefits. The fact is that a simple statistical model with enough, real diversification shows surprisingly strong performance. And while using additional data science techniques such as sentiment analysis, google trend analysis, and deep neural networks could provide a small edge, that effort is better spent minimizing market friction, providing extra diversification, lowering server costs, etc.
Of course, there are certain exceptions such as QRAFT, a Korean startup recently drawing a lot of attention, but even here you should notice that the startup’s most valuable assets are not only its deep reinforcement learning based ETFs, but also AXE (ML based order execution system) and Kirin API (automated data cleaning module).
To summarize, data science does have a place in the active investing world, but while its impact on stock return prediction is unclear, its positive influence on other aspects of trading such as order execution seems much clear-cut.
Non-random RPS Competition and the Inefficient Market
Drawing the Analogy
You’re probably wondering now how all the information above come together to form the conclusion the Kaggle’s RPS competition resembles the stock market.
Well, let’s consider the semi-strong form of market efficiency. Here it is argued that whenever there is a disclosed event that changes the value of an asset, the market price instantly reflects that information, propelled by market participants who act on the opportunity to make profit.
One way of seeing how this works, is to consider what would happen if it is guaranteed that market efficiency holds true. If that is the case, then there is no incentive for any market participant to risk losing the transaction cost by actively trading. The best move would be to only take the systematic risk by investing in passive ETFs.
However, this is usually not the case. Investors, whether institutional or individual, trade actively under the assumption that certain inefficiencies exist for exploitation. If any inefficiency exists, then this active movement removes that inefficiency from the market as investors greedily take opportunity from the wrong pricing, quickly reverting the price towards its correct value. The return that an investor gains for such activity can be thought of as the reward for re-instilling efficiency to the market.
Interestingly this form of active investing that restores efficiency is also the very source of inefficiency.
Now let’s go back to Kaggle’s RPS competition. Under Nash equilibrium one should only submit random agents and hope to win. However, in this case we know for a fact that there are non-random, irrational agents participating in the competition. Under this new information, the best move becomes to introduce similarly non-random (inefficient) agents that could hopefully beat all other non-random agents. If you succeed in coming up with such algorithm, then you revert the performance of the other non-random agents to the random mean (restoring efficiency), and gain points in the leaderboard as the reward. At the same time, because you submitted a non-random agent, you also risk introducing inefficiency to the market unless another better bot emerges.
In Kaggle’s RPS competition, past algorithms like Iocaine Powder and Greenberg performed very poorly. Why? While an obvious reason is that almost every other top-performing agent has a mirror version of such algorithms to defeat them 100% of the time, the other reason is that because so many copies of such successful-in-past-but-publicly-available algorithms are already submitted, bots that would lose to such algorithms already hang too low on the leaderboard. In other words, all inefficiencies in the competition that Iocaine Powder or Greenberg could feed on are already resolved by the collective crowd.
This is very similar to what happens when a successful trading strategy gets publicly known in the investing world. In the very beginning utilizing option pricing using Black-Scholes model, arbitrage opportunities like pair-trading, and contrarian strategies were all pretty lucrative because limited number of market participants employed them. By the market crashes in the 2000s, however, extreme leverage became essential to attain similar levels of return.
I can be frowned upon for drafting these conclusions without any real data to back them up. After all, it is the spirit of Kaggle and data science community in general to actually extract data and draw concrete conclusions. But the analogy drawn here does bring an interesting view on the nature of the competition.
Portfolio Optimization as the Ensemble Methodology
With less than a couple of weeks until the competition deadline, it became increasingly clear that an ensemble is probably the way to go in order to be successful. While there was a remarkable reveal of a simple, non-ensemble agent that showed superb performance, most of the discussions leaned towards coming up with a reliable ensemble methodology.
Personally, as I was fascinated by the similarity of the competition to the stock market, I desperately wanted to make use of knowledge in the quantitative finance field that I had been wrestling with for the past year.
A detailed write-up of my thoughts is well documented here, but to add on to what is said in the linked thread, let me concretely explain how I set up the problem.
Regardless of whether you make a deterministic or stochastic choice at each step of an episode, the fact is that when trying to incorporate information from multiple algorithms, you are going to weight the decision of those algorithms for the final decision. If algorithm A has produced excellent results against the current opponent, then it is most likely that you should stick with the recommended move of algorithm A; if algorithm A recommends scissors, you should pick scissors. In such case, the weight map would look like: {0 : 0.02, 1 : 0.18, 2 : 0.80}, in which 0, 1, 2 correspond to rock, paper, scissors, respectively. With 80% of the weights concentrated on 2, the ensemble would pick scissors as the next move.
So in some aspect, picking weights for algorithms to use for the current step is similar to how one would choose portfolio weights for different assets during a re-balancing period. If an algorithm(=asset) performed well recently, you would be likely to give it more weight. Then we can think of each step in the episode as a natural time-step for re-balancing our portfolio.
Now, most of the publicly available RPS algorithms use the same core logic; it was frequently discussed in the competition threads that it is essential to remove redundant agents in order to improve the performance of an ensemble. Well, if we were to incentivize algorithms with low correlation with others, then there is no need for manual hand-picking.
Fortunately, our famous mean-variance portfolio optimization theory gives us the means to do exactly that. I don’t want to get too deep into the portfolio optimization theory, as there are murky areas that I am still not so sure about, but the important takeaway is that as long as expected returns of target assets are excellent, the resulting portfolio will also be excellent in terms of risk-adjusted returns: the portfolio will rarely lose.
Regrettably, Kaggle only allows a maximum of 1 second to decide on the next move, so I was not able to get an ensemble model that examines the efficient frontier to find the point of maximum Sharpe. Had that been possible, I dare presume I might have finished higher in the leaderboard.
Wait, though. How do I know that this ensemble method was indeed successful in the competition? After all, a lucky random agent could wander very high up in the leaderboard, due to the nature of RPS. My defense would be that I had 8 of these portfolio ensemble agents in the medal zone by the end of the competition, and that this ensemble method has proved much more successful than multi-armed bandit with beta distribution in local testing (and in the leaderboard).
In Conclusion
Once you register all the facts, it probably is not a shock that RPS competition resembles a stock market in some aspects. Both could be described as systems that are completely random in the long run, yet somewhat predictable in the short term.
What RPS competition really showed me, however, is that using complicated methods like LSTMs to bash through all the random noise is usually very pointless. The fact is that there does not exist any such strong signals in past score trends (=past price trends) that give meaningful information about the future. Instead of worrying about tuning hyper-parameters, more effort could be better spent on trimming down execution time of algorithms (=reducing market friction?) or coming up with and adding more effective algorithms (=adding diversification).
Also, math is king. I really recommend checking out this thread. It shows how effective a simple mathematical solution could be.
Thanks to Kaggle staff for hosting this fun competition, great gratitude to people like Stas Sl, Taaha Khan, SuperAnt, Tony Robinson, etc, who made so much meaningful intellectual contributions to the community.
Note from Towards Data Science’s editors: While we allow independent authors to publish articles in accordance with our rules and guidelines, we do not endorse each author’s contribution. You should not rely on an author’s works without seeking professional advice. See our Reader Terms for details.