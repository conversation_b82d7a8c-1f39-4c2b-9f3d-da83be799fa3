<!DOCTYPE html>
<html class="client-nojs vector-feature-language-in-header-enabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-enabled vector-feature-custom-font-size-clientpref-1 vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-available" lang="en" dir="ltr">
<head>
<meta charset="UTF-8">
<title>Algorithmic trading - Wikipedia</title>
<script>(function(){var className="client-js vector-feature-language-in-header-enabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-enabled vector-feature-custom-font-size-clientpref-1 vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-available";var cookie=document.cookie.match(/(?:^|; )enwikimwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":false,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"0a4c3485-258f-41e1-8dc4-826e32ed6ef5","wgCanonicalNamespace":"","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":0,"wgPageName":"Algorithmic_trading","wgTitle":"Algorithmic trading","wgCurRevisionId":1306548555,"wgRevisionId":1306548555,"wgArticleId":2484768,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":["CS1 errors: periodical ignored","CS1 errors: missing periodical","Webarchive template wayback links","CS1 maint: multiple names: authors list","CS1 maint: archived copy as title","Articles with short description","Short description is different from Wikidata","Use mdy dates from January 2019","Articles needing additional references from February 2025","All articles needing additional references","Algorithmic trading","Electronic trading systems","Financial markets","Share trading"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"Algorithmic_trading","wgRelevantArticleId":2484768,"wgIsProbablyEditable":true,"wgRelevantPageIsProbablyEditable":true,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgNoticeProject":"wikipedia","wgCiteReferencePreviewsActive":false,"wgFlaggedRevsParams":{"tags":{"status":{"levels":1}}},"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":true,"wgPopupsFlags":0,"wgVisualEditor":{"pageLanguageCode":"en","pageLanguageDir":"ltr","pageVariantFallbacks":"en"},"wgMFDisplayWikibaseDescriptions":{"search":true,"watchlist":true,"tagline":false,"nearby":true},"wgWMESchemaEditAttemptStepOversample":false,"wgWMEPageLength":90000,"wgMetricsPlatformUserExperiments":{"active_experiments":[],"overrides":[],"enrolled":[],"assigned":[],"subject_ids":[],"sampling_units":[]},"wgEditSubmitButtonLabelPublish":true,"wgULSPosition":"interlanguage","wgULSisCompactLinksEnabled":false,"wgVector2022LanguageInHeader":true,"wgULSisLanguageSelectorEmpty":false,"wgWikibaseItemId":"Q139445","wgCheckUserClientHintsHeadersJsApi":["brands","architecture","bitness","fullVersionList","mobile","model","platform","platformVersion"],"GEHomepageSuggestedEditsEnableTopics":true,"wgGESuggestedEditsTaskTypes":{"taskTypes":["copyedit","link-recommendation"],"unavailableTaskTypes":[]},"wgGETopicsMatchModeEnabled":false,"wgGELevelingUpEnabledForUser":false};
RLSTATE={"ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","ext.cite.styles":"ready","skins.vector.search.codex.styles":"ready","skins.vector.styles":"ready","skins.vector.icons":"ready","jquery.makeCollapsible.styles":"ready","ext.wikimediamessages.styles":"ready","ext.visualEditor.desktopArticleTarget.noscript":"ready","ext.uls.interlanguage":"ready","wikibase.client.init":"ready"};RLPAGEMODULES=["ext.xLab","ext.cite.ux-enhancements","mediawiki.page.media","site","mediawiki.page.ready","jquery.makeCollapsible","mediawiki.toc","skins.vector.js","ext.centralNotice.geoIP","ext.centralNotice.startUp","ext.gadget.ReferenceTooltips","ext.gadget.switcher","ext.urlShortener.toolbar","ext.centralauth.centralautologin","mmv.bootstrap","ext.popups","ext.visualEditor.desktopArticleTarget.init","ext.visualEditor.targetLoader","ext.echo.centralauth","ext.eventLogging","ext.wikimediaEvents","ext.navigationTiming","ext.uls.interface","ext.cx.eventlogging.campaigns","ext.cx.uls.quick.actions","wikibase.client.vector-2022","ext.checkUser.clientHints","ext.quicksurveys.init","ext.growthExperiments.SuggestedEditSession"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="/w/load.php?lang=en&amp;modules=ext.cite.styles%7Cext.uls.interlanguage%7Cext.visualEditor.desktopArticleTarget.noscript%7Cext.wikimediamessages.styles%7Cjquery.makeCollapsible.styles%7Cskins.vector.icons%2Cstyles%7Cskins.vector.search.codex.styles%7Cwikibase.client.init&amp;only=styles&amp;skin=vector-2022">
<script async="" src="/w/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector-2022"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="/w/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector-2022">
<meta name="generator" content="MediaWiki 1.45.0-wmf.15">
<meta name="referrer" content="origin">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="robots" content="max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta property="og:image" content="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5e/Assorted_United_States_coins.jpg/1200px-Assorted_United_States_coins.jpg">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="900">
<meta property="og:image" content="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5e/Assorted_United_States_coins.jpg/960px-Assorted_United_States_coins.jpg">
<meta property="og:image:width" content="800">
<meta property="og:image:height" content="600">
<meta property="og:image:width" content="640">
<meta property="og:image:height" content="480">
<meta name="viewport" content="width=1120">
<meta property="og:title" content="Algorithmic trading - Wikipedia">
<meta property="og:type" content="website">
<link rel="preconnect" href="//upload.wikimedia.org">
<link rel="alternate" media="only screen and (max-width: 640px)" href="//en.m.wikipedia.org/wiki/Algorithmic_trading">
<link rel="alternate" type="application/x-wiki" title="Edit this page" href="/w/index.php?title=Algorithmic_trading&amp;action=edit">
<link rel="apple-touch-icon" href="/static/apple-touch/wikipedia.png">
<link rel="icon" href="/static/favicon/wikipedia.ico">
<link rel="search" type="application/opensearchdescription+xml" href="/w/rest.php/v1/search" title="Wikipedia (en)">
<link rel="EditURI" type="application/rsd+xml" href="//en.wikipedia.org/w/api.php?action=rsd">
<link rel="canonical" href="https://en.wikipedia.org/wiki/Algorithmic_trading">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0/deed.en">
<link rel="alternate" type="application/atom+xml" title="Wikipedia Atom feed" href="/w/index.php?title=Special:RecentChanges&amp;feed=atom">
<link rel="dns-prefetch" href="//meta.wikimedia.org" />
<link rel="dns-prefetch" href="auth.wikimedia.org">
</head>
<body class="skin--responsive skin-vector skin-vector-search-vue mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-0 ns-subject mw-editable page-Algorithmic_trading rootpage-Algorithmic_trading skin-vector-2022 action-view"><a class="mw-jump-link" href="#bodyContent">Jump to content</a>
<div class="vector-header-container">
	<header class="vector-header mw-header no-font-mode-scale">
		<div class="vector-header-start">
			<nav class="vector-main-menu-landmark" aria-label="Site">
				
<div id="vector-main-menu-dropdown" class="vector-dropdown vector-main-menu-dropdown vector-button-flush-left vector-button-flush-right"  title="Main menu" >
	<input type="checkbox" id="vector-main-menu-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-main-menu-dropdown" class="vector-dropdown-checkbox "  aria-label="Main menu"  >
	<label id="vector-main-menu-dropdown-label" for="vector-main-menu-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-menu mw-ui-icon-wikimedia-menu"></span>

<span class="vector-dropdown-label-text">Main menu</span>
	</label>
	<div class="vector-dropdown-content">


				<div id="vector-main-menu-unpinned-container" class="vector-unpinned-container">
		
<div id="vector-main-menu" class="vector-main-menu vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-main-menu-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="main-menu-pinned"
	data-pinnable-element-id="vector-main-menu"
	data-pinned-container-id="vector-main-menu-pinned-container"
	data-unpinned-container-id="vector-main-menu-unpinned-container"
>
	<div class="vector-pinnable-header-label">Main menu</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-main-menu.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-main-menu.unpin">hide</button>
</div>

	
<div id="p-navigation" class="vector-menu mw-portlet mw-portlet-navigation"  >
	<div class="vector-menu-heading">
		Navigation
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="/wiki/Main_Page" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-contents" class="mw-list-item"><a href="/wiki/Wikipedia:Contents" title="Guides to browsing Wikipedia"><span>Contents</span></a></li><li id="n-currentevents" class="mw-list-item"><a href="/wiki/Portal:Current_events" title="Articles related to current events"><span>Current events</span></a></li><li id="n-randompage" class="mw-list-item"><a href="/wiki/Special:Random" title="Visit a randomly selected article [x]" accesskey="x"><span>Random article</span></a></li><li id="n-aboutsite" class="mw-list-item"><a href="/wiki/Wikipedia:About" title="Learn about Wikipedia and how it works"><span>About Wikipedia</span></a></li><li id="n-contactpage" class="mw-list-item"><a href="//en.wikipedia.org/wiki/Wikipedia:Contact_us" title="How to contact Wikipedia"><span>Contact us</span></a></li>
		</ul>
		
	</div>
</div>

	
	
<div id="p-interaction" class="vector-menu mw-portlet mw-portlet-interaction"  >
	<div class="vector-menu-heading">
		Contribute
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-help" class="mw-list-item"><a href="/wiki/Help:Contents" title="Guidance on how to use and edit Wikipedia"><span>Help</span></a></li><li id="n-introduction" class="mw-list-item"><a href="/wiki/Help:Introduction" title="Learn how to edit Wikipedia"><span>Learn to edit</span></a></li><li id="n-portal" class="mw-list-item"><a href="/wiki/Wikipedia:Community_portal" title="The hub for editors"><span>Community portal</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="/wiki/Special:RecentChanges" title="A list of recent changes to Wikipedia [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-upload" class="mw-list-item"><a href="/wiki/Wikipedia:File_upload_wizard" title="Add images or other media for use on Wikipedia"><span>Upload file</span></a></li><li id="n-specialpages" class="mw-list-item"><a href="/wiki/Special:SpecialPages"><span>Special pages</span></a></li>
		</ul>
		
	</div>
</div>

</div>

				</div>

	</div>
</div>

		</nav>
			
<a href="/wiki/Main_Page" class="mw-logo">
	<img class="mw-logo-icon" src="/static/images/icons/wikipedia.png" alt="" aria-hidden="true" height="50" width="50">
	<span class="mw-logo-container skin-invert">
		<img class="mw-logo-wordmark" alt="Wikipedia" src="/static/images/mobile/copyright/wikipedia-wordmark-en.svg" style="width: 7.5em; height: 1.125em;">
		<img class="mw-logo-tagline" alt="The Free Encyclopedia" src="/static/images/mobile/copyright/wikipedia-tagline-en.svg" width="117" height="13" style="width: 7.3125em; height: 0.8125em;">
	</span>
</a>

		</div>
		<div class="vector-header-end">
			
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-collapses vector-search-box-show-thumbnail vector-search-box-auto-expand-width vector-search-box">
	<a href="/wiki/Special:Search" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only search-toggle" title="Search Wikipedia [f]" accesskey="f"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
	</a>
	<div class="vector-typeahead-search-container">
		<div class="cdx-typeahead-search cdx-typeahead-search--show-thumbnail cdx-typeahead-search--auto-expand-width">
			<form action="/w/index.php" id="searchform" class="cdx-search-input cdx-search-input--has-end-button">
				<div id="simpleSearch" class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
					<div class="cdx-text-input cdx-text-input--has-start-icon">
						<input
							class="cdx-text-input__input mw-searchInput" autocomplete="off"
							 type="search" name="search" placeholder="Search Wikipedia" aria-label="Search Wikipedia" autocapitalize="sentences" spellcheck="false" title="Search Wikipedia [f]" accesskey="f" id="searchInput"
							>
						<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
					</div>
					<input type="hidden" name="title" value="Special:Search">
				</div>
				<button class="cdx-button cdx-search-input__end-button">Search</button>
			</form>
		</div>
	</div>
</div>

			<nav class="vector-user-links vector-user-links-wide" aria-label="Personal tools">
	<div class="vector-user-links-main">
	
<div id="p-vector-user-menu-preferences" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-userpage" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	<nav class="vector-appearance-landmark" aria-label="Appearance">
		
<div id="vector-appearance-dropdown" class="vector-dropdown "  title="Change the appearance of the page&#039;s font size, width, and color" >
	<input type="checkbox" id="vector-appearance-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-appearance-dropdown" class="vector-dropdown-checkbox "  aria-label="Appearance"  >
	<label id="vector-appearance-dropdown-label" for="vector-appearance-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-appearance mw-ui-icon-wikimedia-appearance"></span>

<span class="vector-dropdown-label-text">Appearance</span>
	</label>
	<div class="vector-dropdown-content">


			<div id="vector-appearance-unpinned-container" class="vector-unpinned-container">
				
			</div>
		
	</div>
</div>

	</nav>
	
<div id="p-vector-user-menu-notifications" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-overflow" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="pt-sitesupport-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=en.wikipedia.org&amp;uselang=en" class=""><span>Donate</span></a>
</li>
<li id="pt-createaccount-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="/w/index.php?title=Special:CreateAccount&amp;returnto=Algorithmic+trading" title="You are encouraged to create an account and log in; however, it is not mandatory" class=""><span>Create account</span></a>
</li>
<li id="pt-login-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="/w/index.php?title=Special:UserLogin&amp;returnto=Algorithmic+trading" title="You&#039;re encouraged to log in; however, it&#039;s not mandatory. [o]" accesskey="o" class=""><span>Log in</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	</div>
	
<div id="vector-user-links-dropdown" class="vector-dropdown vector-user-menu vector-button-flush-right vector-user-menu-logged-out"  title="Log in and more options" >
	<input type="checkbox" id="vector-user-links-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-user-links-dropdown" class="vector-dropdown-checkbox "  aria-label="Personal tools"  >
	<label id="vector-user-links-dropdown-label" for="vector-user-links-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-ellipsis mw-ui-icon-wikimedia-ellipsis"></span>

<span class="vector-dropdown-label-text">Personal tools</span>
	</label>
	<div class="vector-dropdown-content">


		
<div id="p-personal" class="vector-menu mw-portlet mw-portlet-personal user-links-collapsible-item"  title="User menu" >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-sitesupport" class="user-links-collapsible-item mw-list-item"><a href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=en.wikipedia.org&amp;uselang=en"><span>Donate</span></a></li><li id="pt-createaccount" class="user-links-collapsible-item mw-list-item"><a href="/w/index.php?title=Special:CreateAccount&amp;returnto=Algorithmic+trading" title="You are encouraged to create an account and log in; however, it is not mandatory"><span class="vector-icon mw-ui-icon-userAdd mw-ui-icon-wikimedia-userAdd"></span> <span>Create account</span></a></li><li id="pt-login" class="user-links-collapsible-item mw-list-item"><a href="/w/index.php?title=Special:UserLogin&amp;returnto=Algorithmic+trading" title="You&#039;re encouraged to log in; however, it&#039;s not mandatory. [o]" accesskey="o"><span class="vector-icon mw-ui-icon-logIn mw-ui-icon-wikimedia-logIn"></span> <span>Log in</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-user-menu-anon-editor" class="vector-menu mw-portlet mw-portlet-user-menu-anon-editor"  >
	<div class="vector-menu-heading">
		Pages for logged out editors <a href="/wiki/Help:Introduction" aria-label="Learn more about editing"><span>learn more</span></a>
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-anoncontribs" class="mw-list-item"><a href="/wiki/Special:MyContributions" title="A list of edits made from this IP address [y]" accesskey="y"><span>Contributions</span></a></li><li id="pt-anontalk" class="mw-list-item"><a href="/wiki/Special:MyTalk" title="Discussion about edits from this IP address [n]" accesskey="n"><span>Talk</span></a></li>
		</ul>
		
	</div>
</div>

	
	</div>
</div>

</nav>

		</div>
	</header>
</div>
<div class="mw-page-container">
	<div class="mw-page-container-inner">
		<div class="vector-sitenotice-container">
			<div id="siteNotice"><!-- CentralNotice --></div>
		</div>
		<div class="vector-column-start">
			<div class="vector-main-menu-container">
		<div id="mw-navigation">
			<nav id="mw-panel" class="vector-main-menu-landmark" aria-label="Site">
				<div id="vector-main-menu-pinned-container" class="vector-pinned-container">
				
				</div>
		</nav>
		</div>
	</div>
	<div class="vector-sticky-pinned-container">
				<nav id="mw-panel-toc" aria-label="Contents" data-event-name="ui.sidebar-toc" class="mw-table-of-contents-container vector-toc-landmark">
					<div id="vector-toc-pinned-container" class="vector-pinned-container">
					<div id="vector-toc" class="vector-toc vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-toc-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="toc-pinned"
	data-pinnable-element-id="vector-toc"
	
	
>
	<h2 class="vector-pinnable-header-label">Contents</h2>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-toc.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-toc.unpin">hide</button>
</div>


	<ul class="vector-toc-contents" id="mw-panel-toc-list">
		<li id="toc-mw-content-text"
			class="vector-toc-list-item vector-toc-level-1">
			<a href="#" class="vector-toc-link">
				<div class="vector-toc-text">(Top)</div>
			</a>
		</li>
		<li id="toc-Machine_Learning_Integration"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#Machine_Learning_Integration">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">1</span>
				<span>Machine Learning Integration</span>
			</div>
		</a>
		
			<button aria-controls="toc-Machine_Learning_Integration-sublist" class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-toc-toggle">
				<span class="vector-icon mw-ui-icon-wikimedia-expand"></span>
				<span>Toggle Machine Learning Integration subsection</span>
			</button>
		
		<ul id="toc-Machine_Learning_Integration-sublist" class="vector-toc-list">
			<li id="toc-Ethical_Implications_and_Fairness"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Ethical_Implications_and_Fairness">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">1.1</span>
					<span>Ethical Implications and Fairness</span>
				</div>
			</a>
			
			<ul id="toc-Ethical_Implications_and_Fairness-sublist" class="vector-toc-list">
			</ul>
		</li>
	</ul>
	</li>
	<li id="toc-History"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#History">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">2</span>
				<span>History</span>
			</div>
		</a>
		
			<button aria-controls="toc-History-sublist" class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-toc-toggle">
				<span class="vector-icon mw-ui-icon-wikimedia-expand"></span>
				<span>Toggle History subsection</span>
			</button>
		
		<ul id="toc-History-sublist" class="vector-toc-list">
			<li id="toc-Early_developments"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Early_developments">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">2.1</span>
					<span>Early developments</span>
				</div>
			</a>
			
			<ul id="toc-Early_developments-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Refinement_and_growth"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Refinement_and_growth">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">2.2</span>
					<span>Refinement and growth</span>
				</div>
			</a>
			
			<ul id="toc-Refinement_and_growth-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Case_studies"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Case_studies">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">2.3</span>
					<span>Case studies</span>
				</div>
			</a>
			
			<ul id="toc-Case_studies-sublist" class="vector-toc-list">
			</ul>
		</li>
	</ul>
	</li>
	<li id="toc-Strategies"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#Strategies">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">3</span>
				<span>Strategies</span>
			</div>
		</a>
		
			<button aria-controls="toc-Strategies-sublist" class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-toc-toggle">
				<span class="vector-icon mw-ui-icon-wikimedia-expand"></span>
				<span>Toggle Strategies subsection</span>
			</button>
		
		<ul id="toc-Strategies-sublist" class="vector-toc-list">
			<li id="toc-Trading_ahead_of_index_fund_rebalancing"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Trading_ahead_of_index_fund_rebalancing">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">3.1</span>
					<span>Trading ahead of index fund rebalancing</span>
				</div>
			</a>
			
			<ul id="toc-Trading_ahead_of_index_fund_rebalancing-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Pairs_trading"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Pairs_trading">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">3.2</span>
					<span>Pairs trading</span>
				</div>
			</a>
			
			<ul id="toc-Pairs_trading-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Delta-neutral_strategies"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Delta-neutral_strategies">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">3.3</span>
					<span>Delta-neutral strategies</span>
				</div>
			</a>
			
			<ul id="toc-Delta-neutral_strategies-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Arbitrage"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Arbitrage">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">3.4</span>
					<span>Arbitrage</span>
				</div>
			</a>
			
			<ul id="toc-Arbitrage-sublist" class="vector-toc-list">
				<li id="toc-Conditions_for_arbitrage"
			class="vector-toc-list-item vector-toc-level-3">
			<a class="vector-toc-link" href="#Conditions_for_arbitrage">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">3.4.1</span>
					<span>Conditions for arbitrage</span>
				</div>
			</a>
			
			<ul id="toc-Conditions_for_arbitrage-sublist" class="vector-toc-list">
			</ul>
		</li>
	</ul>
		</li>
		<li id="toc-Mean_reversion"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Mean_reversion">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">3.5</span>
					<span>Mean reversion</span>
				</div>
			</a>
			
			<ul id="toc-Mean_reversion-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Scalping"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Scalping">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">3.6</span>
					<span>Scalping</span>
				</div>
			</a>
			
			<ul id="toc-Scalping-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Transaction_cost_reduction"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Transaction_cost_reduction">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">3.7</span>
					<span>Transaction cost reduction</span>
				</div>
			</a>
			
			<ul id="toc-Transaction_cost_reduction-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Strategies_that_only_pertain_to_dark_pools"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Strategies_that_only_pertain_to_dark_pools">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">3.8</span>
					<span>Strategies that only pertain to dark pools</span>
				</div>
			</a>
			
			<ul id="toc-Strategies_that_only_pertain_to_dark_pools-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Market_timing"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Market_timing">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">3.9</span>
					<span>Market timing</span>
				</div>
			</a>
			
			<ul id="toc-Market_timing-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Algorithmic_trading_under_the_assumption_of_non-ergodicity"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Algorithmic_trading_under_the_assumption_of_non-ergodicity">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">3.10</span>
					<span>Algorithmic trading under the assumption of non-ergodicity</span>
				</div>
			</a>
			
			<ul id="toc-Algorithmic_trading_under_the_assumption_of_non-ergodicity-sublist" class="vector-toc-list">
			</ul>
		</li>
	</ul>
	</li>
	<li id="toc-High-frequency_trading"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#High-frequency_trading">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">4</span>
				<span>High-frequency trading</span>
			</div>
		</a>
		
			<button aria-controls="toc-High-frequency_trading-sublist" class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-toc-toggle">
				<span class="vector-icon mw-ui-icon-wikimedia-expand"></span>
				<span>Toggle High-frequency trading subsection</span>
			</button>
		
		<ul id="toc-High-frequency_trading-sublist" class="vector-toc-list">
			<li id="toc-Market_making"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Market_making">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">4.1</span>
					<span>Market making</span>
				</div>
			</a>
			
			<ul id="toc-Market_making-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Statistical_arbitrage"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Statistical_arbitrage">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">4.2</span>
					<span>Statistical arbitrage</span>
				</div>
			</a>
			
			<ul id="toc-Statistical_arbitrage-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Event_arbitrage"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Event_arbitrage">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">4.3</span>
					<span>Event arbitrage</span>
				</div>
			</a>
			
			<ul id="toc-Event_arbitrage-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Spoofing"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Spoofing">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">4.4</span>
					<span>Spoofing</span>
				</div>
			</a>
			
			<ul id="toc-Spoofing-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Quote_stuffing"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Quote_stuffing">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">4.5</span>
					<span>Quote stuffing</span>
				</div>
			</a>
			
			<ul id="toc-Quote_stuffing-sublist" class="vector-toc-list">
			</ul>
		</li>
	</ul>
	</li>
	<li id="toc-Low_latency_trading_systems"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#Low_latency_trading_systems">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">5</span>
				<span>Low latency trading systems</span>
			</div>
		</a>
		
		<ul id="toc-Low_latency_trading_systems-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-Strategy_implementation"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#Strategy_implementation">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">6</span>
				<span>Strategy implementation</span>
			</div>
		</a>
		
		<ul id="toc-Strategy_implementation-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-Issues_and_developments"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#Issues_and_developments">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">7</span>
				<span>Issues and developments</span>
			</div>
		</a>
		
			<button aria-controls="toc-Issues_and_developments-sublist" class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-toc-toggle">
				<span class="vector-icon mw-ui-icon-wikimedia-expand"></span>
				<span>Toggle Issues and developments subsection</span>
			</button>
		
		<ul id="toc-Issues_and_developments-sublist" class="vector-toc-list">
			<li id="toc-Cyborg_finance"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Cyborg_finance">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">7.1</span>
					<span>Cyborg finance</span>
				</div>
			</a>
			
			<ul id="toc-Cyborg_finance-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Concerns"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Concerns">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">7.2</span>
					<span>Concerns</span>
				</div>
			</a>
			
			<ul id="toc-Concerns-sublist" class="vector-toc-list">
			</ul>
		</li>
		<li id="toc-Recent_developments"
			class="vector-toc-list-item vector-toc-level-2">
			<a class="vector-toc-link" href="#Recent_developments">
				<div class="vector-toc-text">
					<span class="vector-toc-numb">7.3</span>
					<span>Recent developments</span>
				</div>
			</a>
			
			<ul id="toc-Recent_developments-sublist" class="vector-toc-list">
			</ul>
		</li>
	</ul>
	</li>
	<li id="toc-System_architecture"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#System_architecture">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">8</span>
				<span>System architecture</span>
			</div>
		</a>
		
		<ul id="toc-System_architecture-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-Effects"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#Effects">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">9</span>
				<span>Effects</span>
			</div>
		</a>
		
		<ul id="toc-Effects-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-Communication_standards"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#Communication_standards">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">10</span>
				<span>Communication standards</span>
			</div>
		</a>
		
		<ul id="toc-Communication_standards-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-See_also"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#See_also">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">11</span>
				<span>See also</span>
			</div>
		</a>
		
		<ul id="toc-See_also-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-Notes"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#Notes">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">12</span>
				<span>Notes</span>
			</div>
		</a>
		
		<ul id="toc-Notes-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-References"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#References">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">13</span>
				<span>References</span>
			</div>
		</a>
		
		<ul id="toc-References-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-External_links"
		class="vector-toc-list-item vector-toc-level-1">
		<a class="vector-toc-link" href="#External_links">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">14</span>
				<span>External links</span>
			</div>
		</a>
		
		<ul id="toc-External_links-sublist" class="vector-toc-list">
		</ul>
	</li>
</ul>
</div>

					</div>
		</nav>
			</div>
		</div>
		<div class="mw-content-container">
			<main id="content" class="mw-body">
				<header class="mw-body-header vector-page-titlebar no-font-mode-scale">
					<nav aria-label="Contents" class="vector-toc-landmark">
						
<div id="vector-page-titlebar-toc" class="vector-dropdown vector-page-titlebar-toc vector-button-flush-left"  title="Table of Contents" >
	<input type="checkbox" id="vector-page-titlebar-toc-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-titlebar-toc" class="vector-dropdown-checkbox "  aria-label="Toggle the table of contents"  >
	<label id="vector-page-titlebar-toc-label" for="vector-page-titlebar-toc-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-listBullet mw-ui-icon-wikimedia-listBullet"></span>

<span class="vector-dropdown-label-text">Toggle the table of contents</span>
	</label>
	<div class="vector-dropdown-content">


							<div id="vector-page-titlebar-toc-unpinned-container" class="vector-unpinned-container">
			</div>
		
	</div>
</div>

					</nav>
					<h1 id="firstHeading" class="firstHeading mw-first-heading"><span class="mw-page-title-main">Algorithmic trading</span></h1>
							
<div id="p-lang-btn" class="vector-dropdown mw-portlet mw-portlet-lang"  >
	<input type="checkbox" id="p-lang-btn-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-p-lang-btn" class="vector-dropdown-checkbox mw-interlanguage-selector" aria-label="Go to an article in another language. Available in 22 languages"   >
	<label id="p-lang-btn-label" for="p-lang-btn-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--action-progressive mw-portlet-lang-heading-22" aria-hidden="true"  ><span class="vector-icon mw-ui-icon-language-progressive mw-ui-icon-wikimedia-language-progressive"></span>

<span class="vector-dropdown-label-text">22 languages</span>
	</label>
	<div class="vector-dropdown-content">

		<div class="vector-menu-content">
			
			<ul class="vector-menu-content-list">
				
				<li class="interlanguage-link interwiki-az mw-list-item"><a href="https://az.wikipedia.org/wiki/Alqoritmik_ticar%C9%99t" title="Alqoritmik ticarət – Azerbaijani" lang="az" hreflang="az" data-title="Alqoritmik ticarət" data-language-autonym="Azərbaycanca" data-language-local-name="Azerbaijani" class="interlanguage-link-target"><span>Azərbaycanca</span></a></li><li class="interlanguage-link interwiki-ceb mw-list-item"><a href="https://ceb.wikipedia.org/wiki/Trading_algoritmiko" title="Trading algoritmiko – Cebuano" lang="ceb" hreflang="ceb" data-title="Trading algoritmiko" data-language-autonym="Cebuano" data-language-local-name="Cebuano" class="interlanguage-link-target"><span>Cebuano</span></a></li><li class="interlanguage-link interwiki-de mw-list-item"><a href="https://de.wikipedia.org/wiki/Automatisierter_Handel" title="Automatisierter Handel – German" lang="de" hreflang="de" data-title="Automatisierter Handel" data-language-autonym="Deutsch" data-language-local-name="German" class="interlanguage-link-target"><span>Deutsch</span></a></li><li class="interlanguage-link interwiki-es mw-list-item"><a href="https://es.wikipedia.org/wiki/Trading_algor%C3%ADtmico" title="Trading algorítmico – Spanish" lang="es" hreflang="es" data-title="Trading algorítmico" data-language-autonym="Español" data-language-local-name="Spanish" class="interlanguage-link-target"><span>Español</span></a></li><li class="interlanguage-link interwiki-fa mw-list-item"><a href="https://fa.wikipedia.org/wiki/%D9%85%D8%B9%D8%A7%D9%85%D9%84%D8%A7%D8%AA_%D8%A7%D9%84%DA%AF%D9%88%D8%B1%DB%8C%D8%AA%D9%85%DB%8C" title="معاملات الگوریتمی – Persian" lang="fa" hreflang="fa" data-title="معاملات الگوریتمی" data-language-autonym="فارسی" data-language-local-name="Persian" class="interlanguage-link-target"><span>فارسی</span></a></li><li class="interlanguage-link interwiki-fr mw-list-item"><a href="https://fr.wikipedia.org/wiki/Trading_algorithmique" title="Trading algorithmique – French" lang="fr" hreflang="fr" data-title="Trading algorithmique" data-language-autonym="Français" data-language-local-name="French" class="interlanguage-link-target"><span>Français</span></a></li><li class="interlanguage-link interwiki-ko mw-list-item"><a href="https://ko.wikipedia.org/wiki/%EC%95%8C%EA%B3%A0%EB%A6%AC%EC%A6%98_%ED%8A%B8%EB%A0%88%EC%9D%B4%EB%94%A9" title="알고리즘 트레이딩 – Korean" lang="ko" hreflang="ko" data-title="알고리즘 트레이딩" data-language-autonym="한국어" data-language-local-name="Korean" class="interlanguage-link-target"><span>한국어</span></a></li><li class="interlanguage-link interwiki-hy mw-list-item"><a href="https://hy.wikipedia.org/wiki/%D4%B1%D5%AC%D5%A3%D5%B8%D6%80%D5%AB%D5%A9%D5%B4%D5%A1%D5%AF%D5%A1%D5%B6_%D5%A1%D5%BC%D6%87%D5%BF%D5%B8%D6%82%D6%80" title="Ալգորիթմական առևտուր – Armenian" lang="hy" hreflang="hy" data-title="Ալգորիթմական առևտուր" data-language-autonym="Հայերեն" data-language-local-name="Armenian" class="interlanguage-link-target"><span>Հայերեն</span></a></li><li class="interlanguage-link interwiki-he mw-list-item"><a href="https://he.wikipedia.org/wiki/%D7%9E%D7%A1%D7%97%D7%A8_%D7%90%D7%9C%D7%92%D7%95%D7%A8%D7%99%D7%AA%D7%9E%D7%99" title="מסחר אלגוריתמי – Hebrew" lang="he" hreflang="he" data-title="מסחר אלגוריתמי" data-language-autonym="עברית" data-language-local-name="Hebrew" class="interlanguage-link-target"><span>עברית</span></a></li><li class="interlanguage-link interwiki-lt mw-list-item"><a href="https://lt.wikipedia.org/wiki/Algoritmin%C4%97_prekyba" title="Algoritminė prekyba – Lithuanian" lang="lt" hreflang="lt" data-title="Algoritminė prekyba" data-language-autonym="Lietuvių" data-language-local-name="Lithuanian" class="interlanguage-link-target"><span>Lietuvių</span></a></li><li class="interlanguage-link interwiki-ja mw-list-item"><a href="https://ja.wikipedia.org/wiki/%E3%82%A2%E3%83%AB%E3%82%B4%E3%83%AA%E3%82%BA%E3%83%A0%E5%8F%96%E5%BC%95" title="アルゴリズム取引 – Japanese" lang="ja" hreflang="ja" data-title="アルゴリズム取引" data-language-autonym="日本語" data-language-local-name="Japanese" class="interlanguage-link-target"><span>日本語</span></a></li><li class="interlanguage-link interwiki-no mw-list-item"><a href="https://no.wikipedia.org/wiki/Algoritmehandel" title="Algoritmehandel – Norwegian Bokmål" lang="nb" hreflang="nb" data-title="Algoritmehandel" data-language-autonym="Norsk bokmål" data-language-local-name="Norwegian Bokmål" class="interlanguage-link-target"><span>Norsk bokmål</span></a></li><li class="interlanguage-link interwiki-pl mw-list-item"><a href="https://pl.wikipedia.org/wiki/Handel_algorytmiczny" title="Handel algorytmiczny – Polish" lang="pl" hreflang="pl" data-title="Handel algorytmiczny" data-language-autonym="Polski" data-language-local-name="Polish" class="interlanguage-link-target"><span>Polski</span></a></li><li class="interlanguage-link interwiki-pt mw-list-item"><a href="https://pt.wikipedia.org/wiki/Rob%C3%B4_de_investimento" title="Robô de investimento – Portuguese" lang="pt" hreflang="pt" data-title="Robô de investimento" data-language-autonym="Português" data-language-local-name="Portuguese" class="interlanguage-link-target"><span>Português</span></a></li><li class="interlanguage-link interwiki-ru mw-list-item"><a href="https://ru.wikipedia.org/wiki/%D0%90%D0%BB%D0%B3%D0%BE%D1%80%D0%B8%D1%82%D0%BC%D0%B8%D1%87%D0%B5%D1%81%D0%BA%D0%B0%D1%8F_%D1%82%D0%BE%D1%80%D0%B3%D0%BE%D0%B2%D0%BB%D1%8F" title="Алгоритмическая торговля – Russian" lang="ru" hreflang="ru" data-title="Алгоритмическая торговля" data-language-autonym="Русский" data-language-local-name="Russian" class="interlanguage-link-target"><span>Русский</span></a></li><li class="interlanguage-link interwiki-sr mw-list-item"><a href="https://sr.wikipedia.org/wiki/Algoritamsko_trgovanje" title="Algoritamsko trgovanje – Serbian" lang="sr" hreflang="sr" data-title="Algoritamsko trgovanje" data-language-autonym="Српски / srpski" data-language-local-name="Serbian" class="interlanguage-link-target"><span>Српски / srpski</span></a></li><li class="interlanguage-link interwiki-sh mw-list-item"><a href="https://sh.wikipedia.org/wiki/Algoritamsko_trgovanje" title="Algoritamsko trgovanje – Serbo-Croatian" lang="sh" hreflang="sh" data-title="Algoritamsko trgovanje" data-language-autonym="Srpskohrvatski / српскохрватски" data-language-local-name="Serbo-Croatian" class="interlanguage-link-target"><span>Srpskohrvatski / српскохрватски</span></a></li><li class="interlanguage-link interwiki-sv mw-list-item"><a href="https://sv.wikipedia.org/wiki/Algoritmisk_handel" title="Algoritmisk handel – Swedish" lang="sv" hreflang="sv" data-title="Algoritmisk handel" data-language-autonym="Svenska" data-language-local-name="Swedish" class="interlanguage-link-target"><span>Svenska</span></a></li><li class="interlanguage-link interwiki-ta mw-list-item"><a href="https://ta.wikipedia.org/wiki/%E0%AE%AA%E0%AE%9F%E0%AE%BF%E0%AE%AE%E0%AF%81%E0%AE%B1%E0%AF%88%E0%AE%AF%E0%AE%BF%E0%AE%AF%E0%AE%B2%E0%AF%8D_%E0%AE%B5%E0%AE%A3%E0%AE%BF%E0%AE%95%E0%AE%AE%E0%AF%8D" title="படிமுறையியல் வணிகம் – Tamil" lang="ta" hreflang="ta" data-title="படிமுறையியல் வணிகம்" data-language-autonym="தமிழ்" data-language-local-name="Tamil" class="interlanguage-link-target"><span>தமிழ்</span></a></li><li class="interlanguage-link interwiki-uk mw-list-item"><a href="https://uk.wikipedia.org/wiki/%D0%90%D0%BB%D0%B3%D0%BE%D1%80%D0%B8%D1%82%D0%BC%D1%96%D1%87%D0%BD%D0%B0_%D1%82%D0%BE%D1%80%D0%B3%D1%96%D0%B2%D0%BB%D1%8F" title="Алгоритмічна торгівля – Ukrainian" lang="uk" hreflang="uk" data-title="Алгоритмічна торгівля" data-language-autonym="Українська" data-language-local-name="Ukrainian" class="interlanguage-link-target"><span>Українська</span></a></li><li class="interlanguage-link interwiki-vi mw-list-item"><a href="https://vi.wikipedia.org/wiki/Giao_d%E1%BB%8Bch_thu%E1%BA%ADt_to%C3%A1n" title="Giao dịch thuật toán – Vietnamese" lang="vi" hreflang="vi" data-title="Giao dịch thuật toán" data-language-autonym="Tiếng Việt" data-language-local-name="Vietnamese" class="interlanguage-link-target"><span>Tiếng Việt</span></a></li><li class="interlanguage-link interwiki-zh mw-list-item"><a href="https://zh.wikipedia.org/wiki/%E7%AE%97%E6%B3%95%E4%BA%A4%E6%98%93" title="算法交易 – Chinese" lang="zh" hreflang="zh" data-title="算法交易" data-language-autonym="中文" data-language-local-name="Chinese" class="interlanguage-link-target"><span>中文</span></a></li>
			</ul>
			<div class="after-portlet after-portlet-lang"><span class="wb-langlinks-edit wb-langlinks-link"><a href="https://www.wikidata.org/wiki/Special:EntityPage/Q139445#sitelinks-wikipedia" title="Edit interlanguage links" class="wbc-editpage">Edit links</a></span></div>
		</div>

	</div>
</div>
</header>
				<div class="vector-page-toolbar vector-feature-custom-font-size-clientpref--excluded">
					<div class="vector-page-toolbar-container">
						<div id="left-navigation">
							<nav aria-label="Namespaces">
								
<div id="p-associated-pages" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-associated-pages"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-nstab-main" class="selected vector-tab-noicon mw-list-item"><a href="/wiki/Algorithmic_trading" title="View the content page [c]" accesskey="c"><span>Article</span></a></li><li id="ca-talk" class="vector-tab-noicon mw-list-item"><a href="/wiki/Talk:Algorithmic_trading" rel="discussion" title="Discuss improvements to the content page [t]" accesskey="t"><span>Talk</span></a></li>
		</ul>
		
	</div>
</div>

								
<div id="vector-variants-dropdown" class="vector-dropdown emptyPortlet"  >
	<input type="checkbox" id="vector-variants-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-variants-dropdown" class="vector-dropdown-checkbox " aria-label="Change language variant"   >
	<label id="vector-variants-dropdown-label" for="vector-variants-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">English</span>
	</label>
	<div class="vector-dropdown-content">


					
<div id="p-variants" class="vector-menu mw-portlet mw-portlet-variants emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

				
	</div>
</div>

							</nav>
						</div>
						<div id="right-navigation" class="vector-collapsible">
							<nav aria-label="Views">
								
<div id="p-views" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-views"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-view" class="selected vector-tab-noicon mw-list-item"><a href="/wiki/Algorithmic_trading"><span>Read</span></a></li><li id="ca-edit" class="vector-tab-noicon mw-list-item"><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-history" class="vector-tab-noicon mw-list-item"><a href="/w/index.php?title=Algorithmic_trading&amp;action=history" title="Past revisions of this page [h]" accesskey="h"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

							</nav>
				
							<nav class="vector-page-tools-landmark" aria-label="Page tools">
								
<div id="vector-page-tools-dropdown" class="vector-dropdown vector-page-tools-dropdown"  >
	<input type="checkbox" id="vector-page-tools-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-tools-dropdown" class="vector-dropdown-checkbox "  aria-label="Tools"  >
	<label id="vector-page-tools-dropdown-label" for="vector-page-tools-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">Tools</span>
	</label>
	<div class="vector-dropdown-content">


									<div id="vector-page-tools-unpinned-container" class="vector-unpinned-container">
						
<div id="vector-page-tools" class="vector-page-tools vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-page-tools-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="page-tools-pinned"
	data-pinnable-element-id="vector-page-tools"
	data-pinned-container-id="vector-page-tools-pinned-container"
	data-unpinned-container-id="vector-page-tools-unpinned-container"
>
	<div class="vector-pinnable-header-label">Tools</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-page-tools.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-page-tools.unpin">hide</button>
</div>

	
<div id="p-cactions" class="vector-menu mw-portlet mw-portlet-cactions emptyPortlet vector-has-collapsible-items"  title="More options" >
	<div class="vector-menu-heading">
		Actions
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-more-view" class="selected vector-more-collapsible-item mw-list-item"><a href="/wiki/Algorithmic_trading"><span>Read</span></a></li><li id="ca-more-edit" class="vector-more-collapsible-item mw-list-item"><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-more-history" class="vector-more-collapsible-item mw-list-item"><a href="/w/index.php?title=Algorithmic_trading&amp;action=history"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="vector-menu mw-portlet mw-portlet-tb"  >
	<div class="vector-menu-heading">
		General
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="t-whatlinkshere" class="mw-list-item"><a href="/wiki/Special:WhatLinksHere/Algorithmic_trading" title="List of all English Wikipedia pages containing links to this page [j]" accesskey="j"><span>What links here</span></a></li><li id="t-recentchangeslinked" class="mw-list-item"><a href="/wiki/Special:RecentChangesLinked/Algorithmic_trading" rel="nofollow" title="Recent changes in pages linked from this page [k]" accesskey="k"><span>Related changes</span></a></li><li id="t-upload" class="mw-list-item"><a href="//en.wikipedia.org/wiki/Wikipedia:File_Upload_Wizard" title="Upload files [u]" accesskey="u"><span>Upload file</span></a></li><li id="t-permalink" class="mw-list-item"><a href="/w/index.php?title=Algorithmic_trading&amp;oldid=1306548555" title="Permanent link to this revision of this page"><span>Permanent link</span></a></li><li id="t-info" class="mw-list-item"><a href="/w/index.php?title=Algorithmic_trading&amp;action=info" title="More information about this page"><span>Page information</span></a></li><li id="t-cite" class="mw-list-item"><a href="/w/index.php?title=Special:CiteThisPage&amp;page=Algorithmic_trading&amp;id=1306548555&amp;wpFormIdentifier=titleform" title="Information on how to cite this page"><span>Cite this page</span></a></li><li id="t-urlshortener" class="mw-list-item"><a href="/w/index.php?title=Special:UrlShortener&amp;url=https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FAlgorithmic_trading"><span>Get shortened URL</span></a></li><li id="t-urlshortener-qrcode" class="mw-list-item"><a href="/w/index.php?title=Special:QrCode&amp;url=https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FAlgorithmic_trading"><span>Download QR code</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-coll-print_export" class="vector-menu mw-portlet mw-portlet-coll-print_export"  >
	<div class="vector-menu-heading">
		Print/export
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="coll-download-as-rl" class="mw-list-item"><a href="/w/index.php?title=Special:DownloadAsPdf&amp;page=Algorithmic_trading&amp;action=show-download-screen" title="Download this page as a PDF file"><span>Download as PDF</span></a></li><li id="t-print" class="mw-list-item"><a href="/w/index.php?title=Algorithmic_trading&amp;printable=yes" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-wikibase-otherprojects" class="vector-menu mw-portlet mw-portlet-wikibase-otherprojects"  >
	<div class="vector-menu-heading">
		In other projects
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="t-wikibase" class="wb-otherproject-link wb-otherproject-wikibase-dataitem mw-list-item"><a href="https://www.wikidata.org/wiki/Special:EntityPage/Q139445" title="Structured data on this page hosted by Wikidata [g]" accesskey="g"><span>Wikidata item</span></a></li>
		</ul>
		
	</div>
</div>

</div>

									</div>
				
	</div>
</div>

							</nav>
						</div>
					</div>
				</div>
				<div class="vector-column-end no-font-mode-scale">
					<div class="vector-sticky-pinned-container">
						<nav class="vector-page-tools-landmark" aria-label="Page tools">
							<div id="vector-page-tools-pinned-container" class="vector-pinned-container">
				
							</div>
		</nav>
						<nav class="vector-appearance-landmark" aria-label="Appearance">
							<div id="vector-appearance-pinned-container" class="vector-pinned-container">
				<div id="vector-appearance" class="vector-appearance vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-appearance-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="appearance-pinned"
	data-pinnable-element-id="vector-appearance"
	data-pinned-container-id="vector-appearance-pinned-container"
	data-unpinned-container-id="vector-appearance-unpinned-container"
>
	<div class="vector-pinnable-header-label">Appearance</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-appearance.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-appearance.unpin">hide</button>
</div>


</div>

							</div>
		</nav>
					</div>
				</div>
				<div id="bodyContent" class="vector-body" aria-labelledby="firstHeading" data-mw-ve-target-container>
					<div class="vector-body-before-content">
							<div class="mw-indicators">
		</div>

						<div id="siteSub" class="noprint">From Wikipedia, the free encyclopedia</div>
					</div>
					<div id="contentSub"><div id="mw-content-subtitle"></div></div>
					
					
					<div id="mw-content-text" class="mw-body-content"><div class="mw-content-ltr mw-parser-output" lang="en" dir="ltr"><div class="shortdescription nomobile noexcerpt noprint searchaux" style="display:none">Method of executing orders</div>
<style data-mw-deduplicate="TemplateStyles:r1236090951">.mw-parser-output .hatnote{font-style:italic}.mw-parser-output div.hatnote{padding-left:1.6em;margin-bottom:0.5em}.mw-parser-output .hatnote i{font-style:normal}.mw-parser-output .hatnote+link+.hatnote{margin-top:-0.5em}@media print{body.ns-0 .mw-parser-output .hatnote{display:none!important}}</style><div role="note" class="hatnote navigation-not-searchable">For trading using algorithms, see <a href="/wiki/Automated_trading_system" title="Automated trading system">automated trading system</a>.</div>
<p class="mw-empty-elt">
</p>
<style data-mw-deduplicate="TemplateStyles:r1129693374">.mw-parser-output .hlist dl,.mw-parser-output .hlist ol,.mw-parser-output .hlist ul{margin:0;padding:0}.mw-parser-output .hlist dd,.mw-parser-output .hlist dt,.mw-parser-output .hlist li{margin:0;display:inline}.mw-parser-output .hlist.inline,.mw-parser-output .hlist.inline dl,.mw-parser-output .hlist.inline ol,.mw-parser-output .hlist.inline ul,.mw-parser-output .hlist dl dl,.mw-parser-output .hlist dl ol,.mw-parser-output .hlist dl ul,.mw-parser-output .hlist ol dl,.mw-parser-output .hlist ol ol,.mw-parser-output .hlist ol ul,.mw-parser-output .hlist ul dl,.mw-parser-output .hlist ul ol,.mw-parser-output .hlist ul ul{display:inline}.mw-parser-output .hlist .mw-empty-li{display:none}.mw-parser-output .hlist dt::after{content:": "}.mw-parser-output .hlist dd::after,.mw-parser-output .hlist li::after{content:" · ";font-weight:bold}.mw-parser-output .hlist dd:last-child::after,.mw-parser-output .hlist dt:last-child::after,.mw-parser-output .hlist li:last-child::after{content:none}.mw-parser-output .hlist dd dd:first-child::before,.mw-parser-output .hlist dd dt:first-child::before,.mw-parser-output .hlist dd li:first-child::before,.mw-parser-output .hlist dt dd:first-child::before,.mw-parser-output .hlist dt dt:first-child::before,.mw-parser-output .hlist dt li:first-child::before,.mw-parser-output .hlist li dd:first-child::before,.mw-parser-output .hlist li dt:first-child::before,.mw-parser-output .hlist li li:first-child::before{content:" (";font-weight:normal}.mw-parser-output .hlist dd dd:last-child::after,.mw-parser-output .hlist dd dt:last-child::after,.mw-parser-output .hlist dd li:last-child::after,.mw-parser-output .hlist dt dd:last-child::after,.mw-parser-output .hlist dt dt:last-child::after,.mw-parser-output .hlist dt li:last-child::after,.mw-parser-output .hlist li dd:last-child::after,.mw-parser-output .hlist li dt:last-child::after,.mw-parser-output .hlist li li:last-child::after{content:")";font-weight:normal}.mw-parser-output .hlist ol{counter-reset:listitem}.mw-parser-output .hlist ol>li{counter-increment:listitem}.mw-parser-output .hlist ol>li::before{content:" "counter(listitem)"\a0 "}.mw-parser-output .hlist dd ol>li:first-child::before,.mw-parser-output .hlist dt ol>li:first-child::before,.mw-parser-output .hlist li ol>li:first-child::before{content:" ("counter(listitem)"\a0 "}</style><style data-mw-deduplicate="TemplateStyles:r1246091330">.mw-parser-output .sidebar{width:22em;float:right;clear:right;margin:0.5em 0 1em 1em;background:var(--background-color-neutral-subtle,#f8f9fa);border:1px solid var(--border-color-base,#a2a9b1);padding:0.2em;text-align:center;line-height:1.4em;font-size:88%;border-collapse:collapse;display:table}body.skin-minerva .mw-parser-output .sidebar{display:table!important;float:right!important;margin:0.5em 0 1em 1em!important}.mw-parser-output .sidebar-subgroup{width:100%;margin:0;border-spacing:0}.mw-parser-output .sidebar-left{float:left;clear:left;margin:0.5em 1em 1em 0}.mw-parser-output .sidebar-none{float:none;clear:both;margin:0.5em 1em 1em 0}.mw-parser-output .sidebar-outer-title{padding:0 0.4em 0.2em;font-size:125%;line-height:1.2em;font-weight:bold}.mw-parser-output .sidebar-top-image{padding:0.4em}.mw-parser-output .sidebar-top-caption,.mw-parser-output .sidebar-pretitle-with-top-image,.mw-parser-output .sidebar-caption{padding:0.2em 0.4em 0;line-height:1.2em}.mw-parser-output .sidebar-pretitle{padding:0.4em 0.4em 0;line-height:1.2em}.mw-parser-output .sidebar-title,.mw-parser-output .sidebar-title-with-pretitle{padding:0.2em 0.8em;font-size:145%;line-height:1.2em}.mw-parser-output .sidebar-title-with-pretitle{padding:0.1em 0.4em}.mw-parser-output .sidebar-image{padding:0.2em 0.4em 0.4em}.mw-parser-output .sidebar-heading{padding:0.1em 0.4em}.mw-parser-output .sidebar-content{padding:0 0.5em 0.4em}.mw-parser-output .sidebar-content-with-subgroup{padding:0.1em 0.4em 0.2em}.mw-parser-output .sidebar-above,.mw-parser-output .sidebar-below{padding:0.3em 0.8em;font-weight:bold}.mw-parser-output .sidebar-collapse .sidebar-above,.mw-parser-output .sidebar-collapse .sidebar-below{border-top:1px solid #aaa;border-bottom:1px solid #aaa}.mw-parser-output .sidebar-navbar{text-align:right;font-size:115%;padding:0 0.4em 0.4em}.mw-parser-output .sidebar-list-title{padding:0 0.4em;text-align:left;font-weight:bold;line-height:1.6em;font-size:105%}.mw-parser-output .sidebar-list-title-c{padding:0 0.4em;text-align:center;margin:0 3.3em}@media(max-width:640px){body.mediawiki .mw-parser-output .sidebar{width:100%!important;clear:both;float:none!important;margin-left:0!important;margin-right:0!important}}body.skin--responsive .mw-parser-output .sidebar a>img{max-width:none!important}@media screen{html.skin-theme-clientpref-night .mw-parser-output .sidebar:not(.notheme) .sidebar-list-title,html.skin-theme-clientpref-night .mw-parser-output .sidebar:not(.notheme) .sidebar-title-with-pretitle{background:transparent!important}html.skin-theme-clientpref-night .mw-parser-output .sidebar:not(.notheme) .sidebar-title-with-pretitle a{color:var(--color-progressive)!important}}@media screen and (prefers-color-scheme:dark){html.skin-theme-clientpref-os .mw-parser-output .sidebar:not(.notheme) .sidebar-list-title,html.skin-theme-clientpref-os .mw-parser-output .sidebar:not(.notheme) .sidebar-title-with-pretitle{background:transparent!important}html.skin-theme-clientpref-os .mw-parser-output .sidebar:not(.notheme) .sidebar-title-with-pretitle a{color:var(--color-progressive)!important}}@media print{body.ns-0 .mw-parser-output .sidebar{display:none!important}}</style><table class="sidebar nomobile nowraplinks hlist"><tbody><tr><th class="sidebar-title"><a href="/wiki/Financial_market_participants" title="Financial market participants">Financial market participants</a></th></tr><tr><td class="sidebar-image"><span typeof="mw:File"><a href="/wiki/File:Assorted_United_States_coins.jpg" class="mw-file-description"><img src="//upload.wikimedia.org/wikipedia/commons/thumb/5/5e/Assorted_United_States_coins.jpg/250px-Assorted_United_States_coins.jpg" decoding="async" width="160" height="120" class="mw-file-element" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/5/5e/Assorted_United_States_coins.jpg/330px-Assorted_United_States_coins.jpg 2x" data-file-width="2816" data-file-height="2112" /></a></span></td></tr><tr><th class="sidebar-heading">
Organisations</th></tr><tr><td class="sidebar-content">
<ul><li><a href="/wiki/Credit_union" title="Credit union">Credit unions</a></li>
<li><a href="/wiki/Development_finance_institution" title="Development finance institution">Development finance institution</a></li>
<li><a href="/wiki/Insurance" title="Insurance">Insurance companies</a></li>
<li><a href="/wiki/Investment_banking" title="Investment banking">Investment banks</a></li>
<li><a href="/wiki/Investment_fund" title="Investment fund">Investment funds</a></li>
<li><a href="/wiki/Pension_fund" title="Pension fund">Pension funds</a></li>
<li><a href="/wiki/Prime_brokerage" title="Prime brokerage">Prime brokers</a></li>
<li><a href="/wiki/Trust_company" title="Trust company">Trusts</a></li></ul></td>
</tr><tr><th class="sidebar-heading">
Terms</th></tr><tr><td class="sidebar-content">
<ul><li><a href="/wiki/Angel_investor" title="Angel investor">Angel investor</a></li>
<li><a href="/wiki/Bull_(stock_market_speculator)" title="Bull (stock market speculator)">Bull (stock market speculator)</a></li>
<li><a href="/wiki/Finance" title="Finance">Finance</a></li>
<li><a href="/wiki/Financial_market" title="Financial market">Financial market</a></li>
<li><a href="/wiki/Financial_market_participants" title="Financial market participants">Participants</a></li>
<li><a href="/wiki/Corporate_finance" title="Corporate finance">Corporate finance</a></li>
<li><a href="/wiki/Personal_finance" title="Personal finance">Personal finance</a></li>
<li><a href="/wiki/Public_finance" title="Public finance">Public finance</a></li>
<li><a href="/wiki/Bank" title="Bank">Banks and banking</a></li>
<li><a href="/wiki/Financial_analyst" title="Financial analyst">Financial analyst</a></li>
<li><a href="/wiki/Financial_planner" title="Financial planner">Financial planner</a></li>
<li><a href="/wiki/Financial_regulation" title="Financial regulation">Financial regulation</a></li>
<li><a href="/wiki/Fund_governance" title="Fund governance">Fund governance</a></li>
<li><a href="/wiki/Stock_Market" class="mw-redirect" title="Stock Market">Stock Market</a></li>
<li><a href="/wiki/Super_angel" title="Super angel">Super angel</a></li></ul></td>
</tr></tbody></table>
<p><b>Algorithmic trading</b> is a method of executing orders using automated pre-programmed trading instructions accounting for variables such as time, price, and volume.<sup id="cite_ref-1" class="reference"><a href="#cite_note-1"><span class="cite-bracket">&#91;</span>1<span class="cite-bracket">&#93;</span></a></sup> This type of trading attempts to leverage the speed and computational resources of computers relative to human traders. In the twenty-first century, algorithmic trading has been gaining traction with both retail and institutional traders.<sup id="cite_ref-economist.com_2-0" class="reference"><a href="#cite_note-economist.com-2"><span class="cite-bracket">&#91;</span>2<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-3" class="reference"><a href="#cite_note-3"><span class="cite-bracket">&#91;</span>3<span class="cite-bracket">&#93;</span></a></sup> A study in 2019 showed that around 92% of trading in the Forex market was performed by trading algorithms rather than humans.<sup id="cite_ref-4" class="reference"><a href="#cite_note-4"><span class="cite-bracket">&#91;</span>4<span class="cite-bracket">&#93;</span></a></sup> 
</p><p>It is widely used by <a href="/wiki/Investment_bank" class="mw-redirect" title="Investment bank">investment banks</a>, <a href="/wiki/Pension_fund" title="Pension fund">pension funds</a>, <a href="/wiki/Mutual_fund" title="Mutual fund">mutual funds</a>, and <a href="/wiki/Hedge_fund" title="Hedge fund">hedge funds</a> that may need to spread out the execution of a larger order or perform trades too fast for human traders to react to. However, it is also available to private traders using simple retail tools. Algorithmic trading is widely used in equities, futures, crypto and foreign exchange markets.
</p><p>The term algorithmic trading is often used synonymously with <a href="/wiki/Automated_trading_system" title="Automated trading system">automated trading system</a>. These encompass a variety of <a href="/wiki/Trading_strategy" title="Trading strategy">trading strategies</a>, some of which are based on formulas and results from <a href="/wiki/Mathematical_finance" title="Mathematical finance">mathematical finance</a>, and often rely on specialized software.<sup id="cite_ref-5" class="reference"><a href="#cite_note-5"><span class="cite-bracket">&#91;</span>5<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-6" class="reference"><a href="#cite_note-6"><span class="cite-bracket">&#91;</span>6<span class="cite-bracket">&#93;</span></a></sup>
</p><p>Examples of strategies used in algorithmic trading include <a href="/wiki/Systematic_trading" title="Systematic trading">systematic trading</a>, <a href="/wiki/Market_maker" title="Market maker">market making</a>, inter-market spreading, <a href="/wiki/Arbitrage" title="Arbitrage">arbitrage</a>, or pure <a href="/wiki/Speculation" title="Speculation">speculation</a>, such as <a href="/wiki/Trend_following" title="Trend following">trend following</a>. Many fall into the category of <a href="/wiki/High-frequency_trading" title="High-frequency trading">high-frequency trading</a> (HFT), which is characterized by high turnover and high order-to-trade ratios.<sup id="cite_ref-ReferenceA_7-0" class="reference"><a href="#cite_note-ReferenceA-7"><span class="cite-bracket">&#91;</span>7<span class="cite-bracket">&#93;</span></a></sup> HFT strategies utilize computers that make elaborate decisions to initiate orders based on information that is received electronically, before human traders are capable of processing the information they observe. As a result, in February 2013, the <a href="/wiki/Commodity_Futures_Trading_Commission" title="Commodity Futures Trading Commission">Commodity Futures Trading Commission</a> (CFTC) formed a special working group that included academics and industry experts to advise the CFTC on how best to define HFT.<sup id="cite_ref-8" class="reference"><a href="#cite_note-8"><span class="cite-bracket">&#91;</span>8<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-9" class="reference"><a href="#cite_note-9"><span class="cite-bracket">&#91;</span>9<span class="cite-bracket">&#93;</span></a></sup>  Algorithmic trading and HFT have resulted in a dramatic change of the <a href="/wiki/Market_microstructure" title="Market microstructure">market microstructure</a> and in the complexity and uncertainty of the market macrodynamic,<sup id="cite_ref-HilbertDarmon2_10-0" class="reference"><a href="#cite_note-HilbertDarmon2-10"><span class="cite-bracket">&#91;</span>10<span class="cite-bracket">&#93;</span></a></sup> particularly in the way <a href="/wiki/Market_liquidity" title="Market liquidity">liquidity</a> is provided.<sup id="cite_ref-toxic_11-0" class="reference"><a href="#cite_note-toxic-11"><span class="cite-bracket">&#91;</span>11<span class="cite-bracket">&#93;</span></a></sup>
</p>
<meta property="mw:PageProp/toc" />
<div class="mw-heading mw-heading2"><h2 id="Machine_Learning_Integration">Machine Learning Integration</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=1" title="Edit section: Machine Learning Integration"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Before machine learning, the early stage of algorithmic trading consisted of pre-programmed rules designed to respond to that market's specific condition. Traders and developers coded instructions based on technical indicators - such as <a href="/wiki/Relative_strength_index" title="Relative strength index">relative strength index</a>, <a href="/wiki/Moving_average" title="Moving average">moving averages</a> - to automate long or short orders. A significant pivotal shift in algorithmic trading as machine learning was adopted. Specifically <a href="/wiki/Deep_reinforcement_learning" title="Deep reinforcement learning">deep reinforcement learning (DRL)</a> which allows systems to dynamically adapt to its current market conditions. Unlike previous models, DRL uses simulations to train algorithms. Enabling them to learn and optimize its algorithm iteratively. A 2022 study by Ansari et al., showed that DRL framework “learns adaptive policies by balancing risks and reward, excelling in volatile conditions where static systems falter”. This self-adapting capability allows algorithms to market shifts, offering a significant edge over traditional algorithmic trading.<sup id="cite_ref-12" class="reference"><a href="#cite_note-12"><span class="cite-bracket">&#91;</span>12<span class="cite-bracket">&#93;</span></a></sup>
</p><p>Complementing DRL, <a href="/wiki/Directional-change_intrinsic_time" title="Directional-change intrinsic time">directional change</a> (DC) algorithms represent another advancement on core market events rather than fixed time intervals. A 2023 study by Adegboye, Kampouridis, and Otero explains that “DC algorithms detect subtle trend transitions, improving trade timing and profitability in turbulent markets”. DC algorithms detect subtle trend transitions such as uptrend, reversals, improving trade timing and profitability in volatile markets. This approach specifically captures the natural flow of market movement from higher high to lows.<sup id="cite_ref-13" class="reference"><a href="#cite_note-13"><span class="cite-bracket">&#91;</span>13<span class="cite-bracket">&#93;</span></a></sup>
</p><p>In practice, the DC algorithm works by defining two trends: upwards or downwards, which are triggered when a price moves beyond a certain threshold followed by a confirmation period(overshoot). This algorithm structure allows traders to pinpoint the stabilization of trends with higher accuracy. DC aligns trades with volatile, unstable market rhythms. By aligning trades with basic market rhythms, DC enhances precision, especially in volatile markets where traditional algorithms tend to misjudge their momentum due to fixed-interval data.
</p>
<div class="mw-heading mw-heading3"><h3 id="Ethical_Implications_and_Fairness">Ethical Implications and Fairness</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=2" title="Edit section: Ethical Implications and Fairness"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>The technical advancement of algorithmic trading comes with profound ethical challenges concerning fairness and market equity. The key concern is the unequal access to this technology. <a href="/wiki/High-frequency_trading" title="High-frequency trading">High-frequency trading</a>, one of the leading forms of algorithmic trading, reliant on ultra-fast networks, co-located servers and live data feeds which is only available to large institutions such as <a href="/wiki/Hedge_fund" title="Hedge fund">hedge funds</a>, <a href="/wiki/Investment_banking" title="Investment banking">investment banks</a> and other <a href="/wiki/Financial_institution" title="Financial institution">financial institutions</a>. This access creates a gap amongst the participants in the market, where retail traders are unable to match the speed and the precision of these systems.
</p><p>Aside from the inequality this system brings, another issue revolves around the potential of market manipulation. These algorithms can execute trades such as placing and cancelling orders rapidly to mislead other participants. An event to demonstrate such effects is the <a href="/wiki/2010_flash_crash" title="2010 flash crash">2010 flash crash</a>. This crash had occurred due to algorithmic activity before partially recovering. Executing at such high speeds beyond human oversight and thinking, these systems blur the lines of accountability. When these crashes occur, it is unclear who bears the responsibility: the developers, institutes using them or the regulators.
</p><p>With these systems in place, it can increase market volatility, often leaving retail traders vulnerable to sudden price swings where they lack the certain tools to navigate. Some argue this concentrates wealth among a handful of powerful firms, potentially widening the <a href="/wiki/Economic_gap" class="mw-redirect" title="Economic gap">economic gaps</a>.<sup id="cite_ref-14" class="reference"><a href="#cite_note-14"><span class="cite-bracket">&#91;</span>14<span class="cite-bracket">&#93;</span></a></sup> An example would be individuals or firms with the necessary resources gain profits by executing rapid trades sidelining smaller traders. On the contrary, it has its own benefits as well which are claimed to boost market liquidity and cut transaction costs.<sup id="cite_ref-15" class="reference"><a href="#cite_note-15"><span class="cite-bracket">&#91;</span>15<span class="cite-bracket">&#93;</span></a></sup> This creates an ethical tug of war: does the pursuit of an efficient market outweigh the risk of entrenching inequality?
</p><p><a href="/wiki/European_Union" title="European Union">European Union</a> efforts to address these concerns lead to regulatory action. These rules mandate rigorous testing of algorithmic trading and require firms to report significant disruptions.<sup id="cite_ref-16" class="reference"><a href="#cite_note-16"><span class="cite-bracket">&#91;</span>16<span class="cite-bracket">&#93;</span></a></sup>.This approach aims to minimize the manipulation and enhance oversight, but enforcement is a challenge. As time goes on, algorithmic trading evolves, whereas the ethical stakes grow higher.
</p>
<div class="mw-heading mw-heading2"><h2 id="History">History</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=3" title="Edit section: History"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<div class="mw-heading mw-heading3"><h3 id="Early_developments">Early developments</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=4" title="Edit section: Early developments"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Computerization of the order flow in financial markets began in the early 1970s, when the <a href="/wiki/New_York_Stock_Exchange" title="New York Stock Exchange">New York Stock Exchange</a> introduced the "designated order turnaround" system (DOT). <a href="/wiki/SuperDOT" class="mw-redirect" title="SuperDOT">SuperDOT</a> was introduced in 1984 as an upgraded version of DOT.  Both systems allowed for the routing of orders electronically to the proper trading post. The "opening automated reporting system" (OARS) aided the specialist in determining the <a href="/wiki/Market_clearing" title="Market clearing">market clearing</a> opening price (SOR; Smart Order Routing).
</p><p>With the rise of fully electronic markets came the introduction of <a href="/wiki/Program_trading" title="Program trading">program trading</a>, which is defined by the New York Stock Exchange as an order to buy or sell 15 or more stocks valued at over US$1 million total. In practice, program trades were pre-programmed to automatically enter or exit trades based on various factors.<sup id="cite_ref-:0_17-0" class="reference"><a href="#cite_note-:0-17"><span class="cite-bracket">&#91;</span>17<span class="cite-bracket">&#93;</span></a></sup> In the 1980s, program trading became widely used in trading between the S&amp;P 500 <a href="/wiki/Stock" title="Stock">equity</a> and <a href="/wiki/Futures_contract" title="Futures contract">futures</a> markets in a strategy known as index arbitrage.
</p><p>At about the same time, <a href="/wiki/Constant_proportion_portfolio_insurance" title="Constant proportion portfolio insurance">portfolio insurance</a> was designed to create a synthetic <a href="/wiki/Put_option" title="Put option">put option</a> on a stock portfolio by dynamically trading stock index futures according to a computer model based on the <a href="/wiki/Black%E2%80%93Scholes" class="mw-redirect" title="Black–Scholes">Black–Scholes</a> option pricing model.
</p><p>Both strategies, often simply lumped together as "program trading", were blamed by many people (for example by the <a href="/wiki/Nicholas_F._Brady" title="Nicholas F. Brady">Brady report</a>) for exacerbating or even starting the <a href="/wiki/Black_Monday_(1987)#Causes" title="Black Monday (1987)">1987 stock market crash</a>. Yet the impact of computer driven trading on stock market crashes is unclear and widely discussed in the academic community.<sup id="cite_ref-18" class="reference"><a href="#cite_note-18"><span class="cite-bracket">&#91;</span>18<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading3"><h3 id="Refinement_and_growth">Refinement and growth</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=5" title="Edit section: Refinement and growth"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>The financial landscape was changed again with the emergence of <a href="/wiki/Electronic_communication_network" title="Electronic communication network">electronic communication networks</a> (ECNs) in the 1990s, which allowed for trading of stock and currencies outside of traditional exchanges.<sup id="cite_ref-:0_17-1" class="reference"><a href="#cite_note-:0-17"><span class="cite-bracket">&#91;</span>17<span class="cite-bracket">&#93;</span></a></sup> In the U.S., <a href="/wiki/Decimalization" class="mw-redirect" title="Decimalization">decimalization</a> changed the minimum tick size from 1/16 of a dollar (US$0.0625)<sup id="cite_ref-21" class="reference"><a href="#cite_note-21"><span class="cite-bracket">&#91;</span>a<span class="cite-bracket">&#93;</span></a></sup> to US$0.01 per share in 2001, and may have encouraged algorithmic trading as it changed the <a href="/wiki/Market_microstructure" title="Market microstructure">market microstructure</a> by permitting smaller differences between the <a href="/wiki/Bid%E2%80%93ask_spread" title="Bid–ask spread">bid and offer prices</a>, decreasing the <a href="/wiki/Market-maker" class="mw-redirect" title="Market-maker">market-makers</a>' trading advantage, thus increasing <a href="/wiki/Market_liquidity" title="Market liquidity">market liquidity</a>.<sup id="cite_ref-22" class="reference"><a href="#cite_note-22"><span class="cite-bracket">&#91;</span>21<span class="cite-bracket">&#93;</span></a></sup>
</p><p>This increased market liquidity led to institutional traders splitting up orders according to computer algorithms so they could execute orders at a better average price. These average price benchmarks are measured and calculated by computers by applying the <a href="/wiki/Time-weighted_average_price" title="Time-weighted average price">time-weighted average price</a> or more usually by the <a href="/wiki/Volume-weighted_average_price" title="Volume-weighted average price">volume-weighted average price</a>.
</p>
<style data-mw-deduplicate="TemplateStyles:r1224211176">.mw-parser-output .quotebox{background-color:#F9F9F9;border:1px solid #aaa;box-sizing:border-box;padding:10px;font-size:88%;max-width:100%}.mw-parser-output .quotebox.floatleft{margin:.5em 1.4em .8em 0}.mw-parser-output .quotebox.floatright{margin:.5em 0 .8em 1.4em}.mw-parser-output .quotebox.centered{overflow:hidden;position:relative;margin:.5em auto .8em auto}.mw-parser-output .quotebox.floatleft span,.mw-parser-output .quotebox.floatright span{font-style:inherit}.mw-parser-output .quotebox>blockquote{margin:0;padding:0;border-left:0;font-family:inherit;font-size:inherit}.mw-parser-output .quotebox-title{text-align:center;font-size:110%;font-weight:bold}.mw-parser-output .quotebox-quote>:first-child{margin-top:0}.mw-parser-output .quotebox-quote:last-child>:last-child{margin-bottom:0}.mw-parser-output .quotebox-quote.quoted:before{font-family:"Times New Roman",serif;font-weight:bold;font-size:large;color:gray;content:" “ ";vertical-align:-45%;line-height:0}.mw-parser-output .quotebox-quote.quoted:after{font-family:"Times New Roman",serif;font-weight:bold;font-size:large;color:gray;content:" ” ";line-height:0}.mw-parser-output .quotebox .left-aligned{text-align:left}.mw-parser-output .quotebox .right-aligned{text-align:right}.mw-parser-output .quotebox .center-aligned{text-align:center}.mw-parser-output .quotebox .quote-title,.mw-parser-output .quotebox .quotebox-quote{display:block}.mw-parser-output .quotebox cite{display:block;font-style:normal}@media screen and (max-width:640px){.mw-parser-output .quotebox{width:100%!important;margin:0 0 .8em!important;float:none!important}}</style><div class="quotebox pullquote floatright" style="width:250px; ; color: #202122;background-color: #c6dbf7;">
<blockquote class="quotebox-quote left-aligned" style="">
<p>It is over. The trading that existed down the centuries has died. We have an electronic market today. It is the present. It is the future.
</p>
</blockquote>
<div style="padding-bottom: 0; padding-top: 0.5em"><cite class="left-aligned" style=""><a href="/wiki/Robert_Greifeld" title="Robert Greifeld">Robert Greifeld</a>, <a href="/wiki/NASDAQ" class="mw-redirect" title="NASDAQ">NASDAQ</a> CEO, April 2011<sup id="cite_ref-23" class="reference"><a href="#cite_note-23"><span class="cite-bracket">&#91;</span>22<span class="cite-bracket">&#93;</span></a></sup></cite></div>
</div>
<p>A further encouragement for the adoption of algorithmic trading in the financial markets came in 2001 when a team of <a href="/wiki/IBM" title="IBM">IBM</a> researchers published a paper<sup id="cite_ref-24" class="reference"><a href="#cite_note-24"><span class="cite-bracket">&#91;</span>23<span class="cite-bracket">&#93;</span></a></sup> at the <a href="/wiki/International_Joint_Conference_on_Artificial_Intelligence" title="International Joint Conference on Artificial Intelligence">International Joint Conference on Artificial Intelligence</a> where they showed that in experimental laboratory versions of the electronic auctions used in the financial markets, two algorithmic strategies (IBM's own <i>MGD</i>, and <a href="/wiki/Hewlett-Packard" title="Hewlett-Packard">Hewlett-Packard</a>'s <i>ZIP</i>) could consistently out-perform human traders. <i>MGD</i> was a modified version of the "GD" algorithm invented by Steven Gjerstad &amp; John Dickhaut in 1996/7;<sup id="cite_ref-25" class="reference"><a href="#cite_note-25"><span class="cite-bracket">&#91;</span>24<span class="cite-bracket">&#93;</span></a></sup> the <i>ZIP</i> algorithm had been invented at HP by <a href="/wiki/Dave_Cliff_(professor)" class="mw-redirect" title="Dave Cliff (professor)">Dave Cliff (professor)</a> in 1996.<sup id="cite_ref-26" class="reference"><a href="#cite_note-26"><span class="cite-bracket">&#91;</span>25<span class="cite-bracket">&#93;</span></a></sup> In their paper, the IBM team wrote that the financial impact of their results showing MGD and ZIP outperforming human traders "...might be measured in billions of dollars annually"; the IBM paper generated international media coverage.
</p><p>In 2005, the Regulation National Market System was put in place by the SEC to strengthen the equity market.<sup id="cite_ref-:0_17-2" class="reference"><a href="#cite_note-:0-17"><span class="cite-bracket">&#91;</span>17<span class="cite-bracket">&#93;</span></a></sup> This changed the way firms traded with rules such as the Trade Through Rule, which mandates that market orders must be posted and executed electronically at the best available price, thus preventing brokerages from profiting from the price differences when matching buy and sell orders.<sup id="cite_ref-:0_17-3" class="reference"><a href="#cite_note-:0-17"><span class="cite-bracket">&#91;</span>17<span class="cite-bracket">&#93;</span></a></sup>
</p><p>As more electronic markets opened, other algorithmic trading strategies were introduced. These strategies are more easily implemented by computers, as they can react rapidly to price changes and observe several markets simultaneously.
</p><p>Many broker-dealers offered algorithmic trading strategies to their clients – differentiating them by behavior, options and branding. Examples include Chameleon (developed by <a href="/wiki/BNP_Paribas" title="BNP Paribas">BNP Paribas</a>), Stealth<sup id="cite_ref-27" class="reference"><a href="#cite_note-27"><span class="cite-bracket">&#91;</span>26<span class="cite-bracket">&#93;</span></a></sup> (developed by the <i><a href="/wiki/Deutsche_Bank" title="Deutsche Bank">Deutsche Bank</a></i>), Sniper and Guerilla (developed by <i><a href="/wiki/Credit_Suisse" title="Credit Suisse">Credit Suisse</a></i>).<sup id="cite_ref-28" class="reference"><a href="#cite_note-28"><span class="cite-bracket">&#91;</span>27<span class="cite-bracket">&#93;</span></a></sup> These implementations adopted practices from the investing approaches of <a href="/wiki/Arbitrage" title="Arbitrage">arbitrage</a>, <a href="/wiki/Statistical_arbitrage" title="Statistical arbitrage">statistical arbitrage</a>, <a href="/wiki/Trend_following" title="Trend following">trend following</a>, and <a href="/wiki/Mean_reversion_(finance)" title="Mean reversion (finance)">mean reversion</a>.
</p><p>In modern global financial markets, algorithmic trading plays a crucial role in achieving financial objectives.<sup id="cite_ref-29" class="reference"><a href="#cite_note-29"><span class="cite-bracket">&#91;</span>28<span class="cite-bracket">&#93;</span></a></sup> For nearly 30 years, traders, investment banks, investment funds, and other financial entities have utilized algorithms to refine and implement trading strategies.<sup id="cite_ref-30" class="reference"><a href="#cite_note-30"><span class="cite-bracket">&#91;</span>29<span class="cite-bracket">&#93;</span></a></sup> The use of algorithms in financial markets has grown substantially since the mid-1990s, although the exact contribution to daily trading volumes remains imprecise.<sup id="cite_ref-31" class="reference"><a href="#cite_note-31"><span class="cite-bracket">&#91;</span>30<span class="cite-bracket">&#93;</span></a></sup>
</p><p>Technological advancements and algorithmic trading have facilitated increased transaction volumes, reduced costs, improved portfolio performance, and enhanced transparency in financial markets.<sup id="cite_ref-32" class="reference"><a href="#cite_note-32"><span class="cite-bracket">&#91;</span>31<span class="cite-bracket">&#93;</span></a></sup> According to the Foreign Exchange Activity in April 2019 report, foreign exchange markets had a daily turnover of US$6.6 trillion, a significant increase from US$5.1 trillion in 2016.<sup id="cite_ref-33" class="reference"><a href="#cite_note-33"><span class="cite-bracket">&#91;</span>32<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading3"><h3 id="Case_studies">Case studies</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=6" title="Edit section: Case studies"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Profitability projections by the TABB Group, a financial services industry research firm, for the US equities HFT industry were US$1.3 <a href="/wiki/1000000000_(number)" class="mw-redirect" title="1000000000 (number)">billion</a> before expenses for 2014,<sup id="cite_ref-34" class="reference"><a href="#cite_note-34"><span class="cite-bracket">&#91;</span>33<span class="cite-bracket">&#93;</span></a></sup> significantly down on the maximum of US$21 <a href="/wiki/1000000000_(number)" class="mw-redirect" title="1000000000 (number)">billion</a> that the 300 securities firms and hedge funds that then specialized in this type of trading took in profits in 2008,<sup id="cite_ref-35" class="reference"><a href="#cite_note-35"><span class="cite-bracket">&#91;</span>34<span class="cite-bracket">&#93;</span></a></sup> which the authors had then called "relatively small" and "surprisingly modest" when compared to the market's overall trading volume. In March 2014, <a href="/wiki/Virtu_Financial" title="Virtu Financial">Virtu Financial</a>, a high-frequency trading firm, reported that during five years the firm as a whole was profitable on 1,277 out of 1,278 trading days,<sup id="cite_ref-36" class="reference"><a href="#cite_note-36"><span class="cite-bracket">&#91;</span>35<span class="cite-bracket">&#93;</span></a></sup> losing money just one day, demonstrating the benefits of trading millions of times, across a diverse set of instruments every trading day.<sup id="cite_ref-37" class="reference"><a href="#cite_note-37"><span class="cite-bracket">&#91;</span>36<span class="cite-bracket">&#93;</span></a></sup>
</p>
<figure class="mw-default-size" typeof="mw:File/Thumb"><a href="/wiki/File:Algorithmic_Trading._Percentage_of_Market_Volume.png" class="mw-file-description"><img src="//upload.wikimedia.org/wikipedia/commons/thumb/5/51/Algorithmic_Trading._Percentage_of_Market_Volume.png/250px-Algorithmic_Trading._Percentage_of_Market_Volume.png" decoding="async" width="250" height="126" class="mw-file-element" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/5/51/Algorithmic_Trading._Percentage_of_Market_Volume.png/500px-Algorithmic_Trading._Percentage_of_Market_Volume.png 1.5x" data-file-width="812" data-file-height="410" /></a><figcaption>Algorithmic trading. Percentage of market volume.<sup id="cite_ref-38" class="reference"><a href="#cite_note-38"><span class="cite-bracket">&#91;</span>37<span class="cite-bracket">&#93;</span></a></sup></figcaption></figure>
<p>A third of all European Union and United States stock trades in 2006 were driven by automatic programs, or algorithms.<sup id="cite_ref-39" class="reference"><a href="#cite_note-39"><span class="cite-bracket">&#91;</span>38<span class="cite-bracket">&#93;</span></a></sup> As of 2009, studies suggested HFT firms accounted for 60–73% of all US equity trading volume, with that number falling to approximately 50% in 2012.<sup id="cite_ref-advtrade_40-0" class="reference"><a href="#cite_note-advtrade-40"><span class="cite-bracket">&#91;</span>39<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-41" class="reference"><a href="#cite_note-41"><span class="cite-bracket">&#91;</span>40<span class="cite-bracket">&#93;</span></a></sup> In 2006, at the <a href="/wiki/London_Stock_Exchange" title="London Stock Exchange">London Stock Exchange</a>, over 40% of all orders were entered by algorithmic traders, with 60% predicted for 2007. American markets and European markets generally have a higher proportion of algorithmic trades than other markets, and estimates for 2008 range as high as an 80% proportion in some markets. <a href="/wiki/Foreign_exchange_market" title="Foreign exchange market">Foreign exchange markets</a> also have active algorithmic trading, measured at about 80% of orders in 2016 (up from about 25% of orders in 2006).<sup id="cite_ref-42" class="reference"><a href="#cite_note-42"><span class="cite-bracket">&#91;</span>41<span class="cite-bracket">&#93;</span></a></sup> <a href="/wiki/Futures_contract" title="Futures contract">Futures</a> markets are considered fairly easy to integrate into algorithmic trading,<sup id="cite_ref-43" class="reference"><a href="#cite_note-43"><span class="cite-bracket">&#91;</span>42<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-44" class="reference"><a href="#cite_note-44"><span class="cite-bracket">&#91;</span>43<span class="cite-bracket">&#93;</span></a></sup> with about 40% of options trading done via trading algorithms in 2016.<sup id="cite_ref-45" class="reference"><a href="#cite_note-45"><span class="cite-bracket">&#91;</span>44<span class="cite-bracket">&#93;</span></a></sup> <a href="/wiki/Bond_(finance)" title="Bond (finance)">Bond</a> markets are moving toward more access to algorithmic traders.<sup id="cite_ref-46" class="reference"><a href="#cite_note-46"><span class="cite-bracket">&#91;</span>45<span class="cite-bracket">&#93;</span></a></sup>
</p><p>Algorithmic trading and HFT have been the subject of much public debate since the <a href="/wiki/U.S._Securities_and_Exchange_Commission" class="mw-redirect" title="U.S. Securities and Exchange Commission">U.S. Securities and Exchange Commission</a> and the <a href="/wiki/Commodity_Futures_Trading_Commission" title="Commodity Futures Trading Commission">Commodity Futures Trading Commission</a> said in reports that an algorithmic trade entered by a mutual fund company triggered a wave of selling that led to the <a href="/wiki/2010_Flash_Crash" class="mw-redirect" title="2010 Flash Crash">2010 Flash Crash</a>.<sup id="cite_ref-WSJ1_47-0" class="reference"><a href="#cite_note-WSJ1-47"><span class="cite-bracket">&#91;</span>46<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-bloomberg1_48-0" class="reference"><a href="#cite_note-bloomberg1-48"><span class="cite-bracket">&#91;</span>47<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-NYT1_49-0" class="reference"><a href="#cite_note-NYT1-49"><span class="cite-bracket">&#91;</span>48<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-reuters1_50-0" class="reference"><a href="#cite_note-reuters1-50"><span class="cite-bracket">&#91;</span>49<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-wapo1_51-0" class="reference"><a href="#cite_note-wapo1-51"><span class="cite-bracket">&#91;</span>50<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-popper_52-0" class="reference"><a href="#cite_note-popper-52"><span class="cite-bracket">&#91;</span>51<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-younglai_53-0" class="reference"><a href="#cite_note-younglai-53"><span class="cite-bracket">&#91;</span>52<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-reuters2_54-0" class="reference"><a href="#cite_note-reuters2-54"><span class="cite-bracket">&#91;</span>53<span class="cite-bracket">&#93;</span></a></sup> The same reports found HFT strategies may have contributed to subsequent <a href="/wiki/Volatility_(finance)" title="Volatility (finance)">volatility</a> by rapidly pulling liquidity from the market. As a result of these events, the Dow Jones Industrial Average suffered its second largest intraday point swing ever to that date, though prices quickly recovered. (See <a href="/wiki/List_of_largest_daily_changes_in_the_Dow_Jones_Industrial_Average" title="List of largest daily changes in the Dow Jones Industrial Average">List of largest daily changes in the Dow Jones Industrial Average</a>.) A July 2011 report by the <a href="/wiki/International_Organization_of_Securities_Commissions" title="International Organization of Securities Commissions">International Organization of Securities Commissions</a> (IOSCO), an international body of securities regulators, concluded that while "algorithms and HFT technology have been used by market participants to manage their trading and risk, their usage was also clearly a contributing factor in the flash crash event of May 6, 2010."<sup id="cite_ref-iosco_55-0" class="reference"><a href="#cite_note-iosco-55"><span class="cite-bracket">&#91;</span>54<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-reutersiosco_56-0" class="reference"><a href="#cite_note-reutersiosco-56"><span class="cite-bracket">&#91;</span>55<span class="cite-bracket">&#93;</span></a></sup> However, other researchers have reached a different conclusion. One 2010 study found that HFT did not significantly alter trading inventory during the Flash Crash.<sup id="cite_ref-kirilenko_57-0" class="reference"><a href="#cite_note-kirilenko-57"><span class="cite-bracket">&#91;</span>56<span class="cite-bracket">&#93;</span></a></sup> Some algorithmic trading ahead of <a href="/wiki/Index_fund" title="Index fund">index fund</a> rebalancing transfers profits from investors.<sup id="cite_ref-AmeryRebalancing_58-0" class="reference"><a href="#cite_note-AmeryRebalancing-58"><span class="cite-bracket">&#91;</span>57<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-Petajisto_59-0" class="reference"><a href="#cite_note-Petajisto-59"><span class="cite-bracket">&#91;</span>58<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-Montgomery_60-0" class="reference"><a href="#cite_note-Montgomery-60"><span class="cite-bracket">&#91;</span>59<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading2"><h2 id="Strategies">Strategies</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=7" title="Edit section: Strategies"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<div class="mw-heading mw-heading3"><h3 id="Trading_ahead_of_index_fund_rebalancing">Trading ahead of index fund rebalancing</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=8" title="Edit section: Trading ahead of index fund rebalancing"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Most <a href="/wiki/Retirement_savings" class="mw-redirect" title="Retirement savings">retirement savings</a>, such as private <a href="/wiki/Pension" title="Pension">pension</a> funds or <a href="/wiki/401(k)" title="401(k)">401(k)</a> and <a href="/wiki/Individual_retirement_account" title="Individual retirement account">individual retirement accounts</a> in the US, are invested in <a href="/wiki/Mutual_fund" title="Mutual fund">mutual funds</a>, the most popular of which are <a href="/wiki/Index_fund" title="Index fund">index funds</a> which must periodically "rebalance" or adjust their portfolio to match the new prices and <a href="/wiki/Market_capitalization" title="Market capitalization">market capitalization</a> of the underlying securities in the <a href="/wiki/Stock_index" class="mw-redirect" title="Stock index">stock or other index</a> that they track.<sup id="cite_ref-BloombergFA_61-0" class="reference"><a href="#cite_note-BloombergFA-61"><span class="cite-bracket">&#91;</span>60<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-62" class="reference"><a href="#cite_note-62"><span class="cite-bracket">&#91;</span>61<span class="cite-bracket">&#93;</span></a></sup> Profits are transferred from passive index investors to active investors, some of whom are algorithmic traders specifically exploiting the index rebalance effect. The magnitude of these losses incurred by passive investors has been estimated at 21–28bp per year for the S&amp;P 500 and 38–77bp per year for the Russell 2000.<sup id="cite_ref-Petajisto_59-1" class="reference"><a href="#cite_note-Petajisto-59"><span class="cite-bracket">&#91;</span>58<span class="cite-bracket">&#93;</span></a></sup> John Montgomery of <a href="/wiki/Bridgeway_Capital_Management" title="Bridgeway Capital Management">Bridgeway Capital Management</a> says that the resulting "poor investor returns" from trading ahead of mutual funds is "the elephant in the room" that "shockingly, people are not talking about".<sup id="cite_ref-Montgomery_60-1" class="reference"><a href="#cite_note-Montgomery-60"><span class="cite-bracket">&#91;</span>59<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading3"><h3 id="Pairs_trading">Pairs trading</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=9" title="Edit section: Pairs trading"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p><a href="/wiki/Pairs_trading" class="mw-redirect" title="Pairs trading">Pairs trading</a> or <b>pair trading</b> is a long-short, ideally <a href="/wiki/Market-neutral" class="mw-redirect" title="Market-neutral">market-neutral</a> strategy enabling traders to profit from transient discrepancies in relative value of close substitutes. Unlike in the case of classic arbitrage, in case of pairs trading, the <a href="/wiki/Law_of_one_price" title="Law of one price">law of one price</a> cannot guarantee convergence of prices. This is especially true when the strategy is applied to individual stocks – these imperfect substitutes can in fact diverge indefinitely. In theory, the long-short nature of the strategy should make it work regardless of the stock market direction. In practice, execution risk, persistent and large divergences, as well as a decline in volatility can make this strategy unprofitable for long periods of time (e.g. 2004-2007). It belongs to wider categories of <a href="/wiki/Statistical_arbitrage" title="Statistical arbitrage">statistical arbitrage</a>, <a href="/wiki/Convergence_trading" class="mw-redirect" title="Convergence trading">convergence trading</a>, and <a href="/wiki/Relative_value_(economics)" title="Relative value (economics)">relative value</a> strategies.<sup id="cite_ref-63" class="reference"><a href="#cite_note-63"><span class="cite-bracket">&#91;</span>62<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading3"><h3 id="Delta-neutral_strategies">Delta-neutral strategies</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=10" title="Edit section: Delta-neutral strategies"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>In finance, <a href="/wiki/Delta-neutral" class="mw-redirect" title="Delta-neutral">delta-neutral</a> describes a portfolio of related financial securities, in which the portfolio value remains unchanged due to small changes in the value of the underlying security. Such a portfolio typically contains options and their corresponding underlying securities such that positive and negative <a href="/wiki/Option_delta" class="mw-redirect" title="Option delta">delta</a> components offset, resulting in the portfolio's value being relatively insensitive to changes in the value of the underlying security.
</p>
<div class="mw-heading mw-heading3"><h3 id="Arbitrage">Arbitrage</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=11" title="Edit section: Arbitrage"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>In <a href="/wiki/Economics" title="Economics">economics</a> and <a href="/wiki/Finance" title="Finance">finance</a>, arbitrage <span class="rt-commentedText nowrap"><span class="IPA nopopups noexcerpt" lang="en-fonipa"><a href="/wiki/Help:IPA/English" title="Help:IPA/English">/<span style="border-bottom:1px dotted"><span title="/ˈ/: primary stress follows">ˈ</span><span title="/ɑːr/: &#39;ar&#39; in &#39;far&#39;">ɑːr</span><span title="&#39;b&#39; in &#39;buy&#39;">b</span><span title="/ɪ/: &#39;i&#39; in &#39;kit&#39;">ɪ</span><span title="&#39;t&#39; in &#39;tie&#39;">t</span><span title="&#39;r&#39; in &#39;rye&#39;">r</span><span title="/ɑː/: &#39;a&#39; in &#39;father&#39;">ɑː</span><span title="/ʒ/: &#39;s&#39; in &#39;pleasure&#39;">ʒ</span></span>/</a></span></span> is the practice of taking advantage of a price difference between two or more <a href="/wiki/Market_(economics)" title="Market (economics)">markets</a>: striking a combination of matching deals that capitalize upon the imbalance, the profit being the difference between the <a href="/wiki/Market_price" class="mw-redirect" title="Market price">market prices</a>. When used by academics, an arbitrage is a transaction that involves no negative <a href="/wiki/Cash_flow" title="Cash flow">cash flow</a> at any probabilistic or temporal state and a positive cash flow in at least one state; in simple terms, it is the possibility of a risk-free profit at zero cost. Example: One of the most popular arbitrage trading opportunities is played with the S&amp;P futures and the S&amp;P 500 stocks. During most trading days, these two will develop disparity in the pricing between the two of them. This happens when the price of the stocks which are mostly traded on the <a href="/wiki/New_York_Stock_Exchange" title="New York Stock Exchange">NYSE</a> and NASDAQ markets either get ahead or behind the S&amp;P Futures which are traded in the CME market.
</p>
<div class="mw-heading mw-heading4"><h4 id="Conditions_for_arbitrage">Conditions for arbitrage</h4><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=12" title="Edit section: Conditions for arbitrage"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1236090951" /><div role="note" class="hatnote navigation-not-searchable">Further information: <a href="/wiki/Rational_pricing#Arbitrage_mechanics" title="Rational pricing">Rational pricing §&#160;Arbitrage mechanics</a></div>
<p>Arbitrage is possible when one of three conditions is met:
</p>
<ul><li>The same asset does not trade at the same price on all markets (the "<a href="/wiki/Law_of_one_price" title="Law of one price">law of one price</a>" is temporarily violated).</li>
<li>Two assets with identical cash flows do not trade at the same price.</li>
<li>An asset with a known price in the future does not today trade at its future price <a href="/wiki/Discounting" title="Discounting">discounted</a> at the <a href="/wiki/Risk-free_interest_rate" class="mw-redirect" title="Risk-free interest rate">risk-free interest rate</a> (or, the asset does not have negligible costs of storage; as such, for example, this condition holds for grain but not for <a href="/wiki/Security_(finance)" title="Security (finance)">securities</a>).</li></ul>
<p>Arbitrage is not simply the act of buying a product in one market and selling it in another for a higher price at some later time. The long and short transactions should ideally occur <i>simultaneously</i> to minimize the exposure to market risk, or the risk that prices may change on one market before both transactions are complete. In practical terms, this is generally only possible with securities and financial products which can be traded electronically, and even then, when first leg(s) of the trade is executed, the prices in the other legs may have worsened, locking in a guaranteed loss. Missing one of the legs of the trade (and subsequently having to open it at a worse price) is called 'execution risk' or more specifically 'leg-in and leg-out risk'.<sup id="cite_ref-64" class="reference"><a href="#cite_note-64"><span class="cite-bracket">&#91;</span>b<span class="cite-bracket">&#93;</span></a></sup> In the simplest example, any good sold in one market should sell for the same price in another. <a href="/wiki/Merchant" title="Merchant">Traders</a> may, for example, find that the price of wheat is lower in agricultural regions than in cities, purchase the good, and transport it to another region to sell at a higher price. This type of price arbitrage is the most common, but this simple example ignores the cost of transport, storage, risk, and other factors. "True" arbitrage requires that there be no market risk involved. Where securities are traded on more than one exchange, arbitrage occurs by simultaneously buying in one and selling on the other. Such simultaneous execution, if perfect substitutes are involved, minimizes capital requirements, but in practice never creates a "self-financing" (free) position, as many sources incorrectly assume following the theory. As long as there is some difference in the market value and riskiness of the two legs, capital would have to be put up in order to carry the long-short arbitrage position.
</p>
<div class="mw-heading mw-heading3"><h3 id="Mean_reversion">Mean reversion</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=13" title="Edit section: Mean reversion"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p><a href="/wiki/Mean_reversion_(finance)" title="Mean reversion (finance)">Mean reversion</a> is a mathematical methodology sometimes used for stock investing, but it can be applied to other processes. In general terms the idea is that both a stock's high and low prices are temporary, and that a stock's price tends to have an average price over time. An example of a mean-reverting process is the <a href="/wiki/Ornstein%E2%80%93Uhlenbeck_process" title="Ornstein–Uhlenbeck process">Ornstein-Uhlenbeck</a> stochastic equation.
</p><p>Mean reversion involves first identifying the trading range for a stock, and then computing the average price using analytical techniques as it relates to assets, earnings, etc.
</p><p>When the current market price is less than the average price, the stock is considered attractive for purchase, with the expectation that the price will rise. When the current market price is above the average price, the market price is expected to fall. In other words, deviations from the average price are expected to revert to the average.
</p><p>The <a href="/wiki/Standard_deviation" title="Standard deviation">standard deviation</a> of the most recent prices (e.g., the last 20) is often used as a buy or sell indicator.
</p><p>Stock reporting services (such as <a href="/wiki/Yahoo!_Finance" class="mw-redirect" title="Yahoo! Finance">Yahoo! Finance</a>, MS Investor, <a href="/wiki/Morningstar,_Inc." title="Morningstar, Inc.">Morningstar</a>, etc.), commonly offer moving averages for periods such as 50 and 100 days. While reporting services provide the averages, identifying the high and low prices for the study period is still necessary.
</p>
<div class="mw-heading mw-heading3"><h3 id="Scalping">Scalping</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=14" title="Edit section: Scalping"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p><a href="/wiki/Scalping_(trading)" title="Scalping (trading)">Scalping</a> is liquidity provision by non-traditional <a href="/wiki/Market_maker" title="Market maker">market makers</a>, whereby traders attempt to earn (or <i>make</i>) the bid-ask spread. This procedure allows for profit for so long as price moves are less than this spread and normally involves establishing and liquidating a position quickly, usually within minutes or less.<sup id="cite_ref-65" class="reference"><a href="#cite_note-65"><span class="cite-bracket">&#91;</span>63<span class="cite-bracket">&#93;</span></a></sup>
</p><p>A <a href="/wiki/Market_maker" title="Market maker">market maker</a> is basically a specialized scalper and also referred to as dealers.<sup id="cite_ref-:1_66-0" class="reference"><a href="#cite_note-:1-66"><span class="cite-bracket">&#91;</span>64<span class="cite-bracket">&#93;</span></a></sup> The volume a market maker trades is many times more than the average individual scalper and would make use of more sophisticated trading systems and technology. However, registered market makers are bound by exchange rules stipulating their minimum quote obligations. For instance, <a href="/wiki/NASDAQ" class="mw-redirect" title="NASDAQ">NASDAQ</a> requires each market maker to post at least one bid and one ask at some price level, so as to maintain a <a href="/wiki/Two-sided_market" title="Two-sided market">two-sided market</a> for each stock represented.<sup id="cite_ref-67" class="reference"><a href="#cite_note-67"><span class="cite-bracket">&#91;</span>65<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-:1_66-1" class="reference"><a href="#cite_note-:1-66"><span class="cite-bracket">&#91;</span>64<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-68" class="reference"><a href="#cite_note-68"><span class="cite-bracket">&#91;</span>66<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading3"><h3 id="Transaction_cost_reduction">Transaction cost reduction</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=15" title="Edit section: Transaction cost reduction"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Most strategies referred to as algorithmic trading (as well as algorithmic liquidity-seeking) fall into the cost-reduction category. The basic idea is to break down a large order into small orders and place them in the market over time. The choice of algorithm depends on various factors, with the most important being volatility and liquidity of the stock. For example, for a highly liquid stock, matching a certain percentage of the overall orders of stock (called volume inline algorithms) is usually a good strategy, but for a highly illiquid stock, algorithms try to match every order that has a favorable price (called liquidity-seeking algorithms).
</p><p>The success of these strategies is usually measured by comparing the average price at which the entire order was executed with the average price achieved through a benchmark execution for the same duration. Usually, the volume-weighted average price is used as the benchmark. At times, the execution price is also compared with the price of the instrument at the time of placing the order.
</p><p>A special class of these algorithms attempts to detect algorithmic or iceberg orders on the other side (i.e. if you are trying to buy, the algorithm will try to detect orders for the sell side). These algorithms are called sniffing algorithms. A typical example is "Stealth".
</p><p>Some examples of algorithms are <a href="/wiki/VWAP" class="mw-redirect" title="VWAP">VWAP</a>, <a href="/wiki/TWAP" class="mw-redirect" title="TWAP">TWAP</a>, <a href="/wiki/Implementation_shortfall" title="Implementation shortfall">Implementation shortfall</a>, POV, Display size, Liquidity seeker, and Stealth. Modern algorithms are often optimally constructed via either static or dynamic programming.<sup id="cite_ref-69" class="reference"><a href="#cite_note-69"><span class="cite-bracket">&#91;</span>67<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-70" class="reference"><a href="#cite_note-70"><span class="cite-bracket">&#91;</span>68<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-71" class="reference"><a href="#cite_note-71"><span class="cite-bracket">&#91;</span>69<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading3"><h3 id="Strategies_that_only_pertain_to_dark_pools">Strategies that only pertain to dark pools</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=16" title="Edit section: Strategies that only pertain to dark pools"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>As of 2009, HFT, which comprises a broad set of buy-side as well as <a href="/wiki/Market_maker" title="Market maker">market making</a> sell side traders, has become more prominent and controversial.<sup id="cite_ref-Wilmott_72-0" class="reference"><a href="#cite_note-Wilmott-72"><span class="cite-bracket">&#91;</span>70<span class="cite-bracket">&#93;</span></a></sup> These algorithms or techniques are commonly given names such as "Stealth" (developed by the Deutsche Bank), "Iceberg", "Dagger", " Monkey", "Guerrilla", "Sniper", "BASOR" (developed by Quod Financial) and "Sniffer".<sup id="cite_ref-73" class="reference"><a href="#cite_note-73"><span class="cite-bracket">&#91;</span>71<span class="cite-bracket">&#93;</span></a></sup> <a href="/wiki/Dark_liquidity" class="mw-redirect" title="Dark liquidity">Dark pools</a> are alternative trading systems that are private in nature—and thus do not interact with public order flow—and seek instead to provide undisplayed liquidity to large blocks of securities.<sup id="cite_ref-74" class="reference"><a href="#cite_note-74"><span class="cite-bracket">&#91;</span>72<span class="cite-bracket">&#93;</span></a></sup> In dark pools, trading takes place anonymously, with most orders hidden or "iceberged".<sup id="cite_ref-sharks_75-0" class="reference"><a href="#cite_note-sharks-75"><span class="cite-bracket">&#91;</span>73<span class="cite-bracket">&#93;</span></a></sup> Gamers or "sharks" sniff out large orders by "pinging" small market orders to buy and sell. When several small orders are filled the sharks may have discovered the presence of a large iceberged order.
</p><p>"Now it's an arms race," said Andrew Lo, director of the <a href="/wiki/Massachusetts_Institute_of_Technology" title="Massachusetts Institute of Technology">Massachusetts Institute of Technology</a>'s Laboratory for Financial Engineering in 2006. "Everyone is building more sophisticated algorithms, and the more competition exists, the smaller the profits."<sup id="cite_ref-iht.com_76-0" class="reference"><a href="#cite_note-iht.com-76"><span class="cite-bracket">&#91;</span>74<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading3"><h3 id="Market_timing">Market timing</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=17" title="Edit section: Market timing"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Strategies designed to generate alpha are considered market timing strategies. These types of strategies are designed using a methodology that includes backtesting, forward testing and live testing. Market timing algorithms will typically use technical indicators such as moving averages but can also include pattern recognition logic implemented using <a href="/wiki/Finite-state_machine" title="Finite-state machine">finite-state machines</a>.<sup id="cite_ref-77" class="reference"><a href="#cite_note-77"><span class="cite-bracket">&#91;</span>75<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-78" class="reference"><a href="#cite_note-78"><span class="cite-bracket">&#91;</span>76<span class="cite-bracket">&#93;</span></a></sup>
</p><p><a href="/wiki/Backtesting" title="Backtesting">Backtesting</a> the algorithm is typically the first stage and involves simulating the hypothetical trades through an in-sample data period. Optimization is performed in order to determine the most optimal inputs. Steps taken to reduce the chance of over-optimization can include modifying the inputs +/- 10%, <a href="/wiki/Shmoo_plot" title="Shmoo plot">shmooing</a> the inputs in large steps, running <a href="/wiki/Monte_Carlo_simulation" class="mw-redirect" title="Monte Carlo simulation">Monte Carlo simulations</a> and ensuring <a href="/wiki/Slippage_(finance)" title="Slippage (finance)">slippage</a> and commission is accounted for.<sup id="cite_ref-79" class="reference"><a href="#cite_note-79"><span class="cite-bracket">&#91;</span>77<span class="cite-bracket">&#93;</span></a></sup>
</p><p>Forward testing the algorithm is the next stage and involves running the algorithm through an out of sample data set to ensure the algorithm performs within backtested expectations.
</p><p>Live testing is the final stage of development and requires the developer to compare actual live trades with both the backtested and forward tested models. Metrics compared include percent profitable, profit factor, maximum drawdown and average gain per trade.
</p>
<div class="mw-heading mw-heading3"><h3 id="Algorithmic_trading_under_the_assumption_of_non-ergodicity">Algorithmic trading under the assumption of non-ergodicity</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=18" title="Edit section: Algorithmic trading under the assumption of non-ergodicity"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<style data-mw-deduplicate="TemplateStyles:r1305433154">.mw-parser-output .ambox{border:1px solid #a2a9b1;border-left:10px solid #36c;background-color:#fbfbfb;box-sizing:border-box}.mw-parser-output .ambox+link+.ambox,.mw-parser-output .ambox+link+style+.ambox,.mw-parser-output .ambox+link+link+.ambox,.mw-parser-output .ambox+.mw-empty-elt+link+.ambox,.mw-parser-output .ambox+.mw-empty-elt+link+style+.ambox,.mw-parser-output .ambox+.mw-empty-elt+link+link+.ambox{margin-top:-1px}html body.mediawiki .mw-parser-output .ambox.mbox-small-left{margin:4px 1em 4px 0;overflow:hidden;width:238px;border-collapse:collapse;font-size:88%;line-height:1.25em}.mw-parser-output .ambox-speedy{border-left:10px solid #b32424;background-color:#fee7e6}.mw-parser-output .ambox-delete{border-left:10px solid #b32424}.mw-parser-output .ambox-content{border-left:10px solid #f28500}.mw-parser-output .ambox-style{border-left:10px solid #fc3}.mw-parser-output .ambox-move{border-left:10px solid #9932cc}.mw-parser-output .ambox-protection{border-left:10px solid #a2a9b1}.mw-parser-output .ambox .mbox-text{border:none;padding:0.25em 0.5em;width:100%}.mw-parser-output .ambox .mbox-image{border:none;padding:2px 0 2px 0.5em;text-align:center}.mw-parser-output .ambox .mbox-imageright{border:none;padding:2px 0.5em 2px 0;text-align:center}.mw-parser-output .ambox .mbox-empty-cell{border:none;padding:0;width:1px}.mw-parser-output .ambox .mbox-image-div{width:52px}@media(min-width:720px){.mw-parser-output .ambox{margin:0 10%}}@media print{body.ns-0 .mw-parser-output .ambox{display:none!important}}</style><table class="box-More_citations_needed_section plainlinks metadata ambox ambox-content ambox-Refimprove" role="presentation"><tbody><tr><td class="mbox-image"><div class="mbox-image-div"><span typeof="mw:File"><a href="/wiki/File:Question_book-new.svg" class="mw-file-description"><img alt="" src="//upload.wikimedia.org/wikipedia/en/thumb/9/99/Question_book-new.svg/60px-Question_book-new.svg.png" decoding="async" width="50" height="39" class="mw-file-element" srcset="//upload.wikimedia.org/wikipedia/en/thumb/9/99/Question_book-new.svg/120px-Question_book-new.svg.png 1.5x" data-file-width="512" data-file-height="399" /></a></span></div></td><td class="mbox-text"><div class="mbox-text-span">This section <b>needs additional citations for <a href="/wiki/Wikipedia:Verifiability" title="Wikipedia:Verifiability">verification</a></b>.<span class="hide-when-compact"> Please help <a href="/wiki/Special:EditPage/Algorithmic_trading" title="Special:EditPage/Algorithmic trading">improve this article</a> by <a href="/wiki/Help:Referencing_for_beginners" title="Help:Referencing for beginners">adding citations to reliable sources</a>&#32;in this section. Unsourced material may be challenged and removed.</span>  <span class="date-container"><i>(<span class="date">February 2025</span>)</i></span><span class="hide-when-compact"><i> (<small><a href="/wiki/Help:Maintenance_template_removal" title="Help:Maintenance template removal">Learn how and when to remove this message</a></small>)</i></span></div></td></tr></tbody></table> 
<p>In modern algorithmic trading, financial markets are considered non-ergodic, meaning they do not follow stationary and predictable dynamics.<sup id="cite_ref-80" class="reference"><a href="#cite_note-80"><span class="cite-bracket">&#91;</span>78<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-81" class="reference"><a href="#cite_note-81"><span class="cite-bracket">&#91;</span>79<span class="cite-bracket">&#93;</span></a></sup> In fact, empirical evidence shows that returns are neither independent nor normally distributed, making forecasting more complex. In a non-ergodic system, the success of a strategy depends on its ability to anticipate market evolutions.<sup id="cite_ref-82" class="reference"><a href="#cite_note-82"><span class="cite-bracket">&#91;</span>80<span class="cite-bracket">&#93;</span></a></sup> For this reason, in quantitative trading, it is essential to develop tools that can estimate and exploit this predictive capacity.<sup id="cite_ref-83" class="reference"><a href="#cite_note-83"><span class="cite-bracket">&#91;</span>81<span class="cite-bracket">&#93;</span></a></sup>
</p><p>For this purpose, a function of particular interest is the Binomial Evolution Function, which estimates the probability of obtaining the same results, of the analyzed investment strategy, using a random method, such as tossing a coin.
</p><p>• If this probability is low, it means that the algorithm has a real predictive capacity.
</p><p>• If it is high, it indicates that the strategy operates randomly, and the profits obtained may not be indicative for the future.
</p><p>Given a sequence of financial operations, the function is applied by following these steps:
</p><p>1. <b>Trade aggregation:</b> Consecutive trades in the same direction (buy or sell) are combined into a single trade. The profit or loss of this new trade is calculated by adding the results of the individual merged trades.
</p><p>2. <b>Conversion to a binary sequence:</b> The sequence obtained in the first step is transformed into a series of 0s and 1s. Profitable trades are assigned the value 1, while losing trades are assigned the value 0.
</p><p>3. <b>Calculating random probability using the binomial distribution:</b> It's calculated the probability of obtaining an equal or greater number of correct predictions (wins) randomly, for example by tossing a coin. This calculation is done using the binomial function, where:
</p><p>• k is the total number of successes (the number of "1s" in the sequence),
</p><p>• p is equal to 50% (assuming a fair coin).
</p><p>This function shifts the focus from the result, which may be too influenced by individual lucky trades, to the ability of the algorithm to predict the market. This approach is increasingly widespread in modern quantitative trading, where it is recognized that future profits depend on the ability of the algorithm to anticipate market evolutions.
</p>
<div class="mw-heading mw-heading2"><h2 id="High-frequency_trading">High-frequency trading</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=19" title="Edit section: High-frequency trading"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1236090951" /><div role="note" class="hatnote navigation-not-searchable">Main article: <a href="/wiki/High-frequency_trading" title="High-frequency trading">High-frequency trading</a></div>
<p>As noted above, high-frequency trading (HFT) is a form of algorithmic trading characterized by high turnover and high order-to-trade ratios. Although there is no single definition of HFT, among its key attributes are highly sophisticated algorithms, specialized order types, co-location, very short-term investment horizons, and high cancellation rates for orders.<sup id="cite_ref-ReferenceA_7-1" class="reference"><a href="#cite_note-ReferenceA-7"><span class="cite-bracket">&#91;</span>7<span class="cite-bracket">&#93;</span></a></sup>
In the U.S., high-frequency trading (HFT) firms represent 2% of the approximately 20,000 firms operating today, but account for 73% of all equity trading volume.<sup id="cite_ref-84" class="reference"><a href="#cite_note-84"><span class="cite-bracket">&#91;</span>82<span class="cite-bracket">&#93;</span></a></sup> As of the first quarter in 2009, total assets under management for hedge funds with HFT strategies were US$141 billion, down about 21% from their high.<sup id="cite_ref-mktbeat_85-0" class="reference"><a href="#cite_note-mktbeat-85"><span class="cite-bracket">&#91;</span>83<span class="cite-bracket">&#93;</span></a></sup> The HFT strategy was first made successful by <a href="/wiki/Renaissance_Technologies" title="Renaissance Technologies">Renaissance Technologies</a>.<sup id="cite_ref-Olsen_86-0" class="reference"><a href="#cite_note-Olsen-86"><span class="cite-bracket">&#91;</span>84<span class="cite-bracket">&#93;</span></a></sup>
</p><p>High-frequency funds started to become especially popular in 2007 and 2008.<sup id="cite_ref-Olsen_86-1" class="reference"><a href="#cite_note-Olsen-86"><span class="cite-bracket">&#91;</span>84<span class="cite-bracket">&#93;</span></a></sup> Many HFT firms are <a href="/wiki/Market_maker" title="Market maker">market makers</a> and provide liquidity to the market, which has lowered volatility and helped narrow <a href="/wiki/Bid%E2%80%93offer_spread" class="mw-redirect" title="Bid–offer spread">bid–offer spreads</a> making trading and investing cheaper for other market participants.<sup id="cite_ref-mktbeat_85-1" class="reference"><a href="#cite_note-mktbeat-85"><span class="cite-bracket">&#91;</span>83<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-87" class="reference"><a href="#cite_note-87"><span class="cite-bracket">&#91;</span>85<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-88" class="reference"><a href="#cite_note-88"><span class="cite-bracket">&#91;</span>86<span class="cite-bracket">&#93;</span></a></sup> HFT has been a subject of intense public focus since the <a href="/wiki/U.S._Securities_and_Exchange_Commission" class="mw-redirect" title="U.S. Securities and Exchange Commission">U.S. Securities and Exchange Commission</a> and the Commodity Futures Trading Commission stated that both algorithmic trading and HFT contributed to volatility in the <a href="/wiki/2010_Flash_Crash" class="mw-redirect" title="2010 Flash Crash">2010 Flash Crash</a>. Among the major U.S. high frequency trading firms are Chicago Trading Company, <a href="/wiki/Optiver" title="Optiver">Optiver</a>, <a href="/wiki/Virtu_Financial" title="Virtu Financial">Virtu Financial</a>, <a href="/wiki/DRW_Trading_Group" title="DRW Trading Group">DRW</a>, <a href="/wiki/Jump_Trading" title="Jump Trading">Jump Trading</a>, <a href="/wiki/Two_Sigma" title="Two Sigma">Two Sigma Securities</a>, GTS, <a href="/wiki/IMC_Financial_Markets" title="IMC Financial Markets">IMC Financial</a>, and <a href="/wiki/Citadel_LLC" title="Citadel LLC">Citadel LLC</a>.<sup id="cite_ref-cutter_89-0" class="reference"><a href="#cite_note-cutter-89"><span class="cite-bracket">&#91;</span>87<span class="cite-bracket">&#93;</span></a></sup>
</p><p>There are four key categories of HFT strategies: market-making based on order flow, market-making based on tick data information, event arbitrage and statistical arbitrage. All portfolio-allocation decisions are made by computerized quantitative models. The success of computerized strategies is largely driven by their ability to simultaneously process volumes of information, something ordinary human traders cannot do.
</p>
<div class="mw-heading mw-heading3"><h3 id="Market_making">Market making</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=20" title="Edit section: Market making"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p><a href="/wiki/Market_maker" title="Market maker">Market making</a> involves placing a limit order to sell (or offer) above the current market price or a buy limit order (or bid) below the current price on a regular and continuous basis to capture the bid-ask spread. Automated Trading Desk, which was bought by Citigroup in July 2007, has been an active market maker, accounting for about 6% of total volume on both NASDAQ and the New York Stock Exchange.<sup id="cite_ref-90" class="reference"><a href="#cite_note-90"><span class="cite-bracket">&#91;</span>88<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading3"><h3 id="Statistical_arbitrage">Statistical arbitrage</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=21" title="Edit section: Statistical arbitrage"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Another set of HFT strategies in classical arbitrage strategy might involve several securities such as covered <a href="/wiki/Interest_rate_parity" title="Interest rate parity">interest rate parity</a> in the <a href="/wiki/Foreign_exchange_market" title="Foreign exchange market">foreign exchange market</a> which gives a relation between the prices of a domestic bond, a bond denominated in a foreign currency, the spot price of the currency, and the price of a <a href="/wiki/Forward_contract" title="Forward contract">forward contract</a> on the currency. If the market prices are different enough from those implied in the model to cover <a href="/wiki/Transaction_cost" title="Transaction cost">transaction cost</a> then four transactions can be made to guarantee a risk-free profit. HFT allows similar arbitrages using models of greater complexity involving many more than 4 securities. The TABB Group estimates that annual aggregate profits of low latency arbitrage strategies currently exceed US$21 billion.<sup id="cite_ref-advtrade_40-1" class="reference"><a href="#cite_note-advtrade-40"><span class="cite-bracket">&#91;</span>39<span class="cite-bracket">&#93;</span></a></sup>
</p><p>A wide range of statistical arbitrage strategies have been developed whereby trading decisions are made on the basis of deviations from statistically significant relationships. Like market-making strategies, statistical arbitrage can be applied in all asset classes.
</p>
<div class="mw-heading mw-heading3"><h3 id="Event_arbitrage">Event arbitrage</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=22" title="Edit section: Event arbitrage"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>A subset of risk, merger, convertible, or distressed securities arbitrage that counts on a specific event, such as a contract signing, regulatory approval, judicial decision, etc., to change the price or rate relationship of two or more financial instruments and permit the arbitrageur to earn a profit.<sup id="cite_ref-eventarbdef_91-0" class="reference"><a href="#cite_note-eventarbdef-91"><span class="cite-bracket">&#91;</span>89<span class="cite-bracket">&#93;</span></a></sup>
</p><p><a href="/wiki/Merger_arbitrage" class="mw-redirect" title="Merger arbitrage">Merger arbitrage</a> also called <a href="/wiki/Risk_arbitrage" title="Risk arbitrage">risk arbitrage</a> would be an example of this. Merger arbitrage generally consists of buying the stock of a company that is the target of a <a href="/wiki/Takeover" title="Takeover">takeover</a> while <a href="/wiki/Short_(finance)" title="Short (finance)">shorting</a> the stock of the acquiring company. Usually the market price of the target company is less than the price offered by the acquiring company. The spread between these two prices depends mainly on the probability and the timing of the takeover being completed, as well as the prevailing level of interest rates. The bet in a merger arbitrage is that such a spread will eventually be zero, if and when the takeover is completed. The risk is that the deal "breaks" and the spread massively widens.
</p>
<div class="mw-heading mw-heading3"><h3 id="Spoofing">Spoofing</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=23" title="Edit section: Spoofing"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1236090951" /><div role="note" class="hatnote navigation-not-searchable">Main article: <a href="/wiki/Layering_(finance)" title="Layering (finance)">Layering (finance)</a></div>
<p>One strategy that some traders have employed, which has been proscribed yet likely continues, is called spoofing. It is the act of placing orders to give the impression of wanting to buy or sell shares, without ever having the intention of letting the order execute to temporarily manipulate the market to buy or sell shares at a more favorable price. This is done by creating limit orders outside the current bid or ask price to change the reported price to other market participants. The trader can subsequently place trades based on the artificial change in price, then canceling the limit orders before they are executed.
</p><p>Suppose a trader desires to sell shares of a company with a current bid of $20 and a current ask of $20.20. The trader would place a buy order at $20.10, still some distance from the ask so it will not be executed, and the $20.10 bid is reported as the National Best Bid and Offer best bid price. The trader then executes a market order for the sale of the shares they wished to sell. Because the best bid price is the investor's artificial bid, a market maker fills the sale order at $20.10, allowing for a $.10 higher sale price per share. The trader subsequently cancels their limit order on the purchase he never had the intention of completing.
</p>
<div class="mw-heading mw-heading3"><h3 id="Quote_stuffing">Quote stuffing</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=24" title="Edit section: Quote stuffing"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1236090951" /><div role="note" class="hatnote navigation-not-searchable">Main article: <a href="/wiki/Quote_stuffing" title="Quote stuffing">Quote stuffing</a></div>
<p>Quote stuffing is a tactic employed by malicious traders that involves quickly entering and withdrawing large quantities of orders in an attempt to flood the market, thereby gaining an advantage over slower market participants.<sup id="cite_ref-92" class="reference"><a href="#cite_note-92"><span class="cite-bracket">&#91;</span>90<span class="cite-bracket">&#93;</span></a></sup> The rapidly placed and canceled orders cause market data feeds that ordinary investors rely on to delay price quotes while the stuffing is occurring. HFT firms benefit from proprietary, higher-capacity feeds and the most capable, lowest latency infrastructure. Researchers showed high-frequency traders are able to profit by the artificially induced latencies and arbitrage opportunities that result from quote stuffing.<sup id="cite_ref-93" class="reference"><a href="#cite_note-93"><span class="cite-bracket">&#91;</span>91<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading2"><h2 id="Low_latency_trading_systems">Low latency trading systems</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=25" title="Edit section: Low latency trading systems"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Network-induced latency, a synonym for delay, measured in one-way delay or round-trip time, is normally defined as how much time it takes for a data packet to travel from one point to another.<sup id="cite_ref-94" class="reference"><a href="#cite_note-94"><span class="cite-bracket">&#91;</span>92<span class="cite-bracket">&#93;</span></a></sup> Low latency trading refers to the algorithmic trading systems and network routes used by financial institutions connecting to stock exchanges and electronic communication networks (ECNs) to rapidly execute financial transactions.<sup id="cite_ref-95" class="reference"><a href="#cite_note-95"><span class="cite-bracket">&#91;</span>93<span class="cite-bracket">&#93;</span></a></sup> Most HFT firms depend on low latency execution of their trading strategies. Joel Hasbrouck and Gideon Saar (2013) measure latency based on three components: the time it takes for (1) information to reach the trader, (2) the trader's algorithms to analyze the information, and (3) the generated action to reach the exchange and get implemented.<sup id="cite_ref-96" class="reference"><a href="#cite_note-96"><span class="cite-bracket">&#91;</span>94<span class="cite-bracket">&#93;</span></a></sup> In a contemporary electronic market (circa 2009), low latency trade processing time was qualified as under 10 milliseconds, and ultra-low latency as under 1 millisecond.<sup id="cite_ref-97" class="reference"><a href="#cite_note-97"><span class="cite-bracket">&#91;</span>95<span class="cite-bracket">&#93;</span></a></sup>
</p><p>Low-latency traders depend on <a href="/wiki/Ultra-low_latency_direct_market_access" title="Ultra-low latency direct market access">ultra-low latency networks</a>. They profit by providing information, such as competing bids and offers, to their algorithms microseconds faster than their competitors.<sup id="cite_ref-advtrade_40-2" class="reference"><a href="#cite_note-advtrade-40"><span class="cite-bracket">&#91;</span>39<span class="cite-bracket">&#93;</span></a></sup> The revolutionary advance in speed has led to the need for firms to have a real-time, <a href="/wiki/Colocation_(business)" title="Colocation (business)">colocated</a> trading platform to benefit from implementing high-frequency strategies.<sup id="cite_ref-advtrade_40-3" class="reference"><a href="#cite_note-advtrade-40"><span class="cite-bracket">&#91;</span>39<span class="cite-bracket">&#93;</span></a></sup> Strategies are constantly altered to reflect the subtle changes in the market as well as to combat the threat of the strategy being <a href="/wiki/Reverse_engineering" title="Reverse engineering">reverse engineered</a> by competitors. This is due to the evolutionary nature of algorithmic trading strategies – they must be able to adapt and trade intelligently, regardless of market conditions, which involves being flexible enough to withstand a vast array of market scenarios. As a result, a significant proportion of net revenue from firms is spent on the R&amp;D of these autonomous trading systems.<sup id="cite_ref-advtrade_40-4" class="reference"><a href="#cite_note-advtrade-40"><span class="cite-bracket">&#91;</span>39<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading2"><h2 id="Strategy_implementation">Strategy implementation</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=26" title="Edit section: Strategy implementation"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Most of the algorithmic strategies are implemented using modern programming languages, although some still implement strategies designed in spreadsheets. Increasingly, the algorithms used by large brokerages and asset managers are written to the FIX Protocol's Algorithmic Trading Definition Language (<a href="/wiki/FIXatdl" title="FIXatdl">FIXatdl</a>), which allows firms receiving orders to specify exactly how their electronic orders should be expressed. Orders built using FIXatdl can then be transmitted from traders' systems via the FIX Protocol.<sup id="cite_ref-98" class="reference"><a href="#cite_note-98"><span class="cite-bracket">&#91;</span>96<span class="cite-bracket">&#93;</span></a></sup> Basic models can rely on as little as a linear regression, while more complex game-theoretic and <a href="/wiki/Pattern_recognition" title="Pattern recognition">pattern recognition</a><sup id="cite_ref-99" class="reference"><a href="#cite_note-99"><span class="cite-bracket">&#91;</span>97<span class="cite-bracket">&#93;</span></a></sup> or predictive models can also be used to initiate trading. More complex methods such as <a href="/wiki/Markov_chain_Monte_Carlo" title="Markov chain Monte Carlo">Markov chain Monte Carlo</a> have been used to create these models.<sup id="cite_ref-100" class="reference"><a href="#cite_note-100"><span class="cite-bracket">&#91;</span>98<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading2"><h2 id="Issues_and_developments">Issues and developments</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=27" title="Edit section: Issues and developments"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Algorithmic trading has been shown to substantially improve <a href="/wiki/Market_liquidity" title="Market liquidity">market liquidity</a><sup id="cite_ref-101" class="reference"><a href="#cite_note-101"><span class="cite-bracket">&#91;</span>99<span class="cite-bracket">&#93;</span></a></sup> among other benefits. However, improvements in productivity brought by algorithmic trading have been opposed by human brokers and traders facing stiff competition from computers.
</p>
<div class="mw-heading mw-heading3"><h3 id="Cyborg_finance">Cyborg finance</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=28" title="Edit section: Cyborg finance"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Technological advances in finance, particularly those relating to algorithmic trading, has increased financial speed, connectivity, reach, and complexity while simultaneously reducing its humanity. Computers running software based on complex algorithms have replaced humans in many functions in the financial industry. Finance is essentially becoming an industry where machines and humans share the dominant roles – transforming modern finance into what one scholar has called, "cyborg finance".<sup id="cite_ref-102" class="reference"><a href="#cite_note-102"><span class="cite-bracket">&#91;</span>100<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading3"><h3 id="Concerns">Concerns</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=29" title="Edit section: Concerns"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>While many experts laud the benefits of innovation in computerized algorithmic trading, other analysts have expressed concern with specific aspects of computerized trading.
</p>
<blockquote><p>"The downside with these systems is their <a href="/wiki/Black_box" title="Black box">black box</a>-ness," Mr. Williams said. "Traders have intuitive senses of how the world works. But with these systems you pour in a bunch of numbers, and something comes out the other end, and it's not always intuitive or clear why the black box latched onto certain data or relationships."<sup id="cite_ref-iht.com_76-1" class="reference"><a href="#cite_note-iht.com-76"><span class="cite-bracket">&#91;</span>74<span class="cite-bracket">&#93;</span></a></sup></p></blockquote>
<blockquote><p>"The <a href="/wiki/Financial_Services_Authority" title="Financial Services Authority">Financial Services Authority</a> has been keeping a watchful eye on the development of black box trading. In its annual report the regulator remarked on the great benefits of efficiency that new technology is bringing to the market. But it also pointed out that 'greater reliance on sophisticated technology and modelling brings with it a greater risk that systems failure can result in business interruption'."<sup id="cite_ref-103" class="reference"><a href="#cite_note-103"><span class="cite-bracket">&#91;</span>101<span class="cite-bracket">&#93;</span></a></sup>
</p></blockquote>
<blockquote><p>UK Treasury minister <a href="/wiki/Lord_Myners" class="mw-redirect" title="Lord Myners">Lord Myners</a> has warned that companies could become the "playthings" of speculators because of automatic high-frequency trading. Lord Myners said the process risked destroying the relationship between an investor and a company.<sup id="cite_ref-104" class="reference"><a href="#cite_note-104"><span class="cite-bracket">&#91;</span>102<span class="cite-bracket">&#93;</span></a></sup>
</p></blockquote>
<p>Other issues include the technical problem of <a href="/wiki/Latency_(engineering)" title="Latency (engineering)">latency</a> or the delay in getting quotes to traders,<sup id="cite_ref-105" class="reference"><a href="#cite_note-105"><span class="cite-bracket">&#91;</span>103<span class="cite-bracket">&#93;</span></a></sup> security and the possibility of a complete system breakdown leading to a <a href="/wiki/Stock_market_crash" title="Stock market crash">market crash</a>.<sup id="cite_ref-106" class="reference"><a href="#cite_note-106"><span class="cite-bracket">&#91;</span>104<span class="cite-bracket">&#93;</span></a></sup>
</p>
<blockquote><p>"Goldman spends tens of millions of dollars on this stuff. They have more people working in their technology area than people on the trading desk...The nature of the markets has changed dramatically."<sup id="cite_ref-107" class="reference"><a href="#cite_note-107"><span class="cite-bracket">&#91;</span>105<span class="cite-bracket">&#93;</span></a></sup></p></blockquote>
<p>On August 1, 2012 <a href="/wiki/Knight_Capital_Group" title="Knight Capital Group">Knight Capital Group</a> experienced a technology issue in their automated trading system,<sup id="cite_ref-108" class="reference"><a href="#cite_note-108"><span class="cite-bracket">&#91;</span>106<span class="cite-bracket">&#93;</span></a></sup> causing a loss of $440 million.
</p>
<blockquote><p>This issue was related to Knight's installation of trading software and resulted in Knight sending numerous <a href="/wiki/Fat-finger_error" title="Fat-finger error">erroneous</a> orders in NYSE-listed securities into the market. This software has been removed from the company's systems. ... Clients were not negatively affected by the <a href="/wiki/Erroneous_trade" class="mw-redirect" title="Erroneous trade">erroneous</a> orders, and the software issue was limited to the routing of certain listed stocks to NYSE. Knight has traded out of its entire <a href="/wiki/Erroneous_trade" class="mw-redirect" title="Erroneous trade">erroneous trade</a> position, which has resulted in a realized pre-tax loss of approximately $440 million.</p></blockquote>
<p>Algorithmic and high-frequency trading were shown to have contributed to volatility during the May 6, 2010 Flash Crash,<sup id="cite_ref-WSJ1_47-1" class="reference"><a href="#cite_note-WSJ1-47"><span class="cite-bracket">&#91;</span>46<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-NYT1_49-1" class="reference"><a href="#cite_note-NYT1-49"><span class="cite-bracket">&#91;</span>48<span class="cite-bracket">&#93;</span></a></sup> when the Dow Jones Industrial Average plunged about 600 points only to recover those losses within minutes. At the time, it was the second largest point swing, 1,010.14 points, and the biggest one-day point decline, 998.5 points, on an intraday basis in Dow Jones Industrial Average history.<sup id="cite_ref-Lauricella_109-0" class="reference"><a href="#cite_note-Lauricella-109"><span class="cite-bracket">&#91;</span>107<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading3"><h3 id="Recent_developments">Recent developments</h3><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=30" title="Edit section: Recent developments"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Financial market news is now being formatted by firms such as Need To Know News, <a href="/wiki/Thomson_Reuters" title="Thomson Reuters">Thomson Reuters</a>, <a href="/wiki/Dow_Jones_%26_Company" title="Dow Jones &amp; Company">Dow Jones</a>, and <a href="/wiki/Bloomberg_L.P." title="Bloomberg L.P.">Bloomberg</a>, to be read and traded on via algorithms.
</p>
<blockquote><p>"Computers are now being used to generate news stories about company earnings results or economic statistics as they are released. And this almost instantaneous information forms a direct feed into other computers which trade on the news."<sup id="cite_ref-ft_too_late_110-0" class="reference"><a href="#cite_note-ft_too_late-110"><span class="cite-bracket">&#91;</span>108<span class="cite-bracket">&#93;</span></a></sup> </p></blockquote>
<p>The algorithms do not simply trade on simple news stories but also interpret more difficult to understand news. Some firms are also attempting to automatically assign <i>sentiment</i> (deciding if the news is good or bad) to news stories so that automated trading can work directly on the news story.<sup id="cite_ref-salgo_111-0" class="reference"><a href="#cite_note-salgo-111"><span class="cite-bracket">&#91;</span>109<span class="cite-bracket">&#93;</span></a></sup>
</p>
<blockquote><p>"Increasingly, people are looking at all forms of news and building their own indicators around it in a semi-structured way," as they constantly seek out new trading advantages said Rob Passarella, global director of strategy at Dow Jones Enterprise Media Group. His firm provides both a low latency news feed and news analytics for traders. Passarella also pointed to new academic research being conducted on the degree to which frequent Google searches on various stocks can serve as trading indicators, the potential impact of various phrases and words that may appear in Securities and Exchange Commission statements and the latest wave of online communities devoted to stock trading topics.<sup id="cite_ref-salgo_111-1" class="reference"><a href="#cite_note-salgo-111"><span class="cite-bracket">&#91;</span>109<span class="cite-bracket">&#93;</span></a></sup></p></blockquote>
<blockquote><p>"Markets are by their very nature conversations, having grown out of coffee houses and taverns," he said. So the way conversations get created in a digital society will be used to convert news into trades, as well, Passarella said.<sup id="cite_ref-salgo_111-2" class="reference"><a href="#cite_note-salgo-111"><span class="cite-bracket">&#91;</span>109<span class="cite-bracket">&#93;</span></a></sup></p></blockquote>
<blockquote><p>"There is a real interest in moving the process of interpreting news from the humans to the machines" says Kirsti Suutari, global business manager of algorithmic trading at Reuters. "More of our customers are finding ways to use news content to make money."<sup id="cite_ref-ft_too_late_110-1" class="reference"><a href="#cite_note-ft_too_late-110"><span class="cite-bracket">&#91;</span>108<span class="cite-bracket">&#93;</span></a></sup></p></blockquote>
<p>An example of the importance of news reporting speed to algorithmic traders was an <a href="/wiki/Advertising" title="Advertising">advertising</a> campaign by <a href="/wiki/Dow_Jones_%26_Company" title="Dow Jones &amp; Company">Dow Jones</a> (appearances included page W15 of <i><a href="/wiki/The_Wall_Street_Journal" title="The Wall Street Journal">The Wall Street Journal</a></i>, on March 1, 2008) claiming that their service had beaten other news services by two seconds in reporting an interest rate cut by the Bank of England.
</p><p>In July 2007, <a href="/wiki/Citigroup" title="Citigroup">Citigroup</a>, which had already developed its own trading algorithms, paid $680 million for Automated Trading Desk, a 19-year-old firm that trades about 200 million shares a day.<sup id="cite_ref-112" class="reference"><a href="#cite_note-112"><span class="cite-bracket">&#91;</span>110<span class="cite-bracket">&#93;</span></a></sup> Citigroup had previously bought Lava Trading and OnTrade Inc.
</p><p>In late 2010, The UK Government Office for Science initiated a <i>Foresight</i> project investigating the future of computer trading in the financial markets,<sup id="cite_ref-auto_113-0" class="reference"><a href="#cite_note-auto-113"><span class="cite-bracket">&#91;</span>111<span class="cite-bracket">&#93;</span></a></sup> led by <a href="/wiki/Dame_Clara_Furse" class="mw-redirect" title="Dame Clara Furse">Dame Clara Furse</a>, ex-CEO of the <a href="/wiki/London_Stock_Exchange" title="London Stock Exchange">London Stock Exchange</a> and in September 2011 the project published its initial findings in the form of a three-chapter working paper available in three languages, along with 16 additional papers that provide supporting evidence.<sup id="cite_ref-auto_113-1" class="reference"><a href="#cite_note-auto-113"><span class="cite-bracket">&#91;</span>111<span class="cite-bracket">&#93;</span></a></sup> All of these findings are authored or co-authored by leading academics and practitioners, and were subjected to anonymous peer-review. Released in 2012, the Foresight study acknowledged issues related to periodic illiquidity, new forms of manipulation and potential threats to market stability due to errant algorithms or <a href="/wiki/Quote_stuffing" title="Quote stuffing">excessive message traffic</a>. However, the report was also criticized for adopting "standard pro-HFT arguments" and advisory panel members being linked to the HFT industry.<sup id="cite_ref-114" class="reference"><a href="#cite_note-114"><span class="cite-bracket">&#91;</span>112<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading2"><h2 id="System_architecture">System architecture</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=31" title="Edit section: System architecture"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>A traditional trading system consists primarily of two blocks – one that receives the market data while the other that sends the order request to the exchange. However, an algorithmic trading system can be broken down into three parts:
</p>
<ol><li>Exchange</li>
<li>The server</li>
<li>Application</li></ol>
<p>Exchange(s) provide data to the system, which typically consists of the latest order book, traded volumes, and last traded price (LTP) of scrip. The server in turn receives the data simultaneously acting as a store for historical database. The data is analyzed at the application side, where trading strategies are fed from the user and can be viewed on the <a href="/wiki/Graphical_user_interface" title="Graphical user interface">GUI</a>. Once the order is generated, it is sent to the <a href="/w/index.php?title=Order_management_system&amp;action=edit&amp;redlink=1" class="new" title="Order management system (page does not exist)">order management system</a> (OMS), which in turn transmits it to the exchange.<sup id="cite_ref-:2_115-0" class="reference"><a href="#cite_note-:2-115"><span class="cite-bracket">&#91;</span>113<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-:3_116-0" class="reference"><a href="#cite_note-:3-116"><span class="cite-bracket">&#91;</span>114<span class="cite-bracket">&#93;</span></a></sup>
</p><p>Gradually, old-school, high latency architecture of algorithmic systems is being replaced by newer, state-of-the-art, high infrastructure, <a href="/wiki/Low_latency_(capital_markets)" title="Low latency (capital markets)">low-latency networks</a>. The <a href="/wiki/Complex_event_processing" title="Complex event processing">complex event processing engine</a> (CEP), which is the heart of decision making in algo-based trading systems, is used for order routing and risk management.<sup id="cite_ref-:2_115-1" class="reference"><a href="#cite_note-:2-115"><span class="cite-bracket">&#91;</span>113<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-:3_116-1" class="reference"><a href="#cite_note-:3-116"><span class="cite-bracket">&#91;</span>114<span class="cite-bracket">&#93;</span></a></sup>
</p><p>With the emergence of the <a href="/wiki/Financial_Information_eXchange" title="Financial Information eXchange">FIX (Financial Information Exchange)</a> protocol, the connection to different destinations has become easier and the go-to market time has reduced, when it comes to connecting with a new destination. With the standard protocol in place, integration of third-party vendors for data feeds is not cumbersome anymore.<sup id="cite_ref-:2_115-2" class="reference"><a href="#cite_note-:2-115"><span class="cite-bracket">&#91;</span>113<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading2"><h2 id="Effects">Effects</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=32" title="Edit section: Effects"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>One of the more ironic findings of academic research on algorithmic trading might be that individual trader introduce algorithms to make communication more simple and predictable, while markets end up more complex and more uncertain.<sup id="cite_ref-HilbertDarmon2_10-1" class="reference"><a href="#cite_note-HilbertDarmon2-10"><span class="cite-bracket">&#91;</span>10<span class="cite-bracket">&#93;</span></a></sup> Since trading algorithms follow local rules that either respond to programmed instructions or learned patterns, on the micro-level, their automated and reactive behavior makes certain parts of the communication dynamic more predictable. However, on the macro-level, it has been shown that the overall emergent process becomes both more complex and less predictable.<sup id="cite_ref-HilbertDarmon2_10-2" class="reference"><a href="#cite_note-HilbertDarmon2-10"><span class="cite-bracket">&#91;</span>10<span class="cite-bracket">&#93;</span></a></sup> This phenomenon is not unique to the stock market, and has also been detected with editing bots on Wikipedia.<sup id="cite_ref-117" class="reference"><a href="#cite_note-117"><span class="cite-bracket">&#91;</span>115<span class="cite-bracket">&#93;</span></a></sup>
</p><p>Though its development may have been prompted by decreasing trade sizes caused by decimalization, algorithmic trading has reduced trade sizes further. Jobs once done by human traders are being switched to computers. The speeds of computer connections, measured in <a href="/wiki/Millisecond" title="Millisecond">milliseconds</a> and even <a href="/wiki/Microsecond" title="Microsecond">microseconds</a>, have become very important.<sup id="cite_ref-118" class="reference"><a href="#cite_note-118"><span class="cite-bracket">&#91;</span>116<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-119" class="reference"><a href="#cite_note-119"><span class="cite-bracket">&#91;</span>117<span class="cite-bracket">&#93;</span></a></sup>
</p><p>More fully automated markets such as NASDAQ, Direct Edge and BATS (formerly an acronym for Better Alternative Trading System) in the US, have gained market share from less automated markets such as the NYSE. Economies of scale in electronic trading have contributed to lowering commissions and trade processing fees, and contributed to international mergers and consolidation of <a href="/wiki/Financial_market" title="Financial market">financial exchanges</a>.
</p><p>Competition is developing among exchanges for the fastest processing times for completing trades. For example, in June 2007, the <a href="/wiki/London_Stock_Exchange" title="London Stock Exchange">London Stock Exchange</a> launched a new system called TradElect that promises an average 10 millisecond turnaround time from placing an order to final confirmation and can process 3,000 orders per second.<sup id="cite_ref-120" class="reference"><a href="#cite_note-120"><span class="cite-bracket">&#91;</span>118<span class="cite-bracket">&#93;</span></a></sup> Since then, competitive exchanges have continued to reduce latency with turnaround times of 3 milliseconds available. This is of great importance to high-frequency traders, because they have to attempt to pinpoint the consistent and probable performance ranges of given financial instruments. These professionals are often dealing in versions of stock index funds like the E-mini S&amp;Ps, because they seek consistency and risk-mitigation along with top performance. They must filter market data to work into their software programming so that there is the lowest latency and highest liquidity at the time for placing stop-losses and/or taking profits. With high volatility in these markets, this becomes a complex and potentially nerve-wracking endeavor, where a small mistake can lead to a large loss. Absolute frequency data play into the development of the trader's pre-programmed instructions.<sup id="cite_ref-121" class="reference"><a href="#cite_note-121"><span class="cite-bracket">&#91;</span>119<span class="cite-bracket">&#93;</span></a></sup>
</p><p>In the U.S., spending on computers and software in the financial industry increased to $26.4 billion in 2005.<sup id="cite_ref-economist.com_2-1" class="reference"><a href="#cite_note-economist.com-2"><span class="cite-bracket">&#91;</span>2<span class="cite-bracket">&#93;</span></a></sup><sup id="cite_ref-122" class="reference"><a href="#cite_note-122"><span class="cite-bracket">&#91;</span>120<span class="cite-bracket">&#93;</span></a></sup>
</p><p>Algorithmic trading has caused a shift in the types of employees working in the financial industry. For example, many physicists have entered the financial industry as quantitative analysts. Some physicists have even begun to do research in economics as part of doctoral research. This interdisciplinary movement is sometimes called <a href="/wiki/Econophysics" title="Econophysics">econophysics</a>.<sup id="cite_ref-123" class="reference"><a href="#cite_note-123"><span class="cite-bracket">&#91;</span>121<span class="cite-bracket">&#93;</span></a></sup> Some researchers also cite a "cultural divide" between employees of firms primarily engaged in algorithmic trading and traditional investment managers. Algorithmic trading has encouraged an increased focus on data and had decreased emphasis on sell-side research.<sup id="cite_ref-124" class="reference"><a href="#cite_note-124"><span class="cite-bracket">&#91;</span>122<span class="cite-bracket">&#93;</span></a></sup>
</p>
<div class="mw-heading mw-heading2"><h2 id="Communication_standards">Communication standards</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=33" title="Edit section: Communication standards"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Algorithmic trades require communicating considerably more parameters than traditional market and limit orders. A trader on one end (the "<a href="/wiki/Buy_side" title="Buy side">buy side</a>") must enable their trading system (often called an "<a href="/w/index.php?title=Order_management_system&amp;action=edit&amp;redlink=1" class="new" title="Order management system (page does not exist)">order management system</a>" or "<a href="/wiki/Execution_management_system" title="Execution management system">execution management system</a>") to understand a constantly proliferating flow of new algorithmic order types. The R&amp;D and other costs to construct complex new algorithmic orders types, along with the execution infrastructure, and marketing costs to distribute them, are fairly substantial. What was needed was a way that marketers (the "<a href="/wiki/Sell_side" title="Sell side">sell side</a>") could express algo orders electronically such that buy-side traders could just drop the new order types into their system and be ready to trade them without constant coding custom new order entry screens each time.
</p><p><a href="/wiki/FIX_Protocol" class="mw-redirect" title="FIX Protocol">FIX Protocol</a> is a trade association that publishes free, open standards in the securities trading area. The FIX language was originally created by Fidelity Investments, and the association Members include virtually all large and many midsized and smaller broker dealers, money center banks, institutional investors, mutual funds, etc. This institution dominates standard setting in the pretrade and trade areas of security transactions. In 2006–2007, several members got together and published a draft XML standard for expressing algorithmic order types. The standard is called FIX Algorithmic Trading Definition Language (<a href="/wiki/FIXatdl" title="FIXatdl">FIXatdl</a>).
</p>
<div class="mw-heading mw-heading2"><h2 id="See_also">See also</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=34" title="Edit section: See also"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<ul><li><a href="/wiki/2010_Flash_Crash" class="mw-redirect" title="2010 Flash Crash">2010 Flash Crash</a></li>
<li><a href="/wiki/Algorithmic_tacit_collusion" class="mw-redirect" title="Algorithmic tacit collusion">Algorithmic tacit collusion</a></li>
<li><a href="/wiki/Alpha_generation_platform" title="Alpha generation platform">Alpha generation platform</a></li>
<li><a href="/wiki/Alternative_trading_system" title="Alternative trading system">Alternative trading system</a></li>
<li><a href="/wiki/Artificial_intelligence" title="Artificial intelligence">Artificial intelligence</a></li>
<li><a href="/wiki/Best_execution" title="Best execution">Best execution</a></li>
<li><a href="/wiki/Complex_event_processing" title="Complex event processing">Complex event processing</a></li>
<li><a href="/wiki/Electronic_trading_platform" title="Electronic trading platform">Electronic trading platform</a></li>
<li><a href="/wiki/Mirror_trading" title="Mirror trading">Mirror trading</a></li>
<li><a href="/wiki/Quantitative_investing" class="mw-redirect" title="Quantitative investing">Quantitative investing</a></li>
<li><a href="/wiki/Technical_analysis" title="Technical analysis">Technical analysis</a></li></ul>
<div class="mw-heading mw-heading2"><h2 id="Notes">Notes</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=35" title="Edit section: Notes"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<style data-mw-deduplicate="TemplateStyles:r1239543626">.mw-parser-output .reflist{margin-bottom:0.5em;list-style-type:decimal}@media screen{.mw-parser-output .reflist{font-size:90%}}.mw-parser-output .reflist .references{font-size:100%;margin-bottom:0;list-style-type:inherit}.mw-parser-output .reflist-columns-2{column-width:30em}.mw-parser-output .reflist-columns-3{column-width:25em}.mw-parser-output .reflist-columns{margin-top:0.3em}.mw-parser-output .reflist-columns ol{margin-top:0}.mw-parser-output .reflist-columns li{page-break-inside:avoid;break-inside:avoid-column}.mw-parser-output .reflist-upper-alpha{list-style-type:upper-alpha}.mw-parser-output .reflist-upper-roman{list-style-type:upper-roman}.mw-parser-output .reflist-lower-alpha{list-style-type:lower-alpha}.mw-parser-output .reflist-lower-greek{list-style-type:lower-greek}.mw-parser-output .reflist-lower-roman{list-style-type:lower-roman}</style><div class="reflist reflist-lower-alpha">
<div class="mw-references-wrap"><ol class="references">
<li id="cite_note-21"><span class="mw-cite-backlink"><b><a href="#cite_ref-21">^</a></b></span> <span class="reference-text">Trading stocks in fractions dates back to the 1700s.<sup id="cite_ref-19" class="reference"><a href="#cite_note-19"><span class="cite-bracket">&#91;</span>19<span class="cite-bracket">&#93;</span></a></sup> It's a legacy of the Spanish traders, whose currency (the <a href="/wiki/Spanish_real" title="Spanish real">Spanish <i>real</i></a>) was in increments of eighths.<sup id="cite_ref-20" class="reference"><a href="#cite_note-20"><span class="cite-bracket">&#91;</span>20<span class="cite-bracket">&#93;</span></a></sup></span>
</li>
<li id="cite_note-64"><span class="mw-cite-backlink"><b><a href="#cite_ref-64">^</a></b></span> <span class="reference-text">As an arbitrage consists of at least two trades, the metaphor is of putting on a pair of pants, one leg (trade) at a time. The risk that one trade (leg) fails to execute is thus 'leg risk'.</span>
</li>
</ol></div></div>
<div class="mw-heading mw-heading2"><h2 id="References">References</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=36" title="Edit section: References"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1239543626" /><div class="reflist reflist-columns references-column-width" style="column-width: 30em;">
<ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><b><a href="#cite_ref-1">^</a></b></span> <span class="reference-text">The New Investor, UCLA Law Review, available at: <a rel="nofollow" class="external free" href="https://ssrn.com/abstract=2227498">https://ssrn.com/abstract=2227498</a></span>
</li>
<li id="cite_note-economist.com-2"><span class="mw-cite-backlink">^ <a href="#cite_ref-economist.com_2-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-economist.com_2-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><style data-mw-deduplicate="TemplateStyles:r1238218222">.mw-parser-output cite.citation{font-style:inherit;word-wrap:break-word}.mw-parser-output .citation q{quotes:"\"""\"""'""'"}.mw-parser-output .citation:target{background-color:rgba(0,127,255,0.133)}.mw-parser-output .id-lock-free.id-lock-free a{background:url("//upload.wikimedia.org/wikipedia/commons/6/65/Lock-green.svg")right 0.1em center/9px no-repeat}.mw-parser-output .id-lock-limited.id-lock-limited a,.mw-parser-output .id-lock-registration.id-lock-registration a{background:url("//upload.wikimedia.org/wikipedia/commons/d/d6/Lock-gray-alt-2.svg")right 0.1em center/9px no-repeat}.mw-parser-output .id-lock-subscription.id-lock-subscription a{background:url("//upload.wikimedia.org/wikipedia/commons/a/aa/Lock-red-alt-2.svg")right 0.1em center/9px no-repeat}.mw-parser-output .cs1-ws-icon a{background:url("//upload.wikimedia.org/wikipedia/commons/4/4c/Wikisource-logo.svg")right 0.1em center/12px no-repeat}body:not(.skin-timeless):not(.skin-minerva) .mw-parser-output .id-lock-free a,body:not(.skin-timeless):not(.skin-minerva) .mw-parser-output .id-lock-limited a,body:not(.skin-timeless):not(.skin-minerva) .mw-parser-output .id-lock-registration a,body:not(.skin-timeless):not(.skin-minerva) .mw-parser-output .id-lock-subscription a,body:not(.skin-timeless):not(.skin-minerva) .mw-parser-output .cs1-ws-icon a{background-size:contain;padding:0 1em 0 0}.mw-parser-output .cs1-code{color:inherit;background:inherit;border:none;padding:inherit}.mw-parser-output .cs1-hidden-error{display:none;color:var(--color-error,#d33)}.mw-parser-output .cs1-visible-error{color:var(--color-error,#d33)}.mw-parser-output .cs1-maint{display:none;color:#085;margin-left:0.3em}.mw-parser-output .cs1-kern-left{padding-left:0.2em}.mw-parser-output .cs1-kern-right{padding-right:0.2em}.mw-parser-output .citation .mw-selflink{font-weight:inherit}@media screen{.mw-parser-output .cs1-format{font-size:95%}html.skin-theme-clientpref-night .mw-parser-output .cs1-maint{color:#18911f}}@media screen and (prefers-color-scheme:dark){html.skin-theme-clientpref-os .mw-parser-output .cs1-maint{color:#18911f}}</style><cite class="citation news cs1"><a rel="nofollow" class="external text" href="https://web.archive.org/web/20080622165320/http://www.economist.com/finance/displaystory.cfm?story_id=E1_VQSVPRT">"Business and finance"</a>. <i>The Economist</i>. Archived from <a rel="nofollow" class="external text" href="http://www.economist.com/finance/displaystory.cfm?story_id=E1_VQSVPRT">the original</a> on June 22, 2008<span class="reference-accessdate">. Retrieved <span class="nowrap">April 18,</span> 2007</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+Economist&amp;rft.atitle=Business+and+finance&amp;rft_id=http%3A%2F%2Fwww.economist.com%2Ffinance%2Fdisplaystory.cfm%3Fstory_id%3DE1_VQSVPRT&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-3"><span class="mw-cite-backlink"><b><a href="#cite_ref-3">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://www.aitegroup.com/reports/20050328.php">"&#124; Aite Group"</a>. <i>www.aitegroup.com</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=www.aitegroup.com&amp;rft.atitle=%26%23124%3B+Aite+Group&amp;rft_id=https%3A%2F%2Fwww.aitegroup.com%2Freports%2F20050328.php&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-4"><span class="mw-cite-backlink"><b><a href="#cite_ref-4">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFKissell2020" class="citation cs2">Kissell, Robert (September 4, 2020), <a rel="nofollow" class="external text" href="https://www.elsevier.com/books/algorithmic-trading-methods/kissell/978-0-12-815630-8"><i>Algorithmic Trading Methods</i></a>, Elsevier Science, <a href="/wiki/ISBN_(identifier)" class="mw-redirect" title="ISBN (identifier)">ISBN</a>&#160;<a href="/wiki/Special:BookSources/978-0-12-815630-8" title="Special:BookSources/978-0-12-815630-8"><bdi>978-0-12-815630-8</bdi></a></cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=book&amp;rft.btitle=Algorithmic+Trading+Methods&amp;rft.pub=Elsevier+Science&amp;rft.date=2020-09-04&amp;rft.isbn=978-0-12-815630-8&amp;rft.aulast=Kissell&amp;rft.aufirst=Robert&amp;rft_id=https%3A%2F%2Fwww.elsevier.com%2Fbooks%2Falgorithmic-trading-methods%2Fkissell%2F978-0-12-815630-8&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-5"><span class="mw-cite-backlink"><b><a href="#cite_ref-5">^</a></b></span> <span class="reference-text">The New Financial Industry, Alabama Law Review, available at: <a rel="nofollow" class="external free" href="https://ssrn.com/abstract=2417988">https://ssrn.com/abstract=2417988</a></span>
</li>
<li id="cite_note-6"><span class="mw-cite-backlink"><b><a href="#cite_ref-6">^</a></b></span> <span class="reference-text">Lemke and Lins, <i>"Soft Dollars and Other Trading Activities,"</i> §&#160;2:30 (Thomson West, 2015–2016 ed.).</span>
</li>
<li id="cite_note-ReferenceA-7"><span class="mw-cite-backlink">^ <a href="#cite_ref-ReferenceA_7-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-ReferenceA_7-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text">Lemke and Lins, <i>"Soft Dollars and Other Trading Activities,"</i> §&#160;2:31 (Thomson West, 2015–2016 ed.).</span>
</li>
<li id="cite_note-8"><span class="mw-cite-backlink"><b><a href="#cite_ref-8">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFSilla_Brush2012" class="citation web cs1">Silla Brush (June 20, 2012). <a rel="nofollow" class="external text" href="https://www.bloomberg.com/news/2012-06-20/broad-definition-of-high-frequency-trading-urged-by-cftc-panel.html">"CFTC Panel Urges Broad Definition of High-Frequency Trading"</a>. <i>Bloomberg.com</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=Bloomberg.com&amp;rft.atitle=CFTC+Panel+Urges+Broad+Definition+of+High-Frequency+Trading&amp;rft.date=2012-06-20&amp;rft.au=Silla+Brush&amp;rft_id=https%3A%2F%2Fwww.bloomberg.com%2Fnews%2F2012-06-20%2Fbroad-definition-of-high-frequency-trading-urged-by-cftc-panel.html&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-9"><span class="mw-cite-backlink"><b><a href="#cite_ref-9">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="https://www.cftc.gov/PressRoom/PressReleases/pr6178-12Commodity">Futures Trading Commission Votes to Establish a New Subcommittee of the Technology Advisory Committee (TAC) to focus on High Frequency Trading</a>, February 9, 2012, Commodity Futures Trading Commission</span>
</li>
<li id="cite_note-HilbertDarmon2-10"><span class="mw-cite-backlink">^ <a href="#cite_ref-HilbertDarmon2_10-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-HilbertDarmon2_10-1"><sup><i><b>b</b></i></sup></a> <a href="#cite_ref-HilbertDarmon2_10-2"><sup><i><b>c</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://www.martinhilbert.net/how-complexity-and-uncertainty-grew-with-algorithmic-trading/">"How Complexity and Uncertainty Grew with Algorithmic Trading"</a>. <i>MartinHilbert.net</i><span class="reference-accessdate">. Retrieved <span class="nowrap">April 24,</span> 2025</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=MartinHilbert.net&amp;rft.atitle=How+Complexity+and+Uncertainty+Grew+with+Algorithmic+Trading&amp;rft_id=https%3A%2F%2Fwww.martinhilbert.net%2Fhow-complexity-and-uncertainty-grew-with-algorithmic-trading%2F&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-toxic-11"><span class="mw-cite-backlink"><b><a href="#cite_ref-toxic_11-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFO&#39;HaraLopez_De_PradoEasley2011" class="citation cs2">O'Hara, Maureen; Lopez De Prado, Marcos; Easley, David (2011), "Easley, D., M. López de Prado, M. O'Hara: The Microstructure of the 'Flash Crash': Flow Toxicity, Liquidity Crashes and the Probability of Informed Trading", <i>The Journal of Portfolio Management, Vol. 37, No. 2, pp. 118–128, Winter</i>, <a href="/wiki/SSRN_(identifier)" class="mw-redirect" title="SSRN (identifier)">SSRN</a>&#160;<a rel="nofollow" class="external text" href="https://papers.ssrn.com/sol3/papers.cfm?abstract_id=1695041">1695041</a></cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+Journal+of+Portfolio+Management%2C+Vol.+37%2C+No.+2%2C+pp.+118%E2%80%93128%2C+Winter&amp;rft.atitle=Easley%2C+D.%2C+M.+L%C3%B3pez+de+Prado%2C+M.+O%27Hara%3A+The+Microstructure+of+the+%27Flash+Crash%27%3A+Flow+Toxicity%2C+Liquidity+Crashes+and+the+Probability+of+Informed+Trading&amp;rft.date=2011&amp;rft_id=https%3A%2F%2Fpapers.ssrn.com%2Fsol3%2Fpapers.cfm%3Fabstract_id%3D1695041%23id-name%3DSSRN&amp;rft.aulast=O%27Hara&amp;rft.aufirst=Maureen&amp;rft.au=Lopez+De+Prado%2C+Marcos&amp;rft.au=Easley%2C+David&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-12"><span class="mw-cite-backlink"><b><a href="#cite_ref-12">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFAnsariYasminNazZaffar2022" class="citation journal cs1">Ansari, Yasmeen; Yasmin, Sadaf; Naz, Sheneela; Zaffar, Hira; Ali, Zeeshan; Moon, Jihoon; Rho, Seungmin (2022). <a rel="nofollow" class="external text" href="https://doi.org/10.1109%2FACCESS.2022.3226629">"A Deep Reinforcement Learning-Based Decision Support System for Automated Stock Market Trading"</a>. <i>IEEE Access</i>. <b>10</b>: <span class="nowrap">127469–</span>127501. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<span class="id-lock-free" title="Freely accessible"><a rel="nofollow" class="external text" href="https://doi.org/10.1109%2FACCESS.2022.3226629">10.1109/ACCESS.2022.3226629</a></span>. <a href="/wiki/ISSN_(identifier)" class="mw-redirect" title="ISSN (identifier)">ISSN</a>&#160;<a rel="nofollow" class="external text" href="https://search.worldcat.org/issn/2169-3536">2169-3536</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=IEEE+Access&amp;rft.atitle=A+Deep+Reinforcement+Learning-Based+Decision+Support+System+for+Automated+Stock+Market+Trading&amp;rft.volume=10&amp;rft.pages=127469-127501&amp;rft.date=2022&amp;rft_id=info%3Adoi%2F10.1109%2FACCESS.2022.3226629&amp;rft.issn=2169-3536&amp;rft.aulast=Ansari&amp;rft.aufirst=Yasmeen&amp;rft.au=Yasmin%2C+Sadaf&amp;rft.au=Naz%2C+Sheneela&amp;rft.au=Zaffar%2C+Hira&amp;rft.au=Ali%2C+Zeeshan&amp;rft.au=Moon%2C+Jihoon&amp;rft.au=Rho%2C+Seungmin&amp;rft_id=https%3A%2F%2Fdoi.org%2F10.1109%252FACCESS.2022.3226629&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-13"><span class="mw-cite-backlink"><b><a href="#cite_ref-13">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFAdegboyeKampouridisOtero2023" class="citation journal cs1">Adegboye, Adesola; Kampouridis, Michael; Otero, Fernando (June 1, 2023). <a rel="nofollow" class="external text" href="https://doi.org/10.1007%2Fs10462-022-10307-0">"Algorithmic trading with directional changes"</a>. <i>Artificial Intelligence Review</i>. <b>56</b> (6): <span class="nowrap">5619–</span>5644. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<span class="id-lock-free" title="Freely accessible"><a rel="nofollow" class="external text" href="https://doi.org/10.1007%2Fs10462-022-10307-0">10.1007/s10462-022-10307-0</a></span>. <a href="/wiki/ISSN_(identifier)" class="mw-redirect" title="ISSN (identifier)">ISSN</a>&#160;<a rel="nofollow" class="external text" href="https://search.worldcat.org/issn/1573-7462">1573-7462</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Artificial+Intelligence+Review&amp;rft.atitle=Algorithmic+trading+with+directional+changes&amp;rft.volume=56&amp;rft.issue=6&amp;rft.pages=5619-5644&amp;rft.date=2023-06-01&amp;rft_id=info%3Adoi%2F10.1007%2Fs10462-022-10307-0&amp;rft.issn=1573-7462&amp;rft.aulast=Adegboye&amp;rft.aufirst=Adesola&amp;rft.au=Kampouridis%2C+Michael&amp;rft.au=Otero%2C+Fernando&amp;rft_id=https%3A%2F%2Fdoi.org%2F10.1007%252Fs10462-022-10307-0&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-14"><span class="mw-cite-backlink"><b><a href="#cite_ref-14">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFBrogaardHendershottRiordan2014" class="citation journal cs1">Brogaard, Jonathan; Hendershott, Terrence; Riordan, Ryan (August 1, 2014). <span class="id-lock-subscription" title="Paid subscription required"><a rel="nofollow" class="external text" href="https://academic.oup.com/rfs/article-abstract/27/8/2267/1582754?redirectedFrom=fulltext">"High-Frequency Trading and Price Discovery"</a></span>. <i>The Review of Financial Studies</i>. <b>27</b> (8): <span class="nowrap">2267–</span>2306. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.1093%2Frfs%2Fhhu032">10.1093/rfs/hhu032</a>. <a href="/wiki/Hdl_(identifier)" class="mw-redirect" title="Hdl (identifier)">hdl</a>:<span class="id-lock-free" title="Freely accessible"><a rel="nofollow" class="external text" href="https://hdl.handle.net/10419%2F154035">10419/154035</a></span>. <a href="/wiki/ISSN_(identifier)" class="mw-redirect" title="ISSN (identifier)">ISSN</a>&#160;<a rel="nofollow" class="external text" href="https://search.worldcat.org/issn/0893-9454">0893-9454</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+Review+of+Financial+Studies&amp;rft.atitle=High-Frequency+Trading+and+Price+Discovery&amp;rft.volume=27&amp;rft.issue=8&amp;rft.pages=2267-2306&amp;rft.date=2014-08-01&amp;rft_id=info%3Ahdl%2F10419%2F154035&amp;rft.issn=0893-9454&amp;rft_id=info%3Adoi%2F10.1093%2Frfs%2Fhhu032&amp;rft.aulast=Brogaard&amp;rft.aufirst=Jonathan&amp;rft.au=Hendershott%2C+Terrence&amp;rft.au=Riordan%2C+Ryan&amp;rft_id=https%3A%2F%2Facademic.oup.com%2Frfs%2Farticle-abstract%2F27%2F8%2F2267%2F1582754%3FredirectedFrom%3Dfulltext&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-15"><span class="mw-cite-backlink"><b><a href="#cite_ref-15">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFBalpStrampelli2018" class="citation journal cs1">Balp, Gaia; Strampelli, Giovanni (2018). <a rel="nofollow" class="external text" href="https://heinonline.org/HOL/LandingPage?handle=hein.journals/jltp2018&amp;div=18&amp;id=&amp;page=">"Preserving Capital Markets Efficiency in the High-Frequency Trading Era"</a>. <i>University of Illinois Journal of Law, Technology &amp; Policy</i>. <b>2018</b>: 349.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=University+of+Illinois+Journal+of+Law%2C+Technology+%26+Policy&amp;rft.atitle=Preserving+Capital+Markets+Efficiency+in+the+High-Frequency+Trading+Era&amp;rft.volume=2018&amp;rft.pages=349&amp;rft.date=2018&amp;rft.aulast=Balp&amp;rft.aufirst=Gaia&amp;rft.au=Strampelli%2C+Giovanni&amp;rft_id=https%3A%2F%2Fheinonline.org%2FHOL%2FLandingPage%3Fhandle%3Dhein.journals%2Fjltp2018%26div%3D18%26id%3D%26page%3D&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-16"><span class="mw-cite-backlink"><b><a href="#cite_ref-16">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://eur-lex.europa.eu/legal-content/EN/TXT/PDF/?uri=CELEX:32017R0589">"COMMISSION DELEGATED REGULATION (EU) 2017/589"</a> <span class="cs1-format">(PDF)</span>. <i>EUR-Lex.europa.eu</i>. March 11, 2017<span class="reference-accessdate">. Retrieved <span class="nowrap">August 15,</span> 2025</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=EUR-Lex.europa.eu&amp;rft.atitle=COMMISSION+DELEGATED+REGULATION+%28EU%29+2017%2F589&amp;rft.date=2017-03-11&amp;rft_id=https%3A%2F%2Feur-lex.europa.eu%2Flegal-content%2FEN%2FTXT%2FPDF%2F%3Furi%3DCELEX%3A32017R0589&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-:0-17"><span class="mw-cite-backlink">^ <a href="#cite_ref-:0_17-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-:0_17-1"><sup><i><b>b</b></i></sup></a> <a href="#cite_ref-:0_17-2"><sup><i><b>c</b></i></sup></a> <a href="#cite_ref-:0_17-3"><sup><i><b>d</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFMcGowan,_Michael_J.2010" class="citation book cs1">McGowan, Michael J. (November 8, 2010). <i>The Rise of Computerized High Frequency Trading: Use and Controversy</i>. Duke University School of Law. <a href="/wiki/OCLC_(identifier)" class="mw-redirect" title="OCLC (identifier)">OCLC</a>&#160;<a rel="nofollow" class="external text" href="https://search.worldcat.org/oclc/798727906">798727906</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=book&amp;rft.btitle=The+Rise+of+Computerized+High+Frequency+Trading%3A+Use+and+Controversy&amp;rft.pub=Duke+University+School+of+Law&amp;rft.date=2010-11-08&amp;rft_id=info%3Aoclcnum%2F798727906&amp;rft.au=McGowan%2C+Michael+J.&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-18"><span class="mw-cite-backlink"><b><a href="#cite_ref-18">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFSornette2003" class="citation cs2">Sornette (2003), <a rel="nofollow" class="external text" href="https://web.archive.org/web/20100503100422/http://www.tempelhove.de/en/scientific-publications-topmenu-68/39-year-of-2003/126-sornette-2003-critical-market-crashes">"Critical Market Crashes"</a>, <i>Physics Reports</i>, <b>378</b> (1): <span class="nowrap">1–</span>98, <a href="/wiki/ArXiv_(identifier)" class="mw-redirect" title="ArXiv (identifier)">arXiv</a>:<span class="id-lock-free" title="Freely accessible"><a rel="nofollow" class="external text" href="https://arxiv.org/abs/cond-mat/0301543">cond-mat/0301543</a></span>, <a href="/wiki/Bibcode_(identifier)" class="mw-redirect" title="Bibcode (identifier)">Bibcode</a>:<a rel="nofollow" class="external text" href="https://ui.adsabs.harvard.edu/abs/2003PhR...378....1S">2003PhR...378....1S</a>, <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.1016%2FS0370-1573%2802%2900634-8">10.1016/S0370-1573(02)00634-8</a>, <a href="/wiki/S2CID_(identifier)" class="mw-redirect" title="S2CID (identifier)">S2CID</a>&#160;<a rel="nofollow" class="external text" href="https://api.semanticscholar.org/CorpusID:12847333">12847333</a>, archived from <a rel="nofollow" class="external text" href="http://www.tempelhove.de/en/scientific-publications-topmenu-68/39-year-of-2003/126-sornette-2003-critical-market-crashes">the original</a> on May 3, 2010</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Physics+Reports&amp;rft.atitle=Critical+Market+Crashes&amp;rft.volume=378&amp;rft.issue=1&amp;rft.pages=1-98&amp;rft.date=2003&amp;rft_id=info%3Aarxiv%2Fcond-mat%2F0301543&amp;rft_id=https%3A%2F%2Fapi.semanticscholar.org%2FCorpusID%3A12847333%23id-name%3DS2CID&amp;rft_id=info%3Adoi%2F10.1016%2FS0370-1573%2802%2900634-8&amp;rft_id=info%3Abibcode%2F2003PhR...378....1S&amp;rft.au=Sornette&amp;rft_id=http%3A%2F%2Fwww.tempelhove.de%2Fen%2Fscientific-publications-topmenu-68%2F39-year-of-2003%2F126-sornette-2003-critical-market-crashes&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-19"><span class="mw-cite-backlink"><b><a href="#cite_ref-19">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFVinzant2001" class="citation news cs1">Vinzant, Carol (February 13, 2001). <a rel="nofollow" class="external text" href="https://web.archive.org/web/20240617054940/https://www.washingtonpost.com/archive/business/2001/02/13/wall-street-taking-another-look-at-decimals/12c2625b-aead-43a3-b4a4-54ce4ef482c9/">"Wall Street Taking Another Look at Decimals"</a>. <i>The Washington Post</i>. Archived from <a rel="nofollow" class="external text" href="https://www.washingtonpost.com/archive/business/2001/02/13/wall-street-taking-another-look-at-decimals/12c2625b-aead-43a3-b4a4-54ce4ef482c9/">the original</a> on June 17, 2024.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+Washington+Post&amp;rft.atitle=Wall+Street+Taking+Another+Look+at+Decimals&amp;rft.date=2001-02-13&amp;rft.aulast=Vinzant&amp;rft.aufirst=Carol&amp;rft_id=https%3A%2F%2Fwww.washingtonpost.com%2Farchive%2Fbusiness%2F2001%2F02%2F13%2Fwall-street-taking-another-look-at-decimals%2F12c2625b-aead-43a3-b4a4-54ce4ef482c9%2F&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-20"><span class="mw-cite-backlink"><b><a href="#cite_ref-20">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation news cs1"><a rel="nofollow" class="external text" href="https://web.archive.org/web/20240617055342/https://www.cbsnews.com/news/wall-street-adios-fractions/">"Wall Street: Adios, Fractions!"</a>. CBS News. January 28, 2001. Archived from <a rel="nofollow" class="external text" href="https://www.cbsnews.com/news/wall-street-adios-fractions/">the original</a> on June 17, 2024.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.atitle=Wall+Street%3A+Adios%2C+Fractions%21&amp;rft.date=2001-01-28&amp;rft_id=https%3A%2F%2Fwww.cbsnews.com%2Fnews%2Fwall-street-adios-fractions%2F&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-22"><span class="mw-cite-backlink"><b><a href="#cite_ref-22">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFHe2022" class="citation encyclopaedia cs1">He, Yan (2022). <span class="id-lock-subscription" title="Paid subscription required"><a rel="nofollow" class="external text" href="https://link.springer.com/10.1007/978-3-030-91231-4_17">"Decimal Trading in the U.S. Stock Markets"</a></span>. In Lee, Cheng-Few; Lee, Alice C. (eds.). <i>Encyclopedia of Finance</i>. Springer. pp.&#160;<span class="nowrap">719–</span>722. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.1007%2F978-3-030-91231-4_17">10.1007/978-3-030-91231-4_17</a>. <a href="/wiki/ISBN_(identifier)" class="mw-redirect" title="ISBN (identifier)">ISBN</a>&#160;<a href="/wiki/Special:BookSources/978-3-030-91231-4" title="Special:BookSources/978-3-030-91231-4"><bdi>978-3-030-91231-4</bdi></a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=bookitem&amp;rft.atitle=Decimal+Trading+in+the+U.S.+Stock+Markets&amp;rft.btitle=Encyclopedia+of+Finance&amp;rft.pages=719-722&amp;rft.pub=Springer&amp;rft.date=2022&amp;rft_id=info%3Adoi%2F10.1007%2F978-3-030-91231-4_17&amp;rft.isbn=978-3-030-91231-4&amp;rft.aulast=He&amp;rft.aufirst=Yan&amp;rft_id=https%3A%2F%2Flink.springer.com%2F10.1007%2F978-3-030-91231-4_17&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-23"><span class="mw-cite-backlink"><b><a href="#cite_ref-23">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFBowley2011" class="citation news cs1">Bowley, Graham (April 25, 2011). <a rel="nofollow" class="external text" href="https://web.archive.org/web/20240510135300/https://www.nytimes.com/2011/04/26/business/26floor.html">"Preserving a Market Symbol"</a>. <i><a href="/wiki/The_New_York_Times" title="The New York Times">The New York Times</a></i>. Archived from <a rel="nofollow" class="external text" href="https://www.nytimes.com/2011/04/26/business/26floor.html">the original</a> on May 10, 2024.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+New+York+Times&amp;rft.atitle=Preserving+a+Market+Symbol&amp;rft.date=2011-04-25&amp;rft.aulast=Bowley&amp;rft.aufirst=Graham&amp;rft_id=https%3A%2F%2Fwww.nytimes.com%2F2011%2F04%2F26%2Fbusiness%2F26floor.html&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-24"><span class="mw-cite-backlink"><b><a href="#cite_ref-24">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation cs2"><a rel="nofollow" class="external text" href="http://spider.sci.brooklyn.cuny.edu/~parsons/courses/840-spring-2005/notes/das.pdf">"Agent-Human Interactions in the Continuous Double Auction"</a> <span class="cs1-format">(PDF)</span>, <i>IBM T.J.Watson Research Center</i>, August 2001</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=IBM+T.J.Watson+Research+Center&amp;rft.atitle=Agent-Human+Interactions+in+the+Continuous+Double+Auction&amp;rft.date=2001-08&amp;rft_id=http%3A%2F%2Fspider.sci.brooklyn.cuny.edu%2F~parsons%2Fcourses%2F840-spring-2005%2Fnotes%2Fdas.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-25"><span class="mw-cite-backlink"><b><a href="#cite_ref-25">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFGjerstadDickhaut1998" class="citation cs2">Gjerstad, Steven; Dickhaut, John (January 1998), <a rel="nofollow" class="external text" href="http://purl.umn.edu/55740">"Price Formation in Double Auctions, <i>Games and Economic Behavior</i>, 22(1):1–29"</a>, <i>S. Gjerstad and J. Dickhaut</i>, vol.&#160;22, no.&#160;1, pp.&#160;<span class="nowrap">1–</span>29, <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.1006%2Fgame.1997.0576">10.1006/game.1997.0576</a></cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=S.+Gjerstad+and+J.+Dickhaut&amp;rft.atitle=Price+Formation+in+Double+Auctions%2C+Games+and+Economic+Behavior%2C+22%281%29%3A1%E2%80%9329&amp;rft.volume=22&amp;rft.issue=1&amp;rft.pages=1-29&amp;rft.date=1998-01&amp;rft_id=info%3Adoi%2F10.1006%2Fgame.1997.0576&amp;rft.aulast=Gjerstad&amp;rft.aufirst=Steven&amp;rft.au=Dickhaut%2C+John&amp;rft_id=http%3A%2F%2Fpurl.umn.edu%2F55740&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-26"><span class="mw-cite-backlink"><b><a href="#cite_ref-26">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation cs2"><a rel="nofollow" class="external text" href="https://web.archive.org/web/20150924041712/http://www.hpl.hp.com/techreports/97/HPL-97-91.html">"Minimal Intelligence Agents for Bargaining Behaviours in Market-Based Environments, <i>Hewlett-Packard Laboratories Technical Report 97-91</i>"</a>, <i>D. Cliff</i>, August 1997, archived from <a rel="nofollow" class="external text" href="http://www.hpl.hp.com/techreports/97/HPL-97-91.html">the original</a> on September 24, 2015<span class="reference-accessdate">, retrieved <span class="nowrap">December 21,</span> 2011</span></cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=D.+Cliff&amp;rft.atitle=Minimal+Intelligence+Agents+for+Bargaining+Behaviours+in+Market-Based+Environments%2C+Hewlett-Packard+Laboratories+Technical+Report+97-91&amp;rft.date=1997-08&amp;rft_id=http%3A%2F%2Fwww.hpl.hp.com%2Ftechreports%2F97%2FHPL-97-91.html&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-27"><span class="mw-cite-backlink"><b><a href="#cite_ref-27">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFLeshikCralle2011" class="citation book cs1">Leshik, Edward; Cralle, Jane (2011). <i>An Introduction to Algorithmic Trading: Basic to Advanced Strategies</i>. West Sussex, UK: Wiley. p.&#160;169. <a href="/wiki/ISBN_(identifier)" class="mw-redirect" title="ISBN (identifier)">ISBN</a>&#160;<a href="/wiki/Special:BookSources/978-0-470-68954-7" title="Special:BookSources/978-0-470-68954-7"><bdi>978-0-470-68954-7</bdi></a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=book&amp;rft.btitle=An+Introduction+to+Algorithmic+Trading%3A+Basic+to+Advanced+Strategies&amp;rft.place=West+Sussex%2C+UK&amp;rft.pages=169&amp;rft.pub=Wiley&amp;rft.date=2011&amp;rft.isbn=978-0-470-68954-7&amp;rft.aulast=Leshik&amp;rft.aufirst=Edward&amp;rft.au=Cralle%2C+Jane&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-28"><span class="mw-cite-backlink"><b><a href="#cite_ref-28">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation cs2"><a rel="nofollow" class="external text" href="https://web.archive.org/web/20210307233431/http://w4.stern.nyu.edu/news/news.cfm?doc_id=6789">"Algo Arms Race Has a Leader – For Now"</a>, <i>NYU Stern School of Business</i>, December 18, 2006, archived from <a rel="nofollow" class="external text" href="http://w4.stern.nyu.edu/news/news.cfm?doc_id=6789">the original</a> on March 7, 2021<span class="reference-accessdate">, retrieved <span class="nowrap">July 13,</span> 2009</span></cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=NYU+Stern+School+of+Business&amp;rft.atitle=Algo+Arms+Race+Has+a+Leader+%E2%80%93+For+Now&amp;rft.date=2006-12-18&amp;rft_id=http%3A%2F%2Fw4.stern.nyu.edu%2Fnews%2Fnews.cfm%3Fdoc_id%3D6789&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-29"><span class="mw-cite-backlink"><b><a href="#cite_ref-29">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFJohnson2010" class="citation journal cs1">Johnson, Barry (2010). <a rel="nofollow" class="external text" href="https://cir.nii.ac.jp/crid/1130282271924397568">"Algorithmic trading &amp; DMA&#160;: an introduction to direct access trading strategies"</a>. <i>(No Title)</i>. 4Myeloma Press.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=%28No+Title%29&amp;rft.atitle=Algorithmic+trading+%26+DMA+%3A+an+introduction+to+direct+access+trading+strategies&amp;rft.date=2010&amp;rft.aulast=Johnson&amp;rft.aufirst=Barry&amp;rft_id=https%3A%2F%2Fcir.nii.ac.jp%2Fcrid%2F1130282271924397568&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-30"><span class="mw-cite-backlink"><b><a href="#cite_ref-30">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFNarang2009" class="citation book cs1">Narang, R.K. (August 7, 2009). <a rel="nofollow" class="external text" href="https://www.wiley.com/en-us/Inside+the+Black+Box%3A+The+Simple+Truth+About+Quantitative+Trading+-p-9781118267738"><i>Inside the Black Box: The Simple Truth About Quantitative Trading</i></a> (1&#160;ed.). John Wiley &amp; Sons. <a href="/wiki/ISBN_(identifier)" class="mw-redirect" title="ISBN (identifier)">ISBN</a>&#160;<a href="/wiki/Special:BookSources/978-0-470-52914-0" title="Special:BookSources/978-0-470-52914-0"><bdi>978-0-470-52914-0</bdi></a><span class="reference-accessdate">. Retrieved <span class="nowrap">June 26,</span> 2024</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=book&amp;rft.btitle=Inside+the+Black+Box%3A+The+Simple+Truth+About+Quantitative+Trading&amp;rft.edition=1&amp;rft.pub=John+Wiley+%26+Sons&amp;rft.date=2009-08-07&amp;rft.isbn=978-0-470-52914-0&amp;rft.aulast=Narang&amp;rft.aufirst=R.K.&amp;rft_id=https%3A%2F%2Fwww.wiley.com%2Fen-us%2FInside%2Bthe%2BBlack%2BBox%253A%2BThe%2BSimple%2BTruth%2BAbout%2BQuantitative%2BTrading%2B-p-9781118267738&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span> <span class="cs1-visible-error citation-comment"><code class="cs1-code">{{<a href="/wiki/Template:Cite_book" title="Template:Cite book">cite book</a>}}</code>: </span><span class="cs1-visible-error citation-comment"><code class="cs1-code">&#124;website=</code> ignored (<a href="/wiki/Help:CS1_errors#periodical_ignored" title="Help:CS1 errors">help</a>)</span></span>
</li>
<li id="cite_note-31"><span class="mw-cite-backlink"><b><a href="#cite_ref-31">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFLeshikCralle2012" class="citation book cs1">Leshik, Edward A; Cralle, Jane, eds. (January 2, 2012). <a rel="nofollow" class="external text" href="https://onlinelibrary.wiley.com/doi/book/10.1002/9781119206033"><i>An Introduction to Algorithmic Trading: Basic to Advanced Strategies</i></a> (1&#160;ed.). Wiley. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.1002%2F9781119206033">10.1002/9781119206033</a>. <a href="/wiki/ISBN_(identifier)" class="mw-redirect" title="ISBN (identifier)">ISBN</a>&#160;<a href="/wiki/Special:BookSources/978-0-470-68954-7" title="Special:BookSources/978-0-470-68954-7"><bdi>978-0-470-68954-7</bdi></a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=book&amp;rft.btitle=An+Introduction+to+Algorithmic+Trading%3A+Basic+to+Advanced+Strategies&amp;rft.edition=1&amp;rft.pub=Wiley&amp;rft.date=2012-01-02&amp;rft_id=info%3Adoi%2F10.1002%2F9781119206033&amp;rft.isbn=978-0-470-68954-7&amp;rft_id=https%3A%2F%2Fonlinelibrary.wiley.com%2Fdoi%2Fbook%2F10.1002%2F9781119206033&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-32"><span class="mw-cite-backlink"><b><a href="#cite_ref-32">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFKissell2006" class="citation journal cs1">Kissell, Robert (June 30, 2006). <span class="id-lock-subscription" title="Paid subscription required"><a rel="nofollow" class="external text" href="https://www.pm-research.com/content/iijtrade/1/3/6">"The Expanded Implementation Shortfall: Understanding Transaction Cost Components"</a></span>. <i>The Journal of Trading</i>. <b>1</b> (3): <span class="nowrap">6–</span>16. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.3905%2Fjot.2006.644083">10.3905/jot.2006.644083</a>. <a href="/wiki/ISSN_(identifier)" class="mw-redirect" title="ISSN (identifier)">ISSN</a>&#160;<a rel="nofollow" class="external text" href="https://search.worldcat.org/issn/1559-3967">1559-3967</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+Journal+of+Trading&amp;rft.atitle=The+Expanded+Implementation+Shortfall%3A+Understanding+Transaction+Cost+Components&amp;rft.volume=1&amp;rft.issue=3&amp;rft.pages=6-16&amp;rft.date=2006-06-30&amp;rft_id=info%3Adoi%2F10.3905%2Fjot.2006.644083&amp;rft.issn=1559-3967&amp;rft.aulast=Kissell&amp;rft.aufirst=Robert&amp;rft_id=https%3A%2F%2Fwww.pm-research.com%2Fcontent%2Fiijtrade%2F1%2F3%2F6&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-33"><span class="mw-cite-backlink"><b><a href="#cite_ref-33">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation journal cs1"><a rel="nofollow" class="external text" href="https://www.bis.org/statistics/rpfx19.htm">"Triennial Central Bank Survey of Foreign Exchange and Over-the-counter (OTC) Derivatives Markets in 2019"</a>. September 16, 2019.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.atitle=Triennial+Central+Bank+Survey+of+Foreign+Exchange+and+Over-the-counter+%28OTC%29+Derivatives+Markets+in+2019&amp;rft.date=2019-09-16&amp;rft_id=https%3A%2F%2Fwww.bis.org%2Fstatistics%2Frpfx19.htm&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span> <span class="cs1-visible-error citation-comment"><code class="cs1-code">{{<a href="/wiki/Template:Cite_journal" title="Template:Cite journal">cite journal</a>}}</code>: </span><span class="cs1-visible-error citation-comment">Cite journal requires <code class="cs1-code">&#124;journal=</code> (<a href="/wiki/Help:CS1_errors#missing_periodical" title="Help:CS1 errors">help</a>)</span></span>
</li>
<li id="cite_note-34"><span class="mw-cite-backlink"><b><a href="#cite_ref-34">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFFT.com2014" class="citation web cs1">FT.com (April 3, 2014). <a rel="nofollow" class="external text" href="https://www.ft.com/intl/cms/s/0/ac3bdb3a-badf-11e3-8b15-00144feabdc0.html#axzz3alvvLN4Z">"Fierce competition forces 'flash' HFT firms into new markets"</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=unknown&amp;rft.btitle=Fierce+competition+forces+%27flash%27+HFT+firms+into+new+markets&amp;rft.date=2014-04-03&amp;rft.au=FT.com&amp;rft_id=http%3A%2F%2Fwww.ft.com%2Fintl%2Fcms%2Fs%2F0%2Fac3bdb3a-badf-11e3-8b15-00144feabdc0.html%23axzz3alvvLN4Z&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-35"><span class="mw-cite-backlink"><b><a href="#cite_ref-35">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFOpalesque2009" class="citation web cs1">Opalesque (August 4, 2009). <a rel="nofollow" class="external text" href="http://www.opalesque.com/53867/High%20frequency%20trading/frequency_under261.html">"Opalesque Exclusive: High-frequency trading under the microscope"</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=unknown&amp;rft.btitle=Opalesque+Exclusive%3A+High-frequency+trading+under+the+microscope&amp;rft.date=2009-08-04&amp;rft.au=Opalesque&amp;rft_id=http%3A%2F%2Fwww.opalesque.com%2F53867%2FHigh%2520frequency%2520trading%2Ffrequency_under261.html&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-36"><span class="mw-cite-backlink"><b><a href="#cite_ref-36">^</a></b></span> <span class="reference-text">Virtu Financial Form S-1, available at <a rel="nofollow" class="external free" href="https://www.sec.gov/Archives/edgar/data/1592386/000104746914002070/a2218589zs-1.htm">https://www.sec.gov/Archives/edgar/data/1592386/000104746914002070/a2218589zs-1.htm</a></span>
</li>
<li id="cite_note-37"><span class="mw-cite-backlink"><b><a href="#cite_ref-37">^</a></b></span> <span class="reference-text">Laughlin, G. Insights into High Frequency Trading from the Virtu Financial IPO <a rel="nofollow" class="external text" href="https://www.wsj.com/public/resources/documents/VirtuOverview.pdf">WSJ.com</a> Retrieved May 22, 2015.</span>
</li>
<li id="cite_note-38"><span class="mw-cite-backlink"><b><a href="#cite_ref-38">^</a></b></span> <span class="reference-text">Morton Glantz, Robert Kissell. <a rel="nofollow" class="external text" href="https://books.google.com/books?id=7TcTAAAAQBAJ&amp;dq=algorithmic+trading%2C+percent+of+total+trades+%22Aite+Group%22&amp;pg=PA258"><i>Multi-Asset Risk Modeling: Techniques for a Global Economy in an Electronic and Algorithmic Trading Era</i></a>. Academic Press, December 3, 2013, p. 258.</span>
</li>
<li id="cite_note-39"><span class="mw-cite-backlink"><b><a href="#cite_ref-39">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://www.aitegroup.com/">"Aite Group"</a>. <i>www.aitegroup.com</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=www.aitegroup.com&amp;rft.atitle=Aite+Group&amp;rft_id=https%3A%2F%2Fwww.aitegroup.com%2F&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-advtrade-40"><span class="mw-cite-backlink">^ <a href="#cite_ref-advtrade_40-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-advtrade_40-1"><sup><i><b>b</b></i></sup></a> <a href="#cite_ref-advtrade_40-2"><sup><i><b>c</b></i></sup></a> <a href="#cite_ref-advtrade_40-3"><sup><i><b>d</b></i></sup></a> <a href="#cite_ref-advtrade_40-4"><sup><i><b>e</b></i></sup></a></span> <span class="reference-text">Rob Iati, <a rel="nofollow" class="external text" href="http://advancedtrading.com/algorithms/showArticle.jhtml?articleID=218401501">The Real Story of Trading Software Espionage</a> <a rel="nofollow" class="external text" href="https://web.archive.org/web/20110707090025/http://advancedtrading.com/algorithms/showArticle.jhtml?articleID=218401501">Archived</a> July 7, 2011, at the <a href="/wiki/Wayback_Machine" title="Wayback Machine">Wayback Machine</a>, <i>AdvancedTrading.com</i>, July 10, 2009</span>
</li>
<li id="cite_note-41"><span class="mw-cite-backlink"><b><a href="#cite_ref-41">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="https://topics.nytimes.com/topics/reference/timestopics/subjects/h/high_frequency_algorithmic_trading/index.html">Times Topics: High-Frequency Trading</a>, The New York Times, December 20, 2012</span>
</li>
<li id="cite_note-42"><span class="mw-cite-backlink"><b><a href="#cite_ref-42">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="https://www.nytimes.com/2006/08/18/business/worldbusiness/18man.html?ex=1313553600&amp;en=b2fee1b41c85af15&amp;ei=5088&amp;partner=rssnyt&amp;emc=rss">A London Hedge Fund That Opts for Engineers, Not M.B.A.'s</a> by Heather Timmons, August 18, 2006</span>
</li>
<li id="cite_note-43"><span class="mw-cite-backlink"><b><a href="#cite_ref-43">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation news cs1"><a rel="nofollow" class="external text" href="http://www.economist.com/finance/displaystory.cfm?story_id=E1_JDNPSDQ">"Business and finance"</a>. <i>The Economist</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+Economist&amp;rft.atitle=Business+and+finance&amp;rft_id=http%3A%2F%2Fwww.economist.com%2Ffinance%2Fdisplaystory.cfm%3Fstory_id%3DE1_JDNPSDQ&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-44"><span class="mw-cite-backlink"><b><a href="#cite_ref-44">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation cs2"><a rel="nofollow" class="external text" href="http://www.economist.com/finance/displaystory.cfm?story_id=9370718">"Algorithmic trading, Ahead of the tape"</a>, <i>The Economist</i>, vol.&#160;383, no.&#160;June 23, 2007, p.&#160;85, June 21, 2007</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+Economist&amp;rft.atitle=Algorithmic+trading%2C+Ahead+of+the+tape&amp;rft.volume=383&amp;rft.issue=June+23%2C+2007&amp;rft.pages=85&amp;rft.date=2007-06-21&amp;rft_id=http%3A%2F%2Fwww.economist.com%2Ffinance%2Fdisplaystory.cfm%3Fstory_id%3D9370718&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-45"><span class="mw-cite-backlink"><b><a href="#cite_ref-45">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://analyzingalpha.com/algorithmic-trading-statistics">"Algorithmic Trading Statistics (2024) - Analyzing Alpha"</a>. <i>analyzingalpha.com</i>. May 31, 2021<span class="reference-accessdate">. Retrieved <span class="nowrap">June 26,</span> 2024</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=analyzingalpha.com&amp;rft.atitle=Algorithmic+Trading+Statistics+%282024%29+-+Analyzing+Alpha&amp;rft.date=2021-05-31&amp;rft_id=https%3A%2F%2Fanalyzingalpha.com%2Falgorithmic-trading-statistics&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-46"><span class="mw-cite-backlink"><b><a href="#cite_ref-46">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation cs2">"MTS to mull bond access", <i><a href="/wiki/The_Wall_Street_Journal_Europe" title="The Wall Street Journal Europe">The Wall Street Journal Europe</a></i>, p.&#160;21, April 18, 2007</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+Wall+Street+Journal+Europe&amp;rft.atitle=MTS+to+mull+bond+access&amp;rft.pages=21&amp;rft.date=2007-04-18&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-WSJ1-47"><span class="mw-cite-backlink">^ <a href="#cite_ref-WSJ1_47-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-WSJ1_47-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFLauricella2010" class="citation news cs1">Lauricella, Tom (October 2, 2010). <a rel="nofollow" class="external text" href="https://www.wsj.com/articles/SB10001424052748704029304575526390131916792">"How a Trading Algorithm Went Awry"</a>. <i>The Wall Street Journal</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+Wall+Street+Journal&amp;rft.atitle=How+a+Trading+Algorithm+Went+Awry&amp;rft.date=2010-10-02&amp;rft.aulast=Lauricella&amp;rft.aufirst=Tom&amp;rft_id=https%3A%2F%2Fwww.wsj.com%2Farticles%2FSB10001424052748704029304575526390131916792&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-bloomberg1-48"><span class="mw-cite-backlink"><b><a href="#cite_ref-bloomberg1_48-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFMehta2010" class="citation news cs1">Mehta, Nina (October 1, 2010). <a rel="nofollow" class="external text" href="https://www.bloomberg.com/news/2010-10-01/automatic-trade-of-futures-drove-may-6-stock-crash-report-says.html">"Automatic Futures Trade Drove May Stock Crash, Report Says"</a>. Bloomberg L.P.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.atitle=Automatic+Futures+Trade+Drove+May+Stock+Crash%2C+Report+Says&amp;rft.date=2010-10-01&amp;rft.aulast=Mehta&amp;rft.aufirst=Nina&amp;rft_id=https%3A%2F%2Fwww.bloomberg.com%2Fnews%2F2010-10-01%2Fautomatic-trade-of-futures-drove-may-6-stock-crash-report-says.html&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-NYT1-49"><span class="mw-cite-backlink">^ <a href="#cite_ref-NYT1_49-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-NYT1_49-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFBowley2010" class="citation news cs1">Bowley, Graham (October 1, 2010). <a rel="nofollow" class="external text" href="https://www.nytimes.com/2010/10/02/business/02flash.html?_r=1&amp;scp=1&amp;sq=flash+crash&amp;st=nyt">"Lone $4.1 Billion Sale Led to 'Flash Crash' in May"</a>. <i>The New York Times</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+New+York+Times&amp;rft.atitle=Lone+%244.1+Billion+Sale+Led+to+%27Flash+Crash%27+in+May&amp;rft.date=2010-10-01&amp;rft.aulast=Bowley&amp;rft.aufirst=Graham&amp;rft_id=https%3A%2F%2Fwww.nytimes.com%2F2010%2F10%2F02%2Fbusiness%2F02flash.html%3F_r%3D1%26scp%3D1%26sq%3Dflash%2Bcrash%26st%3Dnyt&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-reuters1-50"><span class="mw-cite-backlink"><b><a href="#cite_ref-reuters1_50-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFSpicer2010" class="citation news cs1">Spicer, Jonathan (October 1, 2010). <a rel="nofollow" class="external text" href="https://www.reuters.com/article/idUKN0114164220101001">"Single U.S. trade helped spark May's flash crash"</a>. <i>Reuters</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Reuters&amp;rft.atitle=Single+U.S.+trade+helped+spark+May%27s+flash+crash&amp;rft.date=2010-10-01&amp;rft.aulast=Spicer&amp;rft.aufirst=Jonathan&amp;rft_id=https%3A%2F%2Fwww.reuters.com%2Farticle%2FidUKN0114164220101001&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-wapo1-51"><span class="mw-cite-backlink"><b><a href="#cite_ref-wapo1_51-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFGoldfarb2010" class="citation news cs1">Goldfarb, Zachary (October 1, 2010). <a rel="nofollow" class="external text" href="https://www.washingtonpost.com/wp-dyn/content/article/2010/10/01/AR2010100103969.html">"Report examines May's 'flash crash,' expresses concern over high-speed trading"</a>. <i>Washington Post</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Washington+Post&amp;rft.atitle=Report+examines+May%27s+%27flash+crash%2C%27+expresses+concern+over+high-speed+trading&amp;rft.date=2010-10-01&amp;rft.aulast=Goldfarb&amp;rft.aufirst=Zachary&amp;rft_id=https%3A%2F%2Fwww.washingtonpost.com%2Fwp-dyn%2Fcontent%2Farticle%2F2010%2F10%2F01%2FAR2010100103969.html&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-popper-52"><span class="mw-cite-backlink"><b><a href="#cite_ref-popper_52-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFPopper2010" class="citation news cs1">Popper, Nathaniel (October 1, 2010). <a rel="nofollow" class="external text" href="https://www.latimes.com/business/la-fi-flash-crash-20101002,0,7811306.story">"$4.1-billion trade set off Wall Street 'flash crash,' report finds"</a>. <i>Los Angeles Times</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Los+Angeles+Times&amp;rft.atitle=%244.1-billion+trade+set+off+Wall+Street+%27flash+crash%2C%27+report+finds&amp;rft.date=2010-10-01&amp;rft.aulast=Popper&amp;rft.aufirst=Nathaniel&amp;rft_id=https%3A%2F%2Fwww.latimes.com%2Fbusiness%2Fla-fi-flash-crash-20101002%2C0%2C7811306.story&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-younglai-53"><span class="mw-cite-backlink"><b><a href="#cite_ref-younglai_53-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFYounglai2010" class="citation news cs1">Younglai, Rachelle (October 5, 2010). <a rel="nofollow" class="external text" href="https://www.reuters.com/article/idUSTRE6945LH20101005">"U.S. probes computer algorithms after "flash crash"<span class="cs1-kern-right"></span>"</a>. <i>Reuters</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Reuters&amp;rft.atitle=U.S.+probes+computer+algorithms+after+%22flash+crash%22&amp;rft.date=2010-10-05&amp;rft.aulast=Younglai&amp;rft.aufirst=Rachelle&amp;rft_id=https%3A%2F%2Fwww.reuters.com%2Farticle%2FidUSTRE6945LH20101005&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-reuters2-54"><span class="mw-cite-backlink"><b><a href="#cite_ref-reuters2_54-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFSpicer2010" class="citation news cs1">Spicer, Jonathan (October 15, 2010). <a rel="nofollow" class="external text" href="https://www.reuters.com/article/idUSTRE69E1Q520101015">"Special report: Globally, the flash crash is no flash in the pan"</a>. <i>Reuters</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Reuters&amp;rft.atitle=Special+report%3A+Globally%2C+the+flash+crash+is+no+flash+in+the+pan&amp;rft.date=2010-10-15&amp;rft.aulast=Spicer&amp;rft.aufirst=Jonathan&amp;rft_id=https%3A%2F%2Fwww.reuters.com%2Farticle%2FidUSTRE69E1Q520101015&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-iosco-55"><span class="mw-cite-backlink"><b><a href="#cite_ref-iosco_55-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFTECHNICAL_COMMITTEE_OF_THE_INTERNATIONAL_ORGANIZATION_OF_SECURITIES_COMMISSIONS2011" class="citation cs2">TECHNICAL COMMITTEE OF THE INTERNATIONAL ORGANIZATION OF SECURITIES COMMISSIONS (July 2011), <a rel="nofollow" class="external text" href="https://www.iosco.org/library/pubdocs/pdf/IOSCOPD354.pdf">"Regulatory Issues Raised by the Impact of Technological Changes on Market Integrity and Efficiency"</a> <span class="cs1-format">(PDF)</span>, <i><a href="/wiki/International_Organization_of_Securities_Commissions" title="International Organization of Securities Commissions">IOSCO Technical Committee</a></i><span class="reference-accessdate">, retrieved <span class="nowrap">July 12,</span> 2011</span></cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=IOSCO+Technical+Committee&amp;rft.atitle=Regulatory+Issues+Raised+by+the+Impact+of+Technological+Changes+on+Market+Integrity+and+Efficiency&amp;rft.date=2011-07&amp;rft.au=TECHNICAL+COMMITTEE+OF+THE+INTERNATIONAL+ORGANIZATION+OF+SECURITIES+COMMISSIONS&amp;rft_id=http%3A%2F%2Fwww.iosco.org%2Flibrary%2Fpubdocs%2Fpdf%2FIOSCOPD354.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-reutersiosco-56"><span class="mw-cite-backlink"><b><a href="#cite_ref-reutersiosco_56-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFHuw_Jones2011" class="citation news cs1">Huw Jones (July 7, 2011). <a rel="nofollow" class="external text" href="https://web.archive.org/web/20160128152958/http://uk.reuters.com/article/regulation-trading-idUKN1E7661BX20110707">"Ultra fast trading needs curbs -global regulators"</a>. <i><a href="/wiki/Reuters" title="Reuters">Reuters</a></i>. Archived from <a rel="nofollow" class="external text" href="http://uk.reuters.com/article/regulation-trading-idUKN1E7661BX20110707">the original</a> on January 28, 2016<span class="reference-accessdate">. Retrieved <span class="nowrap">July 12,</span> 2011</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Reuters&amp;rft.atitle=Ultra+fast+trading+needs+curbs+-global+regulators&amp;rft.date=2011-07-07&amp;rft.au=Huw+Jones&amp;rft_id=http%3A%2F%2Fuk.reuters.com%2Farticle%2Fregulation-trading-idUKN1E7661BX20110707&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-kirilenko-57"><span class="mw-cite-backlink"><b><a href="#cite_ref-kirilenko_57-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFKirilenkoKyleSamadiTuzun2014" class="citation cs2">Kirilenko, Andrei; Kyle, Albert S.; Samadi, Mehrdad; Tuzun, Tugkan (May 5, 2014), <a rel="nofollow" class="external text" href="https://www.cftc.gov/ucm/groups/public/@economicanalysis/documents/file/oce_flashcrash0314.pdf"><i>The Flash Crash: The Impact of High Frequency Trading on an Electronic Market</i></a> <span class="cs1-format">(PDF)</span></cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=book&amp;rft.btitle=The+Flash+Crash%3A+The+Impact+of+High+Frequency+Trading+on+an+Electronic+Market&amp;rft.date=2014-05-05&amp;rft.aulast=Kirilenko&amp;rft.aufirst=Andrei&amp;rft.au=Kyle%2C+Albert+S.&amp;rft.au=Samadi%2C+Mehrdad&amp;rft.au=Tuzun%2C+Tugkan&amp;rft_id=http%3A%2F%2Fwww.cftc.gov%2Fucm%2Fgroups%2Fpublic%2F%40economicanalysis%2Fdocuments%2Ffile%2Foce_flashcrash0314.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-AmeryRebalancing-58"><span class="mw-cite-backlink"><b><a href="#cite_ref-AmeryRebalancing_58-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFAmery2010" class="citation news cs1">Amery, Paul (November 11, 2010). <a rel="nofollow" class="external text" href="http://www.indexuniverse.eu/europe/opinion-and-analysis/7634-know-your-enemy.html">"Know Your Enemy"</a>. <i>IndexUniverse.eu</i><span class="reference-accessdate">. Retrieved <span class="nowrap">March 26,</span> 2013</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=IndexUniverse.eu&amp;rft.atitle=Know+Your+Enemy&amp;rft.date=2010-11-11&amp;rft.aulast=Amery&amp;rft.aufirst=Paul&amp;rft_id=http%3A%2F%2Fwww.indexuniverse.eu%2Feurope%2Fopinion-and-analysis%2F7634-know-your-enemy.html&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-Petajisto-59"><span class="mw-cite-backlink">^ <a href="#cite_ref-Petajisto_59-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-Petajisto_59-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFPetajisto2011" class="citation journal cs1">Petajisto, Antti (2011). <a rel="nofollow" class="external text" href="http://www.petajisto.net/papers/petajisto%202011%20jef%20-%20hidden%20cost%20for%20index%20funds.pdf">"The index premium and its hidden cost for index funds"</a> <span class="cs1-format">(PDF)</span>. <i>Journal of Empirical Finance</i>. <b>18</b> (2): <span class="nowrap">271–</span>288. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.1016%2Fj.jempfin.2010.10.002">10.1016/j.jempfin.2010.10.002</a><span class="reference-accessdate">. Retrieved <span class="nowrap">March 26,</span> 2013</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Journal+of+Empirical+Finance&amp;rft.atitle=The+index+premium+and+its+hidden+cost+for+index+funds&amp;rft.volume=18&amp;rft.issue=2&amp;rft.pages=271-288&amp;rft.date=2011&amp;rft_id=info%3Adoi%2F10.1016%2Fj.jempfin.2010.10.002&amp;rft.aulast=Petajisto&amp;rft.aufirst=Antti&amp;rft_id=http%3A%2F%2Fwww.petajisto.net%2Fpapers%2Fpetajisto%25202011%2520jef%2520-%2520hidden%2520cost%2520for%2520index%2520funds.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-Montgomery-60"><span class="mw-cite-backlink">^ <a href="#cite_ref-Montgomery_60-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-Montgomery_60-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFRekenthaler2011" class="citation news cs1">Rekenthaler, John (February–March 2011). <a rel="nofollow" class="external text" href="https://web.archive.org/web/20130729192302/http://www.crsp.com/images/Reprint_Feb_Mar11MornignstarConversation_color.pdf">"The Weighting Game, and Other Puzzles of Indexing"</a> <span class="cs1-format">(PDF)</span>. <i>Morningstar Advisor</i>. pp.&#160;52–56 [56]. Archived from <a rel="nofollow" class="external text" href="http://www.crsp.com/images/Reprint_Feb_Mar11MornignstarConversation_color.pdf">the original</a> <span class="cs1-format">(PDF)</span> on July 29, 2013<span class="reference-accessdate">. Retrieved <span class="nowrap">March 26,</span> 2013</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Morningstar+Advisor&amp;rft.atitle=The+Weighting+Game%2C+and+Other+Puzzles+of+Indexing&amp;rft.pages=52-56+56&amp;rft.date=2011-02%2F2011-03&amp;rft.aulast=Rekenthaler&amp;rft.aufirst=John&amp;rft_id=http%3A%2F%2Fwww.crsp.com%2Fimages%2FReprint_Feb_Mar11MornignstarConversation_color.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-BloombergFA-61"><span class="mw-cite-backlink"><b><a href="#cite_ref-BloombergFA_61-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation news cs1"><a rel="nofollow" class="external text" href="http://www.fa-mag.com/news/high-frequency-firms-tripled-trades-in-stock-rout-wedbush-says-8030.html">"High-Frequency Firms Tripled Trades in Stock Rout, Wedbush Says"</a>. <i>Bloomberg/Financial Advisor</i>. August 12, 2011<span class="reference-accessdate">. Retrieved <span class="nowrap">March 26,</span> 2013</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Bloomberg%2FFinancial+Advisor&amp;rft.atitle=High-Frequency+Firms+Tripled+Trades+in+Stock+Rout%2C+Wedbush+Says&amp;rft.date=2011-08-12&amp;rft_id=http%3A%2F%2Fwww.fa-mag.com%2Fnews%2Fhigh-frequency-firms-tripled-trades-in-stock-rout-wedbush-says-8030.html&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-62"><span class="mw-cite-backlink"><b><a href="#cite_ref-62">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFSiedle2013" class="citation news cs1">Siedle, Ted (March 25, 2013). <a rel="nofollow" class="external text" href="https://www.forbes.com/sites/edwardsiedle/2013/03/25/americans-want-more-social-security-not-less-let-them-buy-it/">"Americans Want More Social Security, Not Less"</a>. <i>Forbes</i><span class="reference-accessdate">. Retrieved <span class="nowrap">March 26,</span> 2013</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Forbes&amp;rft.atitle=Americans+Want+More+Social+Security%2C+Not+Less&amp;rft.date=2013-03-25&amp;rft.aulast=Siedle&amp;rft.aufirst=Ted&amp;rft_id=https%3A%2F%2Fwww.forbes.com%2Fsites%2Fedwardsiedle%2F2013%2F03%2F25%2Famericans-want-more-social-security-not-less-let-them-buy-it%2F&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-63"><span class="mw-cite-backlink"><b><a href="#cite_ref-63">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://methods.stat.kit.edu/download/doc_secure1/ptem342v144techreport.pdf">"The Application of Pairs Trading to Energy Futures Markets"</a> <span class="cs1-format">(PDF)</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=unknown&amp;rft.btitle=The+Application+of+Pairs+Trading+to+Energy+Futures+Markets&amp;rft_id=https%3A%2F%2Fmethods.stat.kit.edu%2Fdownload%2Fdoc_secure1%2Fptem342v144techreport.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-65"><span class="mw-cite-backlink"><b><a href="#cite_ref-65">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFSattarovMuminovLeeKang2020" class="citation journal cs1">Sattarov, Otabek; Muminov, Azamjon; Lee, Cheol Won; Kang, Hyun Kyu; Oh, Ryumduck; Ahn, Junho; Oh, Hyung Jun; Jeon, Heung Seok (January 1, 2020). <a rel="nofollow" class="external text" href="https://doi.org/10.3390%2Fapp10041506">"Recommending Cryptocurrency Trading Points with Deep Reinforcement Learning Approach"</a>. <i>Applied Sciences</i>. <b>10</b> (4): 1506. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<span class="id-lock-free" title="Freely accessible"><a rel="nofollow" class="external text" href="https://doi.org/10.3390%2Fapp10041506">10.3390/app10041506</a></span>. <a href="/wiki/ISSN_(identifier)" class="mw-redirect" title="ISSN (identifier)">ISSN</a>&#160;<a rel="nofollow" class="external text" href="https://search.worldcat.org/issn/2076-3417">2076-3417</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Applied+Sciences&amp;rft.atitle=Recommending+Cryptocurrency+Trading+Points+with+Deep+Reinforcement+Learning+Approach&amp;rft.volume=10&amp;rft.issue=4&amp;rft.pages=1506&amp;rft.date=2020-01-01&amp;rft_id=info%3Adoi%2F10.3390%2Fapp10041506&amp;rft.issn=2076-3417&amp;rft.aulast=Sattarov&amp;rft.aufirst=Otabek&amp;rft.au=Muminov%2C+Azamjon&amp;rft.au=Lee%2C+Cheol+Won&amp;rft.au=Kang%2C+Hyun+Kyu&amp;rft.au=Oh%2C+Ryumduck&amp;rft.au=Ahn%2C+Junho&amp;rft.au=Oh%2C+Hyung+Jun&amp;rft.au=Jeon%2C+Heung+Seok&amp;rft_id=https%3A%2F%2Fdoi.org%2F10.3390%252Fapp10041506&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-:1-66"><span class="mw-cite-backlink">^ <a href="#cite_ref-:1_66-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-:1_66-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFWillis2001" class="citation web cs1">Willis, Andrew (2001). <a rel="nofollow" class="external text" href="https://web.archive.org/web/20210924050440/http://dl.fxf1.com/files/books/english/Andrew%20Willis%20-%20The%20Insiders%20Guide%20to%20Trading%20the%20World%20Stock%20Markets.pdf">"The Insiders Guide to Trading the World Stock Markets"</a> <span class="cs1-format">(PDF)</span>. Archived from <a rel="nofollow" class="external text" href="http://dl.fxf1.com/files/books/english/Andrew%20Willis%20-%20The%20Insiders%20Guide%20to%20Trading%20the%20World%20Stock%20Markets.pdf">the original</a> <span class="cs1-format">(PDF)</span> on September 24, 2021.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=unknown&amp;rft.btitle=The+Insiders+Guide+to+Trading+the+World+Stock+Markets&amp;rft.date=2001&amp;rft.aulast=Willis&amp;rft.aufirst=Andrew&amp;rft_id=http%3A%2F%2Fdl.fxf1.com%2Ffiles%2Fbooks%2Fenglish%2FAndrew%2520Willis%2520-%2520The%2520Insiders%2520Guide%2520to%2520Trading%2520the%2520World%2520Stock%2520Markets.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-67"><span class="mw-cite-backlink"><b><a href="#cite_ref-67">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://listingcenter.nasdaq.com/rulebook/nasdaq/rules/nasdaq-equity-2">"Rules | The Nasdaq Stock Market"</a>. <i>Nasdaq</i>. November 23, 2020<span class="reference-accessdate">. Retrieved <span class="nowrap">March 29,</span> 2024</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=Nasdaq&amp;rft.atitle=Rules+%7C+The+Nasdaq+Stock+Market&amp;rft.date=2020-11-23&amp;rft_id=https%3A%2F%2Flistingcenter.nasdaq.com%2Frulebook%2Fnasdaq%2Frules%2Fnasdaq-equity-2&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-68"><span class="mw-cite-backlink"><b><a href="#cite_ref-68">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFBorelli2001" class="citation web cs1">Borelli, Mark (2001). <a rel="nofollow" class="external text" href="https://heinonline.org/HOL/Page?handle=hein.journals/luclj32&amp;div=32&amp;g_sent=1&amp;casa_token=">"Market Making in the Electronic Age"</a>. <i>heinonline.org</i><span class="reference-accessdate">. Retrieved <span class="nowrap">June 26,</span> 2024</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=heinonline.org&amp;rft.atitle=Market+Making+in+the+Electronic+Age&amp;rft.date=2001&amp;rft.aulast=Borelli&amp;rft.aufirst=Mark&amp;rft_id=https%3A%2F%2Fheinonline.org%2FHOL%2FPage%3Fhandle%3Dhein.journals%2Fluclj32%26div%3D32%26g_sent%3D1%26casa_token%3D&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-69"><span class="mw-cite-backlink"><b><a href="#cite_ref-69">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFShen2013" class="citation journal cs1">Shen, Jackie (2013). <a rel="nofollow" class="external text" href="https://ssrn.com/abstract=2327835">"A Pre-Trade Algorithmic Trading Model under Given Volume Measures and Generic Price Dynamics (GVM-GPD)"</a>. <i>SSRN</i>. <a href="/wiki/ArXiv_(identifier)" class="mw-redirect" title="ArXiv (identifier)">arXiv</a>:<span class="id-lock-free" title="Freely accessible"><a rel="nofollow" class="external text" href="https://arxiv.org/abs/1309.5046">1309.5046</a></span>. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.2139%2Fssrn.2327835">10.2139/ssrn.2327835</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=SSRN&amp;rft.atitle=A+Pre-Trade+Algorithmic+Trading+Model+under+Given+Volume+Measures+and+Generic+Price+Dynamics+%28GVM-GPD%29&amp;rft.date=2013&amp;rft_id=info%3Aarxiv%2F1309.5046&amp;rft_id=info%3Adoi%2F10.2139%2Fssrn.2327835&amp;rft.aulast=Shen&amp;rft.aufirst=Jackie&amp;rft_id=https%3A%2F%2Fssrn.com%2Fabstract%3D2327835&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-70"><span class="mw-cite-backlink"><b><a href="#cite_ref-70">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFShenYu2014" class="citation web cs1">Shen, Jackie; Yu, Yingjie (2014). <a rel="nofollow" class="external text" href="https://ssrn.com/abstract=2507002">"Styled Algorithmic Trading and the MV-MVP Style"</a>. <i>SSRN</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=SSRN&amp;rft.atitle=Styled+Algorithmic+Trading+and+the+MV-MVP+Style&amp;rft.date=2014&amp;rft.aulast=Shen&amp;rft.aufirst=Jackie&amp;rft.au=Yu%2C+Yingjie&amp;rft_id=https%3A%2F%2Fssrn.com%2Fabstract%3D2507002&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-71"><span class="mw-cite-backlink"><b><a href="#cite_ref-71">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFShen2017" class="citation web cs1">Shen, Jackie (2017). <a rel="nofollow" class="external text" href="https://ssrn.com/abstract=2984297">"Hybrid IS-VWAP Dynamic Algorithmic Trading via LQR"</a>. <i>SSRN</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=SSRN&amp;rft.atitle=Hybrid+IS-VWAP+Dynamic+Algorithmic+Trading+via+LQR&amp;rft.date=2017&amp;rft.aulast=Shen&amp;rft.aufirst=Jackie&amp;rft_id=https%3A%2F%2Fssrn.com%2Fabstract%3D2984297&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-Wilmott-72"><span class="mw-cite-backlink"><b><a href="#cite_ref-Wilmott_72-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFWilmott2009" class="citation news cs1"><a href="/wiki/Paul_Wilmott" title="Paul Wilmott">Wilmott, Paul</a> (July 29, 2009). <a rel="nofollow" class="external text" href="https://www.nytimes.com/2009/07/29/opinion/29wilmott.html">"Hurrying into the Next Panic"</a>. <i>The New York Times</i>. p.&#160;A19<span class="reference-accessdate">. Retrieved <span class="nowrap">July 29,</span> 2009</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+New+York+Times&amp;rft.atitle=Hurrying+into+the+Next+Panic&amp;rft.pages=A19&amp;rft.date=2009-07-29&amp;rft.aulast=Wilmott&amp;rft.aufirst=Paul&amp;rft_id=https%3A%2F%2Fwww.nytimes.com%2F2009%2F07%2F29%2Fopinion%2F29wilmott.html&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-73"><span class="mw-cite-backlink"><b><a href="#cite_ref-73">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation cs2"><a rel="nofollow" class="external text" href="https://web.archive.org/web/20091007214549/http://www.ftknowledge.com/pma/brochures/Trading%20with%20the%20help%20of%20guerrillas%20and%20snipers.pdf">"Trading with the help of 'guerrillas' and 'snipers'<span class="cs1-kern-right"></span>"</a> <span class="cs1-format">(PDF)</span>, <i>Financial Times</i>, March 19, 2007, archived from <a rel="nofollow" class="external text" href="http://www.ftknowledge.com/pma/brochures/Trading%20with%20the%20help%20of%20guerrillas%20and%20snipers.pdf">the original</a> <span class="cs1-format">(PDF)</span> on October 7, 2009</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Financial+Times&amp;rft.atitle=Trading+with+the+help+of+%27guerrillas%27+and+%27snipers%27&amp;rft.date=2007-03-19&amp;rft_id=http%3A%2F%2Fwww.ftknowledge.com%2Fpma%2Fbrochures%2FTrading%2520with%2520the%2520help%2520of%2520guerrillas%2520and%2520snipers.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-74"><span class="mw-cite-backlink"><b><a href="#cite_ref-74">^</a></b></span> <span class="reference-text">Lemke and Lins, <i>"Soft Dollars and Other Trading Activities,"</i> §&#160;2:29 (Thomson West, 2015–2016 ed.).</span>
</li>
<li id="cite_note-sharks-75"><span class="mw-cite-backlink"><b><a href="#cite_ref-sharks_75-0">^</a></b></span> <span class="reference-text">Rob Curren, <a rel="nofollow" class="external text" href="https://www.wsj.com/articles/SB121911298392752051">Watch Out for Sharks in Dark Pools</a>, The Wall Street Journal, August 19, 2008, p. c5. Available at <a rel="nofollow" class="external text" href="https://blogs.wsj.com/marketbeat/2008/08/18/trading-in-a-dark-pool-watch-for-sharks/">WSJ Blogs</a> retrieved August 19, 2008</span>
</li>
<li id="cite_note-iht.com-76"><span class="mw-cite-backlink">^ <a href="#cite_ref-iht.com_76-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-iht.com_76-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><a rel="nofollow" class="external text" href="https://www.nytimes.com/2006/11/23/business/worldbusiness/23iht-trading.3647885.html">Artificial intelligence applied heavily to picking stocks</a> by Charles Duhigg, November 23, 2006</span>
</li>
<li id="cite_note-77"><span class="mw-cite-backlink"><b><a href="#cite_ref-77">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFOladimejiFolasade2016" class="citation journal cs1">Oladimeji, Ismaila W.; Folasade, Ismaila M. (April 1, 2016). <a rel="nofollow" class="external text" href="https://www.jmest.org/wp-content/uploads/JMESTN42351498.pdf">"Forecasting Shares Trading Signals With Finite State Machine Variant"</a> <span class="cs1-format">(PDF)</span>. <i>Journal of Multidisciplinary Engineering Science and Technology</i>. <b>3</b> (4). <a href="/wiki/ISSN_(identifier)" class="mw-redirect" title="ISSN (identifier)">ISSN</a>&#160;<a rel="nofollow" class="external text" href="https://search.worldcat.org/issn/2458-9403">2458-9403</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Journal+of+Multidisciplinary+Engineering+Science+and+Technology&amp;rft.atitle=Forecasting+Shares+Trading+Signals+With+Finite+State+Machine+Variant&amp;rft.volume=3&amp;rft.issue=4&amp;rft.date=2016-04-01&amp;rft.issn=2458-9403&amp;rft.aulast=Oladimeji&amp;rft.aufirst=Ismaila+W.&amp;rft.au=Folasade%2C+Ismaila+M.&amp;rft_id=https%3A%2F%2Fwww.jmest.org%2Fwp-content%2Fuploads%2FJMESTN42351498.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-78"><span class="mw-cite-backlink"><b><a href="#cite_ref-78">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFManeesilpPrasatkaew2014" class="citation book cs1">Maneesilp, K.; Prasatkaew, C. (November 1, 2014). "Price Pattern Detection Using Finite State Machine with Fuzzy Transitions". <i>2014 IEEE 11th International Conference on e-Business Engineering</i>. pp.&#160;<span class="nowrap">126–</span>130. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.1109%2FICEBE.2014.31">10.1109/ICEBE.2014.31</a>. <a href="/wiki/ISBN_(identifier)" class="mw-redirect" title="ISBN (identifier)">ISBN</a>&#160;<a href="/wiki/Special:BookSources/978-1-4799-6563-2" title="Special:BookSources/978-1-4799-6563-2"><bdi>978-1-4799-6563-2</bdi></a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=bookitem&amp;rft.atitle=Price+Pattern+Detection+Using+Finite+State+Machine+with+Fuzzy+Transitions&amp;rft.btitle=2014+IEEE+11th+International+Conference+on+e-Business+Engineering&amp;rft.pages=126-130&amp;rft.date=2014-11-01&amp;rft_id=info%3Adoi%2F10.1109%2FICEBE.2014.31&amp;rft.isbn=978-1-4799-6563-2&amp;rft.aulast=Maneesilp&amp;rft.aufirst=K.&amp;rft.au=Prasatkaew%2C+C.&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-79"><span class="mw-cite-backlink"><b><a href="#cite_ref-79">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation news cs1"><a rel="nofollow" class="external text" href="https://algorithmictrading.net/project/robust-algorithmic-trading-strategies/">"How To Build Robust Algorithmic Trading Strategies"</a>. <i>AlgorithmicTrading.net</i><span class="reference-accessdate">. Retrieved <span class="nowrap">August 8,</span> 2017</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=AlgorithmicTrading.net&amp;rft.atitle=How+To+Build+Robust+Algorithmic+Trading+Strategies&amp;rft_id=https%3A%2F%2Falgorithmictrading.net%2Fproject%2Frobust-algorithmic-trading-strategies%2F&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-80"><span class="mw-cite-backlink"><b><a href="#cite_ref-80">^</a></b></span> <span class="reference-text">[6]	Cont, R. (2001). "Empirical Properties of Asset Returns: Stylized Facts and Statistical Issues." Quantitative Finance, 1(2), 223-236.</span>
</li>
<li id="cite_note-81"><span class="mw-cite-backlink"><b><a href="#cite_ref-81">^</a></b></span> <span class="reference-text">[7]	Embrechts, P., McNeil, A., &amp; Straumann, D. (1999). "Correlation and Dependence in Risk Management: Properties and Pitfalls." Risk Management: Value at Risk and Beyond, 176-223.</span>
</li>
<li id="cite_note-82"><span class="mw-cite-backlink"><b><a href="#cite_ref-82">^</a></b></span> <span class="reference-text">[14]	Peters, E. E. (1994). "Fractal Market Analysis: Applying Chaos Theory to Investment and Economics." Wiley. </span>
</li>
<li id="cite_note-83"><span class="mw-cite-backlink"><b><a href="#cite_ref-83">^</a></b></span> <span class="reference-text">[9]	Lo, A. W. (2004). "The Adaptive Markets Hypothesis: Market Efficiency from an Evolutionary Perspective." The Journal of Portfolio Management, 30(5), 15-29.</span>
</li>
<li id="cite_note-84"><span class="mw-cite-backlink"><b><a href="#cite_ref-84">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFKenettStanleyBen-Jacob2013" class="citation journal cs1">Kenett, Dror Y.; Stanley, H. Eugene; Ben-Jacob, Eshel (July 2, 2013). <a rel="nofollow" class="external text" href="https://www.ncbi.nlm.nih.gov/pmc/articles/PMC3743071">"How High Frequency Trading Affects a Market Index"</a>. <i>Scientific Reports</i>. <b>3</b> 2110. <a href="/wiki/Bibcode_(identifier)" class="mw-redirect" title="Bibcode (identifier)">Bibcode</a>:<a rel="nofollow" class="external text" href="https://ui.adsabs.harvard.edu/abs/2013NatSR...3.2110K">2013NatSR...3.2110K</a>. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.1038%2Fsrep02110">10.1038/srep02110</a>. <a href="/wiki/PMC_(identifier)" class="mw-redirect" title="PMC (identifier)">PMC</a>&#160;<span class="id-lock-free" title="Freely accessible"><a rel="nofollow" class="external text" href="https://www.ncbi.nlm.nih.gov/pmc/articles/PMC3743071">3743071</a></span>. <a href="/wiki/PMID_(identifier)" class="mw-redirect" title="PMID (identifier)">PMID</a>&#160;<a rel="nofollow" class="external text" href="https://pubmed.ncbi.nlm.nih.gov/23817553">23817553</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Scientific+Reports&amp;rft.atitle=How+High+Frequency+Trading+Affects+a+Market+Index&amp;rft.volume=3&amp;rft.artnum=2110&amp;rft.date=2013-07-02&amp;rft_id=https%3A%2F%2Fwww.ncbi.nlm.nih.gov%2Fpmc%2Farticles%2FPMC3743071%23id-name%3DPMC&amp;rft_id=info%3Apmid%2F23817553&amp;rft_id=info%3Adoi%2F10.1038%2Fsrep02110&amp;rft_id=info%3Abibcode%2F2013NatSR...3.2110K&amp;rft.aulast=Kenett&amp;rft.aufirst=Dror+Y.&amp;rft.au=Stanley%2C+H.+Eugene&amp;rft.au=Ben-Jacob%2C+Eshel&amp;rft_id=https%3A%2F%2Fwww.ncbi.nlm.nih.gov%2Fpmc%2Farticles%2FPMC3743071&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-mktbeat-85"><span class="mw-cite-backlink">^ <a href="#cite_ref-mktbeat_85-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-mktbeat_85-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text">Geoffrey Rogow, <a rel="nofollow" class="external text" href="https://blogs.wsj.com/marketbeat/2009/06/19/rise-of-the-market-machines/">Rise of the (Market) Machines</a>, <i>The Wall Street Journal</i>, June 19, 2009</span>
</li>
<li id="cite_note-Olsen-86"><span class="mw-cite-backlink">^ <a href="#cite_ref-Olsen_86-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-Olsen_86-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://web.archive.org/web/20120225230059/http://www.olsen.ch/fileadmin/Publications/Archive//hedgefuture.pdf">"OlsenInvest – Scientific Investing"</a> <span class="cs1-format">(PDF)</span>. Archived from <a rel="nofollow" class="external text" href="http://www.olsen.ch/fileadmin/Publications/Archive//hedgefuture.pdf">the original</a> <span class="cs1-format">(PDF)</span> on February 25, 2012.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=unknown&amp;rft.btitle=OlsenInvest+%E2%80%93+Scientific+Investing&amp;rft_id=http%3A%2F%2Fwww.olsen.ch%2Ffileadmin%2FPublications%2FArchive%2F%2Fhedgefuture.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-87"><span class="mw-cite-backlink"><b><a href="#cite_ref-87">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFHendershott,_Terrence,_Charles_M._Jones,_and_Albert_J._Menkveld.2010" class="citation cs2">Hendershott, Terrence, Charles M. Jones, and Albert J. Menkveld. (2010), <a rel="nofollow" class="external text" href="http://nbn-resolving.de/urn/resolver.pl?urn:nbn:de:hebis:30-62202">"Does Algorithmic Trading Improve Liquidity?"</a>, <i>Journal of Finance</i>, <b>66</b>: <span class="nowrap">1–</span>33, <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.1111%2Fj.1540-6261.2010.01624.x">10.1111/j.1540-6261.2010.01624.x</a>, <a href="/wiki/Hdl_(identifier)" class="mw-redirect" title="Hdl (identifier)">hdl</a>:<span class="id-lock-free" title="Freely accessible"><a rel="nofollow" class="external text" href="https://hdl.handle.net/10.1111%2Fj.1540-6261.2010.01624.x">10.1111/j.1540-6261.2010.01624.x</a></span>, <a href="/wiki/S2CID_(identifier)" class="mw-redirect" title="S2CID (identifier)">S2CID</a>&#160;<a rel="nofollow" class="external text" href="https://api.semanticscholar.org/CorpusID:30441">30441</a>, <a href="/wiki/SSRN_(identifier)" class="mw-redirect" title="SSRN (identifier)">SSRN</a>&#160;<a rel="nofollow" class="external text" href="https://papers.ssrn.com/sol3/papers.cfm?abstract_id=1100635">1100635</a></cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Journal+of+Finance&amp;rft.atitle=Does+Algorithmic+Trading+Improve+Liquidity%3F&amp;rft.volume=66&amp;rft.pages=1-33&amp;rft.date=2010&amp;rft_id=info%3Ahdl%2F10.1111%2Fj.1540-6261.2010.01624.x&amp;rft_id=https%3A%2F%2Fapi.semanticscholar.org%2FCorpusID%3A30441%23id-name%3DS2CID&amp;rft_id=https%3A%2F%2Fpapers.ssrn.com%2Fsol3%2Fpapers.cfm%3Fabstract_id%3D1100635%23id-name%3DSSRN&amp;rft_id=info%3Adoi%2F10.1111%2Fj.1540-6261.2010.01624.x&amp;rft.au=Hendershott%2C+Terrence%2C+Charles+M.+Jones%2C+and+Albert+J.+Menkveld.&amp;rft_id=http%3A%2F%2Fnbn-resolving.de%2Furn%2Fresolver.pl%3Furn%3Anbn%3Ade%3Ahebis%3A30-62202&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span><span class="cs1-maint citation-comment"><code class="cs1-code">{{<a href="/wiki/Template:Citation" title="Template:Citation">citation</a>}}</code>:  CS1 maint: multiple names: authors list (<a href="/wiki/Category:CS1_maint:_multiple_names:_authors_list" title="Category:CS1 maint: multiple names: authors list">link</a>)</span></span>
</li>
<li id="cite_note-88"><span class="mw-cite-backlink"><b><a href="#cite_ref-88">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFMenkveldJovanovic2010" class="citation cs2">Menkveld, Albert J.; Jovanovic, Boyan (2010), "Jovanovic, Boyan, and Albert J. Menkveld. Middlemen in Securities Markets", <i>working paper</i>, <a href="/wiki/SSRN_(identifier)" class="mw-redirect" title="SSRN (identifier)">SSRN</a>&#160;<a rel="nofollow" class="external text" href="https://papers.ssrn.com/sol3/papers.cfm?abstract_id=1624329">1624329</a></cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=working+paper&amp;rft.atitle=Jovanovic%2C+Boyan%2C+and+Albert+J.+Menkveld.+Middlemen+in+Securities+Markets&amp;rft.date=2010&amp;rft_id=https%3A%2F%2Fpapers.ssrn.com%2Fsol3%2Fpapers.cfm%3Fabstract_id%3D1624329%23id-name%3DSSRN&amp;rft.aulast=Menkveld&amp;rft.aufirst=Albert+J.&amp;rft.au=Jovanovic%2C+Boyan&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-cutter-89"><span class="mw-cite-backlink"><b><a href="#cite_ref-cutter_89-0">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFJames_E._Hollis2013" class="citation news cs1">James E. Hollis (September 2013). <a rel="nofollow" class="external text" href="https://web.archive.org/web/20150701041403/http://www.cutterassociates.com/pdfs/cadv_2013_09.pdf">"HFT: Boon? Or Impending Disaster?"</a> <span class="cs1-format">(PDF)</span>. <i><a href="/w/index.php?title=Cutter_Associates&amp;action=edit&amp;redlink=1" class="new" title="Cutter Associates (page does not exist)">Cutter Associates</a></i>. Archived from <a rel="nofollow" class="external text" href="http://www.cutterassociates.com/pdfs/cadv_2013_09.pdf">the original</a> <span class="cs1-format">(PDF)</span> on July 1, 2015<span class="reference-accessdate">. Retrieved <span class="nowrap">July 1,</span> 2014</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Cutter+Associates&amp;rft.atitle=HFT%3A+Boon%3F+Or+Impending+Disaster%3F&amp;rft.date=2013-09&amp;rft.au=James+E.+Hollis&amp;rft_id=http%3A%2F%2Fwww.cutterassociates.com%2Fpdfs%2Fcadv_2013_09.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-90"><span class="mw-cite-backlink"><b><a href="#cite_ref-90">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation cs2"><a rel="nofollow" class="external text" href="http://www.iht.com/articles/ap/2007/07/02/business/NA-FIN-COM-US-Citigroup-Automated-Trading-Desk.php">"Citigroup to expand electronic trading capabilities by buying Automated Trading Desk"</a>, <i>The Associated Press</i>, International Herald Tribune, July 2, 2007<span class="reference-accessdate">, retrieved <span class="nowrap">July 4,</span> 2007</span></cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+Associated+Press&amp;rft.atitle=Citigroup+to+expand+electronic+trading+capabilities+by+buying+Automated+Trading+Desk&amp;rft.date=2007-07-02&amp;rft_id=http%3A%2F%2Fwww.iht.com%2Farticles%2Fap%2F2007%2F07%2F02%2Fbusiness%2FNA-FIN-COM-US-Citigroup-Automated-Trading-Desk.php&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-eventarbdef-91"><span class="mw-cite-backlink"><b><a href="#cite_ref-eventarbdef_91-0">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="http://www.amex.com/servlet/AmexFnDictionary?pageid=display&amp;word=Event%20Arbitrage">Event Arb Definition</a> <i>Amex.com</i>, September 4, 2010</span>
</li>
<li id="cite_note-92"><span class="mw-cite-backlink"><b><a href="#cite_ref-92">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="http://www.investopedia.com/terms/q/quote-stuffing.asp">"Quote Stuffing Definition"</a>. Investopedia<span class="reference-accessdate">. Retrieved <span class="nowrap">October 27,</span> 2014</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=unknown&amp;rft.btitle=Quote+Stuffing+Definition&amp;rft.pub=Investopedia&amp;rft_id=http%3A%2F%2Fwww.investopedia.com%2Fterms%2Fq%2Fquote-stuffing.asp&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-93"><span class="mw-cite-backlink"><b><a href="#cite_ref-93">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFDiazTheodoulidis2012" class="citation journal cs1">Diaz, David; Theodoulidis, Babis (January 10, 2012). <a rel="nofollow" class="external text" href="https://doi.org/10.2139%2Fssrn.2193636">"Financial Markets Monitoring and Surveillance: A Quote Stuffing Case Study"</a>. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<span class="id-lock-free" title="Freely accessible"><a rel="nofollow" class="external text" href="https://doi.org/10.2139%2Fssrn.2193636">10.2139/ssrn.2193636</a></span>. <a href="/wiki/S2CID_(identifier)" class="mw-redirect" title="S2CID (identifier)">S2CID</a>&#160;<a rel="nofollow" class="external text" href="https://api.semanticscholar.org/CorpusID:166680108">166680108</a>. <a href="/wiki/SSRN_(identifier)" class="mw-redirect" title="SSRN (identifier)">SSRN</a>&#160;<a rel="nofollow" class="external text" href="https://papers.ssrn.com/sol3/papers.cfm?abstract_id=2193636">2193636</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.atitle=Financial+Markets+Monitoring+and+Surveillance%3A+A+Quote+Stuffing+Case+Study&amp;rft.date=2012-01-10&amp;rft_id=https%3A%2F%2Fapi.semanticscholar.org%2FCorpusID%3A166680108%23id-name%3DS2CID&amp;rft_id=https%3A%2F%2Fpapers.ssrn.com%2Fsol3%2Fpapers.cfm%3Fabstract_id%3D2193636%23id-name%3DSSRN&amp;rft_id=info%3Adoi%2F10.2139%2Fssrn.2193636&amp;rft.aulast=Diaz&amp;rft.aufirst=David&amp;rft.au=Theodoulidis%2C+Babis&amp;rft_id=https%3A%2F%2Fdoi.org%2F10.2139%252Fssrn.2193636&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span> <span class="cs1-visible-error citation-comment"><code class="cs1-code">{{<a href="/wiki/Template:Cite_journal" title="Template:Cite journal">cite journal</a>}}</code>: </span><span class="cs1-visible-error citation-comment">Cite journal requires <code class="cs1-code">&#124;journal=</code> (<a href="/wiki/Help:CS1_errors#missing_periodical" title="Help:CS1 errors">help</a>)</span></span>
</li>
<li id="cite_note-94"><span class="mw-cite-backlink"><b><a href="#cite_ref-94">^</a></b></span> <span class="reference-text">High-Speed Devices and Circuits with THz Applications by Jung Han Choi</span>
</li>
<li id="cite_note-95"><span class="mw-cite-backlink"><b><a href="#cite_ref-95">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://web.archive.org/web/20160602075704/http://glossary.reuters.com/index.php?title=Low_Latency_Trading">"Low Latency Trading"</a>. Archived from <a rel="nofollow" class="external text" href="http://glossary.reuters.com/index.php?title=Low_Latency_Trading">the original</a> on June 2, 2016<span class="reference-accessdate">. Retrieved <span class="nowrap">April 26,</span> 2015</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=unknown&amp;rft.btitle=Low+Latency+Trading&amp;rft_id=http%3A%2F%2Fglossary.reuters.com%2Findex.php%3Ftitle%3DLow_Latency_Trading&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-96"><span class="mw-cite-backlink"><b><a href="#cite_ref-96">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFSaarHasbrouck2013" class="citation journal cs1">Saar, Gideon; Hasbrouck, Joel (May 22, 2013). <a rel="nofollow" class="external text" href="https://doi.org/10.2139%2Fssrn.1695460">"Low-Latency Trading"</a>. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<span class="id-lock-free" title="Freely accessible"><a rel="nofollow" class="external text" href="https://doi.org/10.2139%2Fssrn.1695460">10.2139/ssrn.1695460</a></span>. <a href="/wiki/S2CID_(identifier)" class="mw-redirect" title="S2CID (identifier)">S2CID</a>&#160;<a rel="nofollow" class="external text" href="https://api.semanticscholar.org/CorpusID:219368985">219368985</a>. <a href="/wiki/SSRN_(identifier)" class="mw-redirect" title="SSRN (identifier)">SSRN</a>&#160;<a rel="nofollow" class="external text" href="https://papers.ssrn.com/sol3/papers.cfm?abstract_id=1695460">1695460</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.atitle=Low-Latency+Trading&amp;rft.date=2013-05-22&amp;rft_id=https%3A%2F%2Fapi.semanticscholar.org%2FCorpusID%3A219368985%23id-name%3DS2CID&amp;rft_id=https%3A%2F%2Fpapers.ssrn.com%2Fsol3%2Fpapers.cfm%3Fabstract_id%3D1695460%23id-name%3DSSRN&amp;rft_id=info%3Adoi%2F10.2139%2Fssrn.1695460&amp;rft.aulast=Saar&amp;rft.aufirst=Gideon&amp;rft.au=Hasbrouck%2C+Joel&amp;rft_id=https%3A%2F%2Fdoi.org%2F10.2139%252Fssrn.1695460&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span> <span class="cs1-visible-error citation-comment"><code class="cs1-code">{{<a href="/wiki/Template:Cite_journal" title="Template:Cite journal">cite journal</a>}}</code>: </span><span class="cs1-visible-error citation-comment">Cite journal requires <code class="cs1-code">&#124;journal=</code> (<a href="/wiki/Help:CS1_errors#missing_periodical" title="Help:CS1 errors">help</a>)</span></span>
</li>
<li id="cite_note-97"><span class="mw-cite-backlink"><b><a href="#cite_ref-97">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://web.archive.org/web/20160304093319/http://home.business.utah.edu/finmh/moallemi.pdf">"Archived copy"</a> <span class="cs1-format">(PDF)</span>. Archived from <a rel="nofollow" class="external text" href="http://home.business.utah.edu/finmh/moallemi.pdf">the original</a> <span class="cs1-format">(PDF)</span> on March 4, 2016<span class="reference-accessdate">. Retrieved <span class="nowrap">April 26,</span> 2015</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=unknown&amp;rft.btitle=Archived+copy&amp;rft_id=http%3A%2F%2Fhome.business.utah.edu%2Ffinmh%2Fmoallemi.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span><span class="cs1-maint citation-comment"><code class="cs1-code">{{<a href="/wiki/Template:Cite_web" title="Template:Cite web">cite web</a>}}</code>:  CS1 maint: archived copy as title (<a href="/wiki/Category:CS1_maint:_archived_copy_as_title" title="Category:CS1 maint: archived copy as title">link</a>)</span></span>
</li>
<li id="cite_note-98"><span class="mw-cite-backlink"><b><a href="#cite_ref-98">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="http://fixglobal.com/home/<USER>/">FIXatdl – An Emerging Standard</a> <a rel="nofollow" class="external text" href="https://web.archive.org/web/20200305141237/https://www.fixglobal.com/home/<USER>/">Archived</a> March 5, 2020, at the <a href="/wiki/Wayback_Machine" title="Wayback Machine">Wayback Machine</a>, FIXGlobal, December 2009</span>
</li>
<li id="cite_note-99"><span class="mw-cite-backlink"><b><a href="#cite_ref-99">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFPreisPaulSchneider2008" class="citation cs2">Preis, T.; Paul, W.; Schneider, J. J. (2008), "Fluctuation patterns in high-frequency financial asset returns", <i>EPL</i>, <b>82</b> (6): 68005, <a href="/wiki/Bibcode_(identifier)" class="mw-redirect" title="Bibcode (identifier)">Bibcode</a>:<a rel="nofollow" class="external text" href="https://ui.adsabs.harvard.edu/abs/2008EL.....8268005P">2008EL.....8268005P</a>, <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.1209%2F0295-5075%2F82%2F68005">10.1209/0295-5075/82/68005</a>, <a href="/wiki/S2CID_(identifier)" class="mw-redirect" title="S2CID (identifier)">S2CID</a>&#160;<a rel="nofollow" class="external text" href="https://api.semanticscholar.org/CorpusID:56283521">56283521</a></cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=EPL&amp;rft.atitle=Fluctuation+patterns+in+high-frequency+financial+asset+returns&amp;rft.volume=82&amp;rft.issue=6&amp;rft.pages=68005&amp;rft.date=2008&amp;rft_id=https%3A%2F%2Fapi.semanticscholar.org%2FCorpusID%3A56283521%23id-name%3DS2CID&amp;rft_id=info%3Adoi%2F10.1209%2F0295-5075%2F82%2F68005&amp;rft_id=info%3Abibcode%2F2008EL.....8268005P&amp;rft.aulast=Preis&amp;rft.aufirst=T.&amp;rft.au=Paul%2C+W.&amp;rft.au=Schneider%2C+J.+J.&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-100"><span class="mw-cite-backlink"><b><a href="#cite_ref-100">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFHultKiessling2010" class="citation cs2">Hult, Henrik; Kiessling, Jonas (2010), <a rel="nofollow" class="external text" href="https://urn.kb.se/resolve?urn=urn:nbn:se:kth:diva-25075"><i>Algorithmic trading with Markov chains</i></a>, Trita-MAT. MA (8&#160;ed.), Stockholm: KTH: KTH, p.&#160;45, <a href="/wiki/ISBN_(identifier)" class="mw-redirect" title="ISBN (identifier)">ISBN</a>&#160;<a href="/wiki/Special:BookSources/***********-741-3" title="Special:BookSources/***********-741-3"><bdi>***********-741-3</bdi></a>, <a href="/wiki/ISSN_(identifier)" class="mw-redirect" title="ISSN (identifier)">ISSN</a>&#160;<a rel="nofollow" class="external text" href="https://search.worldcat.org/issn/1401-2278">1401-2278</a><span class="reference-accessdate">, retrieved <span class="nowrap">June 26,</span> 2024</span></cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=book&amp;rft.btitle=Algorithmic+trading+with+Markov+chains&amp;rft.place=Stockholm%3A+KTH&amp;rft.series=Trita-MAT.+MA&amp;rft.pages=45&amp;rft.edition=8&amp;rft.pub=KTH&amp;rft.date=2010&amp;rft.issn=1401-2278&amp;rft.isbn=***********-741-3&amp;rft.aulast=Hult&amp;rft.aufirst=Henrik&amp;rft.au=Kiessling%2C+Jonas&amp;rft_id=https%3A%2F%2Furn.kb.se%2Fresolve%3Furn%3Durn%3Anbn%3Ase%3Akth%3Adiva-25075&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-101"><span class="mw-cite-backlink"><b><a href="#cite_ref-101">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFHendershottJonesMenkveld2010" class="citation cs2">Hendershott, Terrence; Jones, Charles M.; Menkveld, Albert J. (2010), <a rel="nofollow" class="external text" href="https://web.archive.org/web/20100716214602/http://www.afajof.org/afa/forthcoming/6130p.pdf">"HENDERSHOTT, TERRENCE, CHARLES M. JONES, AND ALBERT J. MENKVELD. Does Algorithmic Trading Improve Liquidity?"</a> <span class="cs1-format">(PDF)</span>, <i>Journal of Finance</i>, <b>66</b>: <span class="nowrap">1–</span>33, <a href="/wiki/CiteSeerX_(identifier)" class="mw-redirect" title="CiteSeerX (identifier)">CiteSeerX</a>&#160;<span class="id-lock-free" title="Freely accessible"><a rel="nofollow" class="external text" href="https://citeseerx.ist.psu.edu/viewdoc/summary?doi=10.1.1.105.7253">10.1.1.105.7253</a></span>, <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.1111%2Fj.1540-6261.2010.01624.x">10.1111/j.1540-6261.2010.01624.x</a>, <a href="/wiki/S2CID_(identifier)" class="mw-redirect" title="S2CID (identifier)">S2CID</a>&#160;<a rel="nofollow" class="external text" href="https://api.semanticscholar.org/CorpusID:30441">30441</a>, archived from <a rel="nofollow" class="external text" href="http://www.afajof.org/afa/forthcoming/6130p.pdf">the original</a> <span class="cs1-format">(PDF)</span> on July 16, 2010</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Journal+of+Finance&amp;rft.atitle=HENDERSHOTT%2C+TERRENCE%2C+CHARLES+M.+JONES%2C+AND+ALBERT+J.+MENKVELD.+Does+Algorithmic+Trading+Improve+Liquidity%3F&amp;rft.volume=66&amp;rft.pages=1-33&amp;rft.date=2010&amp;rft_id=https%3A%2F%2Fciteseerx.ist.psu.edu%2Fviewdoc%2Fsummary%3Fdoi%3D10.1.1.105.7253%23id-name%3DCiteSeerX&amp;rft_id=https%3A%2F%2Fapi.semanticscholar.org%2FCorpusID%3A30441%23id-name%3DS2CID&amp;rft_id=info%3Adoi%2F10.1111%2Fj.1540-6261.2010.01624.x&amp;rft.aulast=Hendershott&amp;rft.aufirst=Terrence&amp;rft.au=Jones%2C+Charles+M.&amp;rft.au=Menkveld%2C+Albert+J.&amp;rft_id=http%3A%2F%2Fwww.afajof.org%2Fafa%2Fforthcoming%2F6130p.pdf&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-102"><span class="mw-cite-backlink"><b><a href="#cite_ref-102">^</a></b></span> <span class="reference-text">Lin, Tom C.W., The New Investor, 60 UCLA 678 (2013), available at: <a rel="nofollow" class="external free" href="https://ssrn.com/abstract=2227498">https://ssrn.com/abstract=2227498</a></span>
</li>
<li id="cite_note-103"><span class="mw-cite-backlink"><b><a href="#cite_ref-103">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="https://www.telegraph.co.uk/finance/2946240/Black-box-traders-are-on-the-march.html">Black box traders are on the march</a> The Telegraph, 27 August 2006</span>
</li>
<li id="cite_note-104"><span class="mw-cite-backlink"><b><a href="#cite_ref-104">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="http://news.bbc.co.uk/1/hi/business/8338045.stm">Myners' super-fast shares warning</a> BBC News, Tuesday 3 November 2009.</span>
</li>
<li id="cite_note-105"><span class="mw-cite-backlink"><b><a href="#cite_ref-105">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFSkypala2006" class="citation news cs1">Skypala, Pauline (October 2, 2006). <a rel="nofollow" class="external text" href="https://web.archive.org/web/20071030170728/http://search.ft.com/ftArticle?queryText=%22algorithmic+trading%22&amp;aje=true&amp;ct=0&amp;id=061002000774&amp;nclick_check=1">"Enter algorithmic trading systems race or lose returns, report warns"</a>. <i>The Financial Times</i>. Archived from <a rel="nofollow" class="external text" href="http://search.ft.com/ftArticle?queryText=%22algorithmic+trading%22&amp;aje=true&amp;ct=0&amp;id=061002000774&amp;nclick_check=1">the original</a> on October 30, 2007.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+Financial+Times&amp;rft.atitle=Enter+algorithmic+trading+systems+race+or+lose+returns%2C+report+warns&amp;rft.date=2006-10-02&amp;rft.aulast=Skypala&amp;rft.aufirst=Pauline&amp;rft_id=http%3A%2F%2Fsearch.ft.com%2FftArticle%3FqueryText%3D%2522algorithmic%2Btrading%2522%26aje%3Dtrue%26ct%3D0%26id%3D061002000774%26nclick_check%3D1&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-106"><span class="mw-cite-backlink"><b><a href="#cite_ref-106">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="https://web.archive.org/web/20050408175711/http://www.businessweek.com/magazine/content/05_16/b3929113_mz020.htm">Cracking The Street's New Math, Algorithmic trades are sweeping the stock market</a>.</span>
</li>
<li id="cite_note-107"><span class="mw-cite-backlink"><b><a href="#cite_ref-107">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="http://www.iht.com/articles/ap/2007/07/02/business/NA-FIN-COM-US-Citigroup-Automated-Trading-Desk.php">The Associated Press, July 2, 2007</a> Citigroup to expand electronic trading capabilities by buying Automated Trading Desk, accessed July 4, 2007</span>
</li>
<li id="cite_note-108"><span class="mw-cite-backlink"><b><a href="#cite_ref-108">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="http://www.knight.com/investorRelations/pressReleases.asp?compid=105070&amp;releaseID=1721599">Knight Capital Group Provides Update Regarding August 1st Disruption To Routing In NYSE-listed Securities</a> <a rel="nofollow" class="external text" href="https://web.archive.org/web/**************/http://www.knight.com/investorRelations/pressReleases.asp?compid=105070&amp;releaseID=1721599">Archived</a> August 4, 2012, at the <a href="/wiki/Wayback_Machine" title="Wayback Machine">Wayback Machine</a></span>
</li>
<li id="cite_note-Lauricella-109"><span class="mw-cite-backlink"><b><a href="#cite_ref-Lauricella_109-0">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external autonumber" href="https://www.wsj.com/articles/SB10001424052748704370704575227754131412596?mod=rss_com_mostcommentart">[1]</a> Lauricella, Tom, and McKay, Peter A. "Dow Takes a Harrowing 1,010.14-Point Trip," Online Wall Street Journal, May 7, 2010. Retrieved May 9, 2010</span>
</li>
<li id="cite_note-ft_too_late-110"><span class="mw-cite-backlink">^ <a href="#cite_ref-ft_too_late_110-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-ft_too_late_110-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://www.ft.com/cms/s/bb570626-ebb6-11db-b290-000b5df10621.html">"City trusts computers to keep up with the news"</a>. <i>Financial Times</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=Financial+Times&amp;rft.atitle=City+trusts+computers+to+keep+up+with+the+news&amp;rft_id=http%3A%2F%2Fwww.ft.com%2Fcms%2Fs%2Fbb570626-ebb6-11db-b290-000b5df10621.html&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-salgo-111"><span class="mw-cite-backlink">^ <a href="#cite_ref-salgo_111-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-salgo_111-1"><sup><i><b>b</b></i></sup></a> <a href="#cite_ref-salgo_111-2"><sup><i><b>c</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://web.archive.org/web/20110716033553/http://www.securitiestechnologymonitor.com/issues/19_104/-23976-1.html?zkPrintable=true">"Traders News"</a>. <i>Traders Magazine</i>. Archived from <a rel="nofollow" class="external text" href="http://www.securitiestechnologymonitor.com/issues/19_104/-23976-1.html?zkPrintable=true">the original</a> on July 16, 2011.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=Traders+Magazine&amp;rft.atitle=Traders+News&amp;rft_id=http%3A%2F%2Fwww.securitiestechnologymonitor.com%2Fissues%2F19_104%2F-23976-1.html%3FzkPrintable%3Dtrue&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-112"><span class="mw-cite-backlink"><b><a href="#cite_ref-112">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="http://www.siemon.com/us/company/case_studies/atd.asp">Siemon's Case Study</a> <a rel="nofollow" class="external text" href="https://web.archive.org/web/20181229142822/http://www.siemon.com/us/company/case_studies/atd.asp">Archived</a> December 29, 2018, at the <a href="/wiki/Wayback_Machine" title="Wayback Machine">Wayback Machine</a> Automated Trading Desk, accessed July 4, 2007</span>
</li>
<li id="cite_note-auto-113"><span class="mw-cite-backlink">^ <a href="#cite_ref-auto_113-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-auto_113-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://www.gov.uk/government/collections/future-of-computer-trading">"Future of computer trading"</a>. <i>GOV.UK</i>. October 23, 2012.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=GOV.UK&amp;rft.atitle=Future+of+computer+trading&amp;rft.date=2012-10-23&amp;rft_id=https%3A%2F%2Fwww.gov.uk%2Fgovernment%2Fcollections%2Ffuture-of-computer-trading&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-114"><span class="mw-cite-backlink"><b><a href="#cite_ref-114">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation news cs1"><a rel="nofollow" class="external text" href="http://marketsmedia.com/u-k-foresight-study-slammed-for-hft-bias/">"U.K. Foresight Study Slammed For HFT 'Bias'<span class="cs1-kern-right"></span>"</a>. <i>Markets Media</i>. October 30, 2012<span class="reference-accessdate">. Retrieved <span class="nowrap">November 2,</span> 2014</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Markets+Media&amp;rft.atitle=U.K.+Foresight+Study+Slammed+For+HFT+%27Bias%27&amp;rft.date=2012-10-30&amp;rft_id=http%3A%2F%2Fmarketsmedia.com%2Fu-k-foresight-study-slammed-for-hft-bias%2F&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-:2-115"><span class="mw-cite-backlink">^ <a href="#cite_ref-:2_115-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-:2_115-1"><sup><i><b>b</b></i></sup></a> <a href="#cite_ref-:2_115-2"><sup><i><b>c</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFDarbellay2021" class="citation web cs1">Darbellay, Raphaël (2021). <a rel="nofollow" class="external text" href="https://www.theseus.fi/bitstream/handle/10024/496406/Thesis_100421_RaphaelDarbellayV8.pdf?sequence=2&amp;isAllowed=y">"Behind the scenes of algorithmic trading"</a> <span class="cs1-format">(PDF)</span>. University of Applied Science Haaga-Helia.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=unknown&amp;rft.btitle=Behind+the+scenes+of+algorithmic+trading&amp;rft.pub=University+of+Applied+Science+Haaga-Helia&amp;rft.date=2021&amp;rft.aulast=Darbellay&amp;rft.aufirst=Rapha%C3%ABl&amp;rft_id=https%3A%2F%2Fwww.theseus.fi%2Fbitstream%2Fhandle%2F10024%2F496406%2FThesis_100421_RaphaelDarbellayV8.pdf%3Fsequence%3D2%26isAllowed%3Dy&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-:3-116"><span class="mw-cite-backlink">^ <a href="#cite_ref-:3_116-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-:3_116-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFKumar2015" class="citation news cs1">Kumar, Sameer (March 14, 2015). "Technology Edge in Algo Trading: Traditional Vs Automated Trading System Architecture". Finbridge.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.atitle=Technology+Edge+in+Algo+Trading%3A+Traditional+Vs+Automated+Trading+System+Architecture&amp;rft.date=2015-03-14&amp;rft.aulast=Kumar&amp;rft.aufirst=Sameer&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-117"><span class="mw-cite-backlink"><b><a href="#cite_ref-117">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://www.martinhilbert.net/large-scale-communication-is-more-complex-and-unpredictable-with-automated-bots/">"Large-Scale Communication is More Complex and Unpredictable with Automated Bots"</a>. <i>MartinHilbert.net</i><span class="reference-accessdate">. Retrieved <span class="nowrap">April 24,</span> 2025</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=MartinHilbert.net&amp;rft.atitle=Large-Scale+Communication+is+More+Complex+and+Unpredictable+with+Automated+Bots&amp;rft_id=https%3A%2F%2Fwww.martinhilbert.net%2Flarge-scale-communication-is-more-complex-and-unpredictable-with-automated-bots%2F&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-118"><span class="mw-cite-backlink"><b><a href="#cite_ref-118">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation news cs1"><a rel="nofollow" class="external text" href="http://www.economist.com/finance/displaystory.cfm?story_id=E1_RRNJGNP">"Business and finance"</a>. <i>The Economist</i>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=The+Economist&amp;rft.atitle=Business+and+finance&amp;rft_id=http%3A%2F%2Fwww.economist.com%2Ffinance%2Fdisplaystory.cfm%3Fstory_id%3DE1_RRNJGNP&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-119"><span class="mw-cite-backlink"><b><a href="#cite_ref-119">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation web cs1"><a rel="nofollow" class="external text" href="https://web.archive.org/web/20071022160012/http://www.wallstreetandtech.com/showArticle.jhtml?articleID=198001836">"InformationWeek Authors"</a>. <i>InformationWeek</i>. Archived from <a rel="nofollow" class="external text" href="http://www.wallstreetandtech.com/showArticle.jhtml?articleID=198001836">the original</a> on October 22, 2007<span class="reference-accessdate">. Retrieved <span class="nowrap">April 18,</span> 2007</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=unknown&amp;rft.jtitle=InformationWeek&amp;rft.atitle=InformationWeek+Authors&amp;rft_id=http%3A%2F%2Fwww.wallstreetandtech.com%2FshowArticle.jhtml%3FarticleID%3D198001836&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-120"><span class="mw-cite-backlink"><b><a href="#cite_ref-120">^</a></b></span> <span class="reference-text">"LSE leads race for quicker trades" by Alistair MacDonald <a href="/wiki/The_Wall_Street_Journal_Europe" title="The Wall Street Journal Europe">The Wall Street Journal Europe</a>, June 19, 2007, p.3</span>
</li>
<li id="cite_note-121"><span class="mw-cite-backlink"><b><a href="#cite_ref-121">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation news cs1"><a rel="nofollow" class="external text" href="https://www.reuters.com/article/ExchangesandTrading07/idUSN1046529820070511">"Milliseconds are focus in algorithmic trades"</a>. <i>Reuters</i>. May 11, 2007.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Reuters&amp;rft.atitle=Milliseconds+are+focus+in+algorithmic+trades&amp;rft.date=2007-05-11&amp;rft_id=https%3A%2F%2Fwww.reuters.com%2Farticle%2FExchangesandTrading07%2FidUSN1046529820070511&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-122"><span class="mw-cite-backlink"><b><a href="#cite_ref-122">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite class="citation news cs1"><a rel="nofollow" class="external text" href="https://www.economist.com/finance-and-economics/2006/02/02/moving-markets">"Moving markets"</a><span class="reference-accessdate">. Retrieved <span class="nowrap">January 20,</span> 2015</span>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.atitle=Moving+markets&amp;rft_id=https%3A%2F%2Fwww.economist.com%2Ffinance-and-economics%2F2006%2F02%2F02%2Fmoving-markets&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-123"><span class="mw-cite-backlink"><b><a href="#cite_ref-123">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFFarmer1999" class="citation journal cs1">Farmer, J. Done (November 1999). "Physicists attempt to scale the ivory towers of finance". <i>Computing in Science &amp; Engineering</i>. <b>1</b> (6): <span class="nowrap">26–</span>39. <a href="/wiki/ArXiv_(identifier)" class="mw-redirect" title="ArXiv (identifier)">arXiv</a>:<span class="id-lock-free" title="Freely accessible"><a rel="nofollow" class="external text" href="https://arxiv.org/abs/adap-org/9912002">adap-org/9912002</a></span>. <a href="/wiki/Bibcode_(identifier)" class="mw-redirect" title="Bibcode (identifier)">Bibcode</a>:<a rel="nofollow" class="external text" href="https://ui.adsabs.harvard.edu/abs/1999CSE.....1f..26D">1999CSE.....1f..26D</a>. <a href="/wiki/Doi_(identifier)" class="mw-redirect" title="Doi (identifier)">doi</a>:<a rel="nofollow" class="external text" href="https://doi.org/10.1109%2F5992.906615">10.1109/5992.906615</a>. <a href="/wiki/S2CID_(identifier)" class="mw-redirect" title="S2CID (identifier)">S2CID</a>&#160;<a rel="nofollow" class="external text" href="https://api.semanticscholar.org/CorpusID:9058415">9058415</a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.genre=article&amp;rft.jtitle=Computing+in+Science+%26+Engineering&amp;rft.atitle=Physicists+attempt+to+scale+the+ivory+towers+of+finance&amp;rft.volume=1&amp;rft.issue=6&amp;rft.pages=26-39&amp;rft.date=1999-11&amp;rft_id=info%3Aarxiv%2Fadap-org%2F9912002&amp;rft_id=https%3A%2F%2Fapi.semanticscholar.org%2FCorpusID%3A9058415%23id-name%3DS2CID&amp;rft_id=info%3Adoi%2F10.1109%2F5992.906615&amp;rft_id=info%3Abibcode%2F1999CSE.....1f..26D&amp;rft.aulast=Farmer&amp;rft.aufirst=J.+Done&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
<li id="cite_note-124"><span class="mw-cite-backlink"><b><a href="#cite_ref-124">^</a></b></span> <span class="reference-text"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1238218222" /><cite id="CITEREFBrown2010" class="citation book cs1">Brown, Brian (2010). <i>Chasing the Same Signals: How Black-Box Trading Influences Stock Markets from Wall Street to Shanghai</i>. Singapore: John Wiley &amp; Sons. <a href="/wiki/ISBN_(identifier)" class="mw-redirect" title="ISBN (identifier)">ISBN</a>&#160;<a href="/wiki/Special:BookSources/978-0-470-82488-7" title="Special:BookSources/978-0-470-82488-7"><bdi>978-0-470-82488-7</bdi></a>.</cite><span title="ctx_ver=Z39.88-2004&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook&amp;rft.genre=book&amp;rft.btitle=Chasing+the+Same+Signals%3A+How+Black-Box+Trading+Influences+Stock+Markets+from+Wall+Street+to+Shanghai&amp;rft.place=Singapore&amp;rft.pub=John+Wiley+%26+Sons&amp;rft.date=2010&amp;rft.isbn=978-0-470-82488-7&amp;rft.aulast=Brown&amp;rft.aufirst=Brian&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AAlgorithmic+trading" class="Z3988"></span></span>
</li>
</ol></div>
<div class="mw-heading mw-heading2"><h2 id="External_links">External links</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="/w/index.php?title=Algorithmic_trading&amp;action=edit&amp;section=37" title="Edit section: External links"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<style data-mw-deduplicate="TemplateStyles:r1295905060">.mw-parser-output .infobox-subbox{padding:0;border:none;margin:-3px;width:auto;min-width:100%;font-size:100%;clear:none;float:none;background-color:transparent}.mw-parser-output .infobox-3cols-child{margin:auto}.mw-parser-output .infobox .navbar{font-size:100%}@media screen{html.skin-theme-clientpref-night .mw-parser-output .infobox-full-data:not(.notheme)>div:not(.notheme)[style]{background:#1f1f23!important;color:#f8f9fa}}@media screen and (prefers-color-scheme:dark){html.skin-theme-clientpref-os .mw-parser-output .infobox-full-data:not(.notheme)>div:not(.notheme)[style]{background:#1f1f23!important;color:#f8f9fa}}@media(min-width:640px){body.skin--responsive .mw-parser-output .infobox-table{display:table!important}body.skin--responsive .mw-parser-output .infobox-table>caption{display:table-caption!important}body.skin--responsive .mw-parser-output .infobox-table>tbody{display:table-row-group}body.skin--responsive .mw-parser-output .infobox-table th,body.skin--responsive .mw-parser-output .infobox-table td{padding-left:inherit;padding-right:inherit}}</style><table class="infobox" style="width: 300px; clear: right; float:right;margin:0 0 1.5em 1.5em"><tbody><tr><th colspan="2" class="infobox-above" style="font-size:115%">External videos</th></tr><tr><td colspan="2" class="infobox-full-data" style="text-align: left"><span typeof="mw:File"><span><img alt="video icon" src="//upload.wikimedia.org/wikipedia/commons/thumb/1/1b/Nuvola_apps_kaboodle.svg/20px-Nuvola_apps_kaboodle.svg.png" decoding="async" width="16" height="16" class="mw-file-element" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/1/1b/Nuvola_apps_kaboodle.svg/40px-Nuvola_apps_kaboodle.svg.png 1.5x" data-file-width="128" data-file-height="128" /></span></span> <a rel="nofollow" class="external text" href="http://www.ted.com/talks/kevin_slavin_how_algorithms_shape_our_world.html">How algorithms shape our world</a>, <a href="/wiki/TED_(conference)" title="TED (conference)">TED (conference)</a></td></tr></tbody></table>
<div class="navbox-styles"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1129693374" /><style data-mw-deduplicate="TemplateStyles:r1236075235">.mw-parser-output .navbox{box-sizing:border-box;border:1px solid #a2a9b1;width:100%;clear:both;font-size:88%;text-align:center;padding:1px;margin:1em auto 0}.mw-parser-output .navbox .navbox{margin-top:0}.mw-parser-output .navbox+.navbox,.mw-parser-output .navbox+.navbox-styles+.navbox{margin-top:-1px}.mw-parser-output .navbox-inner,.mw-parser-output .navbox-subgroup{width:100%}.mw-parser-output .navbox-group,.mw-parser-output .navbox-title,.mw-parser-output .navbox-abovebelow{padding:0.25em 1em;line-height:1.5em;text-align:center}.mw-parser-output .navbox-group{white-space:nowrap;text-align:right}.mw-parser-output .navbox,.mw-parser-output .navbox-subgroup{background-color:#fdfdfd}.mw-parser-output .navbox-list{line-height:1.5em;border-color:#fdfdfd}.mw-parser-output .navbox-list-with-group{text-align:left;border-left-width:2px;border-left-style:solid}.mw-parser-output tr+tr>.navbox-abovebelow,.mw-parser-output tr+tr>.navbox-group,.mw-parser-output tr+tr>.navbox-image,.mw-parser-output tr+tr>.navbox-list{border-top:2px solid #fdfdfd}.mw-parser-output .navbox-title{background-color:#ccf}.mw-parser-output .navbox-abovebelow,.mw-parser-output .navbox-group,.mw-parser-output .navbox-subgroup .navbox-title{background-color:#ddf}.mw-parser-output .navbox-subgroup .navbox-group,.mw-parser-output .navbox-subgroup .navbox-abovebelow{background-color:#e6e6ff}.mw-parser-output .navbox-even{background-color:#f7f7f7}.mw-parser-output .navbox-odd{background-color:transparent}.mw-parser-output .navbox .hlist td dl,.mw-parser-output .navbox .hlist td ol,.mw-parser-output .navbox .hlist td ul,.mw-parser-output .navbox td.hlist dl,.mw-parser-output .navbox td.hlist ol,.mw-parser-output .navbox td.hlist ul{padding:0.125em 0}.mw-parser-output .navbox .navbar{display:block;font-size:100%}.mw-parser-output .navbox-title .navbar{float:left;text-align:left;margin-right:0.5em}body.skin--responsive .mw-parser-output .navbox-image img{max-width:none!important}@media print{body.ns-0 .mw-parser-output .navbox{display:none!important}}</style></div><div role="navigation" class="navbox" aria-labelledby="Hedge_funds642" style="padding:3px"><table class="nowraplinks hlist mw-collapsible autocollapse navbox-inner" style="border-spacing:0;background:transparent;color:inherit"><tbody><tr><th scope="col" class="navbox-title" colspan="3"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1129693374" /><style data-mw-deduplicate="TemplateStyles:r1239400231">.mw-parser-output .navbar{display:inline;font-size:88%;font-weight:normal}.mw-parser-output .navbar-collapse{float:left;text-align:left}.mw-parser-output .navbar-boxtext{word-spacing:0}.mw-parser-output .navbar ul{display:inline-block;white-space:nowrap;line-height:inherit}.mw-parser-output .navbar-brackets::before{margin-right:-0.125em;content:"[ "}.mw-parser-output .navbar-brackets::after{margin-left:-0.125em;content:" ]"}.mw-parser-output .navbar li{word-spacing:-0.125em}.mw-parser-output .navbar a>span,.mw-parser-output .navbar a>abbr{text-decoration:inherit}.mw-parser-output .navbar-mini abbr{font-variant:small-caps;border-bottom:none;text-decoration:none;cursor:inherit}.mw-parser-output .navbar-ct-full{font-size:114%;margin:0 7em}.mw-parser-output .navbar-ct-mini{font-size:114%;margin:0 4em}html.skin-theme-clientpref-night .mw-parser-output .navbar li a abbr{color:var(--color-base)!important}@media(prefers-color-scheme:dark){html.skin-theme-clientpref-os .mw-parser-output .navbar li a abbr{color:var(--color-base)!important}}@media print{.mw-parser-output .navbar{display:none!important}}</style><div class="navbar plainlinks hlist navbar-mini"><ul><li class="nv-view"><a href="/wiki/Template:Hedge_funds" title="Template:Hedge funds"><abbr title="View this template">v</abbr></a></li><li class="nv-talk"><a href="/wiki/Template_talk:Hedge_funds" title="Template talk:Hedge funds"><abbr title="Discuss this template">t</abbr></a></li><li class="nv-edit"><a href="/wiki/Special:EditPage/Template:Hedge_funds" title="Special:EditPage/Template:Hedge funds"><abbr title="Edit this template">e</abbr></a></li></ul></div><div id="Hedge_funds642" style="font-size:114%;margin:0 4em"><a href="/wiki/Hedge_fund" title="Hedge fund">Hedge funds</a></div></th></tr><tr><th scope="row" class="navbox-group" style="width:1%">Investment<br />strategy</th><td class="navbox-list-with-group navbox-list navbox-odd" style="width:100%;padding:0"><div style="padding:0 0.25em"></div><table class="nowraplinks navbox-subgroup" style="border-spacing:0"><tbody><tr><th scope="row" class="navbox-group" style="width:7em"><a href="/wiki/Arbitrage" title="Arbitrage">Arbitrage</a> /<br /><a href="/wiki/Relative_value_(economics)" title="Relative value (economics)">relative value</a></th><td class="navbox-list-with-group navbox-list navbox-odd" style="padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Capital_structure#Arbitrage" title="Capital structure">Capital structure arbitrage</a></li>
<li><a href="/wiki/Convertible_arbitrage" title="Convertible arbitrage">Convertible arbitrage</a></li>
<li><a href="/wiki/Market_neutral#Equity-market-neutral" title="Market neutral">Equity market neutral</a></li>
<li><a href="/wiki/Fixed_income_arbitrage" title="Fixed income arbitrage">Fixed income arbitrage</a> / <a href="/wiki/Fixed-income_relative-value_investing" title="Fixed-income relative-value investing">fixed-income relative-value investing</a></li>
<li><a href="/wiki/Statistical_arbitrage" title="Statistical arbitrage">Statistical arbitrage</a></li>
<li><a href="/wiki/Volatility_arbitrage" title="Volatility arbitrage">Volatility arbitrage</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:7em"><a href="/wiki/Event-driven_investing" title="Event-driven investing">Event-driven</a></th><td class="navbox-list-with-group navbox-list navbox-even" style="padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Shareholder_activism" title="Shareholder activism">Shareholder activism</a></li>
<li><a href="/wiki/Distressed_securities" title="Distressed securities">Distressed securities</a></li>
<li><a href="/wiki/Risk_arbitrage" title="Risk arbitrage">Risk arbitrage</a></li>
<li><a href="/wiki/Special_situation" title="Special situation">Special situation</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:7em">Directional</th><td class="navbox-list-with-group navbox-list navbox-odd" style="padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Convergence_trade" title="Convergence trade">Convergence trade</a></li>
<li><a href="/wiki/Commodity_trading_advisor" title="Commodity trading advisor">Commodity trading advisors</a> / <a href="/wiki/Managed_futures_account" title="Managed futures account">managed futures account</a></li>
<li><a href="/wiki/Short_(finance)" title="Short (finance)">Dedicated short</a></li>
<li><a href="/wiki/Global_macro" title="Global macro">Global macro</a></li>
<li><a href="/wiki/Long/short_equity" title="Long/short equity">Long/short equity</a></li>
<li><a href="/wiki/Trend_following" title="Trend following">Trend following</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:7em">Other</th><td class="navbox-list-with-group navbox-list navbox-even" style="padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Fund_of_funds#Fund_of_hedge_funds" title="Fund of funds">Fund of hedge funds</a> / <a href="/wiki/Multi-manager_investment" title="Multi-manager investment">Multi-manager</a></li></ul>
</div></td></tr></tbody></table><div></div></td><td class="noviewer navbox-image" rowspan="5" style="width:1px;padding:0 0 0 2px"><div><span typeof="mw:File"><a href="/wiki/File:Chicklet-currency.jpg" class="mw-file-description"><img src="//upload.wikimedia.org/wikipedia/commons/thumb/9/9f/Chicklet-currency.jpg/60px-Chicklet-currency.jpg" decoding="async" width="60" height="54" class="mw-file-element" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/9/9f/Chicklet-currency.jpg/120px-Chicklet-currency.jpg 1.5x" data-file-width="279" data-file-height="250" /></a></span></div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Trading</th><td class="navbox-list-with-group navbox-list navbox-odd" style="width:100%;padding:0"><div style="padding:0 0.25em">
<ul><li><a class="mw-selflink selflink">Algorithmic trading</a></li>
<li><a href="/wiki/Day_trading" title="Day trading">Day trading</a></li>
<li><a href="/wiki/High-frequency_trading" title="High-frequency trading">High-frequency trading</a></li>
<li><a href="/wiki/Prime_brokerage" title="Prime brokerage">Prime brokerage</a></li>
<li><a href="/wiki/Program_trading" title="Program trading">Program trading</a></li>
<li><a href="/wiki/Proprietary_trading" title="Proprietary trading">Proprietary trading</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Related<br />terms</th><td class="navbox-list-with-group navbox-list navbox-odd" style="width:100%;padding:0"><div style="padding:0 0.25em"></div><table class="nowraplinks navbox-subgroup" style="border-spacing:0"><tbody><tr><th scope="row" class="navbox-group" style="width:7em">Markets</th><td class="navbox-list-with-group navbox-list navbox-even" style="padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Commodity_market" title="Commodity market">Commodities</a></li>
<li><a href="/wiki/Derivative_(finance)" title="Derivative (finance)">Derivatives</a></li>
<li><a href="/wiki/Stock_market" title="Stock market">Equity</a></li>
<li><a href="/wiki/Bond_market" title="Bond market">Fixed income</a></li>
<li><a href="/wiki/Foreign_exchange_market" title="Foreign exchange market">Foreign exchange</a></li>
<li><a href="/wiki/Money_market" title="Money market">Money markets</a></li>
<li><a href="/wiki/Structured_finance" title="Structured finance">Structured securities</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:7em">Misc</th><td class="navbox-list-with-group navbox-list navbox-odd" style="padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Absolute_return" title="Absolute return">Absolute return</a></li>
<li><a href="/wiki/Arbitrage_pricing_theory" title="Arbitrage pricing theory">Arbitrage pricing theory</a></li>
<li><a href="/wiki/Assets_under_management" title="Assets under management">Assets under management</a></li>
<li><a href="/wiki/Black%E2%80%93Scholes_model" title="Black–Scholes model">Black–Scholes model</a> (<a href="/wiki/Greeks_(finance)" title="Greeks (finance)">Greeks</a>: <a href="/wiki/Delta_neutral" title="Delta neutral">delta neutral</a>)</li>
<li><a href="/wiki/Capital_asset_pricing_model" title="Capital asset pricing model">Capital asset pricing model</a> (<a href="/wiki/Alpha_(finance)" title="Alpha (finance)">alpha</a> / <a href="/wiki/Beta_(finance)" title="Beta (finance)">beta</a> / <a href="/wiki/Security_characteristic_line" title="Security characteristic line">security characteristic line</a>)</li>
<li><a href="/wiki/Fundamental_analysis" title="Fundamental analysis">Fundamental analysis</a></li>
<li><a href="/wiki/Hedge_(finance)" title="Hedge (finance)">Hedge</a></li>
<li><a href="/wiki/Securitization" title="Securitization">Securitization</a></li>
<li><a href="/wiki/Short_(finance)" title="Short (finance)">Short</a></li>
<li><a href="/wiki/Taxation_of_private_equity_and_hedge_funds" title="Taxation of private equity and hedge funds">Taxation of private equity and hedge funds</a></li>
<li><a href="/wiki/Technical_analysis" title="Technical analysis">Technical analysis</a></li></ul>
</div></td></tr></tbody></table><div></div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Investors</th><td class="navbox-list-with-group navbox-list navbox-even" style="width:100%;padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Vulture_fund" title="Vulture fund">Vulture funds</a></li>
<li><a href="/wiki/Family_office" title="Family office">Family offices</a></li>
<li><a href="/wiki/Financial_endowment" title="Financial endowment">Financial endowments</a></li>
<li><a href="/wiki/Fund_of_funds#Fund_of_hedge_funds" title="Fund of funds">Fund of hedge funds</a></li>
<li><a href="/wiki/High-net-worth_individual" title="High-net-worth individual">High-net-worth individual</a></li>
<li><a href="/wiki/Institutional_investor" title="Institutional investor">Institutional investors</a></li>
<li><a href="/wiki/Insurance#Insurance_companies" title="Insurance">Insurance companies</a></li>
<li><a href="/wiki/Investment_banking" title="Investment banking">Investment banks</a></li>
<li><a href="/wiki/Merchant_bank" title="Merchant bank">Merchant banks</a></li>
<li><a href="/wiki/Pension_fund" title="Pension fund">Pension funds</a></li>
<li><a href="/wiki/Sovereign_wealth_fund" title="Sovereign wealth fund">Sovereign wealth funds</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Governance</th><td class="navbox-list-with-group navbox-list navbox-odd" style="width:100%;padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Fund_governance" title="Fund governance">Fund governance</a></li>
<li><a href="/wiki/Standards_Board_for_Alternative_Investments" title="Standards Board for Alternative Investments">Standards Board for Alternative Investments</a></li>
<li><a href="/wiki/Managed_Funds_Association" title="Managed Funds Association">Managed Funds Association</a></li></ul>
</div></td></tr><tr><td class="navbox-abovebelow" colspan="3"><div>
<ul><li><span class="noviewer" typeof="mw:File"><span title="Category"><img alt="" src="//upload.wikimedia.org/wikipedia/en/thumb/9/96/Symbol_category_class.svg/20px-Symbol_category_class.svg.png" decoding="async" width="16" height="16" class="mw-file-element" srcset="//upload.wikimedia.org/wikipedia/en/thumb/9/96/Symbol_category_class.svg/40px-Symbol_category_class.svg.png 1.5x" data-file-width="180" data-file-height="185" /></span></span><a href="/wiki/Category:Alternative_investment_management_companies" title="Category:Alternative investment management companies">Alternative investment management companies</a></li>
<li><span class="noviewer" typeof="mw:File"><span title="Category"><img alt="" src="//upload.wikimedia.org/wikipedia/en/thumb/9/96/Symbol_category_class.svg/20px-Symbol_category_class.svg.png" decoding="async" width="16" height="16" class="mw-file-element" srcset="//upload.wikimedia.org/wikipedia/en/thumb/9/96/Symbol_category_class.svg/40px-Symbol_category_class.svg.png 1.5x" data-file-width="180" data-file-height="185" /></span></span> <a href="/wiki/Category:Hedge_funds" title="Category:Hedge funds">Hedge funds</a></li>
<li><span class="noviewer" typeof="mw:File"><span title="Category"><img alt="" src="//upload.wikimedia.org/wikipedia/en/thumb/9/96/Symbol_category_class.svg/20px-Symbol_category_class.svg.png" decoding="async" width="16" height="16" class="mw-file-element" srcset="//upload.wikimedia.org/wikipedia/en/thumb/9/96/Symbol_category_class.svg/40px-Symbol_category_class.svg.png 1.5x" data-file-width="180" data-file-height="185" /></span></span><a href="/wiki/Category:Hedge_fund_managers" title="Category:Hedge fund managers">Hedge fund managers</a></li>
<li><span class="noviewer" typeof="mw:File"><span title="List-Class article"><img alt="" src="//upload.wikimedia.org/wikipedia/en/thumb/d/db/Symbol_list_class.svg/20px-Symbol_list_class.svg.png" decoding="async" width="16" height="16" class="mw-file-element" srcset="//upload.wikimedia.org/wikipedia/en/thumb/d/db/Symbol_list_class.svg/40px-Symbol_list_class.svg.png 1.5x" data-file-width="180" data-file-height="185" /></span></span> <a href="/wiki/List_of_hedge_funds" title="List of hedge funds">List of hedge funds</a></li></ul>
</div></td></tr></tbody></table></div>
<div class="navbox-styles"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1129693374" /><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1236075235" /></div><div role="navigation" class="navbox" aria-labelledby="Financial_markets208" style="padding:3px"><table class="nowraplinks hlist mw-collapsible autocollapse navbox-inner" style="border-spacing:0;background:transparent;color:inherit"><tbody><tr><th scope="col" class="navbox-title" colspan="2"><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1129693374" /><link rel="mw-deduplicated-inline-style" href="mw-data:TemplateStyles:r1239400231" /><div class="navbar plainlinks hlist navbar-mini"><ul><li class="nv-view"><a href="/wiki/Template:Financial_markets_navigation" title="Template:Financial markets navigation"><abbr title="View this template">v</abbr></a></li><li class="nv-talk"><a href="/wiki/Template_talk:Financial_markets_navigation" title="Template talk:Financial markets navigation"><abbr title="Discuss this template">t</abbr></a></li><li class="nv-edit"><a href="/wiki/Special:EditPage/Template:Financial_markets_navigation" title="Special:EditPage/Template:Financial markets navigation"><abbr title="Edit this template">e</abbr></a></li></ul></div><div id="Financial_markets208" style="font-size:114%;margin:0 4em"><a href="/wiki/Financial_market" title="Financial market">Financial markets</a></div></th></tr><tr><th scope="row" class="navbox-group" style="width:1%">Types of <a href="/wiki/Capital_market" title="Capital market">markets</a></th><td class="navbox-list-with-group navbox-list navbox-odd" style="width:100%;padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Primary_market" title="Primary market">Primary market</a></li>
<li><a href="/wiki/Secondary_market" title="Secondary market">Secondary market</a></li>
<li><a href="/wiki/Third_market" title="Third market">Third market</a></li>
<li><a href="/wiki/Fourth_market" title="Fourth market">Fourth market</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Types of <a href="/wiki/Stock" title="Stock">stocks</a></th><td class="navbox-list-with-group navbox-list navbox-even" style="width:100%;padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Common_stock" title="Common stock">Common stock</a></li>
<li><a href="/wiki/Golden_share" title="Golden share">Golden share</a></li>
<li><a href="/wiki/Preferred_stock" title="Preferred stock">Preferred stock</a></li>
<li><a href="/wiki/Restricted_stock" title="Restricted stock">Restricted stock</a></li>
<li><a href="/wiki/Tracking_stock" title="Tracking stock">Tracking stock</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%"><a href="/wiki/Share_capital" title="Share capital">Share capital</a></th><td class="navbox-list-with-group navbox-list navbox-odd" style="width:100%;padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Authorised_capital" title="Authorised capital">Authorised capital</a></li>
<li><a href="/wiki/Issued_shares" title="Issued shares">Issued shares</a></li>
<li><a href="/wiki/Shares_outstanding" title="Shares outstanding">Shares outstanding</a></li>
<li><a href="/wiki/Treasury_stock" title="Treasury stock">Treasury stock</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Participants</th><td class="navbox-list-with-group navbox-list navbox-even" style="width:100%;padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Broker" title="Broker">Broker</a>
<ul><li><a href="/wiki/Floor_broker" title="Floor broker">Floor broker</a></li>
<li><a href="/wiki/Inter-dealer_broker" title="Inter-dealer broker">Inter-dealer broker</a></li></ul></li>
<li><a href="/wiki/Broker-dealer" title="Broker-dealer">Broker-dealer</a></li>
<li><a href="/wiki/Market_maker" title="Market maker">Market maker</a></li>
<li><a href="/wiki/Trader_(finance)" title="Trader (finance)">Trader</a>
<ul><li><a href="/wiki/Floor_trader" title="Floor trader">Floor trader</a></li>
<li><a href="/wiki/Proprietary_trading" title="Proprietary trading">Proprietary trader</a></li></ul></li>
<li><a href="/wiki/Quantitative_analysis_(finance)" title="Quantitative analysis (finance)">Quantitative analyst</a></li>
<li><a href="/wiki/Investor" title="Investor">Investor</a></li>
<li><a href="/wiki/Hedge_(finance)" title="Hedge (finance)">Hedger</a></li>
<li><a href="/wiki/Speculation" title="Speculation">Speculator</a></li>
<li><a href="/wiki/Arbitrage" title="Arbitrage">Arbitrager</a>
<ul><li><a href="/wiki/Scalping_(trading)" title="Scalping (trading)">Scalper</a></li></ul></li>
<li><a href="/wiki/Financial_regulation" title="Financial regulation">Regulator</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Trading venues</th><td class="navbox-list-with-group navbox-list navbox-odd" style="width:100%;padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Exchange_(organized_market)" title="Exchange (organized market)">Exchange</a>
<ul><li><a href="/wiki/List_of_major_stock_exchanges" title="List of major stock exchanges">List of major stock exchanges</a></li></ul></li>
<li><a href="/wiki/Over-the-counter_(finance)" title="Over-the-counter (finance)">Over-the-counter</a> (off-exchange)</li>
<li><a href="/wiki/Alternative_trading_system" title="Alternative trading system">Alternative trading system</a> (ATS)</li>
<li><a href="/wiki/Multilateral_trading_facility" title="Multilateral trading facility">Multilateral trading facility</a> (MTF)</li>
<li><a href="/wiki/Electronic_communication_network" title="Electronic communication network">Electronic communication network</a> (ECN)</li>
<li><a href="/wiki/Direct_market_access" title="Direct market access">Direct market access</a> (DMA)</li>
<li><a href="/wiki/Straight-through_processing" title="Straight-through processing">Straight-through processing</a> (STP)</li>
<li><a href="/wiki/Dark_pool" title="Dark pool">Dark pool</a> (private exchange)</li>
<li><a href="/wiki/Crossing_network" title="Crossing network">Crossing network</a></li>
<li><a href="/wiki/Foreign_exchange_aggregator" title="Foreign exchange aggregator">Liquidity aggregator</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%"><a href="/wiki/Stock_valuation" title="Stock valuation">Stock valuation</a></th><td class="navbox-list-with-group navbox-list navbox-even" style="width:100%;padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Alpha_(finance)" title="Alpha (finance)">Alpha</a></li>
<li><a href="/wiki/Arbitrage_pricing_theory" title="Arbitrage pricing theory">Arbitrage pricing theory</a> (APT)</li></ul>
<ul><li><a href="/wiki/Beta_(finance)" title="Beta (finance)">Beta</a></li>
<li><a href="/wiki/Buffett_indicator" title="Buffett indicator">Buffett indicator</a> (Cap-to-GDP)</li>
<li><a href="/wiki/Book_value" title="Book value">Book value</a> (BV)</li>
<li><a href="/wiki/Capital_asset_pricing_model" title="Capital asset pricing model">Capital asset pricing model</a> (CAPM)</li>
<li><a href="/wiki/Capital_market_line" title="Capital market line">Capital market line</a> (CML)</li>
<li><a href="/wiki/Dividend_discount_model" title="Dividend discount model">Dividend discount model</a> (DDM)</li>
<li><a href="/wiki/Dividend_yield" title="Dividend yield">Dividend yield</a></li>
<li><a href="/wiki/Earnings_yield" title="Earnings yield">Earnings yield</a></li>
<li><a href="/wiki/EV/Ebitda" class="mw-redirect" title="EV/Ebitda">EV/EBITDA</a></li>
<li><a href="/wiki/Fed_model" title="Fed model">Fed model</a></li>
<li><a href="/wiki/Net_asset_value" title="Net asset value">Net asset value</a> (NAV)</li>
<li><a href="/wiki/Security_characteristic_line" title="Security characteristic line">Security characteristic line</a></li>
<li><a href="/wiki/Security_market_line" title="Security market line">Security market line</a> (SML)</li>
<li><a href="/wiki/T-model" title="T-model">T-model</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Trading theories<br /> and <a href="/wiki/Trading_strategy" title="Trading strategy">strategies</a></th><td class="navbox-list-with-group navbox-list navbox-odd" style="width:100%;padding:0"><div style="padding:0 0.25em">
<ul><li><a class="mw-selflink selflink">Algorithmic trading</a></li>
<li><a href="/wiki/Buy_and_hold" title="Buy and hold">Buy and hold</a></li>
<li><a href="/wiki/Contrarian_investing" title="Contrarian investing">Contrarian investing</a></li>
<li><a href="/wiki/Dollar_cost_averaging" title="Dollar cost averaging">Dollar cost averaging</a></li>
<li><a href="/wiki/Efficient-market_hypothesis" title="Efficient-market hypothesis">Efficient-market hypothesis</a> (EMH)</li>
<li><a href="/wiki/Fundamental_analysis" title="Fundamental analysis">Fundamental analysis</a></li>
<li><a href="/wiki/Growth_stock" title="Growth stock">Growth stock</a></li>
<li><a href="/wiki/Market_timing" title="Market timing">Market timing</a></li>
<li><a href="/wiki/Modern_portfolio_theory" title="Modern portfolio theory">Modern portfolio theory</a> (MPT)</li>
<li><a href="/wiki/Momentum_investing" title="Momentum investing">Momentum investing</a></li>
<li><a href="/wiki/Mosaic_theory_(investments)" title="Mosaic theory (investments)">Mosaic theory</a></li>
<li><a href="/wiki/Pairs_trade" title="Pairs trade">Pairs trade</a></li>
<li><a href="/wiki/Post-modern_portfolio_theory" title="Post-modern portfolio theory">Post-modern portfolio theory</a> (PMPT)</li>
<li><a href="/wiki/Random_walk_hypothesis" title="Random walk hypothesis">Random walk hypothesis</a> (RMH)</li>
<li><a href="/wiki/Sector_rotation" title="Sector rotation">Sector rotation</a></li>
<li><a href="/wiki/Style_investing" title="Style investing">Style investing</a></li>
<li><a href="/wiki/Swing_trading" title="Swing trading">Swing trading</a></li>
<li><a href="/wiki/Technical_analysis" title="Technical analysis">Technical analysis</a></li>
<li><a href="/wiki/Trend_following" title="Trend following">Trend following</a></li>
<li><a href="/wiki/Value_averaging" title="Value averaging">Value averaging</a></li>
<li><a href="/wiki/Value_investing" title="Value investing">Value investing</a></li></ul>
</div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Related terms</th><td class="navbox-list-with-group navbox-list navbox-even" style="width:100%;padding:0"><div style="padding:0 0.25em">
<ul><li><a href="/wiki/Bid%E2%80%93ask_spread" title="Bid–ask spread">Bid–ask spread</a></li>
<li><a href="/wiki/Block_trade" title="Block trade">Block trade</a></li>
<li><a href="/wiki/Cross_listing" title="Cross listing">Cross listing</a></li>
<li><a href="/wiki/Dividend" title="Dividend">Dividend</a></li>
<li><a href="/wiki/Dual-listed_company" title="Dual-listed company">Dual-listed company</a></li>
<li><a href="/wiki/DuPont_analysis" title="DuPont analysis">DuPont analysis</a></li>
<li><a href="/wiki/Efficient_frontier" title="Efficient frontier">Efficient frontier</a></li>
<li><a href="/wiki/Financial_law" title="Financial law">Financial law</a></li>
<li><a href="/wiki/Flight-to-quality" title="Flight-to-quality">Flight-to-quality</a></li>
<li><a href="/wiki/Government_bond" title="Government bond">Government bond</a></li>
<li><a href="/wiki/Greenspan_put" title="Greenspan put">Greenspan put</a></li>
<li><a href="/wiki/Haircut_(finance)" title="Haircut (finance)">Haircut</a></li>
<li><a href="/wiki/Initial_public_offering" title="Initial public offering">Initial public offering</a> (IPO)</li>
<li><a href="/wiki/Long_(finance)" title="Long (finance)">Long</a></li>
<li><a href="/wiki/Mandatory_offer" title="Mandatory offer">Mandatory offer</a></li>
<li><a href="/wiki/Margin_(finance)" title="Margin (finance)">Margin</a></li>
<li><a href="/wiki/Market_anomaly" title="Market anomaly">Market anomaly</a></li>
<li><a href="/wiki/Market_capitalization" title="Market capitalization">Market capitalization</a></li>
<li><a href="/wiki/Market_depth" title="Market depth">Market depth</a></li>
<li><a href="/wiki/Market_manipulation" title="Market manipulation">Market manipulation</a></li>
<li><a href="/wiki/Market_trend" title="Market trend">Market trend</a></li>
<li><a href="/wiki/Mean_reversion_(finance)" title="Mean reversion (finance)">Mean reversion</a></li>
<li><a href="/wiki/Momentum_(finance)" title="Momentum (finance)">Momentum</a></li>
<li><a href="/wiki/Open_outcry" title="Open outcry">Open outcry</a></li>
<li><a href="/wiki/Order_book" title="Order book">Order book</a></li>
<li><a href="/wiki/Position_(finance)" title="Position (finance)">Position</a></li>
<li><a href="/wiki/Public_float" title="Public float">Public float</a></li>
<li><a href="/wiki/Public_offering" title="Public offering">Public offering</a></li>
<li><a href="/wiki/Rally_(stock_market)" title="Rally (stock market)">Rally</a></li>
<li><a href="/wiki/Returns-based_style_analysis" title="Returns-based style analysis">Returns-based style analysis</a></li>
<li><a href="/wiki/Reverse_stock_split" title="Reverse stock split">Reverse stock split</a></li>
<li><a href="/wiki/Share_repurchase" title="Share repurchase">Share repurchase</a></li>
<li><a href="/wiki/Short_(finance)" title="Short (finance)">Short selling</a></li>
<li><a href="/wiki/Short_squeeze" title="Short squeeze">Short squeeze</a></li>
<li><a href="/wiki/Slippage_(finance)" title="Slippage (finance)">Slippage</a></li>
<li><a href="/wiki/Speculation" title="Speculation">Speculation</a></li>
<li><a href="/wiki/Squeeze-out" title="Squeeze-out">Squeeze-out</a></li>
<li><a href="/wiki/Stock_dilution" title="Stock dilution">Stock dilution</a></li>
<li><a href="/wiki/Stock_exchange" title="Stock exchange">Stock exchange</a></li>
<li><a href="/wiki/Stock_market_index" title="Stock market index">Stock market index</a></li>
<li><a href="/wiki/Stock_split" title="Stock split">Stock split</a></li>
<li><a href="/wiki/Stock_swap" title="Stock swap">Stock swap</a></li>
<li><a href="/wiki/Trade_(finance)" title="Trade (finance)">Trade</a></li>
<li><a href="/wiki/Tender_offer" title="Tender offer">Tender offer</a></li>
<li><a href="/wiki/Uptick_rule" title="Uptick rule">Uptick rule</a></li>
<li><a href="/wiki/Volatility_(finance)" title="Volatility (finance)">Volatility</a></li>
<li><a href="/wiki/Voting_interest" title="Voting interest">Voting interest</a></li>
<li><a href="/wiki/Yield_(finance)" title="Yield (finance)">Yield</a></li></ul>
</div></td></tr></tbody></table></div>
<!-- 
NewPP limit report
Parsed by mw‐api‐int.eqiad.main‐6895f5cf8‐zjghm
Cached time: 20250825161524
Cache expiry: 2592000
Reduced expiry: false
Complications: [vary‐revision‐sha1, show‐toc]
CPU time usage: 1.155 seconds
Real time usage: 1.301 seconds
Preprocessor visited node count: 6463/1000000
Revision size: 88967/2097152 bytes
Post‐expand include size: 228675/2097152 bytes
Template argument size: 2704/2097152 bytes
Highest expansion depth: 12/100
Expensive parser function count: 8/500
Unstrip recursion depth: 1/20
Unstrip post‐expand size: 375601/5000000 bytes
Lua time usage: 0.745/10.000 seconds
Lua memory usage: 7375185/52428800 bytes
Number of Wikibase entities loaded: 0/500
-->
<!--
Transclusion expansion time report (%,ms,calls,template)
100.00% 1112.381      1 -total
 61.38%  682.782      2 Template:Reflist
 18.66%  207.562     27 Template:Cite_news
 11.24%  125.068     24 Template:Cite_web
 10.42%  115.859     18 Template:Citation
  9.37%  104.195     15 Template:Cite_journal
  7.00%   77.884      1 Template:Financial_market_participants
  6.85%   76.245      1 Template:Sidebar
  6.53%   72.673      1 Template:Short_description
  6.26%   69.669      4 Template:Navbox
-->

<!-- Saved in parser cache with key enwiki:pcache:2484768:|#|:idhash:canonical and timestamp 20250825161524 and revision id 1306548555. Rendering was triggered because: api-parse
 -->
</div><noscript><img src="https://en.wikipedia.org/wiki/Special:CentralAutoLogin/start?type=1x1&amp;usesul3=1" alt="" width="1" height="1" style="border: none; position: absolute;"></noscript>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://en.wikipedia.org/w/index.php?title=Algorithmic_trading&amp;oldid=1306548555">https://en.wikipedia.org/w/index.php?title=Algorithmic_trading&amp;oldid=1306548555</a>"</div></div>
					<div id="catlinks" class="catlinks" data-mw="interface"><div id="mw-normal-catlinks" class="mw-normal-catlinks"><a href="/wiki/Help:Category" title="Help:Category">Categories</a>: <ul><li><a href="/wiki/Category:Algorithmic_trading" title="Category:Algorithmic trading">Algorithmic trading</a></li><li><a href="/wiki/Category:Electronic_trading_systems" title="Category:Electronic trading systems">Electronic trading systems</a></li><li><a href="/wiki/Category:Financial_markets" title="Category:Financial markets">Financial markets</a></li><li><a href="/wiki/Category:Share_trading" title="Category:Share trading">Share trading</a></li></ul></div><div id="mw-hidden-catlinks" class="mw-hidden-catlinks mw-hidden-cats-hidden">Hidden categories: <ul><li><a href="/wiki/Category:CS1_errors:_periodical_ignored" title="Category:CS1 errors: periodical ignored">CS1 errors: periodical ignored</a></li><li><a href="/wiki/Category:CS1_errors:_missing_periodical" title="Category:CS1 errors: missing periodical">CS1 errors: missing periodical</a></li><li><a href="/wiki/Category:Webarchive_template_wayback_links" title="Category:Webarchive template wayback links">Webarchive template wayback links</a></li><li><a href="/wiki/Category:CS1_maint:_multiple_names:_authors_list" title="Category:CS1 maint: multiple names: authors list">CS1 maint: multiple names: authors list</a></li><li><a href="/wiki/Category:CS1_maint:_archived_copy_as_title" title="Category:CS1 maint: archived copy as title">CS1 maint: archived copy as title</a></li><li><a href="/wiki/Category:Articles_with_short_description" title="Category:Articles with short description">Articles with short description</a></li><li><a href="/wiki/Category:Short_description_is_different_from_Wikidata" title="Category:Short description is different from Wikidata">Short description is different from Wikidata</a></li><li><a href="/wiki/Category:Use_mdy_dates_from_January_2019" title="Category:Use mdy dates from January 2019">Use mdy dates from January 2019</a></li><li><a href="/wiki/Category:Articles_needing_additional_references_from_February_2025" title="Category:Articles needing additional references from February 2025">Articles needing additional references from February 2025</a></li><li><a href="/wiki/Category:All_articles_needing_additional_references" title="Category:All articles needing additional references">All articles needing additional references</a></li></ul></div></div>
				</div>
			</main>
			
		</div>
		<div class="mw-footer-container">
			
<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
	<li id="footer-info-lastmod"> This page was last edited on 18 August 2025, at 10:24<span class="anonymous-show">&#160;(UTC)</span>.</li>
	<li id="footer-info-copyright">Text is available under the <a href="/wiki/Wikipedia:Text_of_the_Creative_Commons_Attribution-ShareAlike_4.0_International_License" title="Wikipedia:Text of the Creative Commons Attribution-ShareAlike 4.0 International License">Creative Commons Attribution-ShareAlike 4.0 License</a>;
additional terms may apply. By using this site, you agree to the <a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Terms_of_Use" class="extiw" title="foundation:Special:MyLanguage/Policy:Terms of Use">Terms of Use</a> and <a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy" class="extiw" title="foundation:Special:MyLanguage/Policy:Privacy policy">Privacy Policy</a>. Wikipedia® is a registered trademark of the <a rel="nofollow" class="external text" href="https://wikimediafoundation.org/">Wikimedia Foundation, Inc.</a>, a non-profit organization.</li>
</ul>

	<ul id="footer-places">
	<li id="footer-places-privacy"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy policy</a></li>
	<li id="footer-places-about"><a href="/wiki/Wikipedia:About">About Wikipedia</a></li>
	<li id="footer-places-disclaimers"><a href="/wiki/Wikipedia:General_disclaimer">Disclaimers</a></li>
	<li id="footer-places-contact"><a href="//en.wikipedia.org/wiki/Wikipedia:Contact_us">Contact Wikipedia</a></li>
	<li id="footer-places-wm-codeofconduct"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Universal_Code_of_Conduct">Code of Conduct</a></li>
	<li id="footer-places-developers"><a href="https://developer.wikimedia.org">Developers</a></li>
	<li id="footer-places-statslink"><a href="https://stats.wikimedia.org/#/en.wikipedia.org">Statistics</a></li>
	<li id="footer-places-cookiestatement"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Cookie_statement">Cookie statement</a></li>
	<li id="footer-places-mobileview"><a href="//en.m.wikipedia.org/w/index.php?title=Algorithmic_trading&amp;mobileaction=toggle_view_mobile" class="noprint stopMobileRedirectToggle">Mobile view</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://www.wikimedia.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/static/images/footer/wikimedia-button.svg" width="84" height="29"><img src="/static/images/footer/wikimedia.svg" width="25" height="25" alt="Wikimedia Foundation" lang="en" loading="lazy"></picture></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/w/resources/assets/poweredby_mediawiki.svg" width="88" height="31"><img src="/w/resources/assets/mediawiki_compact.svg" alt="Powered by MediaWiki" lang="en" width="25" height="25" loading="lazy"></picture></a></li>
</ul>

</footer>

		</div>
	</div> 
</div> 
<div class="vector-header-container vector-sticky-header-container no-font-mode-scale">
	<div id="vector-sticky-header" class="vector-sticky-header">
		<div class="vector-sticky-header-start">
			<div class="vector-sticky-header-icon-start vector-button-flush-left vector-button-flush-right" aria-hidden="true">
				<button class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-sticky-header-search-toggle" tabindex="-1" data-event-name="ui.vector-sticky-search-form.icon"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
			</button>
		</div>
			
		<div role="search" class="vector-search-box-vue  vector-search-box-show-thumbnail vector-search-box">
			<div class="vector-typeahead-search-container">
				<div class="cdx-typeahead-search cdx-typeahead-search--show-thumbnail">
					<form action="/w/index.php" id="vector-sticky-search-form" class="cdx-search-input cdx-search-input--has-end-button">
						<div  class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
							<div class="cdx-text-input cdx-text-input--has-start-icon">
								<input
									class="cdx-text-input__input mw-searchInput" autocomplete="off"
									
									type="search" name="search" placeholder="Search Wikipedia">
								<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
							</div>
							<input type="hidden" name="title" value="Special:Search">
						</div>
						<button class="cdx-button cdx-search-input__end-button">Search</button>
					</form>
				</div>
			</div>
		</div>
		<div class="vector-sticky-header-context-bar">
				<nav aria-label="Contents" class="vector-toc-landmark">
						
					<div id="vector-sticky-header-toc" class="vector-dropdown mw-portlet mw-portlet-sticky-header-toc vector-sticky-header-toc vector-button-flush-left"  >
						<input type="checkbox" id="vector-sticky-header-toc-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-sticky-header-toc" class="vector-dropdown-checkbox "  aria-label="Toggle the table of contents"  >
						<label id="vector-sticky-header-toc-label" for="vector-sticky-header-toc-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-listBullet mw-ui-icon-wikimedia-listBullet"></span>

<span class="vector-dropdown-label-text">Toggle the table of contents</span>
						</label>
						<div class="vector-dropdown-content">
					
						<div id="vector-sticky-header-toc-unpinned-container" class="vector-unpinned-container">
						</div>
					
						</div>
					</div>
			</nav>
				<div class="vector-sticky-header-context-bar-primary" aria-hidden="true" ><span class="mw-page-title-main">Algorithmic trading</span></div>
			</div>
		</div>
		<div class="vector-sticky-header-end" aria-hidden="true">
			<div class="vector-sticky-header-icons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-talk-sticky-header" tabindex="-1" data-event-name="talk-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbles mw-ui-icon-wikimedia-speechBubbles"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-subject-sticky-header" tabindex="-1" data-event-name="subject-sticky-header"><span class="vector-icon mw-ui-icon-article mw-ui-icon-wikimedia-article"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-history-sticky-header" tabindex="-1" data-event-name="history-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-history mw-ui-icon-wikimedia-wikimedia-history"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only mw-watchlink" id="ca-watchstar-sticky-header" tabindex="-1" data-event-name="watch-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-star mw-ui-icon-wikimedia-wikimedia-star"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-edit-sticky-header" tabindex="-1" data-event-name="wikitext-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-wikiText mw-ui-icon-wikimedia-wikimedia-wikiText"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-ve-edit-sticky-header" tabindex="-1" data-event-name="ve-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-edit mw-ui-icon-wikimedia-wikimedia-edit"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-viewsource-sticky-header" tabindex="-1" data-event-name="ve-edit-protected-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-editLock mw-ui-icon-wikimedia-wikimedia-editLock"></span>

<span></span>
			</a>
		</div>
			<div class="vector-sticky-header-buttons">
				<button class="cdx-button cdx-button--weight-quiet mw-interlanguage-selector" id="p-lang-btn-sticky-header" tabindex="-1" data-event-name="ui.dropdown-p-lang-btn-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-language mw-ui-icon-wikimedia-wikimedia-language"></span>

<span>22 languages</span>
			</button>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--action-progressive" id="ca-addsection-sticky-header" tabindex="-1" data-event-name="addsection-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbleAdd-progressive mw-ui-icon-wikimedia-speechBubbleAdd-progressive"></span>

<span>Add topic</span>
			</a>
		</div>
			<div class="vector-sticky-header-icon-end">
				<div class="vector-user-links">
				</div>
			</div>
		</div>
	</div>
</div>
<div class="mw-portlet mw-portlet-dock-bottom emptyPortlet" id="p-dock-bottom">
	<ul>
		
	</ul>
</div>
<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgHostname":"mw-web.eqiad.main-6d547c57c5-qg4rd","wgBackendResponseTime":173,"wgPageParseReport":{"limitreport":{"cputime":"1.155","walltime":"1.301","ppvisitednodes":{"value":6463,"limit":1000000},"revisionsize":{"value":88967,"limit":2097152},"postexpandincludesize":{"value":228675,"limit":2097152},"templateargumentsize":{"value":2704,"limit":2097152},"expansiondepth":{"value":12,"limit":100},"expensivefunctioncount":{"value":8,"limit":500},"unstrip-depth":{"value":1,"limit":20},"unstrip-size":{"value":375601,"limit":5000000},"entityaccesscount":{"value":0,"limit":500},"timingprofile":["100.00% 1112.381      1 -total"," 61.38%  682.782      2 Template:Reflist"," 18.66%  207.562     27 Template:Cite_news"," 11.24%  125.068     24 Template:Cite_web"," 10.42%  115.859     18 Template:Citation","  9.37%  104.195     15 Template:Cite_journal","  7.00%   77.884      1 Template:Financial_market_participants","  6.85%   76.245      1 Template:Sidebar","  6.53%   72.673      1 Template:Short_description","  6.26%   69.669      4 Template:Navbox"]},"scribunto":{"limitreport-timeusage":{"value":"0.745","limit":"10.000"},"limitreport-memusage":{"value":7375185,"limit":52428800}},"cachereport":{"origin":"mw-api-int.eqiad.main-6895f5cf8-zjghm","timestamp":"20250825161524","ttl":2592000,"transientcontent":false}}});});</script>
<script type="application/ld+json">{"@context":"https:\/\/schema.org","@type":"Article","name":"Algorithmic trading","url":"https:\/\/en.wikipedia.org\/wiki\/Algorithmic_trading","sameAs":"http:\/\/www.wikidata.org\/entity\/Q139445","mainEntity":"http:\/\/www.wikidata.org\/entity\/Q139445","author":{"@type":"Organization","name":"Contributors to Wikimedia projects"},"publisher":{"@type":"Organization","name":"Wikimedia Foundation, Inc.","logo":{"@type":"ImageObject","url":"https:\/\/www.wikimedia.org\/static\/images\/wmf-hor-googpub.png"}},"datePublished":"2005-08-18T20:04:42Z","dateModified":"2025-08-18T10:24:02Z","image":"https:\/\/upload.wikimedia.org\/wikipedia\/commons\/5\/5e\/Assorted_United_States_coins.jpg","headline":"method of executing orders using automated pre-programmed trading instructions"}</script>
</body>
</html>