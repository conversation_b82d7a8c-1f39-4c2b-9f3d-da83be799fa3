[{"title": "Meta-Stock: Task-Difficulty-Adaptive Meta-learning for Sub-new Stock Price Prediction", "url": "http://arxiv.org/abs/2308.11117v1", "engines": ["arxiv"]}, {"title": "Stockformer: A Price-Volume Factor Stock Selection Model Based on Wavelet Transform and Multi-Task Self-Attention Networks", "url": "http://arxiv.org/abs/2401.06139v2", "engines": ["arxiv"]}, {"title": "Discrete Wavelet Transform-Based Prediction of Stock Index: A Study on National Stock Exchange Fifty Index", "url": "http://arxiv.org/abs/1605.07278v1", "engines": ["arxiv"]}, {"title": "How Wave - Wavelet Trading Wins and \"Beats\" the Market", "url": "http://arxiv.org/abs/1704.00383v1", "engines": ["arxiv"]}, {"title": "Integration of Wavelet Transform Convolution and Channel Attention with LSTM for Stock Price Prediction based Portfolio Allocation", "url": "http://arxiv.org/abs/2507.01973v2", "engines": ["arxiv"]}, {"title": "Stock Forecasting using M-Band Wavelet-Based SVR and RNN-LSTMs Models", "url": "http://arxiv.org/abs/1904.08459v1", "engines": ["arxiv"]}, {"title": "Image Processing Tools for Financial Time Series Classification", "url": "http://arxiv.org/abs/2008.06042v2", "engines": ["arxiv"]}, {"title": "Stock Prices Prediction using Deep Learning Models", "url": "http://arxiv.org/abs/1909.12227v1", "engines": ["arxiv"]}, {"title": "Pre-training Time Series Models with Stock Data Customization", "url": "http://arxiv.org/abs/2506.16746v1", "engines": ["arxiv"]}, {"title": "Using the Discrete Wavelet Transform in Stock Index ...", "url": "http://www.diva-portal.org/smash/get/diva2:1849041/FULLTEXT01.pdf", "engines": ["google"]}, {"title": "Wavelet-CNN for temporal data: Enhancing long-term stock ...", "url": "https://www.sciencedirect.com/science/article/pii/S2667096825000424", "engines": ["google"]}, {"title": "Stockformer: A price–volume factor stock selection model ...", "url": "https://www.sciencedirect.com/science/article/abs/pii/S0957417425004257", "engines": ["google"]}, {"title": "Enhancing financial time series forecasting: a comparative ...", "url": "https://link.springer.com/article/10.1007/s11135-025-02325-1", "engines": ["google"]}, {"title": "A Hybrid Method Based on Extreme Learning Machine and ...", "url": "https://pmc.ncbi.nlm.nih.gov/articles/PMC8070264/", "engines": ["google"]}, {"title": "An Evaluation of Deep Learning Models for Stock Market ...", "url": "https://arxiv.org/html/2408.12408v1", "engines": ["google"]}, {"title": "financial time series prediction using wavelet and artificial ...", "url": "https://www.researchgate.net/publication/353175680_FINANCIAL_TIME_SERIES_PREDICTION_USING_WAVELET_AND_ARTIFICIAL_NEURAL_NETWORK", "engines": ["google"]}, {"title": "Enhanced stock market forecasting using dandelion ...", "url": "https://www.nature.com/articles/s41598-024-71873-7", "engines": ["google"]}, {"title": "Winton Stock Market Challenge", "url": "http://cs230.stanford.edu/projects_winter_2019/reports/15717900.pdf", "engines": ["google"]}, {"title": "Impact of COVID-19 on Forecasting Stock Prices: An Integration of Stationary Wavelet Transform and Bidirectional Long Short-Term Memory", "url": "http://arxiv.org/abs/2007.02673v1", "engines": ["arxiv"]}, {"title": "Stock market forecasting using Continuous Wavelet ...", "url": "https://www.ijarcs.info/index.php/Ijarcs/article/view/6919", "engines": ["google"]}]