[{"title": "Statistical Arbitrage: A Pairs Trading Strategy", "url": "https://www.kaggle.com/code/sathyanarayanrao89/statistical-arbitrage-a-pairs-trading-strategy/input", "engines": ["google"]}, {"title": "Naive Statistical Arbitrage Trading Analysis", "url": "https://www.kaggle.com/code/addarm/naive-statistical-arbitrage-trading-analysis/notebook", "engines": ["google"]}, {"title": "Pair Trading Strategy with ML", "url": "https://www.kaggle.com/code/mohammedobeidat/pair-trading-strategy-with-ml/comments", "engines": ["google"]}, {"title": "Statistical Arbitrage: A Pairs Trading Strategy", "url": "https://www.kaggle.com/code/sathyanarayanrao89/statistical-arbitrage-a-pairs-trading-strategy/output", "engines": ["google"]}, {"title": "ETF Pairs Trading Signals (SPY, QQQ)", "url": "https://www.kaggle.com/code/christopher<PERSON><PERSON>i/etf-pairs-trading-signals-spy-qqq", "engines": ["google"]}, {"title": "Pair Trading Strategy: Stock Prediction", "url": "https://www.kaggle.com/code/yekahaaagayeham/pair-trading-strategy-stock-prediction", "engines": ["google"]}, {"title": "Statistical Arbitrage: A Pairs Trading Strategy", "url": "https://www.kaggle.com/code/sathyanarayanrao89/statistical-arbitrage-a-pairs-trading-strategy/comments", "engines": ["google"]}, {"title": "Pair Trading Strategy with ML", "url": "https://www.kaggle.com/code/mohammedobeidat/pair-trading-strategy-with-ml", "engines": ["google"]}, {"title": "Pair Trading Strategy: Stock Prediction", "url": "https://www.kaggle.com/code/yekahaaagayeham/pair-trading-strategy-stock-prediction/execution", "engines": ["google"]}, {"title": "Backtesting Pair Trading Strategy", "url": "https://www.kaggle.com/code/achrafbenssassi/backtesting-pair-trading-strategy", "engines": ["google"]}]