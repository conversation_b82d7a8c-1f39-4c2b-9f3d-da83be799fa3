{"title": "Portfolio Optimization Ensemble Methodology in RPS Competition", "source_url": "https://medium.com/data-science/rock-paper-scissors-and-the-stock-market-f1ad81785a76", "thesis": "The competition's structure resembles the stock market, where inefficiencies can be exploited. By using portfolio optimization techniques to weight and combine multiple algorithms' predictions, one can create an ensemble that outperforms random agents by exploiting non-random patterns in opponent behavior.", "universe": "All available RPS algorithms", "rebalancing": "Each step of the episode (1000 games)", "signals": [{"name": "Algorithm Performance Signal", "definition": "Measure each algorithm's performance against the current opponent and adjust its weight in the ensemble accordingly."}]}