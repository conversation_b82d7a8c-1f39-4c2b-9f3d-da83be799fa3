Computer Science > Software Engineering
Title:The Developers' Design Thinking Toolbox in Hackathons: A Study on the Recurring Design Methods in Software Development Marathons
View PDFAbstract:Hackathons are time-bounded collaborative events of intense teamwork to build prototypes usually in the form of software, aiming to specific challenges proposed by the organizers. These events became a widespread practice in the IT industry, universities and many other scenarios, as a result of a growing open-innovation trend in the last decade. Since the main deliverable of these events is a demonstrable version of an idea, such as early hardware or software prototypes, the short time frame requires participants to quickly understand the proposed challenge or even identify issues related to a given domain. To create solutions, teams follow an ad-hoc but effective design approach, that many times seems informal since the background of the participants is rather centered on technical aspects (e.g., web and mobile programming) and does not involve any training in Design Thinking.
To understand this creative process, we conducted 37 interviews (32 hackathons winners and 5 hackathon organizers) with people from 16 countries. We aimed to identify the design processes and recurring design methods applied by winners in these events. Also, we conducted a focus group with 8 people experienced in hackathons (participants and organizers) to discuss our findings. Our analysis revealed that although hackathon winners with IT background have no formal training on Design Thinking, they are aware of many design methods, typically following a sequence of phases that involve divergent and convergent thinking to explore the problem space and propose alternatives in a solution space, which is the rationale behind Design Thinking. We derived a set of recommendations based on design strategies that seem to lead to successful hackathon participation. These recommendations can also be useful to organizers who intend to enhance the experience of newcomers in hackathons.
Bibliographic and Citation Tools
Code, Data and Media Associated with this Article
Demos
Recommenders and Search Tools
arXivLabs: experimental projects with community collaborators
arXivLabs is a framework that allows collaborators to develop and share new arXiv features directly on our website.
Both individuals and organizations that work with arXivLabs have embraced and accepted our values of openness, community, excellence, and user data privacy. arXiv is committed to these values and only works with partners that adhere to them.
Have an idea for a project that will add value for arXiv's community? Learn more about arXivLabs.