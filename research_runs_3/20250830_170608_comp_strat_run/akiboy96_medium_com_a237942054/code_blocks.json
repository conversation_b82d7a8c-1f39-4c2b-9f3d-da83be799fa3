["stocks_cross_corr, _, _ = calculate_corr(df_stock_prices,1, len(df_stock_prices), 'pearson')\nstocks_cross_corr = stocks_cross_corr[1]cor_thresold = 0.7\nG = build_graph(stocks_cross_corr, cor_thresold)\npartition = community.best_partition(G)\nmodularity = community.modularity(partition, G)\nvalues = [partition.get(node) for node \nin\n G.nodes()]\nplt.figure(figsize=(10,10))\nnx.draw_spring(G, cmap = plt.get_cmap('jet'), node_color = values, node_size=30, with_labels=False)\nprint(modularity)\nprint(\"Total number of Communities=\", len(G.nodes()))\ndict_betwenness_centrality = nx.betweenness_centrality(G)\ndict_degree_centrality = nx.degree_centrality(G)\ndict_closeness_centrality = nx.closeness_centrality(G)\ndict_eigenvector_centrality = nx.eigenvector_centrality(G)\nprint(\"dict_degree_centrality: \", dict_degree_centrality)\nprint(\"dict_closeness_centrality: \", dict_closeness_centrality)\nprint(\"dict_eigenvector_centrality: \", dict_eigenvector_centrality)\nprint(\"dict_betweenness_centrality: \", dict_betwenness_centrality)", "#Portfolio Formula: \nc_dict = dict([(k, [dict_betwenness_centrality[k], dict_eigenvector_centrality[k], dict_degree_centrality[k], dict_closeness_centrality[k] ]) for k \nin\n dict_betwenness_centrality])\n#print(c_dict)    \n    \nC_total = {}\nfor key \nin\n c_dict: \n    C_total[key] = sum(c_dict[key]) \n        \nprint(\"The Centrality total for stocks are:\", C_total)   \nnewDict = dict(filter(lambda elem: elem[1] > 0, C_total.items()))\nprint(\"Stocks greater than 0.3 centrality are\",newDict)\nprint(len(newDict))\ndf_centrality = pd.DataFrame(list(newDict.items()),columns = ['Symbol','Centrality']) \ndf_centrality.sort_values(by='Centrality', ascending=False)\n#df_centrality.head(20)\n#type(df_centrality['Centrality'])\ndf_centrality.to_csv('centrality_of_stocks_0.7cor.csv',index=False)", "# This R environment comes with many helpful analytics packages installed\n# It is defined by the kaggle/rstats Docker image: https://github.com/kaggle/docker-rstats\n# For example, here's a helpful package to load\nlibrary(tidyverse)\nlibrary(tidyquant)\nlibrary(jsonlite)\nlibrary(tidyverse)\nlibrary(readr)\nlibrary(igraph)\nlibrary(dplyr)\nlibrary(lubridate)\nlibrary(data.table)\nlibrary(Quandl) \n# metapackage of all tidyverse packages\n# Input data files are available in the read-only \"../input/\" directory\n# For example, running this (by clicking run or pressing Shift+Enter) will list all files under the input directory\nlist.files(path = \"../input\")\n# You can write up to 5GB to the current directory (/kaggle/working/) that gets preserved as output when you create a version using \"Save & Run All\" \n# You can also write temporary files to /kaggle/temp/, but they won't be saved outside of the current session", "#DataLoad \nstockprices <- read.csv(\"../input/usstockprices/stocks_price_final.csv\")\ncentrality0.5 <- read.csv(\"../input/centrality-of-stocks-network-analysis/centrality_of_stocks_0.5cor.csv\")\ncentrality0.6 <- read.csv(\"../input/centrality-of-stocks-network-analysis/centrality_of_stocks_0.6cor.csv\")\ncentrality0.7 <- read.csv(\"../input/centrality-of-stocks-network-analysis/centrality_of_stocks_0.7cor.csv\")", "#Merges Centtraility values with other potential useful information like market cap and sector for further filtering\nstocks <- stockprices[!duplicated(stockprices$symbol), ] \n# removes duplicate symbols\nIndex0.5cor <- merge(stocks, centrality0.5, by.x = \"symbol\", by.y = \"Symbol\")\nIndex0.6cor <- merge(stocks, centrality0.6, by.x = \"symbol\", by.y = \"Symbol\")\nIndex0.7cor <- merge(stocks, centrality0.7, by.x = \"symbol\", by.y = \"Symbol\")\n#head(Index0.6cor)\nIndexGet0.5 <- as.character(Index0.5cor$symbol)\nIndexGet0.6 <- as.character(Index0.6cor$symbol)\nIndexGet0.7 <- as.character(Index0.7cor$symbol)\n#Portfolio Testing\n##0.5 correlation centrality prices\nstockindex0.5 <- tq_get(IndexGet0.5, get=\"stock.prices\", from = \"2015-07-01\",warnings = FALSE,\n                             stringsAsFactors = FALSE) %>%\n  group_by(symbol) %>%\n  tq_transmute(select=adjusted,\n               mutate_fun=periodReturn,\n               period=\"monthly\",\n               col_rename = \"monthly_return\")\nstockindex0.5\n##0.6 correlation centrality prices\nstockindex0.6 <- tq_get(IndexGet0.6, get=\"stock.prices\", from = \"2015-07-01\",warnings = FALSE,\n                             stringsAsFactors = FALSE) %>%\n  group_by(symbol) %>%\n  tq_transmute(select=adjusted,\n               mutate_fun=periodReturn,\n               period=\"monthly\",\n               col_rename = \"monthly_return\")\n##0.7 correlation centrality prices\nstockindex0.7 <- tq_get(IndexGet0.7, get=\"stock.prices\", from = \"2015-07-01\",warnings = FALSE,\n                             stringsAsFactors = FALSE) %>%\n  group_by(symbol) %>%\n  tq_transmute(select=adjusted,\n               mutate_fun=periodReturn,\n               period=\"monthly\",\n               col_rename = \"monthly_return\")\n##Base Portfolio to compare \nbaseline_returns_monthly <- \"SPY\" %>%\n    tq_get(get  = \"stock.prices\",\n           from = \"2015-07-01\", warnings = FALSE,stringsAsFactors = FALSE) %>%\n    tq_transmute(select     = adjusted, \n                 mutate_fun = periodReturn, \n                 period     = \"monthly\", \n                 col_rename = \"spy_monthly_return\")\nbaseline_returns_monthly", "portfolio_returns_monthly0.5 <- stockindex0.5 %>%\n    tq_portfolio(assets_col  = symbol, \n                 returns_col = monthly_return, \n                 col_rename  = \"portfolio-monthly\")\nportfolio_returns_monthly0.6 <- stockindex0.6 %>%\n    tq_portfolio(assets_col  = symbol, \n                 returns_col = monthly_return, \n                 col_rename  = \"portfolio-monthly\")\nportfolio_returns_monthly0.7 <- stockindex0.7 %>%\n    tq_portfolio(assets_col  = symbol, \n                 returns_col = monthly_return, \n                 col_rename  = \"portfolio-monthly\")\n#Portfolio Compare\nstock0.5indexVSSPY <- left_join(portfolio_returns_monthly0.5, \n                                   baseline_returns_monthly,\n                                   by = \"date\")\nstock0.6indexVSSPY <- left_join(portfolio_returns_monthly0.6, \n                                   baseline_returns_monthly,\n                                   by = \"date\")\nstock0.7indexVSSPY <- left_join(portfolio_returns_monthly0.7, \n                                   baseline_returns_monthly,\n                                   by = \"date\")\n#stock0.5indexVSSPY\nggplot(stock0.5indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = \"blue\")+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = \"red\")\nggplot(stock0.6indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = \"blue\")+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = \"red\")\nggplot(stock0.7indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = \"blue\")+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = \"red\")", "##PLAYGROUND - LETS DO TRIAL AND ERROR HERE TO BEAT THE SPY GRAPH in RETURNS\ncentrality_filter <- Index0.6cor\n#filter(Centrality > 0.8 & Centrality < 0.5)\ncentrality_filter\nIndexGet0.6 <- as.character(centrality_filter$symbol)\nstockindex0.6 <- tq_get(IndexGet0.6, get=\"stock.prices\", from = \"2015-07-01\",warnings = FALSE,\n                             stringsAsFactors = FALSE) %>%\n  group_by(symbol) %>%\n  tq_transmute(select=adjusted,\n               mutate_fun=periodReturn,\n               period=\"monthly\",\n               col_rename = \"monthly_return\")\nportfolio_returns_monthly0.6 <- stockindex0.6 %>%\n    tq_portfolio(assets_col  = symbol, \n                 returns_col = monthly_return, \n                 col_rename  = \"portfolio-monthly\")\nstock0.6indexVSSPY <- left_join(portfolio_returns_monthly0.6, \n                                   baseline_returns_monthly,\n                                   by = \"date\")\nggplot(stock0.6indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = \"blue\")+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = \"red\")", "centrality_filter <- Index0.5cor %>%\nfilter((sector == \"Health Care\" & Centrality > 0.4) | (sector == \"Technology\" & Centrality > 0.42) | (sector == \"Consumer Services\" & Centrality > 0.49) | (sector == \"Finance\" & Centrality > 0.5)  | (sector == \"Transportation\" & Centrality > 0.5) | (sector == \"Capital Goods\" & Centrality > 0.35) | (sector == \"Miscellaneous\" & Centrality > 0.5) | (sector == \"Basic Industries\" & Centrality > 0.39) | (sector == \"Public Utilities\" & Centrality > 0.36) | (sector == \"Consumer Durables\" & Centrality > 0.3)| (sector == \"Consumer Non-Durables\" & Centrality > 0.25))\ncentrality_filter\nIndexGet0.5 <- as.character(centrality_filter$symbol)\nstockindex0.5 <- tq_get(IndexGet0.5, get=\"stock.prices\", from = \"2015-07-01\", till = \"2020-07-22\",warnings = FALSE,\n                             stringsAsFactors = FALSE) %>%\n  group_by(symbol) %>%\n  tq_transmute(select=adjusted,\n               mutate_fun=periodReturn,\n               period=\"monthly\",\n               col_rename = \"monthly_return\")\nportfolio_returns_monthly0.5 <- stockindex0.5 %>%\n    tq_portfolio(assets_col  = symbol, \n                 returns_col = monthly_return, \n                 col_rename  = \"portfolio-monthly\")\nstock0.5indexVSSPY <- left_join(portfolio_returns_monthly0.5, \n                                   baseline_returns_monthly,\n                                   by = \"date\")\nstock0.5indexVSSPY$`portfolio-monthly` <- stock0.5indexVSSPY$`portfolio-monthly` * 100\nstock0.5indexVSSPY$spy_monthly_return <- stock0.5indexVSSPY$spy_monthly_return * 100\nstock0.5indexVSSPY <- stock0.5indexVSSPY[-c(14), ]\nreturns <- ggplot(stock0.5indexVSSPY) + geom_line(aes(x = `date`, y = `portfolio-monthly`), color = \"blue\")+ geom_line(aes(x = `date`, y = `spy_monthly_return`), color = \"red\") + ggtitle(\"Portfolio vs S&P 500 Returns over 5 years\") + labs(y=\"Returns Percenatge\", x = \"Date\") \nplot(returns)"]