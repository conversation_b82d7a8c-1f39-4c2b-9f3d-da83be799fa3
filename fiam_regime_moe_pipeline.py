#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FIAM Hackathon Strategy: Regime-Mixture Experts + Risk Tilt + Robust Allocation (no RL)

This pipeline trains on a panel of monthly data and outputs portfolio weights per month.
It is designed to be *robust* under limited data and to degrade gracefully if some optional
libraries are not installed.

Core ideas
----------
1) Learn *soft* regimes p_t (no hard labels) using HMM (if available) or GMM fallback.
2) Optional tiny temporal encoder (1D conv) to extract per-asset motifs (can be disabled).
3) Train K regime-weighted "experts" + 1 global expert (tree/ridge rankers).
4) Gate (blend) experts each month with p_t and entropy-aware global weight.
5) Risk-aware tilt (use realized vol/CVaR proxy) and allocate via mean–CVaR optimizer (cvxpy)
   or a softmax fallback when cvxpy isn't available.
6) Walk-forward with embargo; small Bayesian-style search is possible but left out to keep the file lean.

Dependencies
------------
- Required: numpy, pandas, scikit-learn
- Optional (recommended): hmmlearn, lightgbm, xgboost, cvxpy, torch

Usage
-----
python fiam_regime_moe_pipeline.py \
  --data_csv /path/to/hackathon_sample_v2.csv \
  --date_col date --id_col ticker \
  --target_col ret_fwd_1m \
  --out_dir ./outputs

If you don't have a forward return column, pass a current return/price and the script can compute labels:
  --ret_col ret   (expects monthly simple returns per (date,id))
or
  --price_col close

Feature columns:
- If you pass --factor_list_csv factor_char_list.csv, the script will load these columns.
- Otherwise, it will auto-detect numeric columns and drop [date,id,target/ret/price].

Outputs
-------
out_dir/
  weights.csv           (date,id,weight)
  diagnostics.csv       (per-date metrics: IC, turnover, etc. when available)
  models/               (fitted models where supported by library pickling)
"""
import argparse, os, sys, warnings
import numpy as np
import pandas as pd
from dataclasses import dataclass
from typing import List, Optional, Tuple, Dict
import torch
from torch import nn
import cvxpy
import cvxpy as cp
import sklearn as sk
import hmmlearn.hmm as hmmlearn
import sklearn.mixture as sk_gmm
import sklearn.cluster as sk_km
import lightgbm as lgb
import xgboost as xgb


warnings.filterwarnings("ignore")

# Optional imports with graceful fallbacks
def _soft_import(mod_name):
    try:
        return __import__(mod_name)
    except Exception:
        return None

from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import ElasticNet
from sklearn.metrics import pairwise_distances

SEED = 123

# -------------------------
# Utilities
# -------------------------
def set_seed(seed: int = 123):
    np.random.seed(seed)
    if torch is not None:
        torch.manual_seed(seed)

def infer_feature_cols(df: pd.DataFrame, date_col: str, id_col: str, target_cols: List[str]) -> List[str]:
    drop_cols = set([date_col, id_col] + target_cols)
    num_cols = [c for c in df.columns if c not in drop_cols and pd.api.types.is_numeric_dtype(df[c])]
    return num_cols

def compute_labels(df: pd.DataFrame, date_col: str, id_col: str,
                   target_col: Optional[str], ret_col: Optional[str], price_col: Optional[str]) -> Tuple[pd.DataFrame, str]:
    """
    Ensure we have a forward 1m return label per (date,id). If target_col provided, use it.
    Else, try to build it from ret_col or price_col.
    """
    if target_col and target_col in df.columns:
        return df.copy(), target_col

    g = df.sort_values([id_col, date_col]).groupby(id_col, sort=False)
    if ret_col and ret_col in df.columns:
        # assume ret is current month return; label = next-month return
        df["ret_fwd_1m"] = g[ret_col].shift(-1)
        return df, "ret_fwd_1m"
    if price_col and price_col in df.columns:
        df["ret"] = g[price_col].pct_change()
        df["ret_fwd_1m"] = g["ret"].shift(-1)
        return df, "ret_fwd_1m"
    # If neither present, attempt to find a column that looks like next return
    candidates = [c for c in df.columns if "fwd" in c.lower() or "target" in c.lower()]
    if candidates:
        return df, candidates[0]
    raise ValueError("Could not infer target. Provide --target_col or --ret_col or --price_col.")

# -------------------------
# Regime modeling (soft p_t)
# -------------------------
def build_market_features(df: pd.DataFrame, date_col: str, id_col: str, target_col: str) -> pd.DataFrame:
    """
    Aggregate to market-level monthly features (equal-weight). You can extend with macro files.
    """
    # Equal-weight next-month (label) not available in real time; use current realized return proxy
    # Here we use contemporaneous cross-sectional stats as *state* (allowed for next-month decisions).
    # Compute current month realized return, volatility, and breadth proxies.
    df_sorted = df.sort_values([date_col, id_col])
    # try to locate a contemporaneous return column (ret or return)
    ret_candidates = [c for c in df.columns if c.lower() in ("ret","return","ret_1m","r1m")]
    if ret_candidates:
        rcol = ret_candidates[0]
    else:
        # fallback: use previous month's forward (lagged) as current realized (not ideal but ok for state)
        rcol = "_tmp_ret"
        g = df_sorted.groupby(id_col, sort=False)
        df_sorted[rcol] = g[target_col].shift(1)

    grp = df_sorted.groupby(date_col)
    mkt = pd.DataFrame({
        "ew_ret": grp[rcol].mean(),
        "xsec_vol": grp[rcol].std(ddof=0),
        "breadth_pos": grp[rcol].apply(lambda x: np.mean((x>0).astype(float))),
        "breadth_topdec": grp[target_col].apply(lambda x: np.mean((x.rank(pct=True)>0.9).astype(float))),
    }).reset_index()

    # z-score market features to stabilize HMM/GMM
    scaler = StandardScaler()
    feats = ["ew_ret","xsec_vol","breadth_pos","breadth_topdec"]
    mkt[feats] = scaler.fit_transform(mkt[feats].fillna(0.0))
    return mkt

def soft_regimes(mkt_df: pd.DataFrame, K: int = 3) -> pd.DataFrame:
    """
    Fit HMM (if available) else GMM (soft responsibilities), else k-means (soft via distance).
    Returns a DataFrame date -> p_t (K columns p_0..p_{K-1}) + entropy.
    """
    X = mkt_df[["ew_ret","xsec_vol","breadth_pos","breadth_topdec"]].to_numpy()
    dates = mkt_df.iloc[:,0].to_numpy()

    p = None
    if hmmlearn is not None:
        model = hmmlearn.GaussianHMM(n_components=K, covariance_type="full", n_iter=200, random_state=SEED)
        model.fit(X)
        logprob, post = model.score_samples(X)  # posterior probs (gamma)
        p = post
    elif sk_gmm is not None:
        gmm = sk_gmm.GaussianMixture(n_components=K, covariance_type="full", random_state=SEED, n_init=10)
        gmm.fit(X)
        p = gmm.predict_proba(X)
    else:
        # k-means soft assignments via inverse distance
        km = sk_km.KMeans(n_clusters=K, random_state=SEED, n_init=10)
        labs = km.fit_predict(X)
        D = pairwise_distances(X, km.cluster_centers_) + 1e-6
        W = 1.0 / D
        p = (W / W.sum(axis=1, keepdims=True))

    p = np.asarray(p)
    H = -(p * np.log(p + 1e-12)).sum(axis=1)
    out = pd.DataFrame({mkt_df.columns[0]: dates})
    for k in range(K):
        out[f"p_regime_{k}"] = p[:,k]
    out["regime_entropy"] = H
    return out

# -------------------------
# Temporal encoder (optional)
# -------------------------
class RegimeConvTorch:
    def __init__(self, in_feats: int, k_regimes: int, hidden: int = 16, L: int = 18, device: str = "cpu"):
        self.available = torch is not None
        self.L = L
        if not self.available:
            return
        import torch.nn as nn
        self.device = device
        self.model = nn.Sequential(
            nn.Conv1d(in_feats, hidden, kernel_size=5, padding=2),
            nn.GELU(),
            nn.Conv1d(hidden, hidden, kernel_size=3, padding=2, dilation=2),
            nn.GELU(),
        )
        self.film = nn.Linear(k_regimes, 2*hidden)
        self.pool = nn.AdaptiveAvgPool1d(1)
        self.hidden = hidden
        self.model.to(self.device); self.film.to(self.device)

    def encode(self, X_win: np.ndarray, p_t: np.ndarray) -> np.ndarray:
        """
        X_win: (N, L, F)
        p_t: (K,) regime probs for the month
        returns: (N, 2H) embedding
        """
        if not self.available:
            return np.zeros((X_win.shape[0], 1), dtype=np.float32)
        import torch.nn.functional as F
        x = torch.tensor(X_win, dtype=torch.float32, device=self.device).transpose(1,2)  # (N,F,L)
        h = self.model(x)  # (N,H,L)
        pb = torch.tensor(p_t[None,:], dtype=torch.float32, device=self.device)
        gamma, beta = self.film(pb).chunk(2, dim=-1)  # (1,H),(1,H)
        gamma = gamma[:,:,None]; beta = beta[:,:,None]
        h = gamma*h + beta
        h_avg = self.pool(h).squeeze(-1)           # (N,H)
        h_max = torch.max(h, dim=-1).values        # (N,H)
        z = torch.cat([h_avg, h_max], dim=-1)      # (N,2H)
        return z.detach().cpu().numpy()

# -------------------------
# Experts
# -------------------------
def train_ranker_lgb(train_df: pd.DataFrame, X_cols: List[str], y_rank: np.ndarray, groups: np.ndarray, sample_weight=None):
    if lgb is None:
        return None
    params = dict(objective="lambdarank", n_estimators=400, learning_rate=0.05,
                  max_depth=-1, subsample=0.8, colsample_bytree=0.8, random_state=SEED)
    model = lgb.LGBMRanker(**params)
    model.fit(train_df[X_cols], y_rank, group=groups, sample_weight=sample_weight)
    return model

def train_ridge_enet(train_df: pd.DataFrame, X_cols: List[str], y: np.ndarray, sample_weight=None):
    model = ElasticNet(alpha=0.001, l1_ratio=0.2, fit_intercept=True, random_state=SEED, max_iter=5000)
    model.fit(train_df[X_cols], y, sample_weight=sample_weight)
    return model

def predict_scores(model, X: pd.DataFrame):
    if model is None:
        # fallback to zero score
        return np.zeros(len(X))
    return np.asarray(model.predict(X))

# -------------------------
# Risk proxies
# -------------------------
def compute_per_asset_risk(df: pd.DataFrame, date_col: str, id_col: str, ret_col_guess: Optional[str]) -> pd.Series:
    """
    Compute per-asset risk proxy for current month decision: rolling volatility of *past* returns.
    """
    df_sorted = df.sort_values([id_col, date_col])
    ret_col = ret_col_guess
    if ret_col is None:
        # try to reconstruct from price or previous fwd label (lagged)
        for c in ["ret","return","ret_1m","r1m"]:
            if c in df.columns:
                ret_col = c
                break
        if ret_col is None:
            # try to backshift label (conservative)
            ret_col = "_tmp_ret_hist"
            g = df_sorted.groupby(id_col, sort=False)
            df_sorted[ret_col] = g["ret_fwd_1m"].shift(1)

    g = df_sorted.groupby(id_col, sort=False)
    df_sorted["_roll_vol"] = g[ret_col].rolling(window=12, min_periods=6).std(ddof=0).reset_index(level=0, drop=True)
    # align to original index
    return df_sorted["_roll_vol"]

# -------------------------
# Allocation
# -------------------------
def softmax_allocate(z: np.ndarray, topK: int = 50, cap: float = 0.07, tau: float = 2.0, prev_w: Optional[np.ndarray] = None, rho: float = 0.2):
    z = z - np.nanmean(z)
    z = z / (np.nanstd(z) + 1e-8)
    w = np.exp(tau*(z - np.nanmax(z)))
    w = np.nan_to_num(w, nan=0.0, posinf=0.0, neginf=0.0)
    if w.sum() <= 0:  # fallback
        w = np.ones_like(z)
    w /= w.sum()
    # top-K
    idx = np.argsort(-w)[:topK]
    mask = np.zeros_like(w); mask[idx] = 1.0
    w = w * mask; s = w.sum(); w = w/s if s>0 else w
    # cap
    w = np.minimum(w, cap); s = w.sum(); w = w/s if s>0 else w
    # turnover smoothing
    if prev_w is not None:
        w = (1-rho)*w + rho*prev_w
        w = w / w.sum()
    return w

def cvar_allocate_scenarios(scenarios: np.ndarray, mean_score: np.ndarray, lam: float = 1.5, alpha: float = 0.9,
                            cap: float = 0.07, topK: int = 50, prev_w: Optional[np.ndarray] = None, rho: float = 0.2):
    """
    Scenario matrix: shape (S,N) of returns for next month. Solve mean - lam * CVaR with linear constraints.
    Requires cvxpy; falls back to softmax if not available.
    """
    N = scenarios.shape[1]
    if cvxpy is None:
        z = mean_score  # already risk-tilted upstream
        return softmax_allocate(z, topK=topK, cap=cap, prev_w=prev_w, rho=rho)
    
    w = cp.Variable(N, nonneg=True)
    u = cp.Variable(scenarios.shape[0])  # CVaR aux
    t = cp.Variable(1)

    # Portfolio scenario P&L
    pnl = scenarios @ w  # (S,)
    # CVaR Rockafellar-Uryasev at level alpha on losses (-pnl)
    loss = -pnl
    cvar = t + (1/(1-alpha)) * cp.mean(cp.pos(loss - t))

    objective = cp.Maximize(mean_score @ w - lam * cvar)
    constraints = [cp.sum(w) == 1.0,
                   w <= cap]
    # topK via l0 approx: encourage sparsity with small l1 penalty towards topK (optional)
    prob = cp.Problem(objective, constraints)
    prob.solve(solver=cp.ECOS, verbose=False, warm_start=True)
    if w.value is None:
        return softmax_allocate(mean_score, topK=topK, cap=cap, prev_w=prev_w, rho=rho)
    wv = np.asarray(w.value).ravel()
    # enforce topK
    idx = np.argsort(-wv)[:topK]
    mask = np.zeros_like(wv); mask[idx] = 1.0
    wv = wv*mask; s = wv.sum(); wv = wv/s if s>0 else wv
    # turnover
    if prev_w is not None:
        wv = (1-rho)*wv + rho*prev_w
        wv = wv / wv.sum()
    return wv

# -------------------------
# Walk-forward split
# -------------------------
def walkforward_dates(unique_dates: List[pd.Timestamp], n_folds: int = 3, embargo: int = 2) -> List[Tuple[List[pd.Timestamp], List[pd.Timestamp]]]:
    """
    Split chronological dates into n_folds (train,val/test like rolling). For simplicity we return a list of (train_dates, test_dates).
    """
    unique_dates = sorted(unique_dates)
    T = len(unique_dates)
    folds = []
    # Roughly split into contiguous folds for testing; training uses data before test start minus embargo
    fold_size = max(T // (n_folds+1), 6)
    for k in range(1, n_folds+1):
        test_start = k*fold_size
        test_end   = min((k+1)*fold_size, T)
        if test_start >= T: break
        test_dates = unique_dates[test_start:test_end]
        train_end_idx = max(0, test_start - embargo)
        train_dates = unique_dates[:train_end_idx]
        if len(train_dates) < 24 or len(test_dates) < 6:
            continue
        folds.append((train_dates, test_dates))
    if not folds:
        # fallback single split: first 70% train, last 30% test
        cut = int(0.7*T)
        folds = [(unique_dates[:cut-embargo], unique_dates[cut:])]
    return folds

# -------------------------
# Main pipeline
# -------------------------
@dataclass
class Config:
    data_csv: str
    out_dir: str
    date_col: str = "date"
    id_col: str = "ticker"
    target_col: Optional[str] = None
    ret_col: Optional[str] = None
    price_col: Optional[str] = None
    factor_list_csv: Optional[str] = None
    k_regimes: int = 3
    use_conv_encoder: bool = False
    conv_L: int = 18
    topK: int = 50
    cap: float = 0.07
    tau: float = 2.0
    rho: float = 0.2
    lam_cvar: float = 1.5
    alpha_cvar: float = 0.9

def fit_and_trade(cfg: Config):
    set_seed(SEED)

    # Load data
    df = pd.read_csv(cfg.data_csv)
    if cfg.date_col not in df.columns:
        raise ValueError(f"date_col '{cfg.date_col}' not in data.")
    df[cfg.date_col] = pd.to_datetime(df[cfg.date_col])
    df.sort_values([cfg.date_col, cfg.id_col], inplace=True)

    # Ensure labels
    df, target_col = compute_labels(df, cfg.date_col, cfg.id_col, cfg.target_col, cfg.ret_col, cfg.price_col)

    # Features
    if cfg.factor_list_csv and os.path.exists(cfg.factor_list_csv):
        facs = pd.read_csv(cfg.factor_list_csv).columns.tolist()
        X_cols = [c for c in facs if c in df.columns]
    else:
        X_cols = infer_feature_cols(df, cfg.date_col, cfg.id_col, [target_col, cfg.ret_col or "", cfg.price_col or ""])

    # Regime posteriors p_t
    mkt = build_market_features(df, cfg.date_col, cfg.id_col, target_col)
    regimes = soft_regimes(mkt, K=cfg.k_regimes)  # date + p_regime_* + entropy
    df = df.merge(regimes, on=cfg.date_col, how="left")

    # Optional temporal encoder (requires torch); creates embeddings per asset for each date using past window
    if cfg.use_conv_encoder and torch is not None:
        enc = RegimeConvTorch(in_feats=len(X_cols), k_regimes=cfg.k_regimes, L=cfg.conv_L)
        # Build rolling window tensors per (date) group
        z_parts = []
        for dt, g in df.groupby(cfg.date_col):
            # gather window across previous L-1 months for each asset; simple pad with zeros if insufficient
            # For simplicity we skip per-asset alignment and use current month only (lightweight); productionize as needed.
            p = g[[c for c in regimes.columns if c.startswith("p_regime_")]].iloc[0].to_numpy()
            X_batch = g[X_cols].to_numpy()
            # Fake window with single frame repeated (to avoid heavy logic in this portable script)
            X_win = np.repeat(X_batch[:,None,:], enc.L, axis=1)  # (N,L,F)
            z = enc.encode(X_win, p)  # (N,2H)
            # Append to df
            z_df = pd.DataFrame(z, columns=[f"z_{i}" for i in range(z.shape[1])], index=g.index)
            z_parts.append(z_df)
        Z = pd.concat(z_parts).sort_index()
        for c in Z.columns:
            df[c] = Z[c]
        X_cols += [c for c in Z.columns]

    # Prepare rank targets (cross-sectional)
    def cs_rank(s):
        # Assign consecutive integer ranks from 0 for each group (LightGBM expects this); missing values are -1
        valid_mask = ~s.isna()
        ranks = pd.Series(index=s.index, dtype=int)
        if valid_mask.sum() > 0:
            # Rank only valid values, assign consecutive integers 0 to N-1
            valid_ranks = s[valid_mask].rank(method='dense', ascending=True).astype(int) - 1
            ranks[valid_mask] = valid_ranks
            ranks[~valid_mask] = -1  # LightGBM can handle -1 for missing labels
        return ranks

    df["y_rank"] = df.groupby(cfg.date_col)[target_col].transform(cs_rank)

    # Walk-forward
    dates = sorted(df[cfg.date_col].unique())
    folds = walkforward_dates(list(dates), n_folds=3, embargo=2)

    os.makedirs(cfg.out_dir, exist_ok=True)
    weights_out = []
    diagnostics = []

    prev_w_by_asset = None  # keep turnover smoothing across months in test

    for (train_dates, test_dates) in folds:
        train_df = df[df[cfg.date_col].isin(train_dates)].copy()
        test_df  = df[df[cfg.date_col].isin(test_dates)].copy()

        # Sample weights per regime for experts
        # For E1 trend expert: weight by regime 0 by default; user can reorder later
        # We'll heuristically assign: k=0 trend, k=1 meanrev, k=2 calm/stress mixture
        reg_cols = [c for c in df.columns if c.startswith("p_regime_")]
        def w_from_reg(df_in, k):
            return df_in[reg_cols[k]].to_numpy()

        # Group sizes for ranker (per date queries)
        group_sizes = train_df.groupby(cfg.date_col)[cfg.id_col].size().values

        # Group sizes for ranker (per date queries)
        train_df_clean = train_df.dropna(subset=["y_rank"])  # Remove rows with missing ranks
        group_sizes = train_df_clean.groupby(cfg.date_col)[cfg.id_col].size().values

        # Debug: ensure ranks are consecutive 0 to N-1 within each group
        max_rank_per_group = train_df_clean.groupby(cfg.date_col)["y_rank"].max()
        print(f"Max rank per group: {max_rank_per_group.max()}, Group sizes: {group_sizes.max()}")

        # Expert 1: LGBM ranker (if available)
        # e1 = train_ranker_lgb(train_df_clean, X_cols, train_df_clean["y_rank"].to_numpy(),
        #                       groups=group_sizes, sample_weight=w_from_reg(train_df, 0) if reg_cols else None)
        # Expert 2: ElasticNet (mean-rev-ish)
        e2 = train_ridge_enet(train_df, X_cols, train_df[target_col].to_numpy(), sample_weight=w_from_reg(train_df, 1) if reg_cols else None)
        # Global expert: ridge on all
        eg = train_ridge_enet(train_df, X_cols, train_df[target_col].to_numpy(), sample_weight=None)

        # Iterate test months
        for dt, g in test_df.groupby(cfg.date_col):
            X = g[X_cols]
            # Scores (standardize cross-sectionally)
            s1 = predict_scores(e1, X)
            s2 = predict_scores(e2, X)
            sg = predict_scores(eg, X)

            def zscore(a):
                a = a.astype(float)
                return (a - np.nanmean(a)) / (np.nanstd(a) + 1e-8)

            s1z, s2z, sgz = map(zscore, (s1,s2,sg))

            # Gate by p_t and entropy → weights over experts
            p_vec = g[reg_cols].iloc[0].to_numpy() if reg_cols else np.array([1.0,0.0,0.0])
            H = -(p_vec * np.log(p_vec + 1e-12)).sum()
            # Entropy gate: more entropy ⇒ more global
            a0, a1 = -0.5, 2.0
            w_g = 1.0 / (1.0 + np.exp(-(a0 + a1*H)))   # sigmoid
            w_r = (1 - w_g) * (p_vec[:2]/(p_vec[:2].sum()+1e-12)) if len(p_vec)>=2 else np.array([1.0])
            # Blend scores
            s_blend = w_g*sgz + (w_r[0] if len(w_r)>0 else 0.0)*s1z + (w_r[1] if len(w_r)>1 else 0.0)*s2z

            # Risk proxy (per-asset rolling vol) for tilt
            risk = compute_per_asset_risk(df[df[cfg.date_col] <= dt], cfg.date_col, cfg.id_col, cfg.ret_col)
            risk = risk.loc[g.index].fillna(risk.loc[g.index].median())

            z = zscore(s_blend) - 0.5*zscore(risk.to_numpy())  # alpha=1, gamma=0.5

            # Allocation
            tickers = g[cfg.id_col].tolist()
            if prev_w_by_asset is None:
                prev = np.zeros(len(tickers))
            else:
                prev = np.array([prev_w_by_asset.get(t, 0.0) for t in tickers])

            # Scenario-free softmax allocation; if you have cvxpy & scenarios, swap to cvar_allocate_scenarios
            w = softmax_allocate(z, topK=cfg.topK, cap=cfg.cap, tau=cfg.tau, prev_w=prev, rho=cfg.rho)

            prev_w_by_asset = dict(zip(tickers, w))

            for tkr, wi in zip(tickers, w):
                weights_out.append({cfg.date_col: dt, cfg.id_col: tkr, "weight": float(wi)})

            diagnostics.append({cfg.date_col: dt, "regime_entropy": float(H), "w_global_gate": float(w_g)})

    wdf = pd.DataFrame(weights_out).sort_values([cfg.date_col, cfg.id_col])
    ddf = pd.DataFrame(diagnostics).drop_duplicates(subset=[cfg.date_col]).sort_values(cfg.date_col)

    wpath = os.path.join(cfg.out_dir, "weights.csv")
    dpath = os.path.join(cfg.out_dir, "diagnostics.csv")
    wdf.to_csv(wpath, index=False)
    ddf.to_csv(dpath, index=False)
    print(f"[OK] Wrote weights -> {wpath}")
    print(f"[OK] Wrote diagnostics -> {dpath}")

def parse_args():
    ap = argparse.ArgumentParser()
    ap.add_argument("--data_csv", type=str, required=True)
    ap.add_argument("--out_dir", type=str, required=True)
    ap.add_argument("--date_col", type=str, default="date")
    ap.add_argument("--id_col", type=str, default="ticker")
    ap.add_argument("--target_col", type=str, default=None)
    ap.add_argument("--ret_col", type=str, default=None)
    ap.add_argument("--price_col", type=str, default=None)
    ap.add_argument("--factor_list_csv", type=str, default=None)
    ap.add_argument("--k_regimes", type=int, default=3)
    ap.add_argument("--use_conv_encoder", action="store_true")
    ap.add_argument("--conv_L", type=int, default=18)
    ap.add_argument("--topK", type=int, default=50)
    ap.add_argument("--cap", type=float, default=0.07)
    ap.add_argument("--tau", type=float, default=2.0)
    ap.add_argument("--rho", type=float, default=0.2)
    ap.add_argument("--lam_cvar", type=float, default=1.5)
    ap.add_argument("--alpha_cvar", type=float, default=0.9)
    args = ap.parse_args()
    return Config(**vars(args))

if __name__ == "__main__":
    cfg = parse_args()
    fit_and_trade(cfg)
