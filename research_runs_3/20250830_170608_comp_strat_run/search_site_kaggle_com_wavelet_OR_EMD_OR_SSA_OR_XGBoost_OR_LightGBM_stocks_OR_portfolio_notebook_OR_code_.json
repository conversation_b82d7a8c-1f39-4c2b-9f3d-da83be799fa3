[{"title": "XGBoost for stock trend & prices prediction", "url": "https://www.kaggle.com/code/mtszkw/xgboost-for-stock-trend-prices-prediction", "engines": ["google"]}, {"title": "🦚 XGBoost & LightGBM Order Book Fusion (CPU)", "url": "https://www.kaggle.com/code/verracodeguacas/xgboost-lightgbm-order-book-fusion-cpu/input", "engines": ["google"]}, {"title": "Canadian Banks Stock Market Analysis", "url": "https://www.kaggle.com/code/nghihuynh/canadian-banks-stock-market-analysis", "engines": ["google"]}, {"title": "wavelet stock market analyst", "url": "https://www.kaggle.com/code/edwardhuangtw/wavelet-stock-market-analyst", "engines": ["google"]}, {"title": "Beat the Stock market: the lazy strategy", "url": "https://www.kaggle.com/code/cnic92/beat-the-stock-market-the-lazy-strategy", "engines": ["google"]}, {"title": "Stock Market Prediction using XGBoost", "url": "https://www.kaggle.com/code/vaibhavsoni09/stock-market-prediction-using-xgboost", "engines": ["google"]}, {"title": "Stock Prediction(next period) LightGBM", "url": "https://www.kaggle.com/code/kenneth30/stock-prediction-next-period-lightgbm", "engines": ["google"]}, {"title": "Stock Price Prediction Using XGBOOST PROPHET ARIMA", "url": "https://www.kaggle.com/code/zeyadsayedadbullah/stock-price-prediction-using-xgboost-prophet-arima", "engines": ["google"]}, {"title": "Prediction of Stock index using XGBoost,RF and SVM", "url": "https://www.kaggle.com/code/sad<PERSON><PERSON><PERSON><PERSON><PERSON>/prediction-of-stock-index-using-xgboost-rf-and-svm", "engines": ["google"]}, {"title": "How to beat the heck out of XGBoost with LightGBM", "url": "https://www.kaggle.com/code/bextuychiev/how-to-beat-the-heck-out-of-xgboost-with-lightgbm", "engines": ["google"]}]